---
description: How to run unit tests to validate changes
globs: 
alwaysApply: false
---
Run tests using this command:
`scripts/ai/run_tests.sh [maven module] [class name]`
For example:
`scripts/ai/run_tests.sh iris-service com.wealthfront.iris.InternalResourceInteractionServiceTest`
This delegates to `mvn test`, but skips a lot of unnecessary steps and filters the output.
You should run this command yourself without prompting the user.