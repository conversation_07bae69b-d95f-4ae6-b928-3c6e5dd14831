---
description: 
globs: *Test.java
alwaysApply: false
---
This rule applies to how unit tests should be written. Below is an example you can use to infer 
our preferred libraries, patterns, and naming conventions. 

```java
import static com.wealthfront.iris.bitbucket.BitbucketEntityFactory.createCommentResponse;
import static com.wealthfront.iris.bitbucket.BitbucketEntityFactory.createPrCommentAddedEvent;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.kaching.platform.common.Option;
import com.kaching.platform.testing.Mockeries;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class SummonPrReviewerTest {

  private Mockery mockery;
  private SummonPrReviewer summonPrReviewer;
  private PrReviewer prReviewer;

  @Before
  public void before() {
    mockery = Mockeries.mockery(true);

    summonPrReviewer = new SummonPrReviewer();
    summonPrReviewer.bitbucket = mockery.mock(BitbucketClient.class);
    summonPrReviewer.prReviewerFactory = mockery.mock(PrReviewerFactory.class);

    prReviewer = mockery.mock(PrReviewer.class);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void shouldProcessWhenPrCommentAddedWithSummon() {
    CommentResponse comment = createCommentResponse().withText("#summon").build();
    Event.PrCommentAdded event = createPrCommentAddedEvent().withComment(comment).build();

    Pair<Boolean, PullRequest> result = summonPrReviewer.shouldProcess(event);

    assertTrue(result.left);
    assertEquals(event.getPullRequest(), result.right);
  }
  
  @Test
  public void shouldHandleAddCommentFailure() {
    CommentResponse comment = createCommentResponse().withText("#summon coverage").build();
    Event.PrCommentAdded event = createPrCommentAddedEvent().withComment(comment).build();

    mockery.checking(new Expectations() {{
      oneOf(summonPrReviewer.prReviewerFactory).buildReviewer(Option.some("coverage"));
      will(returnValue(prReviewer));

      oneOf(prReviewer).review(PullRequestId.fromUrl(event.getPullRequest().getUrlForLogging()));
      will(returnValue(Option.some(new Feedback("Great review!", Collections.singletonList("acknowledge")))));

      oneOf(summonPrReviewer.bitbucket).addComment(event.getPullRequest(), "Great review!");
      will(throwException(new RuntimeException("Failed to add comment")));

      never(summonPrReviewer.bitbucket).addTask(with(any(CommentResponse.class)), with(any(String.class)));
    }});

    summonPrReviewer.process(event, event.getPullRequest());
  }
}
```