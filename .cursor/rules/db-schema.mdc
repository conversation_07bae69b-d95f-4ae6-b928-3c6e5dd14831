---
description: 
globs: 
alwaysApply: false
---
I'm making an AI agent framework.

## Base AI Infrastructure
AiWorkflow
- Represents a higher-level task, like 'generate documentation for this system' or 'analyze this PR for bugs'

AiSession
- A stateful back-and-forth conversation with an LLM
- Many AiSessions per AiWorkflow

AiContextItems
- A single item being added to an AiSession (tool call, text input, text output, etc)
- Will allow us an interface to inspect conversations and see how they went
- Many context items per AiSession
- Will be encrypted
- Will probably need to be trimmed

AiApiCalls
- Tracks API usage for cost tracking

## Higher Level AI Tables
AiResearchQuestions
- An abstraction I've found useful for both PR review and doc generation
- Question/Answer, tree-like structure
- Answer will be encrypted
- Will want to show all the Qs and As for a given workflow in the web gui

Documentation Prompts and Results
- Will allow engineers to add prompts
- Prompts are regularly refreshed to create results
- Probably will encrypt results, at least for compression

PR Analysis Result
- Want a table to join the AiWorkflow to the PR being analyzed, and store details

Document Links
- Documents (code, jira tickets, PRs, schemas, etc) can be linked to all kinds of things included AiQuestions, documentation prompts, and documentation results

## Schema
```sql
CREATE TABLE ai_workflows (
  id           BIGINT       NOT NULL AUTO_INCREMENT,
  version      INT          NOT NULL DEFAULT 0,
  created_at   DATETIME     NOT NULL,
  type         VARCHAR(255) NOT NULL,
  type_version INT          NOT NULL,
  details      MEDIUMTEXT   NOT NULL,
  completed_at DATETIME     DEFAULT NULL,
  PRIMARY KEY (id),
  KEY ai_workflows_type_completed_at (type, completed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_sessions (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_workflow_id BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  agent_name     VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  KEY ai_sessions_ai_workflow_id (ai_workflow_id) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_context_items (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_session_id  BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  type           VARCHAR(255),
  content_raw    MEDIUMTEXT,
  content_md     MEDIUMTEXT,
  PRIMARY KEY (id),
  KEY ai_context_items_ai_session_id (ai_session_id) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_api_calls (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_session_id  BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  model          VARCHAR(255),
  cost           DECIMAL(14, 2),
  details        TEXT,
  PRIMARY KEY (id),
  KEY ai_api_calls_ai_session_id (ai_session_id) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_research_questions (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_workflow_id BIGINT       NOT NULL,
  parent_id      BIGINT       DEFAULT NULL,
  created_at     DATETIME     NOT NULL,
  state          VARCHAR(255) NOT NULL,
  updated_at     DATETIME     NOT NULL,
  question       TEXT         NOT NULL,
  details        MEDIUMTEXT   NOT NULL,
  answer         MEDIUMTEXT   DEFAULT NULL,
  PRIMARY KEY (id),
  KEY ai_research_questions_ai_workflow_id (ai_workflow_id),
  KEY ai_research_questions_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_api_cache (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_api_call_id BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  expires_at     DATETIME     NOT NULL,
  cache_key_hash CHAR(64)     NOT NULL,
  cache_key      MEDIUMTEXT   NOT NULL,
  cache_value    MEDIUMTEXT   NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY ai_api_cache_key_hash (cache_key_hash),
  KEY ai_api_cache_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE documentation_prompts (
  id                BIGINT       NOT NULL AUTO_INCREMENT,
  version           INT          NOT NULL DEFAULT 0,
  created_at        DATETIME     NOT NULL,
  deleted_at        DATETIME     DEFAULT NULL,
  last_generated_at DATETIME     DEFAULT NULL,
  system_name_short VARCHAR(255) NOT NULL,
  system_name_long  VARCHAR(255) NOT NULL,
  details           MEDIUMTEXT   NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY documentation_prompts_system_name_short (system_name_short) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE documentation_results (
  id                      BIGINT       NOT NULL AUTO_INCREMENT,
  version                 INT          NOT NULL DEFAULT 0,
  documentation_prompt_id BIGINT       NOT NULL,
  ai_workflow_id          BIGINT       NOT NULL,
  created_at              DATETIME     NOT NULL,
  result                  MEDIUMTEXT   NOT NULL,
  PRIMARY KEY (id),
  KEY documentation_results_prompt_id (documentation_prompt_id),
  KEY documentation_results_ai_workflow_id (ai_workflow_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE pull_request_analysis_results (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  created_at     DATETIME     NOT NULL,
  repository     VARCHAR(255) NOT NULL,
  pull_requestid BIGINT       NOT NULL,
  type           VARCHAR(255) NOT NULL,
  ai_workflow_id BIGINT       DEFAULT NULL,
  result         MEDIUMTEXT   DEFAULT NULL,
  PRIMARY KEY (id),
  KEY pr_analysis_result_ai_workflow_id (ai_workflow_id),
  KEY pr_analysis_result_repository_prid (repository, pull_requestid),
  KEY pr_analysis_result_type_repository_prid (type, repository, pull_requestid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE document_links (
  id             BIGINT        NOT NULL AUTO_INCREMENT,
  version        INT           NOT NULL DEFAULT 0,
  created_at     DATETIME      NOT NULL,
  deleted_at     DATETIME      DEFAULT NULL,
  document_ref   VARCHAR(767)  NOT NULL,
  link_type      VARCHAR(255)  NOT NULL,
  linkid         BIGINT        NOT NULL,
  score          DECIMAL(16,8) NOT NULL,
  details        TEXT          DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY document_links_unique (document_ref, link_type, linkid),
  KEY document_links_type_linkid (link_type, linkid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
```