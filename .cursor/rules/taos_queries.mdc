---
description: 
globs: 
alwaysApply: false
---
<PERSON><PERSON> is Wealthfront's internal tooling web GUI. 
Backend services expose Queries to TAOS using the Query Engine just as they expose Queries to other services.
Taos is written in Ruby, TypeScript, and React, but the Expose framework allows us to interact with a backend Query in a type-safe way. 

## Example TAOS Query
```java
package com.wealthfront.cash.account;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.kaching.api.ExposeQuery;
import com.kaching.api.ExposeTo;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.AbstractQuery;
import com.twolattes.json.Entity;
import com.wealthfront.brokerage.account.AccountId;
import com.wealthfront.brokerage.account.AccountKind;

@ExposeQuery(value = ExposeTo.TAOS)
public class GetSweepAccountForTaos extends AbstractQuery<GetSweepAccountForTaos.SweepAccountView> {

  @Entity
  @ExposeType(value = ExposeTo.TAOS)
  public record SweepAccountView(
      Id<SweepAccount> sweepAccountId,
      SweepAccount.State state,
      AccountId accountId,
      AccountKind accountKind,
      DateTime createdAt
  ) {}
  
  private final AccountId accountId;dd

  public GetSweepAccountForTaos(AccountId accountId) {
    this.accountId = accountId;
  }
  
  @Inject RetryingTransacter transacter;
  
  @Override
  public SweepAccountView process() {
    return transacter.execute(new WithReadOnlySessionExpression<>() {
      @Inject SweepAccountRepository repository;

      @Override
      public SweepAccountView run(DbSession session) {
        SweepAccount account = repository.getByAccountId(accountId).getOrThrow();
        return new SweepAccountView(
            account.getId(),
            account.getCurrentState(),
            account.getAccountId(),
            account.getAccountKind(),
            account.getCreatedAt()
        );
      }
    });
  }

}
```

## Expose Framework
An AbstractQuery that will be used on TAOS should always have `@ExposeQuery(value = ExposeTo.TAOS)`. All parameters and data classes that are used 
or returned from the query should have `@ExposeType(value = ExposeTo.TAOS)`.
```java
package com.kaching.api;

public enum ExposeTo {
  LOCAL,
  BACKEND,
  API_SERVER,
  FRONTEND,
  TAOS
}
```
```java
package com.kaching.api;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

@Retention(RUNTIME)
@Target(TYPE)
public @interface ExposeType {

  enum RewriteNamespace {
    GLOBAL,                // rewritten to com.wealthfront.global.*
    SERVICE,               // rewritten to com.wealthfront.types.[service kind].*
    QUERY,                 // rewritten to com.wealthfront.stubqueries.[service kind].[Query Name].*
    DO_NOT_COPY,           // code gen deactivated
    INLINE_INTO_CHILDREN   // Inline fields of a non-polymorphic abstract json parent class (e.g. AbstractBackendResult) into its children
  }

  ExposeTo[] value();

  RewriteNamespace namespace() default RewriteNamespace.QUERY;

  SkipValidation[] skipValidation() default {};

}
```
Data types for taos queries are fine to keep the default namespace of `QUERY`, as they are rarely shared between queries.
At build time, TypeScript types will automatically be recursively generated for all types that are used in a Taos query.

Hibernate IDs, (com.kaching.platform.hibernate.Id) see [.cursor/rules/hibernate.mdc](mdc:hibernate.mdc), are automatically rewritten into an external
ID type, so long as the AbstractHibernateEntity has a com.kaching.api.ExposeId annotation, as all entities should have:
```java
package com.wealthfront.cash.account;

import com.kaching.api.ExposeId;
import com.kaching.platform.hibernate.Id;
import com.wealthfront.banking.client.SweepAccountId;

@ExposeId(exposedIdClass = SweepAccountId.class)
public class SweepAccount extends AbstractHibernateEntity {
  // ...
}
```

```java
package com.wealthfront.banking.client;

import com.google.common.base.Function;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.util.id.LongIdentifiers;
import com.twolattes.json.MarshalledBy;

@ConvertedBy(SweepAccountId.Converter.class)
@MarshalledBy(SweepAccountId.JsonType.class)
public class SweepAccountId extends AbstractIdentifier<Long> {

  public SweepAccountId(long id) {
    super(id);
  }

  public static class Converter extends LongIdentifiers.Converter<SweepAccountId> {

    @Override
    protected Function<Long, SweepAccountId> constructor() {
      return SweepAccountId::new;
    }

  }

  public static class JsonType extends LongIdentifiers.JsonType<SweepAccountId> {

    @Override
    protected Function<Long, SweepAccountId> constructor() {
      return SweepAccountId::new;
    }

  }

}
```

