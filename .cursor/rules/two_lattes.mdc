---
description: 
globs: 
alwaysApply: false
---
Two-La<PERSON> is Wealthfront's home built Java/JSON marshalling framework.
## Objects
JSON objects can be modeled as records or classes:
```java
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
record CostOverview(
    @Value(optional = true) String modality,
    @Value(nullable = false) int tokenCount,
    @Value(name = "total_cost") Money totalCost
) {}

@Entity
class CostOverviews {

  @Value
  private List<CostOverview> overviews;

  private CostOverviews() { /* JSON */ }

  public CostOverviews(List<CostOverview> overviews) {
    this.overviews = overviews;
  }

  public List<CostOverview> getOverviews() {
    return overviews;
  }
}
```
Records are usually preferred if possible, but sometimes mutability is required.
`optional` means the field is not required to exist in the serialized JSON, and will default to null if not present in the JSON. By default, all fields are required, and `optional=false`
`nullable` means the field is allowed to be null. By default, all fields are nullable `nullable=true` except primitives, which must have `nullable=false`
`name` overrides the serialized object key. Our style guide says that all Java fields must be camel case, so if we need JSON with snake case to interact with a 3rd party API, use `name`

## Polymorphism
Two-Lattes supports discrimated unions, which we use quite a bit. 
```java
@Entity(discriminatorName = "type", subclasses = {
    BuyTransaction.class,
    CashTransaction.class
})
abstract static class Transaction {

  @Value(nullable = true) AccountId accountId;

  Transaction() { /* JSON */ }

  Transaction(AccountId accountId) {
    this.accountId = accountId;
  }

  public Option<AccountId> getAccountId() {
    return Option.of(accountId);
  }
  
}

@Entity(discriminator = "buy")
static class BuyTransaction extends Transaction {

  @Value(nullable = false) InstrumentId instrumentId;

  BuyTransaction() { /* JSON */ }
  
  public BuyTransaction(AccountId accountId, InstrumentId instrumentId) {
    super(accountId);
    this.instrumentId = instrumentId;
  }

  public InstrumentId getInstrumentId() {
    return instrumentId;
  }
  
}

@Entity(discriminator = "cash")
static class CashTransaction extends Transaction {

  @Value(nullable = false) Money value;

  CashTransaction() { /* JSON */ }

  public CashTransaction(AccountId accountId, Money value) {
    super(accountId);
    this.value = value;
  }

  public Money getValue() {
    return value;
  }
  
}
```
In the above example, a `new CashTransaction(new AccountId(11), Money.money(12.34))` will marshall to:
```json
{
  "type": "cash",
  "accountId": 123,
  "value": 12.34
}
```
This polymorphism can only be used with classes, not records.
Only leaf node classes should ever be concrete and instantiable, never the parents.
If the current repository supports sealed abstract classes, we use those. If not, we implement the Visitor pattern, with a `Visitor<T>` interface and methods like this on all leaf node classes:
```java
public <T> T visit(Visitor<T> visitor) {
  return visitor.caseCashTransaction(this);
}
```

## Raw JSON
Two-Lattes has an intermediate, untyped json representation which is used when marshalling and is also used extensively in tests

```java
package com.twolattes.json;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import com.twolattes.json.types.NoOpJsonType;


public final class Json {
  
  public interface Value {
    
    <T> T visit(JsonVisitor<T> visitor);

    java.lang.String toString(); // returns compact json string

    java.lang.String toPrettyPrintString(); // returns nicely indented json string

  }
  
  public interface Object extends Value {

    Json.Value put(Json.String key, Json.Value value);

    Json.Value get(Json.String key);

    boolean containsKey(Json.String key);

    Set<Json.String> keySet();

    Set<Map.Entry<Json.String, Json.Value>> entrySet();

    Collection<Json.Value> values();

    boolean isEmpty();

    int size();

  }
  
  public interface Array extends Value, Iterable<Value> {

    void add(int index, Json.Value element);

    boolean add(Json.Value element);

    Json.Value get(int index);

    boolean isEmpty();

    int size();

    List<Json.Value> values();

  }
  
  public interface String extends Value, Comparable<Json.String> {

    java.lang.String getString();

    boolean isEmpty();

  }
  
  public interface Number extends Value {

    BigDecimal getNumber();

  }
  
  public interface Boolean extends Value {

    boolean getBoolean();

  }
  
  @MarshalledBy(NoOpJsonType.JsonNullJsonType.class)
  public interface Null extends Json.Array, Json.Boolean, Json.Number, Json.Object, Json.String {
  }

  static abstract class BaseValue implements Json.Value {
    
    // hashCode, equals, toString, toPrettyPrintString implemented

  }

  static class ObjectImpl extends BaseValue implements Json.Object {

    final Map<Json.String, Json.Value> delegate = new TreeMap<>();

    <T> T visit(JsonVisitor<T> visitor) {
      return visitor.caseObject(this);
    }
    
    // delegates to TreeMap

  }

  static class ArrayImpl extends BaseValue implements Json.Array {

    private final List<Json.Value> delegate = new LinkedList<>();

    <T> T visit(JsonVisitor<T> visitor) {
      return visitor.caseArray(this);
    }

    // delegates most methods to List

  }

  static class BooleanImpl extends BaseValue implements Json.Boolean {

    final boolean b;

    BooleanImpl(boolean b) {
      this.b = b;
    }

    <T> T visit(JsonVisitor<T> visitor) {
      return visitor.caseBoolean(this);
    }
    
    // delegates to boolean

  }

  static abstract class NumberImpl extends BaseValue implements Json.Number {

    <T> T visit(JsonVisitor<T> visitor) {
      return visitor.caseNumber(this);
    }

  }

  static class NumberImplLong extends NumberImpl {

    final long number;

    NumberImplLong(long number) {
      this.number = number;
    }
    
    BigDecimal getNumber() {
      return BigDecimal.valueOf(number);
    }

  }

  static class NumberImplBigDecimal extends NumberImpl {

    final BigDecimal number;

    NumberImplBigDecimal(BigDecimal number) {
      this.number = number;
    }
    
    BigDecimal getNumber() {
      return number;
    }

  }

  static class StringImpl extends BaseValue implements Json.String {

    final java.lang.String string;

    StringImpl(java.lang.String string) {
      this.string = string;
    }
    
    <T> T visit(JsonVisitor<T> visitor) {
      return visitor.caseString(this);
    }

    int compareTo(Json.String that) {
      return this.string.compareTo(that.getString());
    }

    java.lang.String getString() {
      return string;
    }

    boolean isEmpty() {
      return string.isEmpty();
    }

  }

  static class NullImpl extends BaseValue implements Json.Null {

    <T> T visit(JsonVisitor<T> visitor) {
      return visitor.caseNull();
    }

    boolean getBoolean() {
      throw new NullPointerException();
    }
    
    // All other methods throw NullPointerException

  }
  
  public static final Json.Null NULL = new Json.NullImpl();
  public static final Json.Boolean TRUE = new Json.BooleanImpl(true);
  public static final Json.Boolean FALSE = new Json.BooleanImpl(false);
  
  public static Json.Value fromString(java.lang.String input) {
    // impl truncated
  }

  public static Json.Array array(Json.Value... values) {
    return new Json.ArrayImpl(values);
  }

  public static Json.Array array(Iterable<Json.Value> values) {
    return new Json.ArrayImpl(values);
  }

  public static Json.Array array(Json.Array value) {
    return new Json.ArrayImpl(value);
  }

  public static Json.Array array(java.lang.Object... values) {
    Json.Value[] jsonValues = new Json.Value[values.length];
    for (int i = 0; i < values.length; i++) {
      jsonValues[i] = value(values[i]);
    }
    return new Json.ArrayImpl(jsonValues);
  }

  public static Json.Object object() {
    return new Json.ObjectImpl();
  }

  public static Json.Object object(Json.Value... keyValuePairs) {
    if (keyValuePairs.length % 2 != 0) {
      throw new IllegalArgumentException("Number of arguments must be even");
    }
    Json.Object o = object();
    for (int i = 0; i < keyValuePairs.length; i += 2) {
      Json.Value key = keyValuePairs[i];
      if (key instanceof Json.String) {
        o.put((Json.String) key, keyValuePairs[i + 1]);
      } else {
        throw new IllegalArgumentException("Keys must be JSON strings");
      }
    }
    return o;
  }

  // accepts common java and two-lattes types like Strings, primitives, or Json.Values  
  public static Json.Object object(java.lang.Object... kvPairs) {
    if (kvPairs.length % 2 != 0) {
      throw new IllegalArgumentException("Number of args must be even");
    }
    Json.Object o = object();
    for (int i = 0; i < kvPairs.length; i += 2) {
      java.lang.Object key = kvPairs[i];
      if (key instanceof java.lang.String) {
        o.put(string((java.lang.String) key), value(kvPairs[i + 1]));
      } else if (key instanceof Json.String) {
        o.put((Json.String) key, value(kvPairs[i + 1]));
      } else {
        throw new IllegalArgumentException("Keys must be strings");
      }
    }
    return o;
  }

  public static Json.Number number(long number) {
    return new Json.NumberImplLong(number);
  }

  public static Json.Number number(BigDecimal number) {
    return new Json.NumberImplBigDecimal(number);
  }
  
  // not shown: short, int, float, double number() variants

  public static Json.String string(java.lang.String string) {
    return new Json.StringImpl(string);
  }

  private static <T> Json.Value value(T object) {
    if (object == null) {
      return NULL;
    }
    Descriptor<T, ?> descriptor = (Descriptor<T, ?>)
      DescriptorFactory.baseTypes.get(object.getClass());
    if (descriptor != null) {
      return descriptor.marshall(object);
    } else if (object instanceof Json.Value) {
      return (Json.Value) object;
    } else {
      throw new IllegalArgumentException(
          "Cannot auto-convert " + object.getClass() + " to Json.Value");
    }
  }

}
```
Json.fromString() is often used when interacting with 3rd party APIs, and the intermediate types are often used directly in unit tests:
```java
import static org.junit.Assert.assertEquals;
import static com.twolattes.json.Json.array;
import static com.twolattes.json.Json.object;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.entities.Money.money;

import java.util.List;

Marshaller<CostOverviews> marshaller = createMarshaller(CostOverviews.class);
Json.Value expected = object(
  "overviews", array(
    object(
      "modality", "text",
      "tokenCount", 123,
      "total_cost", 43.11
    )
  )
);
CostOverviews actual = new CostOverviews(List.of(new CostOverview("text", 123, money(43.11))));
assertEquals(expected, marshaller.marshall(actual));
```
Note that when building the `Json.Object` we could wrap all keys and values in `Json.string()`, `Json.number`, etc, but it's preferred to keep them bare 
primitives and let `Json.object()` do that for us.
When building objects like this, only indent 2 spaces.
Note the static `com.kaching.DefaultKachingMarshallers.createMarshaller` and json imports.


## Marshaller interface
```java
package com.twolattes.json;

import java.util.Collection;
import java.util.List;

public interface Marshaller<T> {
  Json.Value marshall(T object);

  Json.Array marshallList(Collection<? extends T> objects);

  T unmarshall(Json.Value value);

  List<T> unmarshallList(Json.Array array);

  Class<? extends Json.Value> getMarshalledClass();

  Class<T> getConcreteClass();
}
```

## Types
The JsonType interface allows individual fields to be marshalled to and from json types
```java
package com.twolattes.json.types;

import com.twolattes.json.Json;

public interface JsonType<T, J extends Json.Value> {
  J marshall(T object);

  T unmarshall(J object);
}
```
Most types extend NullHandlingConverter
```java
package com.twolattes.json.types;

import com.twolattes.json.Json;

public abstract class NullSafeType<E, J extends Json.Value> implements JsonType<E, J> {
  public NullSafeType() {}

  public final J marshall(E entity) {
    return (J)(entity == null ? null : this.nullSafeMarshall(entity));
  }
  public E unmarshall(J object) {
    return (E)(object == null ? null : this.nullSafeUnmarshall(object));
  }
  protected abstract J nullSafeMarshall(E entity);

  protected abstract E nullSafeUnmarshall(J object);
}
```
Custom java types (often identifiers or BigDecimal wrappers like Money, Quantity, Price) can be directly marshalled or used
in objects if they have a type and a MarshalledBy annotation. 
```java
import com.kaching.platform.common.AbstractLongIdentifier;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@MarshalledBy(IdvCheckId.JsonType.class)
public class IdvCheckId extends AbstractLongIdentifier {

  public IdvCheckId(long id) {
    super(id);
  }

  public static class JsonType extends NullSafeType<IdvCheckId, Json.Number> {
    @Override
    protected Json.Number nullSafeMarshall(IdvCheckId id) {
      return Json.number(id.getId());
    }

    @Override
    protected IdvCheckId nullSafeUnmarshall(Json.Number id) {
      return new IdvCheckId(id.getNumber().longValueExact());
    }
  }
}
```
## Common Types
Many common Wealthfront types have such annotations, or otherwise are 'built in' to two-lattes:
- most all things implementing `com.kaching.platform.common.Identifier`
- `com.kaching.platform.hibernate.Id`
- `com.kaching.entities.Money`, `Quantity`, `Price`, `Scalar`
- `org.joda.time.DateTime` ('yyyy-MM-dd', 'yyyy-MM-dd HH:mm:ss', or 'yyyy-MM-dd HH:mm:ss.SSS' format), always in the DateTimeZones.ET timezone
```java
package com.wealthfront.util.time;
import org.joda.time.DateTimeZone;

public class DateTimeZones {
  public static final DateTimeZone ET = DateTimeZone.forID("America/New_York");
  public static final DateTimeZone PT = DateTimeZone.forID("America/Los_Angeles");
  ...
```
- `org.joda.LocalDate` (yyyymmdd format), `Days`, `Years`
- `com.kaching.entities.Symbol` (wraps String)
- `com.kaching.trading.shared.Cusip` (wraps String)
- `com.kaching.trading.shared.InstrumentId` (wraps long)