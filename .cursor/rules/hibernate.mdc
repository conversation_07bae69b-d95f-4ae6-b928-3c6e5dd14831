---
description: 
globs: 
alwaysApply: false
---
## MariaDB
Wealthfront uses Hibernate to interact with our MariaDB clusters, where we store almost all application state. We run MariaDB 10.5.5 in READ_COMMITTED mode.

## Transactions
Database transactions are started by injecting a Transacter, or preferably a RetryingTransacter
```java
package com.kaching.platform.hibernate;

import com.google.inject.ImplementedBy;

@ImplementedBy(TransacterImpl.class)
public interface Transacter {
  void execute(WithSession callback);

  void executeWithSession(WithSession callback);

  void execute(WithReadOnlySession callback);

  void executeWithReadOnlySession(WithReadOnlySession callback);

  <T> T execute(WithSessionExpression<T> expression);

  <T> T executeWithSessionExpression(WithSessionExpression<T> expression);

  <T> T execute(WithReadOnlySessionExpression<T> expression);

  <T> T executeWithReadOnlySessionExpression(WithReadOnlySessionExpression<T> expression);

  // Convenience methods. These should generally not be used, as they require a 'leaked' Hibernate entity
  <T extends HibernateEntity> void update(T t);

  <T extends HibernateEntity> Id<T> save(T t);

  <T extends HibernateEntity> void saveOrUpdate(T t);

  <T extends HibernateEntity> void delete(T t);

}
```
RetryingTransacter is almost always preferred, as transient exceptions are expected whenever interacting with MariaDB:
```java
package com.kaching.platform.hibernate;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.common.MySqlErrorNumbers;
import com.kaching.platform.common.logging.Log;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.sql.SQLTransactionRollbackException;
import java.util.function.Predicate;
import org.hibernate.QueryTimeoutException;
import org.hibernate.StaleStateException;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.GenericJDBCException;
import org.hibernate.exception.LockAcquisitionException;
import org.hibernate.exception.SQLGrammarException;

@Singleton
public class RetryingTransacter {

  private final Transacter transacter;

  @Inject
  public RetryingTransacter(Transacter transacter) {
    this.transacter = transacter;
  }

  public static RetryingTransacter retrying(Transacter transacter) {
    return new RetryingTransacter(transacter);
  }

  public void execute(WithSession callback) {
    this.execute(2, (WithSession) callback);
  }

  public void execute(int retries, WithSession callback) {
    this.execute(retries, defaultRetryingExceptions(), callback);
  }

  // other execute methods similar to Transacter

  @VisibleForTesting
  void execute(int retries, Predicate<Exception> retryableException, Runnable runnable) {
    // try running Runnable, test caught exceptions, and retry up to retries
  }

  public static Predicate<Exception> defaultRetryingExceptions() {
    // retry on all transient problems including LockAquisitionException, StaleStateException, SQLTransactionRollbackException, 
    // MySqlErrorNumbers.ER_LOCK_WAIT_TIMEOUT, MySqlErrorNumbers.ER_LOCK_DEADLOCK
  }

  // Use when the DbSession could violate a unique constraint. Helps the transacter retry in a 
  // look-before-leap optimistic concurrency pattern
  public static Predicate<Exception> retryOnConstraintViolations() {
    return (e) -> e instanceof ConstraintViolationException || e instanceof SQLIntegrityConstraintViolationException || defaultRetryingExceptions().test(e);
  }
}
```

The 'WithSession*' types all look similar
```java
package com.kaching.platform.hibernate;

public interface WithSessionExpression<T> {

  T run(DbSession session);

}
```
The `DbSession` class is a wrapper around a Hibernate `Session`, and can only be created via a `Transacter`
```java
package com.kaching.platform.hibernate;

import java.sql.Connection;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.TypeHelper;

import com.google.inject.ImplementedBy;
import com.google.inject.ProvidedBy;
import com.kaching.platform.common.Option;
import com.kaching.platform.guice.KcHibernateModule;
import com.kaching.platform.hibernate.interceptor.EntityLoadEnforcingInterceptor;

@ImplementedBy(DbSessionImpl.class)
public interface DbSession {

  /**
   * Force the Session to flush, syncing the database with the entity state held in memory. Hibernate compares
   * the entities it knows about (ones it has given to you via queries, or ones you have
   * passed it via .save() or .persist()), with what it originally read from the database. 
   * If it detects any changes, INSERTs and UPDATEs are spawned. Don  
   *
   * Triggered automatically if hibernate detects a Criteria SQL query against a
   * table for which it holds unflushed entities, on any SQL statement using a String query, and 
   * on commit
   */
  void flush();

  Connection connection();

  /**
   * Makes hibernate aware of a new entity, attaching it to the session (similar to persist), and forces hibernate to flush (INSERT) the entity, assigning any auto generated IDs.
   * An 'eager' insert operation. Use if you need access to the Id later in the session.
   */
  <E extends HibernateEntity> Id<E> save(E object);

  void save(HibernateEntity object1, HibernateEntity object2, HibernateEntity... others);

  // Do not use: will not spawn a SQL UPDATE and likely isn't what you need. If this is a new Hibernate entity, use .save() or .persist().
  @Deprecated
  void saveOrUpdate(HibernateEntity object);

  /**
   * Do not use: this method will not spawn a SQL INSERT or UPDATE and likely isn't what you need. Hibernate entities attached to the current session 
   * do not need to be explicitly updated.
   */
  @Deprecated
  void update(HibernateEntity object);

  /**
   * Attaches a new hibernate entity to the current session, making hibernate aware 
   * of its existence. Hibernate will likely not immediately SQL INSERT the entity or any linked
   * entities (a lazy operation). Instead, hibernate will wait until the session is flushed before doing any SQL work. 
   *
   * This flushing will happen automatically before the transaction is committed, can happen via a
   * manual call to .flush(), or can be triggered by hibernate in an auto-flush during a SQL query 
   * which reads data. 
   *
   * Because it's hard to predict when a session will be flushed, it's recommended
   * that the given hibernate entity is fully ready to be flushed (INSERTed) at the time this 
   * method is called. (For example, all non-nullable fields in it and linked entities are set).
   * Otherwise, an exception could be thrown when hibernate flushes at an unexpected moment. 
   */
  <E extends HibernateEntity> void persist(E object);

  <E extends HibernateEntity> PersistentCriteria<E> createCriteria(Class<E> persistentClass);

  <E extends HibernateEntity> PersistentCriteria<E> createCriteria(Class<E> persistentClass, String alias);

  // Hand-written SQL query
  SQLQuery createSQLQuery(String queryString);

  // Hand written SQL query that will return a hibernate entity
  <E extends HibernateEntity> PersistentSQLQuery<E> createSQLQuery(Class<E> clazz, String queryString);

  /**
   * Return an {@link Option} containing the persistent instance of the given entity class with
   * the given identifier, or none if there is no such persistent instance.
   */
  <E extends HibernateEntity> Option<E> get(Class<E> clazz, Id<? super E> id);

  // See {@link get()} but throws a com.kaching.platform.queryengine.exceptions.NotFoundException (code 404) if Option.none()
  <E extends HibernateEntity> E getOrThrow(Class<E> clazz, Id<? super E> id);

  Session getSession();

  // Equivalent to calling {@link #getSessionFactory()}.{@link SessionFactory#getTypeHelper getTypeHelper()}
  TypeHelper getTypeHelper();

  // Runs iff successful commit. Warning: swallows all exceptions
  void onCommit(Runnable runnable);

  // Runs iff transaction rolls back. Warning: swallows all exceptions
  void onRollback(Runnable runnable);

}
```

### Semantics
Write Transactions:
Calls to `Transacter.execute` with a write session (WithSession, WithSessionExpression) will create a READ_COMMITTED database transaction.
A thread will be checked out from the c3p0 thread pool and used for the duration of the lambda.

Read-Only Transactions:
Applies to `Transacter.execute` calls with a read-only session (WithReadOnlySession, etc).
No database transaction is started. Threads are only checked out from c3p0 for the duration of individual queries.

### Opportunistic Locking
We rely heavily on Hibernate's opportunistic locking functionality for correctness. Every entity and table should ideally have a version column.
Hibernate flushes updates using `UPDATE table SET my_col='new val' AND version=1 WHERE id=123 AND version=0`, specifying the version number it read
earlier in the session. If no row was updated, a `org.hibernate.StaleObjectStateException` is thrown.
In this way, updating a given hibernate entity in a transaction is similar to taking out a lock on that entity.
If write contention in a transaction is expected, it's important to
This means that important tables relevant to a logic decision must be updated in a write session if contention is expected.


### Best Practices
Database transactions should be kept short to do the minimal amount of work that needs to be committed atomically.
This is enforced with sql timeouts:
- A per-statement SQL timeout is added to every SQL statement automatically
- The timeout is cumulative, and the amount of time given to SQL statements decreases as the transaction is kept open
- Online services (client facing) are given 10 seconds total per transaction. Batch services are given 5 minutes (though they should really be kept to 30 seconds or less)

### Guice and Dependency Injection
Opening a transaction automatically member-injects the `WithSession`.
A `DbSession` can be injected at any point during the transaction, but not after.

You should never make any cross-service query calls (RPCs via the Query Engine and `com.kaching.platform.queryengine.client.SmartClient`) or
third party API calls within a session because:
- It can hog the c3p0 thread too long, keep the transaction open too long, and cause timeouts or contention
- It can cause the false impression that an RPC call is atomic with a local db transaction. They are never atomic.

As a result, dependencies at Wealthfront tend to be 'colored': they either work in-session and eventually `@Inject DbSession`, or they are
out-of-session and can inject SmartClients or do other I/O. In other words, classes at Wealthfront should generally be written to work
inside of a database transaction or outside, but not both. This distinction also helps prevent nested transactions which are bad.

## Entity Definitions
Defining a hibernate entity requies three parts: a Java class extending `com.kaching.platform.hibernate.AbstractHibernateEntity`, a `.hbm.xml` file in the parallel `resources/` directory, and
a SQL table created in the appropriate MariaDB database.

```java
package com.kaching.platform.hibernate;

public abstract class AbstractHibernateEntity implements HibernateEntity {

  @Override
  public int hashCode() {
    return getId() == null ? super.hashCode() : getId().hashCode();
  }

  @Override
  public boolean equals(Object obj) {
    return this == obj ||
        (obj instanceof HibernateEntity &&
            getId() != null &&
            getId().equals(((HibernateEntity) obj).getId()));
  }
}
```
```java
package com.kaching.platform.hibernate;

import java.util.Collection;
import java.util.List;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.kaching.entities.types.IdJsonType;
import com.kaching.platform.common.AbstractLongIdentifier;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.converters.ConvertedBy;
import com.twolattes.json.MarshalledBy;

@ConvertedBy(IdConverter.class)
@MarshalledBy(IdJsonType.class)
public final class Id<E extends HibernateEntity> extends AbstractLongIdentifier {

  public Id(long id) {
    super(id);
  }

  public static <T extends HibernateEntity> Id<T> of(long id) {
    return new Id<>(id);
  }

  public static <T extends HibernateEntity> Id<T> of(Identifier<Long> id) {
    return new Id<>(id.getId());
  }

  public static <T extends HibernateEntity> Id<T> of(AbstractLongIdentifier id) {
    if (id instanceof Id) {
      return (Id<T>) id;
    }
    return new Id<>(id.asLong());
  }

  public static <T extends HibernateEntity> Id<T> of(String value) {
    return new Id<>(Long.parseLong(value.trim()));
  }

  public static <T extends HibernateEntity> List<Long> toLongs(List<Id<T>> ids) {
    return Lists.transform(ids, (Function<Id<?>, Long>) AbstractLongIdentifier::asLong);
  }

  public static <T extends HibernateEntity> Collection<Long> toLongs(Collection<Id<T>> ids) {
    return Collections2.transform(ids, (Function<Id<?>, Long>) AbstractLongIdentifier::asLong);
  }

}
```
### Example
```java
package com.wealthfront.cash.account;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.SortedSet;
import java.util.TreeSet;

import org.joda.time.DateTime;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.api.ExposeId;
import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.Archived;
import com.kaching.platform.hibernate.Id;
import com.kaching.user.UserId;
import com.wealthfront.brokerage.account.AccountId;
import com.wealthfront.brokerage.account.AccountKind;

@ExposeId(exposedIdClass = SweepAccountId.class, prefix = "SWA", passwordProperty = "externalizers.sweep_accounts")
public class SweepAccount extends AbstractHibernateEntity {

  public enum State {
    PENDING,
    OPENED,
    FAILED
  }

  private Id<SweepAccount> id;
  @SuppressWarnings("unused")
  private int version = 0;
  private DataEnvironment environment;
  private Archived<State> state;
  private List<Transaction> transactions = new ArrayList<>(); // many transactions to one account, uses <bag> and <one-to-many>
  private AccountId accountId;
  private AccountKind accountKind;
  private DateTime createdAt;
  private DateTime updatedAt;

  @SuppressWarnings("unused")
  SweepAccount() { /* Hibernate */ }

  public SweepAccount(
      DataEnvironment environment, UserId userId, AccountId accountId, AccountKind accountKind, DateTime createdAt) {
    this.environment = environment;
    this.state = new Archived<>(this, createdAt, State.PENDING);
    this.accountId = accountId;
    this.accountKind = accountKind;
    this.createdAt = createdAt;
    this.updatedAt = createdAt;
  }

  @Override
  public Id<SweepAccount> getId() {
    return id;
  }

  @VisibleForTesting
  public void setId(Id<SweepAccount> id) {
    this.id = id;
  }

  public State getCurrentState() {
    return state.current();
  }

  public void updateState(State newState, DateTime now) {
    state.update(now, newState);
    this.updatedAt = now;
  }

  // other getters and setters

}

```
Notes on AbstractHibernateEntity construction
- Getters for nullable fields should be wrapped in `com.kaching.platform.common.Option`
- The version field should be defined as `private int version = 0;`
- The empty, default constructor should neither be private nor public: it should have no access modifier

```java
package com.wealthfront.cash.account;

import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.Id;

public class Transaction extends AbstractHibernateEntity {
  
  private Id<Transaction> id;
  private int version = 0;
  private SweepAccount account; // uses <many-to-one>
  
  Transaction() { /* Hibernate */ }

  public Transaction(SweepAccount account) {
    this.account = account;
  }

  @Override
  public Id<Transaction> getId() {
    return id;
  }

  public SweepAccount getAccount() {
    return account;
  }
  
}
```

File: src/main/resources/com/wealthfront/cash/account/SweepAccount.hbm.xml
```xml
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping default-access="field" package="com.wealthfront.cash.account">
  <typedef class="com.kaching.util.types.UserTypeAdapter" name="AccountId">
    <param name="type">com.wealthfront.brokerage.account.AccountId</param>
    <param name="converter">com.wealthfront.brokerage.account.AccountId$Converter</param>
  </typedef>
  <typedef class="com.kaching.util.types.UserTypeAdapter" name="UserId">
    <param name="type">com.kaching.user.UserId</param>
    <param name="converter">com.kaching.user.UserId$Converter</param>
  </typedef>
  <typedef class="com.kaching.util.types.EnumTypeAdapter" name="AccountKind">
    <param name="type">com.wealthfront.brokerage.account.AccountKind</param>
  </typedef>
  <typedef class="com.kaching.util.types.EnumTypeAdapter" name="State">
    <param name="type">com.wealthfront.cash.account.SweepAccount$State</param>
  </typedef>
  <typedef class="com.kaching.util.types.EnumTypeAdapter" name="DataEnvironment">
    <param name="type">com.kaching.platform.common.DataEnvironment</param>
  </typedef>
  <class name="com.wealthfront.cash.account.SweepAccount" table="sweep_accounts">
    <id name="id" column="id" type="Id">
      <generator class="identity"/>
    </id>
    <version name="version" type="int"/>
    <property name="environment" column="environment" type="DataEnvironment" not-null="true"/>
    <kawala:archived
        name="state"
        entity-name="version_sweep_account_state"
        denormalized="true"
        current-column="state"
        parent-column="sweep_account_id"
        version-table="version_sweep_account_state">
      <property name="value" type="State" not-null="true"/>
    </kawala:archived>
    <bag name="transactions" lazy="true" cascade="save-update">
      <!-- Using bags is discouraged at Wealthfront -->
      <key column="sweep_account_id" />
      <one-to-many class="com.wealthfront.cash.account.Transaction"/>
    </bag>
    <property name="accountId" column="accountid" type="AccountId" not-null="true"/>
    <property name="accountKind" column="account_kind" type="AccountKind" not-null="true"/>
    <property name="createdAt" column="created_at" type="DateTime_full" not-null="true"/>
    <property name="updatedAt" column="updated_at" type="DateTime_full" not-null="true"/>
  </class>
</hibernate-mapping>
```

File: src/main/resources/com/wealthfront/cash/account/Transaction.hbm.xml
```xml
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping default-access="field" package="com.wealthfront.cash.account">
  <class name="com.wealthfront.cash.account.Transaction" table="transactions">
    <id name="id" column="id" type="Id">
      <generator class="identity"/>
    </id>
    <version name="version" type="int"/>
    <many-to-one
        name="account"
        column="sweep_account_id"
        class="com.wealthfront.cash.account.SweepAccount"
        cascade="save-update"
        not-null="false" />
  </class>
</hibernate-mapping>
```
<hibernate_mapping_notes>
The `<id>` tag should have `type="Id"`, which is a globally defined type that is always available.
The `<version>` tag can either be type int or long, depending on how it's defined in the Java class.
</hibernate_mapping_notes>

The SQL tables:
```sql
CREATE TABLE sweep_accounts (
  id BIGINT NOT NULL AUTO_INCREMENT,
  version INT NOT NULL DEFAULT 0,
  environment VARCHAR(255) NOT NULL,
  state VARCHAR(255) NOT NULL,
  accountid BIGINT NOT NULL,
  account_kind VARCHAR(255) NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY (accountid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE version_sweep_account_state (
  id BIGINT NOT NULL AUTO_INCREMENT,
  sweep_account_id BIGINT NOT NULL,
  version INT NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_account_state_sweep_account_id
    FOREIGN KEY (sweep_account_id)
    REFERENCES sweep_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE transactions (
  id BIGINT NOT NULL AUTO_INCREMENT,
  version INT NOT NULL,
  sweep_account_id BIGINT NOT NULL
  PRIMARY KEY (id),
  CONSTRAINT transactions_sweep_account_id
    FOREIGN KEY (sweep_account_id)
    REFERENCES sweep_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
```
## Entity Relationships
In the example above, there are many Transactions for one SweepAccount.
This is modeled as the SweepAccount having a List<Transaction>, and the Transaction having a single SweepAccount.
Hibernate uses byte code manipulation to make these relationships lazily appear at runtime with a DbSession.

While many tables have such a one-to-many relationship at Wealthfront, it is now highly discouraged to ever use Hibernate bags, and we rarely write
new code which uses them. 
- Accessing the List<Transaction> on the SweepAccount can easily cause large performance issues when the number of transaction is large
- It is too easy to trigger the "n+1" ORM bug when iterating over the list of Transactions, accidentally causing a new SQL query to be spawned with every iteration
- It is much better to be explicit whenever we are creating a new SQL query, and explicitly SELECT the transactions we need
- It is still OK to keep the Transaction to SweepAccount (many-to-one) mapping, however, as that spawns at most one new query

## Types
### Storing JSON in a Column
#### Plaintext JSON
- For json classes annotated with `com.twolattes.json.Entity`, use `com.kaching.util.types.JsonHibernateType`
- For Java classes annotated with the two-lattes `com.twolattes.json.Entity`
- Very common patter to store 'details' objects that don't need to be indexed
```xml
<typedef name="MyJsonClass" class="com.kaching.util.types.JsonHibernateType">
  <param name="type">com.wealthfront.types.MyJsonClass</param>
</typedef>
```

#### Encrypted GZipped JSON
Also for Java classes with the two-lattes `com.twolattes.json.Entity`
Zips, encrypts, and base64-encodes the JSON blob in the database column. Use the com.kaching.util.types.EncryptedGZippedJsonHibernateType
```xml
<typedef class="com.kaching.util.types.EncryptedGZippedJsonHibernateType" name="SensitiveDetails">
  <param name="type">com.kaching.contacts.SensitiveDetails</param>
</typedef>
```

### Built-in Types
A few common Wealthfront types are built-in and already have a `<typedef>` registered with Hibernate.
Format: [hibernate type name],[java class name],[common sql types]
<built_in_types>
Id, com.kaching.platform.hibernate.Id, BIGINT
Year, com.kaching.util.time.Year, INT
YearMonth, com.kaching.util.time.YearMonth, VARCHAR or DATE
LocalDate, org.joda.time.LocalDate, DATE
DateTime, org.joda.time.DateTime, DATETIME
DateTime_full, org.joda.time.DateTime, DATETIME (preferred)
Scalar, com.kaching.entities.Scalar, DECIMAL
Quantity, com.kaching.entities.Quantity, DECIMAL
Price, com.kaching.entities.Price, DECIMAL
Money, com.kaching.entities.Money, DECIMAL
</built_in_types>
```

### Custom Types
We use many enums and other custom java types in our table definitions.
Most custom types use the `com.kaching.util.types.UserTypeAdapter` in the .hbm.xml file, which maps a `com.kaching.platform.converters.Converter<T>`, commonly a `NullHandlingConverter`

```java
package com.kaching.platform.converters;

public abstract class NullHandlingConverter<T> implements Converter<T> {
  public NullHandlingConverter() {}

  public T fromString(String representation) {
    return (T)(representation == null ? null : this.fromNonNullableString(representation));
  }

  protected abstract T fromNonNullableString(String representation);

  public String toString(T value) {
    return value == null ? null : this.nonNullableToString(value);
  }

  protected abstract String nonNullableToString(T value);
}
```
Example with AccountId:
```java
package com.wealthfront.brokerage.account;

import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(Converter.class)
@MarshalledBy(AccountIdJsonType.class)
public class AccountId extends AbstractIdentifier<Long> {

  public AccountId(long id) {
    super(id);
  }

  public static class Converter extends NullHandlingConverter<AccountId> {
    public Converter() {}

    protected AccountId fromNonNullableString(String r) {
      return new AccountId(Long.parseLong(r));
    }

    protected String nonNullableToString(AccountId val) {
      return val.toString();
    }
  }

  public static class AccountIdJsonType extends NullSafeType<AccountId, Json.Number> {
    public AccountIdJsonType() {
    }

    protected Json.Number nullSafeMarshall(AccountId entity) {
      return Json.number((Long)entity.getId());
    }

    protected AccountId nullSafeUnmarshall(Json.Number number) {
      return new AccountId(number.getNumber().longValueExact());
    }
  }
}
```

## Querying
### Repositories
All queries are organized into one `com.kaching.entities.Repository` per table for centralization and re-use.
Every hibernate entity will have its own `MyEntityRepository` which extends `PersistentRepository` and is commonly injected in in-session classes.
```java
package com.kaching.entities;

import static java.lang.String.format;
import static java.util.Collections.emptyList;
import static org.hibernate.criterion.Restrictions.eq;
import static org.hibernate.criterion.Restrictions.in;

import java.util.Collection;
import java.util.List;

import org.hibernate.Session;
import org.hibernate.criterion.Projections;

import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.PersistentCriteria;
import com.kaching.platform.hibernate.PersistentSQLQuery;
import com.kaching.platform.queryengine.exceptions.NotFoundException;

public abstract class PersistentRepository<E extends HibernateEntity> implements Repository<E> {

  private final Class<E> entityClass;
  protected final DbSession session;

  public PersistentRepository(Class<E> clazz, DbSession session) {
    this.entityClass = clazz;
    this.session = session;
  }

  @Override
  public E get(Id<E> id) {
    return session.get(entityClass, id).getOrNull();
  }

  @Override
  public E getOrThrow(Id<E> id) {
    return session.get(entityClass, id).getOrThrow(new NotFoundException(entityClass, id));
  }

  public Class<E> getEntityClass() {
    return entityClass;
  }

  @Override
  public Id<E> persist(E entity) {
    session.save(entity);
    return (Id<E>) entity.getId();
  }

  public Session getSession() {
    return session.getSession();
  }

  public PersistentCriteria<E> createCriteria() {
    return new PersistentCriteria<>(getSession(), entityClass);
  }

  public PersistentCriteria<E> createCriteria(String alias) {
    return new PersistentCriteria<>(getSession(), entityClass, alias);
  }

  public <T extends E> PersistentCriteria<T> createCriteria(Class<T> clazz) {
    return new PersistentCriteria<>(getSession(), clazz);
  }

  public PersistentSQLQuery<E> createSQLQuery(String queryString) {
    return new PersistentSQLQuery<>(getSession(), entityClass, queryString);
  }

  @Override
  public List<E> get(Collection<Id<E>> ids) {
    if (ids.isEmpty()) {
      return emptyList();
    } else {
      return createCriteria()
          .add(in("id", ids))
          .list();
    }
  }

  @Override
  public List<E> getAll() {
    return createCriteria().list();
  }

  @Override
  public List<Id<E>> getAllIds() {
    List<Id<E>> list = createCriteria()
        .<Id<E>>setProjection(Projections.id())
        .list();
    return list;
  }

  @Override
  public void delete(E entity) {
    session.delete(entity);
    session.flush();
  }

  public E getOrThrow(String key, Object value) {
    List<E> results;
    try {
      results = createCriteria().add(eq(key, value)).list();
    } catch (Exception e) {
      throw new RuntimeException(format("Failed to find %s %s", key, value), e);
    }
    if (results == null || results.isEmpty()) {
      throw new NotFoundException(entityClass, key, value);
    }
    if (results.size() > 1) {
      throw new RuntimeException(format("Failed to find unique %s for %s %s", entityClass, key, value));
    }
    return results.get(0);
  }

}
```

### Example Repository
```java
package com.wealthfront.cash.account;

import static com.wealthfront.util.stream.WFCollectors.pairsToMap;
import static java.util.Collections.emptyMap;
import static java.util.stream.Collectors.toMap;
import static org.hibernate.criterion.Projections.id;
import static org.hibernate.criterion.Projections.property;
import static org.hibernate.criterion.Restrictions.eq;
import static org.hibernate.criterion.Restrictions.in;
import static org.hibernate.criterion.Restrictions.le;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.hibernate.criterion.Projections;
import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.kaching.entities.PersistentRepository;
import com.kaching.entities.types.IdType;
import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.user.UserId;
import com.kaching.util.types.EnumTypeAdapter;
import com.kaching.util.types.UserTypeAdapter;
import com.wealthfront.brokerage.account.AccountId;
import com.wealthfront.brokerage.account.AccountKind;

public class PersistentSweepAccountRepository
    extends PersistentRepository<SweepAccount>
    implements SweepAccountRepository {

  @Inject
  public PersistentSweepAccountRepository(DbSession session) {
    super(SweepAccount.class, session);
  }

  @Override
  public Option<SweepAccount> getByAccountId(AccountId accountId) {
    return Option.of(createCriteria()
        .add(eq("accountId", accountId))
        .uniqueResult());
  }

  @Override
  public Map<AccountId, Id<SweepAccount>> getIdsByAccountIds(Set<AccountId> accountIds) {
    if (accountIds.isEmpty()) {
      return emptyMap();
    }
    return createCriteria()
        .add(in("accountId", accountIds))
        .<AccountId, Id<SweepAccount>>asPair(property("accountId"), Projections.id())
        .list()
        .stream()
        .collect(pairsToMap());
  }

  @Override
  public Set<Id<SweepAccount>> getInStateBeforeForEnvironment(SweepAccount.State state, DateTime beforeInclusive, DataEnvironment environment) {
    return createCriteria()
        .createAlias("state.allVersions", "states")
        .add(eq("state.currentValue", state))
        .add(eq("states.value", state))
        .add(le("states.createdAt", beforeInclusive))
        .add(eq("environment", environment))
        .<Id<SweepAccount>>setProjection(id())
        .set();
  }

  @Override
  public Map<AccountId, AccountKind> filterAccountIdsInStatesAndGetKinds(Set<AccountId> accountIds, SweepAccount.State state) {
    if (accountIds.isEmpty()) {
      return Map.of();
    }
    String query = """
            SELECT accountid, account_kind FROM sweep_accounts WHERE accountids IN (:filter_ids) AND state = :state
        """;
    List<Object[]> rawResult = (List) session.createSQLQuery(query)
        .addScalar("accountid", UserTypeAdapter.custom(session, AccountId.Converter.class))
        .addScalar("account_kind", EnumTypeAdapter.custom(session, AccountKind.class))
        .setParameter("state", state)
        .setParameterList("filter_ids", accountIds, session.getTypeHelper().custom(IdType.class))
        .list();
    return rawResult.stream()
        .collect(toMap(arr -> (AccountId) arr[0], arr -> (AccountKind) arr[1]));
  }

}
```
### Repository Notes
Note in the example we have both a `SweepAccountRepository` interface which extends `Repository<SweepAccount>`, as well as
the implementation, by convention called `PersistentSweepAccountRepository` which extends `PersistentRepository<SweepAccount>`
and implements `SweepAccountRepository`.

### Query Notes
Hibernate Criteria are good for simple queries, but rather than over-complicate criteria it's better to use `DbSession.createSQLQuery` instead.
SQLQueries will return a `List<Object[]>`. The Objects will be well-typed objects if you `addScalar` the returned columns, but will default
to primitives (BigDecimal, String) otherwise.
Running SQL with empty `IN` statements will throw an exception at runtime. It's extremely important to short-circuit all methods using collections and return
appropriate results rather than ever have an empty IN statement.

## Testing
All unit tests that involve the database or Hibernate are run against 'persistent' test bases, which use an ephemeral, in-memory database.
A single persistent test base usually exists for an entire service, uses that service's database schema, and usually extend
the new `com.kaching.util.tests.KawalaTestBase` or the older `com.kaching.util.tests.PersistentTestBase`. In the example below, the unit tests
for SweepAccount extends the CashTestBase, which is the persistent test base for the CASH service. You almost never need to create new test
bases, and can usually always find an existing appropriate one.
```java
package com.wealthfront.cash.account;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.wealthfront.brokerage.account.AccountKind.PERSONAL_CASH;
import static com.wealthfront.cash.account.SweepAccount.State.OPENED;
import static com.wealthfront.cash.account.SweepAccount.State.PENDING;
import static com.wealthfront.test.Assert.assertSameInstant;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.junit.Test;

import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.hibernate.Id;
import com.kaching.user.UserId;
import com.wealthfront.brokerage.account.AccountId;
import com.wealthfront.cash.CashTestBase;

public class SweepAccountTest extends CashTestBase {

  private final DateTime now = new DateTime(2018, 11, 13, 12, 34, ET);
  private final UserId userId = new UserId(888);
  private final AccountId accountId = new AccountId(12345);

  @Test
  public void persist() {
    Id<SweepAccount> id = transacter.executeWithSessionExpression(session ->
        session.save(new SweepAccount(
            DataEnvironment.REAL,
            userId,
            accountId,
            PERSONAL_CASH,
            now)));

    transacter.executeWithSession(session -> {
      SweepAccount retrieved = session.getOrThrow(SweepAccount.class, id);
      assertEquals(DataEnvironment.REAL, retrieved.getEnvironment());
      assertEquals(PENDING, retrieved.getCurrentState());
      assertEquals(accountId, retrieved.getAccountId());
      assertEquals(PERSONAL_CASH, retrieved.getAccountKind());
      assertSameInstant(now, retrieved.getCreatedAt());
      assertSameInstant(now, retrieved.getUpdatedAt());

      retrieved.updateState(OPENED, now.plusMinutes(1));
      session.save(new Transaction(retrieved));
    });

    transacter.executeWithReadOnlySession(session -> {
      SweepAccount retrieved = session.getOrThrow(SweepAccount.class, id);
      assertEquals(now, retrieved.getCreatedAt());
      assertEquals(now.plusMinutes(1), retrieved.getUpdatedAt());
      
      List<Transaction> transactions = retrieved.getTransactions();
      assertEquals(1, transactions.size());
      assertEquals(retrieved, transactions.get(0).getAccount());
    });
  }
}
```
Every hibernate entity requires unit tests, such as the one above, to verify the hibernate mapping.

### Test Bases
Persistent test bases usually contain a `Transacter` which is a fake version of the current service's database
and an `Injector` with a lightweight version of the service's full bindings. In a test base, it's common to find:
- WFMockery and assertIsSatisfied
- A reference to the service's `com.kaching.platform.components.Component` which holds a list of the service's Hibernate entities and queries
- Commonly injected and mocked interfaces for external service interactions, like a Pager, SmartClient, ReportPublisher
- Commonly injected and faked infrastructure like the event bus, current time, FileSystem

Injector Reuse:
With the KawalaTestBase, bindings are configured once in the Injector for the entire test base, which allows the Injector to be re-used in an object
pool between threads. In between every unit test however, `com.kaching.platform.guice.MutableSingleton` and `UnitTestScoped` bindings are cleared
and the in-memory database wiped.

Database Types:
Persistent test bases can be configured to run against hsqldb (faster, less accurate) or a test-container based MariaDB (slower, more accurate and
more features), usually depending on whether the service requires MariaDB-only features.

```java
package com.wealthfront.cash;

import static com.wealthfront.util.time.DateTimeZones.ET;

import java.nio.file.FileSystem;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.After;
import org.junit.Before;

import com.google.common.jimfs.Jimfs;
import com.google.inject.AbstractModule;
import com.google.inject.Injector;
import com.google.inject.Provides;
import com.kaching.platform.bus.Bus;
import com.kaching.platform.bus.FakeBus;
import com.kaching.platform.guice.KachingServices.BUM;
import com.kaching.platform.guice.KachingServices.CASH;
import com.kaching.platform.guice.KachingServices.ICG;
import com.kaching.platform.hibernate.DatabaseType;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.queryengine.QueryExecutorService;
import com.kaching.platform.queryengine.client.SmartClient;
import com.kaching.platform.testing.Mockeries;
import com.kaching.report.ReportPublisher;
import com.kaching.report.big.BigReportComponentTestModule;
import com.kaching.report.big.FakeBigReportPublisher;
import com.kaching.util.http.LongTimeoutPooling;
import com.kaching.util.http.Pooling;
import com.kaching.util.mail.Pager;
import com.kaching.util.tests.KawalaServerTestEnvironment.Service;
import com.kaching.util.tests.KawalaTestBase;
import com.kaching.util.tests.UnitTestScoped;
import com.wealthfront.util.time.calendar.CalendarModule;

public class CashTestBase extends KawalaTestBase {

  private Service<CASH> service;
  protected Transacter transacter;
  protected Injector injector;

  protected Mockeries.WFMockery mockery;
  protected SmartClient<BUM> bumClient;
  protected SmartClient<ICG> icgClient;
  private QueryExecutorService qes;
  protected Pager pager;
  protected ReportPublisher publisher;
  protected FileSystem fs;

  protected DateTime now = new DateTime(2020, 1, 16, 12, 31, 1, ET);
  protected LocalDate today = now.toLocalDate();

  @Before
  public final void beforeCashTestBase() {
    mockery = Mockeries.mockery(false, true);
    bumClient = mockery.mockSmartClient(BUM.class);
    icgClient = mockery.mockSmartClient(ICG.class);
    publisher = mockery.mock(ReportPublisher.class);
    pager = mockery.mock(Pager.class);
    qes = mockery.mock(QueryExecutorService.class);

    fs = Jimfs.newFileSystem();
    service = environment.addService(CASH.class, CashService.class)
        .install(CashTestBaseModule.class)
        .install(CashService.Module.class)
        .withDatabaseType(DatabaseType.MARIA)
        .installOverride(CashTestBaseOverrideModule.class)
        .bindInUnitTestScope(CashTestBase.class, this)
        .build();
    injector = service.injector();
    transacter = injector.getInstance(Transacter.class);
    environment.build();
  }

  @After
  public void afterCashTestBase() throws Exception {
    fs.close();
    mockery.assertIsSatisfied();
  }

  private static class CashTestBaseModule extends AbstractModule {

    @Override
    protected void configure() {
      install(new BigReportComponentTestModule());
    }

    @Provides
    @LongTimeoutPooling
    SmartClient<ICG> getLongIcgClient(CashTestBase self) {
      return self.icgClient;
    }

    @Provides
    @Pooling
    SmartClient<ICG> getIcgClient(CashTestBase self) {
      return self.icgClient;
    }

    @Provides
    @LongTimeoutPooling
    SmartClient<BUM> getBumClient(CashTestBase self) {
      return self.bumClient;
    }

  }

  private static class CashTestBaseOverrideModule extends AbstractModule {

    @Override
    protected void configure() {
      install(new CalendarModule());
      bind(Bus.class).to(FakeBus.class).in(UnitTestScoped.class);
    }

    @Provides
    DateTime getNow(CashTestBase self) {
      return self.now;
    }

    @Provides
    LocalDate getDate(CashTestBase self) {
      return self.now.toLocalDate();
    }

  }

  public FakeBus getBus() {
    return (FakeBus) injector.getInstance(Bus.class);
  }

  public FakeBigReportPublisher getFakeBigReportPublisher() {
    return injector.getInstance(FakeBigReportPublisher.class);
  }

}
```

### Unit tests
Unit tests run against persistent test bases. Principles:
- Test entities should always be defined within the individual test methods, however immutable IDs and DateTime objects can be `private final` fields

The direct unit tests for a hibernate entity should always
1. Be run against some persistent test base that already exists
2. Save the entity and associated entities, returning its ID
3. In a new session, read the entity, asserting its fields and updating anything mutable
4. In a new session, re-read the entity and assert that the update was successful

Builder Factories:
Alongside every hibernate entity should be a factory builder, which allows entities to be easily created in unit tests.
They should almost always be used in unit tests instead of the entity's actual constructor, as they allow readers of the tests
to not be distracted with unnecessary data.
```java
package com.wealthfront.cash.account.factory;

import static com.wealthfront.util.time.DateTimeZones.ET;

import java.util.concurrent.atomic.AtomicLong;

import org.joda.time.DateTime;

import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.common.Pair;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.user.UserId;
import com.kaching.util.tests.PartialObjects;
import com.wealthfront.brokerage.account.AccountId;
import com.wealthfront.brokerage.account.AccountKind;
import com.wealthfront.cash.account.SweepAccount;

public class SweepAccountFactory {

  private static final AtomicLong counter = new AtomicLong(0);

  private static <T extends HibernateEntity> Id<T> uniqueId() {
    return Id.of(counter.incrementAndGet());
  }

  public static PartialSweepAccount createSweepAccount() {
    return new PartialSweepAccount();
  }

  public static class PartialSweepAccount extends PartialObjects.AbstractPartialObject<SweepAccount> {

    private Id<SweepAccount> id;
    private DataEnvironment environment = DataEnvironment.REAL;
    private DateTime createdAt = new DateTime(2018, 11, 14, 14, 34, ET);
    private Pair<DateTime, SweepAccount.State> state = Pair.of(createdAt, SweepAccount.State.PENDING);
    private UserId userId = new UserId(888);
    private AccountId accountId = new AccountId(uniqueId().getId());
    private AccountKind accountKind = AccountKind.PERSONAL_CASH;

    public PartialSweepAccount withId(long id) {
      return withId(Id.of(id));
    }

    public PartialSweepAccount withId(Id<SweepAccount> id) {
      this.id = id;
      return this;
    }

    public PartialSweepAccount withEnvironment(DataEnvironment environment) {
      this.environment = environment;
      return this;
    }

    public PartialSweepAccount withState(DateTime now, SweepAccount.State state) {
      this.state = Pair.of(now, state);
      return this;
    }

    public PartialSweepAccount withUserId(UserId userId) {
      this.userId = userId;
      return this;
    }

    // other methods

    @Override
    public SweepAccount build() {
      SweepAccount account = new SweepAccount(environment, userId, accountId, accountKind, createdAt);
      account.setId(id);
      account.updateState(state.getRight(), state.getLeft());
      return account;
    }

  }

}
```
```java
package com.kaching.util.tests;

import org.hibernate.Session;

import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithSession;

public class PartialObjects {

  interface PartialObject<T> {

    T build();

    T buildAndPersist(Session session);

    T buildAndPersist(Transacter transacter);
  }

  public abstract static class AbstractPartialObject<T> implements PartialObject<T> {

    public T buildAndPersist(Session session) {
      return persist(build(), session);
    }

    public T buildAndPersist(DbSession session) {
      return persist(build(), session.getSession());
    }

    public T buildAndPersist(Transacter transacter) {
      return persist(build(), transacter);
    }

  }

  public static <T> T persist(T entity, Session session) {
    session.save(entity);
    return entity;
  }

  public static <T> T persist(final T entity, Transacter transacter) {
    transacter.execute(new WithSession() {
      public void run(DbSession session) {
        session.getSession().save(entity);
      }
    });
    return entity;
  }

}
```
<partial_object_notes>
It's common for every hibernate entity to have a Partial version somewhere in test scope (an Entity has a PartialEntity, usually as a static inner class under EntityFactory), which extends com.kaching.util.tests.AbstractPartialObject.
When working with existing hibernate entities, these usually already exist. It's helpful to look at other persistent unit tests using the entities to find usage examples.
These Partial objects have reasonable default data and private constructors and public static `createEntity` methods.
</partial_object_notes>

```java
package com.wealthfront.cash.account;

import static com.wealthfront.cash.account.factory.SweepAccountFactory.createSweepAccount;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static org.junit.Assert.assertEquals;

import java.util.Map;

import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.google.inject.Inject;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.wealthfront.brokerage.account.AccountId;
import com.wealthfront.cash.CashTestBase;

public class PersistentSweepAccountRepositoryTest extends CashTestBase {

  @Test
  public void getByAccountId() {
    SweepAccount account = createSweepAccount()
        .withAccountId(1255690)
        .buildAndPersist(transacter);

    transacter.execute(new WithReadOnlySession() {
      @Inject SweepAccountRepository repo;
      @Override
      public void run(DbSession session) {
        assertOptionEquals(account, repo.getByAccountId(new AccountId(1255690)));
        assertOptionEmpty(repo.getByAccountId(new AccountId(528289)));
      }
    });
  }

  @Test
  public void getIdsByAccountIds() {
    Id<SweepAccount> account1 = createSweepAccount()
        .withAccountId(101)
        .buildAndPersist(transacter)
        .getId();
    Id<SweepAccount> account2 = createSweepAccount()
        .withAccountId(102)
        .buildAndPersist(transacter)
        .getId();
    Id<SweepAccount> account3 = createSweepAccount()
        .withAccountId(103)
        .buildAndPersist(transacter)
        .getId();

    transacter.execute(new WithReadOnlySession() {

      @Inject SweepAccountRepository repo;

      @Override
      public void run(DbSession session) {
        Map<AccountId, Id<SweepAccount>> idMap =
            repo.getIdsByAccountIds(ImmutableSet.of(new AccountId(102), new AccountId(103)));

        assertEquals(2, idMap.size());
        assertEquals(account2, idMap.get(new AccountId(102)));
        assertEquals(account3, idMap.get(new AccountId(103)));

        Map<AccountId, Id<SweepAccount>> idMap2 = repo.getIdsByAccountIds(ImmutableSet.of());
        assertEquals(0, idMap2.size());
      }
    });
  }

}
```
## Repository Injection
Note that every Repository must be @Inject injected within a database transaction (transacter.execute), because it itself injects @Inject DbSession, which is only possibly within a transaction.

## Common Problems
`LazyInitializationException` happens when a hibernate entity is being leaked and used outside of a database transaction. Generally only data (records, IDs) should be
returned and used from sessions, and never hibernate entities themselves.

Ambiguous methods
The following code does not work: `transacter.execute(session -> session.save(entity))` because Transacter has multiple similar methods. 
Instead, either `transacter.executeWithSession(session -> session.save(entity))` or `transacter.execute((WithSession) session -> session.save(entity))`