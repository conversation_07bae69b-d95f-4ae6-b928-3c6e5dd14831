---
description: 
globs: 
alwaysApply: false
---
## Local test data
Test data should almost always be local to the individual unit tests and not shared between tests. Common exceptions include:
`private org.joda.time.DateTime now;`
Shared test data, if it does exist, should be `private final` and not static.

## DateTime
We usually use `org.joda.time.DateTime`
It's important that tests are always deterministic. `DateTime.now()` should never be used, and instead fake dates like `new DateTime(2012, 1, 2, 12, 34, 59, ET)` should be used.
All date and times at Wealthfront are in Eastern Time. We refer to it by importing `import static com.wealthfront.util.time.DateTimeZones.ET;` which should be statically imported.
```java
package com.wealthfront.util.time;

import static org.joda.time.DateTimeZone.forID;

import org.joda.time.DateTimeZone;

public class DateTimeZones {
  public static final DateTimeZone ET = forID("America/New_York");
}
```
To assert DateTime equality, `com.wealthfront.test.assertSameInstant(DateTime expected, DateTime actual)`, which is time zone agnostic, should be used.

## Style
Unit tests naming:
```java
@Test
public void myMethod_whenModeIsDryRun_doesNotPersist()
```
following a pattern of `[method]_[condition]_[expected result]()`
It is ok for method names and local variables to be very descriptive, but as a result you should NOT write any code comments unless a test setup really needs explaining.

Inline Test Data:
Usually there's no need to define variables for fake data: indirection should be minimized in tests. Just create and use types directly: `new User("Joe Test", new EmailAddress("<EMAIL>"), new AccountId(11));`

## Test Class Name
Unit tests should always be the name of the top level class plus 'Test'. For example, the unit tests for MyClass should be named MyClassTest. 
Occasionally, functional unit tests will exist which aren't tied to a specific class. For example, DepositFunctionalTest.

## Mocking
We use jmock and JUnit4 not mockito.
Minimal work should take place `@Before`, if any at all. If you need to set up the class being tested, do so with a private helper method at the bottom of the file

Mockery should always be setup like this: 
```java
import com.kaching.platform.testing.Mockeries;

private final Mockery mockery = Mockeries.mockery(/* `true` if we need to mock concrete classes, no args otherwise */);

@After
public void after() {
  mockery.assertIsSatisfied();
}
```
Note that if the test class extends a test base, the Mockery setup and assertIsSatisfied usually happens in the parent class.

And used with com.kaching.platform.testing.WExpectations, copied here:
```java
package com.kaching.platform.testing;

import static com.kaching.platform.common.Strings.format;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;

import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.hamcrest.TypeSafeMatcher;
import org.hamcrest.core.IsAnything;
import org.hamcrest.core.IsNot;
import org.hamcrest.core.IsNull;
import org.jmock.Expectations;
import org.jmock.api.Action;
import org.jmock.api.Invocation;
import org.jmock.lib.action.CustomAction;

import com.google.common.collect.ImmutableSet;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.testing.QueryParamMatcherMatcher.QueryParameterMatcherBuilder;


public class WExpectations extends Expectations {

  public <T> T withArg(Matcher<T> matcher) {
    return super.with(matcher);
  }

  public <T> T withArg(T t) {
    return super.with(t);
  }

  /**
   * Caution! This method doesn't work with primitives, because it returns null.
   * There is no way to make type inference work and have it return a value of the correct primitive type.
   * Use with(any(Integer.class)) or similar instead.
   */
  public <T> T withAnything() {
    return super.with(new IsAnything<T>());
  }

  public <T> T withNull() {
    return super.with(new IsNull<T>());
  }

  public <T> T withNonNull() {
    return super.with(new IsNot<T>(new IsNull<>()));
  }

  public static <T> Matcher<T> allOf(Matcher<? super T>... matchers) {
    return Matchers.allOf(matchers);
  }

  public static Action returnConsecutive(Object... returnValues) {
    Action[] actions = new Action[returnValues.length];
    for (int i = 0; i < returnValues.length; i++) {
      actions[i] = returnValue(returnValues[i]);
    }
    return onConsecutiveCalls(actions);
  }

  public static Action returnWithId(final Id<?> id) {
    return new CustomAction("return with id set") {
      @Override
      public Object invoke(Invocation invocation) throws Throwable {
        Object object = invocation.getParameter(0);
        Class<?> klazz = object.getClass();
        while (klazz.getSuperclass() != AbstractHibernateEntity.class) {
          if (klazz.getSuperclass() == null) {
            throw new IllegalStateException(format("%s not a subclass of AbstractHibernateEntity", object));
          }
          klazz = klazz.getSuperclass();
        }
        Field field = klazz.getDeclaredField("id");
        field.setAccessible(true);
        field.set(object, id);
        return id;
      }
    };
  }

  public static <T> Action returnResultOf(String description, Supplier<T> supplier) {
    return new CustomAction(description) {
      @Override
      public Object invoke(Invocation invocation) throws Throwable {
        return supplier.get();
      }
    };
  }

  public static <T> Action returnResultOf(Supplier<T> supplier) {
    return returnResultOf("return the result of a supplier", supplier);
  }

  public static <T extends Query<U>, U> com.kaching.platform.testing.QueryStringMatcher<T> query(Class<T> queryClass, String... params) {
    return new com.kaching.platform.testing.QueryStringMatcher<>(queryClass, params);
  }

  public static <T extends Query<U>, U> QueryStringMatcherMatcher<T> query(Class<T> queryClass, Matcher<String>... params) {
    return new QueryStringMatcherMatcher<>(queryClass, params);
  }

  public static <T extends Query<U>, U> QueryMatcher<T> query(Class<T> queryClass, Option<String>... params) {
    return QueryMatcher.query(queryClass, params);
  }

  /**
   * Uses a builder to construct a QueryParamMatcherMatcher, where a custom Matcher can be supplied for every query argument.
   * Example:
   * <pre>
   * oneOf(smartClient).invoke(with(queryWithParams(b -> new MyQuery(b.with("arg1"), b.with("arg 2")))));
   * </pre>
   */
  public static <T extends Query<?>> QueryParamMatcherMatcher<T> queryWithParams(Function<QueryParameterMatcherBuilder, T> function) {
    return QueryParamMatcherMatcher.query(function);
  }

  public static <T extends Query<U>, U> QueryMatcher<T> query(T query) {
    return QueryMatcher.query(query);
  }

  public static <T extends Query<?>> QueryMatcher<T> queryFlexPrecision(T query) {
    return QueryMatcher.queryFlexPrecision(query);
  }

  public static <T extends Query<U>, U> QueryMatcher<T> queryWithUnorderedParams(T query) {
    return QueryMatcher.queryWithUnorderedParams(query);
  }

  public static <T extends Query<U>, U> QueriesMatcher<T, U> queries(List<T> queries) {
    return QueriesMatcher.queries(queries);
  }
  
  public static <T extends Query<U>, U> QueriesMatcher<T, U> queries(T... queries) {
    return QueriesMatcher.queries(queries);
  }

  public static <T extends Query<U>, U> UnorderedQueriesMatcher<T, U> unorderedQueries(Collection<T> queries) {
    return UnorderedQueriesMatcher.unorderedQueries(queries);
  }
  
  public static <T extends Query<U>, U> UnorderedQueriesMatcher<T, U> unorderedQueries(T... queries) {
    return UnorderedQueriesMatcher.unorderedQueries(queries);
  }

  public static <K, V extends Query<T>, T> QueryMapMatcher<K, V, T> queryMap(Map<K, V> expected) {
    return QueryMapMatcher.queryMap(expected);
  }

  public static Matcher<Exception> exception(Class<? extends Exception> exceptionType, String messageStub) {
    return new TypeSafeMatcher<Exception>() {
      @Override
      public void describeTo(Description description) {
        description.appendText(Strings.format("Exception of type %s containing the message \"%s\"",
            exceptionType, messageStub));
      }

      @Override
      public boolean matchesSafely(Exception exception) {
        return exception.getClass() == exceptionType && exception.getMessage().contains(messageStub);
      }
    };
  }

  public static <T> Matcher<Collection<T>> collection(final Matcher<T> matcher, final int count) {
    return new TypeSafeMatcher() {
      @Override
      public void describeTo(Description description) {
        description.appendText("each item matching").appendValue(matcher);
      }

      @Override
      public boolean matchesSafely(Object item) {
        Collection<T> collection = (Collection) item;
        if (collection.size() != count) {
          return false;
        }
        for (T t : collection) {
          if (!matcher.matches(t)) {
            return false;
          }
        }
        return true;
      }
    };
  }

  protected static <T> Matcher<Option<T>> none(Class<T> klass) {
    return new TypeSafeMatcher<Option<T>>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("none");
      }

      @Override
      protected boolean matchesSafely(Option<T> param) {
        return param.isEmpty();
      }
    };
  }
  
  public static <T> Action returnList(T... values) {
    return returnValue(Arrays.asList(values));
  }
  
  protected static <T> Action returnSet(T... values) {
    return returnValue(ImmutableSet.copyOf(values));
  }

  protected static <T> Action returnSome(T value) {
    return returnValue(Option.some(value));
  }

  protected static <T> Action returnNone() {
    return returnValue(Option.<T>none());
  }

  protected static <T> Action returnOption(T value) {
    return returnValue(Option.of(value));
  }
  
  protected static <T> Matcher<T> instanceOf(Class<T> type) {
    return Matchers.instanceOf(type);
  }

}
```
Like this, notice the empty line between expectations:
```java
mockery.checking(new WExpectations() {{
    oneOf(executor).invoke(with(query(new MyAwesomeQuery())));
    will(returnValue(true));

    oneOf(sleeper).sleep(with(Duration.standardMillis(2000L)));
    will(returnValue(false));
}});
```
### When to Mock
Interfaces involving cross-service interaction or 3rd party API calls should be mocked.
Classes used to fetch data from the database, like a `Transacter`, `Repository`, `DbSession`, should NEVER be mocked. Instead, some form of persistent test base should be used.
Classes that work inside of database sessions and primarly use Repositories or DbSessions should also never be mocked- they should just be injected.
Data classes (json entities, record, identifiers, AbstractQuery parameters or return types, etc) should NEVER be mocked. 


### Style Notes
When setting expectations, always prefer to assert the exact expected objects, with no matchers if possible.
JMock allows you to ommit with `with()` matchers entirely for a method call, if all parameters are relying on hashCode/equals 
This obviously isn't possible if the types don't implement hashCode/equals/toString.
Writing custom Matchers should be a last resort option- only use TypeSafeMatcher, Matcher, or any kind of other matcher ONLY when `with()` isn't possible.

## Assertions
Never mock data objects (classes the primarly hold data and not logic). Always find a way to construct real versions by looking at other tests.
Don't use `@Test(expected=[Exception class]`, instead use `com.wealthfront.test.Assert.Class<? extends Exception> exceptionClass, String expectedMessage, Runnable runnable` or `assertThrowsRegex(Class<? extends Exception> exceptionClass, String regexMatchingMessage, Runnable runnable)`