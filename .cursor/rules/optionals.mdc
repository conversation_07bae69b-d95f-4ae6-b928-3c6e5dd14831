---
description: Options Optionals Nullability
globs: 
alwaysApply: false
---
# Kaching Option Class
When handling nullable variables in Java, we prefer to use a special com.kaching.platform.common.Option class and not Java's built-in Optional class. Option can be Option.some(T t) or Option.none().
```
package com.kaching.platform.common;
import static java.lang.String.format;
import com.google.common.base.Function;
import com.google.common.base.Preconditions;
import com.google.common.base.Predicate;
import com.google.common.base.Supplier;
import com.google.common.collect.AbstractIterator;

public abstract class Option<T> implements Iterable<T> {

  public abstract T getOrNull();

  public abstract T getOrElse(T defaultValue);
  public abstract T getOrElse(Thunk<T> defaultValue);
  public abstract T getOrElse(Supplier<T> defaultValue);
  public abstract T getOrThrow();
  public abstract T getOrThrow(String message);
  public abstract <E extends Throwable> T getOrThrow(E e) throws E;
  public abstract String toStringOr(String defaultValue);
  public abstract boolean isEmpty();
  public boolean isDefined() {
    return !isEmpty();
  }
  public abstract <V> V visit(OptionVisitor<? super T, V> visitor);
  public abstract <V> V visit(Supplier<? extends V> caseNone, Function<? super T, ? extends V> caseSome);
  public abstract <V> Option<V> transform(Function<? super T, V> function);
  public abstract <V> Option<V> flatTransform(Function<? super T, Option<V>> function);
  public abstract <V> Option<V> flatTransformOptional(Function<? super T, Optional<V>> function);
  public abstract Option<T> filter(Predicate<? super T> predicate);
  public abstract void ifDefined(Consumer<? super T> caseSome);
  public abstract void ifDefinedOrElse(java.util.function.Consumer<? super T> caseSome, Runnable caseNone);
  public abstract boolean isDefinedAnd(java.util.function.Predicate<T> predicate);
  public abstract boolean isEmptyOr(java.util.function.Predicate<T> predicate);
  public abstract Option<T> or(Supplier<Option<T>> supplier);
  public abstract Set<T> asSet();
  public abstract Stream<T> stream();

  public static <T> Option<T> none() {
    // returns a singleton NONE instance
  }
  public static <T> Option<T> some(T t) {
    return new Option.Some<>(t); // returns private inner class Some
  }
  public static <T> Option<T> of(T t) {
    return t == null ? Option.none() : some(t);
  }
  // translation between Java's Optional and Option
  public static <T> Option<T> from(Optional<T> optional) {
    return Option.of(optional.orElse(null));
  }
  public static <T> Option<T> from(@SuppressWarnings("Guava") com.google.common.base.Optional<T> optional) {
    return Option.of(optional.orNull());
  }
  public static <T> Stream<T> toStream(Option<T> option) {
    return option.stream();
  }
}
```
# When to Use
Methods should always wrap their return values in Option if the return value could be empty.
Most data classes, including Json entities annoted with `@Entity` should take in unwrapped nullable parameters in their constructor (no Option) but should wrap getters in Option if the variables are nullable. The exception is Java Records, which should accept Options directly in their constructor.

# Testing
Use these methods in `com.wealthfront.test.Assert` when asserting optional values when possible. They should always be statically imported.
`static <T> void assertOptionEquals(T expected, Option<T> option)`
`static void assertOptionEmpty(Option<?> option)`









