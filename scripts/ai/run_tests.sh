#!/bin/bash

# Process flags and required arguments.
nofilter=false
args=()

for arg in "$@"; do
  case "$arg" in
    --nofilter)
      nofilter=true
      ;;
    -h|--help)
      echo "Usage: $(basename "$0") [module name] [test class] [--nofilter]"
      exit 0
      ;;
    *)
      args+=("$arg")
      ;;
  esac
done

# Check that we have at least the module name and test class.
if [ "${#args[@]}" -lt 2 ]; then
  echo "Usage: $(basename "$0") [module name] [test class] [--nofilter]"
  exit 1
fi

module="${args[0]}"
test_class="${args[1]}"

# Build the Maven command as an array for proper quoting.
cmd=(mvn test \
  -pl "$module" \
  -am \
  -Dsurefire.failIfNoSpecifiedTests=false \
  -Darguments=-Dwlth.skip.stub-query-transfer=true \
  -Dwlth.skip.stub-query-generation=true \
  -Dinvoker.generate-stub-queries.skip=true \
  -Dsort.skip=true \
  -Dinvoker.compile.skip=true \
  -Dcheckstyle.skip=true \
  -Dtest="$test_class")

# Print the command that's about to run.
echo "Running command:"
echo "${cmd[@]}"

if [ "$nofilter" = true ]; then
  # Show full output.
  "${cmd[@]}" 2>&1
else
  echo "Skipping irrelevant lines in output. Use --nofilter to show full output."
  # Define an array of regex patterns for lines you want to filter out.
  patterns=(
    '^\[INFO\]\s*$'                   # Lines that are just "[INFO]" (with optional whitespace)
    '^\[INFO\]\s+[sS]kip'              # Lines that start with "[INFO] skipping" or "Skip"
    '^\[INFO\]\s+Copying'              # Lines that start with "[INFO] Copying"
    '^\[INFO\]\s+Using .+ encoding to copy'  # Lines about "Using 'UTF-8' encoding to copy..."
    '^\[INFO\]\s+The encoding used to copy'   # Lines mentioning "The encoding used to copy..."
    '^\[INFO\]\s+Generating flattened POM'
    '^\[INFO\]\s+Nothing to compile - all classes are up to date'
    '^\[INFO\]\s+--- '                 # Lines that start with "[INFO] ---"
    '^\[INFO\]\s+------------'                 # Lines that start with "[INFO] ---"
    '^\[INFO\]\s+Storing '             # Lines that start with "[INFO] Storing"
    '^\[INFO\]\s+ShortRevision'        # Lines that start with "[INFO] ShortRevision"
    '^\[INFO\]\s+Finished at'        # Lines that start with "[INFO] Finished at"
    '^\[WARNING\]\s+ artifact '        # Lines that start with "[WARNING] artifact"
  )

  # Join the array elements into one regex separated by pipes.
  regex=$(IFS="|"; echo "${patterns[*]}")

  # Run the Maven command and pipe its output to grep to filter out unwanted lines.
  "${cmd[@]}" 2>&1 | grep -vE "$regex"
fi