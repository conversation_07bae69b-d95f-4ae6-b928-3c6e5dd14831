package com.wealthfront.util;

import java.util.function.BiFunction;

public final class ComparableUtils {

  public static <T extends Comparable<T>> T max(T t1, T t2) {
    return t1.compareTo(t2) > 0 ? t1 : t2;
  }

  @SafeVarargs
  public static <T extends Comparable<T>> T max(T t1, T t2, T... rest) {
    return select(ComparableUtils::max, t1, t2, rest);
  }

  public static <T extends Comparable<T>> T min(T t1, T t2) {
    return t1.compareTo(t2) < 0 ? t1 : t2;
  }

  @SafeVarargs
  public static <T extends Comparable<T>> T min(T t1, T t2, T... rest) {
    return select(ComparableUtils::min, t1, t2, rest);
  }

  @SafeVarargs
  private static <T> T select(BiFunction<T, T, T> f, T t1, T t2, T... rest) {
    T result = f.apply(t1, t2);
    for (T t : rest) {
      result = f.apply(result, t);
    }
    return result;
  }

}
