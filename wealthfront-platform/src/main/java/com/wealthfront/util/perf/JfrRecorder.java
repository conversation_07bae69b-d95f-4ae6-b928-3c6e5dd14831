package com.wealthfront.util.perf;

import org.joda.time.Duration;

import com.google.inject.ImplementedBy;
import com.kaching.platform.common.Option;
import com.kaching.platform.functional.Unchecked.ThrowingConsumer;
import com.kaching.platform.functional.Unchecked.ThrowingFunction;
import com.kaching.platform.functional.Unchecked.ThrowingRunnable;
import com.kaching.platform.functional.Unchecked.ThrowingSupplier;
import com.wealthfront.util.objects.Builder;

@ImplementedBy(CmdJfrRecorder.class)
public interface JfrRecorder {

  record Config(
      String uniqueName,
      Option<Duration> minimumDuration
  ) {

    public static Builder<Config> uniquelyNamed(String uniqueName) {
      return new Builder<>(Config.class)
          .set(Config::uniqueName).to(uniqueName);
    }

  }

  <T> T scope(Config config, ThrowingSupplier<T> scope);

  void scope(Config config, ThrowingRunnable scope);

  <T> T scope(Config config, ThrowingFunction<JfrContext, T> scope);

  void scope(Config config, ThrowingConsumer<JfrContext> scope);

  JfrContext jfrContext(Config config);

  default <T> T scope(String uniqueName, ThrowingSupplier<T> scope) {
    return scope(Config.uniquelyNamed(uniqueName).build(), scope);
  }

  default void scope(String uniqueName, ThrowingRunnable scope) {
    scope(Config.uniquelyNamed(uniqueName).build(), scope);
  }

  default <T> T scope(String uniqueName, ThrowingFunction<JfrContext, T> scope) {
    return scope(Config.uniquelyNamed(uniqueName).build(), scope);
  }

  default void scope(String uniqueName, ThrowingConsumer<JfrContext> scope) {
    scope(Config.uniquelyNamed(uniqueName).build(), scope);
  }

  default JfrContext jfrContext(String uniqueName) {
    return jfrContext(Config.uniquelyNamed(uniqueName).build());
  }

  interface JfrContext extends AutoCloseable {

    void abort();

    @Override
    void close();

  }

}
