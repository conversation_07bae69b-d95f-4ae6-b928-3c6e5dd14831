package com.wealthfront.util.stream;

import static java.util.Collections.emptySet;
import static java.util.stream.Collectors.toMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collector;

import com.google.common.collect.ImmutableMap;
import com.kaching.platform.common.Pair;
import com.kaching.util.functional.Tuple2;

public class WFCollectors {

  public static <K, V> Collector<Map<K, V>, ?, Map<K, V>> mergeToImmutableMap() {
    return new Collectors.CollectorImpl<>(
        ImmutableMap::<K, V>builder,
        ImmutableMap.Builder::putAll,
        (builder1, builder2) -> builder1.putAll(builder2.build()),
        ImmutableMap.Builder::build,
        emptySet()
    );
  }

  public static <K, V> Collector<Map<K, V>, ?, Map<K, V>> mergeToMap(BinaryOperator<V> merger) {
    return new Collectors.CollectorImpl<Map<K, V>, Pair<AtomicInteger, List<Set<Map.Entry<K, V>>>>, Map<K, V>>(
        () -> Pair.of(new AtomicInteger(0), new ArrayList<>()),
        (pair, map) -> {
          pair.getLeft().addAndGet(map.size());
          pair.getRight().add(map.entrySet());
        },
        (pair1, pair2) -> {
          pair1.getLeft().addAndGet(pair2.getLeft().get());
          pair1.getRight().addAll(pair2.getRight());
          return pair1;
        },
        pair -> pair.getRight()
            .stream()
            .flatMap(Set::stream)
            .collect(toMap(Map.Entry::getKey, Map.Entry::getValue, merger, () -> new HashMap<>(pair.getLeft().get()))),
        emptySet()
    );
  }

  public static <K, V> Collector<Pair<K, V>, ?, Map<K, V>> pairsToMap() {
    return toMap(Pair::getLeft, Pair::getRight);
  }

  public static <K, V> Collector<Pair<K, V>, ?, Map<K, V>> pairsToMap(BinaryOperator<V> merger) {
    return toMap(Pair::getLeft, Pair::getRight, merger);
  }

  public static <K, V> Collector<Map.Entry<K, V>, ?, Map<K, V>> entriesToMap() {
    return toMap(Map.Entry::getKey, Map.Entry::getValue);
  }

  public static <K, V> Collector<Map.Entry<K, V>, ?, Map<K, V>> entriesToMap(BinaryOperator<V> merger) {
    return toMap(Map.Entry::getKey, Map.Entry::getValue, merger);
  }

  public static <K, V> Collector<Tuple2<K, V>, ?, Map<K, V>> tuplesToMap() {
    return toMap(t2 -> t2._1, t2 -> t2._2);
  }

  public static <K, V> Collector<Tuple2<K, V>, ?, Map<K, V>> tuplesToMap(BinaryOperator<V> merger) {
    return toMap(t2 -> t2._1, t2 -> t2._2, merger);
  }

  public static <K, V> Collector<V, ?, Map<K, V>> toMapWithKey(Function<V, K> keyExtractor) {
    return toMap(keyExtractor, Function.identity());
  }

  public static <K, V> Collector<V, ?, Map<K, V>> toMapWithKey(Function<V, K> keyExtractor, BinaryOperator<V> merger) {
    return toMap(keyExtractor, Function.identity(), merger);
  }

}
