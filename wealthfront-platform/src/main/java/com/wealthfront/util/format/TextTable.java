package com.wealthfront.util.format;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.util.functional.Tuple;
import com.kaching.util.functional.Tuple0;
import com.kaching.util.functional.Tuple1;
import com.kaching.util.functional.Tuple2;
import com.kaching.util.functional.Tuple3;
import com.kaching.util.functional.Tuple4;
import com.kaching.util.functional.Tuple5;
import com.kaching.util.functional.Tuple6;
import com.kaching.util.functional.Tuple7;
import com.kaching.util.functional.Tuple8;
import com.kaching.util.functional.Tuple9;

public class TextTable<T extends Tuple> {

  protected final List<String> headers;
  protected final int[] rowWidth;
  protected int[] rowMaxWidths = null;
  protected String headerSeparator = "-";
  protected String columnSeparator = "|";
  protected String paddingCharacter = " ";
  protected String lineSeparator = "\n";
  protected boolean includeHeaderSeparator = true;
  protected final List<String[]> rows = new ArrayList<>();

  public TextTable(T headers) {
    this(Arrays.stream(tupleToArray(headers))
        .map(Object::toString)
        .collect(Collectors.toList()));
  }

  public TextTable(String... headers) {
    this(Arrays.asList(headers));
  }

  public TextTable(List<String> headers) {
    this.headers = headers;
    rowWidth = new int[this.headers.size()];
    for (int i = 0; i < this.headers.size(); i += 1) {
      rowWidth[i] = this.headers.get(i).length();
    }
  }

  public TextTable<T> withHeaderSeparator(String headerSeparator) {
    this.headerSeparator = headerSeparator;
    return this;
  }

  public TextTable<T> withColumnSeparator(String columnSeparator) {
    this.columnSeparator = columnSeparator;
    return this;
  }

  public TextTable<T> withPaddingCharacter(String paddingCharacter) {
    this.paddingCharacter = paddingCharacter;
    return this;
  }

  public TextTable<T> withLineSeparator(String lineSeparator) {
    this.lineSeparator = lineSeparator;
    return this;
  }

  public TextTable<T> includeHeaderSeparator(boolean includeHeaderSeparator) {
    this.includeHeaderSeparator = includeHeaderSeparator;
    return this;
  }

  public TextTable<T> withRowMaxWidths(T rowMaxWidths) {
    Object[] maxWidths = tupleToArray(rowMaxWidths);
    if (maxWidths.length != headers.size()) {
      throw new IllegalStateException(Strings.format("row max widths has %s columns, differing from %s headers",
          maxWidths.length, headers.size()));
    }
    this.rowMaxWidths = new int[maxWidths.length];
    for (int i = 0; i < maxWidths.length; i += 1) {
      this.rowMaxWidths[i] = (Integer) maxWidths[i];
    }
    for (int i = 0; i < this.rowMaxWidths.length; i += 1) {
      rowWidth[i] = Integer.min(this.rowMaxWidths[i], rowWidth[i]);
    }
    return this;
  }

  public TextTable<T> withVRow(Object... row) {
    if (row.length != headers.size()) {
      throw new IllegalStateException(
          Strings.format("row has %s columns, differing from %s headers", row.length, headers.size()));
    }
    String[] displayRow = new String[row.length];
    for (int i = 0; i < row.length; i += 1) {
      Object rowValue = row[i];
      if (rowValue instanceof Option) {
        rowValue = ((Option<?>) rowValue).getOrNull();
      }
      displayRow[i] = rowValue == null ? "" : rowValue.toString().trim();
      rowWidth[i] = Integer.max(rowWidth[i], displayRow[i].length());
      if (rowMaxWidths != null) {
        rowWidth[i] = Integer.min(rowWidth[i], rowMaxWidths[i]);
      }
    }
    rows.add(displayRow);
    return this;
  }

  public TextTable<T> withRow(T tuple) {
    withVRow(tupleToArray(tuple));
    return this;
  }

  public String display() {
    StringBuilder output = new StringBuilder();

    boolean first = true;
    for (int col = 0; col < headers.size(); col += 1) {
      if (!first) {
        output.append(columnSeparator);
      }
      first = false;

      output.append(paddingCharacter);
      int padding = rowWidth[col] - headers.get(col).length();
      for (int i = 0; i < padding; i += 1) {
        output.append(paddingCharacter);
      }
      String header = headers.get(col);
      if (rowMaxWidths != null) {
        output.append(header, 0, Integer.min(rowMaxWidths[col], header.length()));
      } else {
        output.append(header);
      }
      output.append(paddingCharacter);
    }
    output.append(lineSeparator);

    if (includeHeaderSeparator) {
      first = true;
      for (int col = 0; col < headers.size(); col += 1) {
        if (!first) {
          output.append(columnSeparator);
        }
        first = false;
        output.append(paddingCharacter);
        for (int i = 0; i < rowWidth[col]; i += 1) {
          output.append(headerSeparator);
        }
        output.append(paddingCharacter);
      }
      output.append(lineSeparator);
    }

    for (String[] row : rows) {
      first = true;
      for (int col = 0; col < row.length; col += 1) {
        if (!first) {
          output.append(columnSeparator);
        }
        first = false;

        output.append(paddingCharacter);
        int padding = rowWidth[col] - row[col].length();
        for (int i = 0; i < padding; i += 1) {
          output.append(paddingCharacter);
        }
        String cell = row[col];
        if (rowMaxWidths != null) {
          output.append(cell, 0, Integer.min(rowMaxWidths[col], cell.length()));
        } else {
          output.append(cell);
        }
        output.append(paddingCharacter);
      }
      output.append(lineSeparator);
    }

    return output.toString();
  }

  private static Object[] tupleToArray(Tuple tuple) {
    if (tuple instanceof Tuple9) {
      Tuple9 t = (Tuple9) tuple;
      return new Object[]{t._1, t._2, t._3, t._4, t._5, t._6, t._7, t._8, t._9};
    } else if (tuple instanceof Tuple8) {
      Tuple8 t = (Tuple8) tuple;
      return new Object[]{t._1, t._2, t._3, t._4, t._5, t._6, t._7, t._8};
    } else if (tuple instanceof Tuple7) {
      Tuple7 t = (Tuple7) tuple;
      return new Object[]{t._1, t._2, t._3, t._4, t._5, t._6, t._7};
    } else if (tuple instanceof Tuple6) {
      Tuple6 t = (Tuple6) tuple;
      return new Object[]{t._1, t._2, t._3, t._4, t._5, t._6};
    } else if (tuple instanceof Tuple5) {
      Tuple5 t = (Tuple5) tuple;
      return new Object[]{t._1, t._2, t._3, t._4, t._5};
    } else if (tuple instanceof Tuple4) {
      Tuple4 t = (Tuple4) tuple;
      return new Object[]{t._1, t._2, t._3, t._4};
    } else if (tuple instanceof Tuple3) {
      Tuple3 t = (Tuple3) tuple;
      return new Object[]{t._1, t._2, t._3};
    } else if (tuple instanceof Tuple2) {
      Tuple2 t = (Tuple2) tuple;
      return new Object[]{t._1, t._2};
    } else if (tuple instanceof Tuple1) {
      Tuple1 t = (Tuple1) tuple;
      return new Object[]{t._1};
    } else if (tuple instanceof Tuple0) {
      return new Object[]{};
    } else {
      throw new UnsupportedOperationException(Strings.format("unknown tuple type %s", tuple.getClass()));
    }
  }

}
