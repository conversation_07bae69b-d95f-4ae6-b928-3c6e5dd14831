package com.wealthfront.util.cache;

import static com.wealthfront.util.collections.WfCollections.mergeSortDistinct;
import static com.wealthfront.util.perf.MemoryConstants.CONCURRENT_HASH_MAP_ENTRY_OVERHEAD_BYTES;
import static java.util.Comparator.comparing;

import java.util.AbstractCollection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.SortedMap;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.ImmutableSortedSet;
import com.google.common.collect.Iterables;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.google.common.primitives.UnsignedBytes;
import com.kaching.entities.Scalar;
import com.kaching.entities.collections.MappingList;
import com.kaching.platform.common.Option;
import com.kaching.platform.tinykv.impl.WrappedBytesEntry;
import com.kaching.platform.util.WrappedBytes;
import com.wealthfront.util.collections.WfCollections;

public class BufferedStaticByteCache implements ReadWriteByteCache {

  private static final byte FREQUENCY_STARTING_VALUE = UnsignedBytes.checkedCast(1);
  private static final int MIN_BUFFER_SIZE = 128;
  private static final int MAX_BUFFER_SIZE = 1024;
  private static final Scalar BUFFER_SIZE_FRACTION = Scalar.scalar(.05);

  private static final Scalar RUN_EAGER_MAINTENANCE_FRACTION = Scalar.scalar(.75);

  private static final double BUFFER_FILTER_TARGET_FPP = .03;
  
  private int maxBufferSize;
  private int maxStaticCacheSize;
  private int eagerMaintenanceBufferSize;
  private long bufferFilterSize;
  
  private final ReentrantLock maintenanceLock = new ReentrantLock(false);
  private final AtomicReference<StaticByteCache> cache;
  private final AtomicReference<Map<WrappedBytes, WrappedBytes>> buffer = new AtomicReference<>(new ConcurrentHashMap<>());
  private final AtomicReference<BloomFilter<byte[]>> bufferFilter = new AtomicReference<>();
  private final Supplier<Random> randomSupplier;
  private final ByteCacheStats stats;

  public BufferedStaticByteCache(Supplier<Random> randomSupplier, ByteCacheStats stats, int maxTotalSize) {
    this.randomSupplier = randomSupplier;
    this.stats = stats;

    this.cache = new AtomicReference<>(new StaticByteCache(randomSupplier, stats, List.of()));
    int idealBufferSize = BUFFER_SIZE_FRACTION.multiply(maxTotalSize).intValue();
    this.maxBufferSize = Math.min(Math.max(MIN_BUFFER_SIZE, idealBufferSize), MAX_BUFFER_SIZE);
    this.maxStaticCacheSize = Math.max(0, maxTotalSize - this.maxBufferSize);
    
    this.eagerMaintenanceBufferSize = Math.max(1, RUN_EAGER_MAINTENANCE_FRACTION.multiply(maxBufferSize).intValue());
    this.bufferFilterSize = maxTotalSize * 2L;
    resetBloomFilter();
  }

  @Override
  public Collection<WrappedBytesEntry> getAllSorted() {
    List<WrappedBytesEntry> fromBuffer = sortAndWrapEntries(buffer.getAcquire());
    Collection<WrappedBytesEntry> fromStatic = cache.getAcquire().getAllSorted();
    return new AbstractCollection<>() {
      @Override
      public Iterator<WrappedBytesEntry> iterator() {
        return WfCollections.mergeSortDistinct(fromBuffer.iterator(), fromStatic.iterator(), comparing(WrappedBytesEntry::key));
      }

      @Override
      public int size() {
        return fromBuffer.size() + fromStatic.size();
      }
    };
  }

  @Override
  public int size() {
    return buffer.getAcquire().size() + cache.getAcquire().size();
  }

  @Override
  public Option<WrappedBytes> getIfPresent(WrappedBytes key, boolean recordRead) {
    WrappedBytes result = buffer.getAcquire().get(key);
    if (result == null) {
      result = cache.getAcquire().getIfPresent(key, recordRead).getOrNull();
    }
    if (recordRead) {
      if (result == null) {
        stats.addNumMisses(1);
      } else {
        stats.addNumHits(1);
      }
    }
    return Option.of(result);
  }

  @Override
  public Map<WrappedBytes, WrappedBytes> getIfPresent(Collection<WrappedBytes> keys, boolean recordRead) {
    ImmutableSortedMap.Builder<WrappedBytes, WrappedBytes> result = ImmutableSortedMap.naturalOrder();
    StaticByteCache cache = this.cache.getAcquire();
    Map<WrappedBytes, WrappedBytes> buffer = this.buffer.getAcquire();
    Set<WrappedBytes> keySet = ImmutableSortedSet.copyOf(keys);
    for (WrappedBytes key : keySet) {
      WrappedBytes value = buffer.get(key);
      if (value != null) {
        result.put(key, value);
      } else {
        Option<WrappedBytes> cacheValue = cache.getIfPresent(key, recordRead);
        if (cacheValue.isDefined()) {
          result.put(key, cacheValue.getOrThrow());
        }
      }
    }
    Map<WrappedBytes, WrappedBytes> resultMap = result.buildOrThrow();
    if (recordRead) {
      stats.addNumHits(resultMap.size());
      stats.addNumMisses(keySet.size() - resultMap.size());
    }
    return resultMap;
  }

  @Override
  public void putAll(Map<WrappedBytes, WrappedBytes> newValues) {
    StaticByteCache cache = this.cache.getAcquire();
    BloomFilter<byte[]> filter = this.bufferFilter.getAcquire();
    List<WrappedBytes> toInvalidate = new ArrayList<>();
    int bloomFiltered = 0;
    for (var entry : newValues.entrySet()) {
      WrappedBytes newVal = entry.getValue();
      WrappedBytes existingStatic = cache.getIfPresent(entry.getKey(), false).getOrNull();
      if (existingStatic != null && !existingStatic.equals(newVal)) {
        toInvalidate.add(entry.getKey());
      }
      if (filter.put(entry.getKey().getBytes()) && cache.size() >= maxStaticCacheSize) {
        bloomFiltered++;
        continue;
      }
      buffer.getAcquire().compute(entry.getKey(), (k, oldVal) -> {
        if (oldVal == null) {
          return newVal;
        } else if (oldVal.equals(newVal)) {
          return oldVal;
        } else {
          return newVal;
        }
      });
    }
    invalidate(toInvalidate);
    stats.addNumBloomFilterEvictions(bloomFiltered);
    if (buffer.getAcquire().size() >= maxBufferSize || filter.approximateElementCount() > bufferFilterSize) {
      runMaintenance(true);
    }
  }

  @Override
  public void invalidate(Collection<WrappedBytes> keys) {
    if (keys.isEmpty()) {
      return;
    }
    maintenanceLock.lock();
    try {
      int num = 0;
      var buffer = this.buffer.getAcquire();
      for (WrappedBytes key : keys) {
        WrappedBytes oldVal = buffer.remove(key);
        if (oldVal != null) {
          num++;
        }
      }
      stats.addNumInvalidations(num);
      cache.getAcquire().invalidate(keys);
    } finally {
      maintenanceLock.unlock();
    }
  }

  @Override
  public void preload(SortedMap<WrappedBytes, WrappedBytes> entries) {
    ImmutableList.Builder<WrappedBytesEntry> asList = ImmutableList.builderWithExpectedSize(entries.size());
    for (var entry : entries.entrySet()) {
      asList.add(new WrappedBytesEntry(entry.getKey(), entry.getValue(), FREQUENCY_STARTING_VALUE));
    }
    StaticByteCache newCache = new StaticByteCache(randomSupplier, stats, asList.build());
    this.cache.setRelease(newCache);
    resetBloomFilter();
  }

  @Override
  public void runMaintenance() {
    runMaintenance(false);
  }
  
  @Override
  public long estimateMemoryUsage() {
    long bufferUsage = 0L;
    for (var entry : buffer.getAcquire().entrySet()) {
      bufferUsage += CONCURRENT_HASH_MAP_ENTRY_OVERHEAD_BYTES + entry.getKey().estimateMemoryUsage() + entry.getValue().estimateMemoryUsage();
    }
    long result = bufferUsage;
    StaticByteCache cache = this.cache.getAcquire();
    if (cache != null) {
      result += cache.estimateMemoryUsage();
    }
    // From {@link BloomFilter#optimalNumOfBits}
    result += (long) (-bufferFilterSize * Math.log(BUFFER_FILTER_TARGET_FPP) / (Math.log(2) * Math.log(2))) / 8;
    return result;
  }

  private void runMaintenance(boolean bestEffort) {
    if (bestEffort) {
      if (!maintenanceLock.tryLock()) {
        return;
      }
    } else {
      maintenanceLock.lock();
    }
    try {
      maybeResetBufferFilter();
      if (buffer.getAcquire().size() < eagerMaintenanceBufferSize) {
        return;
      }

      Collection<WrappedBytesEntry> keeping = getEntriesToKeepSorted();
      cache.setRelease(new StaticByteCache(randomSupplier, stats, keeping));
      stats.addNumStaticCacheRebuilds(1);
    } finally {
      maintenanceLock.unlock();
    }
  }

  private Collection<WrappedBytesEntry> getEntriesToKeepSorted() {
    SortedSet<Map.Entry<WrappedBytes, WrappedBytes>> drained = swapAndDrainBuffer();
    int numStaticToKeep = Math.max(0, maxStaticCacheSize - drained.size());
    StaticByteCache cache = this.cache.getAcquire();
    
    StaticByteCache.MostFrequentlyUsed mfu;
    int numEvicted;
    if (cache == null) {
      mfu = new StaticByteCache.MostFrequentlyUsed(Collections.emptyList(), FREQUENCY_STARTING_VALUE);
      numEvicted = 0;
    } else {
      mfu = cache.getMostFrequentlyUsed(numStaticToKeep);
      numEvicted = cache.size() - mfu.entries().size();
    }
    stats.addNumEvictions(numEvicted);
    byte medianFrequency = mfu.entries().isEmpty() ? FREQUENCY_STARTING_VALUE : mfu.percentileFrequency();
    Iterable<WrappedBytesEntry> drainedIter = Iterables.transform(drained, e -> new WrappedBytesEntry(e.getKey(), e.getValue(), medianFrequency));
    return new AbstractCollection<>() {
      @Override
      public Iterator<WrappedBytesEntry> iterator() {
        return mergeSortDistinct(
            mfu.entries().iterator(),
            drainedIter.iterator(),
            comparing(WrappedBytesEntry::key)
        );
      }

      @Override
      public int size() {
        return mfu.entries().size() + drained.size();
      }
    };
  }
  
  private SortedSet<Map.Entry<WrappedBytes, WrappedBytes>> swapAndDrainBuffer() {
    Map<WrappedBytes, WrappedBytes> oldBuffer = this.buffer.getAndSet(new ConcurrentHashMap<>(maxBufferSize));
    SortedSet<Map.Entry<WrappedBytes, WrappedBytes>> result = new TreeSet<>(Map.Entry.comparingByKey());
    for (int i = 0; i < 10; i++) {
      Iterator<Map.Entry<WrappedBytes, WrappedBytes>> iterator = oldBuffer.entrySet().iterator();
      while (iterator.hasNext()) {
        result.add(iterator.next());
        iterator.remove();
      }
      Thread.onSpinWait();
    }
    return result;
  } 

  private void maybeResetBufferFilter() {
    if (bufferFilter.getAcquire().approximateElementCount() > bufferFilterSize) {
      resetBloomFilter();
    }
  }

  private void resetBloomFilter() {
    stats.addNumBloomFilterRebuilds(1);
    bufferFilter.setRelease(BloomFilter.create(Funnels.byteArrayFunnel(), bufferFilterSize, BUFFER_FILTER_TARGET_FPP));
  }

  private static List<WrappedBytesEntry> sortAndWrapEntries(Map<WrappedBytes, WrappedBytes> map) {
    List<Map.Entry<WrappedBytes, WrappedBytes>> list = ImmutableList.sortedCopyOf(Map.Entry.comparingByKey(), map.entrySet());
    return new MappingList<>(list, e -> new WrappedBytesEntry(e.getKey(), e.getValue(), FREQUENCY_STARTING_VALUE), 
        e -> {
      throw new UnsupportedOperationException(); 
    });
  }

  @VisibleForTesting
  void setMaxBufferSize(int maxBufferSize) {
    this.maxBufferSize = maxBufferSize;
  }

  @VisibleForTesting
  void setMaxStaticCacheSize(int maxStaticCacheSize) {
    this.maxStaticCacheSize = maxStaticCacheSize;
  }

  @VisibleForTesting
  void setEagerMaintenanceBufferSize(int eagerMaintenanceBufferSize) {
    this.eagerMaintenanceBufferSize = eagerMaintenanceBufferSize;
  }

  @VisibleForTesting
  StaticByteCache getStaticCache() {
    return cache.getAcquire();
  }

  @VisibleForTesting
  Map<WrappedBytes, WrappedBytes> getBuffer() {
    return buffer.getAcquire();
  }

  @VisibleForTesting
  BloomFilter<byte[]> getBufferFilter() {
    return bufferFilter.getAcquire();
  }
  
}
