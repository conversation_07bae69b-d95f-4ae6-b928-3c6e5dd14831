package com.wealthfront.util.cache;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;

import com.google.common.collect.Collections2;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.ImmutableSortedMap;
import com.kaching.platform.common.Option;
import com.kaching.platform.util.WrappedBytes;
import com.kaching.platform.util.WrappedBytesSerializer;

public class TypedGiantByteCacheImpl<K, V> implements TypedGiantByteCache<K, V> {
  
  private final WrappedBytesSerializer<K> keySerializer;
  private final WrappedBytesSerializer<V> valueSerializer;
  private final GiantByteCache delegate;

  public TypedGiantByteCacheImpl(WrappedBytesSerializer<K> keySerializer,
                                 WrappedBytesSerializer<V> valueSerializer,
                                 GiantByteCache delegate) {  
    this.keySerializer = keySerializer;
    this.valueSerializer = valueSerializer;
    this.delegate = delegate;
  }
  
  public static <K, V> LoadingByteCache.LoadFunction wrapTypedLoadFunction(
      TypedGiantByteCache.LoadFunction<K, V> loadFunction,
      WrappedBytesSerializer<K> keySerializer,
      WrappedBytesSerializer<V> valueSerializer) {
    return keys -> {
      ImmutableList.Builder<K> builder = ImmutableList.builderWithExpectedSize(keys.size());
      for (WrappedBytes key : keys) {
        builder.add(keySerializer.fromBytes(key));
      }
      Map<K, Option<V>> result = loadFunction.load(builder.build());
      ImmutableMap.Builder<WrappedBytes, WrappedBytes> byteResult = ImmutableMap.builderWithExpectedSize(result.size());
      for (Map.Entry<K, Option<V>> entry : result.entrySet()) {
        WrappedBytes key = keySerializer.toBytes(entry.getKey());
        if (entry.getValue().isDefined()) {
          byteResult.put(key, valueSerializer.toBytes(entry.getValue().getOrThrow()));
        } else {
          byteResult.put(key, LoadingByteCache.NO_VALUE);
        }
      }
      return byteResult.build();
    };
  }

  @Override
  public Map<K, Option<V>> getOrLoad(Collection<K> keys) {
    Set<WrappedBytes> serialized = serialize(keys);
    Map<WrappedBytes, WrappedBytes> loaded = delegate.getOrLoad(serialized);
    ImmutableMap.Builder<K, Option<V>> result = ImmutableMap.builderWithExpectedSize(keys.size());
    for (K key : keys) {
      WrappedBytes serializedKey = keySerializer.toBytes(key);
      WrappedBytes serializedValue = loaded.get(serializedKey);
      if (serializedValue == null) {
        throw new NullPointerException("Missing value for key: " + key);
      } else if (serializedValue.equals(LoadingByteCache.NO_VALUE)) {
        result.put(key, Option.none());
      } else {
        V value = valueSerializer.fromBytes(serializedValue);
        result.put(key, Option.some(value));
      }
    }
    return result.build();
  }

  @Override
  public Option<V> getOrLoad(K key) {
    WrappedBytes serialized = keySerializer.toBytes(key);
    WrappedBytes loaded = delegate.getOrLoad(serialized);
    if (loaded == null) {
      throw new NullPointerException("Missing value for key: " + key);
    } else if (loaded.equals(LoadingByteCache.NO_VALUE)) {
      return Option.none();
    } else {
      V value = valueSerializer.fromBytes(loaded);
      return Option.some(value);
    }
  }

  @Override
  public void putAll(Map<K, V> newValues) {
    ImmutableMap.Builder<WrappedBytes, WrappedBytes> serialized = ImmutableMap.builderWithExpectedSize(newValues.size());
    for (Map.Entry<K, V> entry : newValues.entrySet()) {
      WrappedBytes serializedKey = keySerializer.toBytes(entry.getKey());
      WrappedBytes serializedValue = valueSerializer.toBytes(entry.getValue());
      serialized.put(serializedKey, serializedValue);
    }
    delegate.putAll(serialized.build());
  }

  @Override
  public void preload(SortedMap<K, V> entries) {
    ImmutableSortedMap.Builder<WrappedBytes, WrappedBytes> serialized = ImmutableSortedMap.naturalOrder();
    for (Map.Entry<K, V> entry : entries.entrySet()) {
      WrappedBytes serializedKey = keySerializer.toBytes(entry.getKey());
      WrappedBytes serializedValue = valueSerializer.toBytes(entry.getValue());
      serialized.put(serializedKey, serializedValue);
    }
    delegate.preload(serialized.build());
  }

  @Override
  public Collection<Map.Entry<K, V>> getAllSorted() {
    return Collections2.transform(delegate.getAllSorted(), entry -> Map.entry(keySerializer.fromBytes(entry.key()), valueSerializer.fromBytes(entry.value())));
  }

  @Override
  public Map<K, Option<V>> getIfPresent(Collection<K> keys, boolean recordRead) {
    Set<WrappedBytes> serialized = serialize(keys);
    Map<WrappedBytes, WrappedBytes> loaded = delegate.getIfPresent(serialized, recordRead);
    ImmutableMap.Builder<K, Option<V>> result = ImmutableMap.builder();
    for (K key : keys) {
      WrappedBytes serializedKey = keySerializer.toBytes(key);
      WrappedBytes serializedValue = loaded.get(serializedKey);
      if (serializedValue == null) {
        continue;
      } else if (serializedValue.equals(LoadingByteCache.NO_VALUE)) {
        result.put(key, Option.none());
      } else {
        V value = valueSerializer.fromBytes(serializedValue);
        result.put(key, Option.some(value));
      }
    }
    return result.build();
  }

  @Override
  public void invalidate(Collection<K> keys) {
    delegate.invalidate(serialize(keys));
  }

  @Override
  public long estimateMemoryUsage() {
    return delegate.estimateMemoryUsage();
  }

  @Override
  public void runMaintenance() {
    delegate.runMaintenance();
  }

  @Override
  public int size() {
    return delegate.size();
  }

  @Override
  public ByteCacheStats getStats() {
    return delegate.getStats();
  }

  private Set<WrappedBytes> serialize(Collection<K> keys) {
    ImmutableSet.Builder<WrappedBytes> result = ImmutableSet.builderWithExpectedSize(keys.size());
    for (K key : keys) {
      result.add(keySerializer.toBytes(key));
    }
    return result.build();
  }
  
}
