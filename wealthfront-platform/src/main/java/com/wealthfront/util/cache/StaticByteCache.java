package com.wealthfront.util.cache;

import java.util.AbstractCollection;
import java.util.BitSet;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.function.Supplier;

import org.jetbrains.annotations.Nullable;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.AbstractIterator;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.kaching.platform.common.Option;
import com.kaching.platform.tinykv.impl.WrappedBytesEntry;
import com.kaching.platform.util.WrappedBytes;
import com.wealthfront.util.collections.AtomicBitSet;
import com.wealthfront.util.collections.WrappedBytesList;

public class StaticByteCache implements ReadByteCache {
  
  private static final int NOT_FOUND = -1;
  
  private final ByteCacheStats stats;
  private final WrappedBytesList keys;
  private final WrappedBytesList values;
  private final ProbabilisticFrequencyTracker frequencyTracker;
  private final AtomicBitSet invalidated;
  
  public StaticByteCache(Supplier<Random> randomSupplier, ByteCacheStats stats, Collection<WrappedBytesEntry> sortedEntries) {
    List<WrappedBytesEntry> entryList = ImmutableList.copyOf(sortedEntries);
    this.stats = stats;
    this.keys = WrappedBytesList.from(Iterables.transform(entryList, WrappedBytesEntry::key));
    this.values = WrappedBytesList.from(Iterables.transform(entryList, WrappedBytesEntry::value));
    
    byte[] frequencies = new byte[entryList.size()];
    WrappedBytes lastKey = null;
    for (int i = 0; i < frequencies.length; i++) {
      WrappedBytesEntry entry = entryList.get(i);
      frequencies[i] = entry.frequency();
      if (i > 0 && lastKey.compareTo(entry.key()) >= 0) {
        throw new IllegalArgumentException("Entries must be unique and sorted by key");
      }
      lastKey = entry.key();
    }
    this.frequencyTracker = new ProbabilisticFrequencyTracker(randomSupplier, frequencies);
    this.invalidated = new AtomicBitSet(entryList.size());
  }

  @Override
  public Collection<WrappedBytesEntry> getAllSorted() {
    return getMostFrequentlyUsed(Integer.MAX_VALUE).entries();
  }

  @Override
  public int size() {
    return keys.size();
  }

  @Override
  public Option<WrappedBytes> getIfPresent(WrappedBytes key, boolean recordRead) {
    int index = getIndex(key);
    if (index == NOT_FOUND) {
      return Option.none();
    }
    if (invalidated.getVolatile(index)) {
      return Option.none();
    }
    if (recordRead) {
      frequencyTracker.notifyRead(index);
    }
    return Option.some(values.get(index));
  }
  
  @Override
  public Map<WrappedBytes, WrappedBytes> getIfPresent(Collection<WrappedBytes> keys, boolean recordRead) {
    ImmutableMap.Builder<WrappedBytes, WrappedBytes> result = ImmutableMap.builder();
    for (WrappedBytes key : keys) {
      Option<WrappedBytes> value = getIfPresent(key, recordRead);
      if (value.isDefined()) {
        result.put(key, value.getOrThrow());
      }
    }
    return result.buildOrThrow();
  }

  @Override
  public void invalidate(Collection<WrappedBytes> keys) {
    int num = 0;
    for (WrappedBytes key : keys) {
      int index = getIndex(key);
      if (index == NOT_FOUND) {
        continue;
      }
      if (!invalidated.getAndSetVolatile(index, true)) {
        num++;
      }
      frequencyTracker.zeroOut(index);
    }
    this.stats.addNumInvalidations(num);
  }

  @Override
  public long estimateMemoryUsage() {
    return keys.estimateMemoryUsage() + values.estimateMemoryUsage() + frequencyTracker.estimateMemoryUsage();
  }

  public record MostFrequentlyUsed(Collection<WrappedBytesEntry> entries, byte percentileFrequency) {}
  
  public MostFrequentlyUsed getMostFrequentlyUsed(int limit) {
    ProbabilisticFrequencyTracker.MostFrequentlyUsed frequency = frequencyTracker.getMostFrequentlyUsed(limit);
    if (frequency.frequencyCopy().length == 0) {
      return new MostFrequentlyUsed(Collections.emptyList(), (byte) 0);
    }
    frequency.mfu().andNot(invalidated.toBitSetVolatile());
    
    int size = frequency.mfu().cardinality();
    Collection<WrappedBytesEntry> collection = new AbstractCollection<>() {
      @Override
      public Iterator<WrappedBytesEntry> iterator() {
        return new EntryIterator(frequency.mfu(), frequency.frequencyCopy());
      }

      @Override
      public int size() {
        return size;
      }
    };
    return new MostFrequentlyUsed(collection, frequency.percentile25Freq());
  }

  private class EntryIterator extends AbstractIterator<WrappedBytesEntry> {

    private final BitSet filter;
    private final byte[] frequencies;
    private int i = 0;

    EntryIterator(BitSet filter, byte[] frequencies) {
      this.frequencies = frequencies;
      this.filter = filter;
    }

    @Nullable
    @Override
    protected WrappedBytesEntry computeNext() {
      while (i < keys.size()) {
        if (filter.get(i)) {
          WrappedBytesEntry entry = new WrappedBytesEntry(keys.get(i), values.get(i), frequencies[i]);
          i++;
          return entry;
        }
        i++;
      }
      return endOfData();
    }

  }

  @VisibleForTesting
  List<WrappedBytes> getAllKeys() {
    return keys;
  }
  
  @VisibleForTesting
  WrappedBytesList getAllValues() {
    return values;
  }
  
  private int getIndex(WrappedBytes key) {
    int index = Collections.binarySearch(keys, key);
    if (index >= 0) {
      return index;
    }
    return NOT_FOUND;
  }
  
}
