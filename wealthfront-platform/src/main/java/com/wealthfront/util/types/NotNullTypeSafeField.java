package com.wealthfront.util.types;

import static com.kaching.util.Preconditions.checkNotNull;

import java.lang.reflect.Field;

import org.apache.commons.lang.ClassUtils;

import com.kaching.platform.common.Strings;
import com.wealthfront.util.objects.DerivedMethods;

class NotNullTypeSafeField<Clazz, Type> implements TypeSafeField<Clazz, Type, Type> {

  @SuppressWarnings("unchecked")
  private static final DerivedMethods<NotNullTypeSafeField<?, ?>> DERIVED_METHODS =
      new DerivedMethods<>((Class<NotNullTypeSafeField<?, ?>>) (Object) NotNullTypeSafeField.class);

  static <Clazz, Type> Field checkField(
      Class<Clazz> clazz, String fieldName, Class<Type> fieldType, boolean isNullable) {
    try {
      Field field = clazz.getDeclaredField(fieldName);
      field.setAccessible(true);
      if (!ClassUtils.isAssignable(field.getType(), fieldType, true)) {
        throw new RuntimeException(Strings.format("for class %s, type of field %s is %s, not %s",
            clazz.getTypeName(), fieldName, field.getType().getTypeName(), fieldType.getTypeName()));
      }
      if (isNullable && field.getType().isPrimitive()) {
        throw new RuntimeException("primitive fields are not nullable");
      }
      return field;
    } catch (NoSuchFieldException e) {
      throw new RuntimeException(e);
    }
  }

  private final Field field;
  private final Class<Clazz> fieldClass;

  NotNullTypeSafeField(Class<Clazz> clazz, String field, Class<Type> fieldType) {
    this.field = checkField(clazz, field, fieldType, false);
    this.fieldClass = clazz;
  }

  @Override
  public String fieldName() {
    return field.getName();
  }

  @Override
  @SuppressWarnings("unchecked")
  public Class<Type> fieldType() {
    return (Class<Type>) field.getType();
  }

  @Override
  public Class<Clazz> getFieldClass() {
    return fieldClass;
  }

  @Override
  @SuppressWarnings("unchecked")
  public Type get(Clazz object) {
    try {
      Type value = (Type) field.get(object);
      checkNotNull(value, "got a null value from a not null field");
      return value;
    } catch (IllegalAccessException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public void set(Clazz object, Type value) {
    checkNotNull(value, "tried to set a not null field to null");
    try {
      field.set(object, value);
    } catch (IllegalAccessException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

  @Override
  public boolean equals(Object obj) {
    return DERIVED_METHODS.equals(this, obj);
  }

  @Override
  public String toString() {
    return DERIVED_METHODS.toString(this);
  }

}
