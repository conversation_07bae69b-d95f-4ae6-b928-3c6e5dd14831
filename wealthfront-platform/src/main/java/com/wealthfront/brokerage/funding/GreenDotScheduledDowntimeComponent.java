package com.wealthfront.brokerage.funding;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.kaching.platform.components.Component;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.tinykv.TinyKvComponent;

@Component(
    dependsOn = TinyKvComponent.class,
    modules = GreenDotScheduledDowntimeComponent.Module.class
)
public class GreenDotScheduledDowntimeComponent {

  public static class Module extends AbstractModule {

    private final Class<? extends ServiceKind> serviceKind;
    private final TinyKvComponent.Options options;

    @Inject
    public Module(Class<? extends ServiceKind> serviceKind, TinyKvComponent.Options options) {
      this.serviceKind = serviceKind;
      this.options = options;
    }

    @Override
    protected void configure() {
      TinyKvComponent.bindStore(binder(), serviceKind, options, new GreenDotScheduledDowntimeStore());
    }

  }

}
