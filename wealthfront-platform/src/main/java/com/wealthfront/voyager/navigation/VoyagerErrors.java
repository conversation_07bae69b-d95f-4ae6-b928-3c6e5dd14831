package com.wealthfront.voyager.navigation;

import static com.google.common.collect.Maps.newHashMap;
import static java.lang.String.format;

import static java.util.Collections.EMPTY_MAP;
import static java.util.Collections.emptyMap;

import java.util.Map;

import com.google.common.collect.ImmutableMap;

public class VoyagerErrors {

  private Map<String, String> errors;

  public VoyagerErrors addError(String key, String clientVisibleMessage, Object... values) {
    if (errors == null) {
      errors = newHashMap();
    }
    String formattedMessage = format(clientVisibleMessage, values);
    if (!errors.containsKey(key)) {
      errors.put(key, formattedMessage);
    }
    return this;
  }

  public VoyagerErrors addVoyagerErrors(VoyagerErrors voyagerErrors) {
    if (voyagerErrors.errors == null) {
      return this;
    }
    this.errors.putAll(voyagerErrors.errors);
    return this;
  }

  public Map<String, String> getErrors() {
    if (errors == null) {
      return emptyMap();
    }
    return ImmutableMap.copyOf(errors);
  }

  public int size() {
    return errors == null ? 0 : errors.size();
  }

  public boolean hasVoyagerErrors() {
    return 0 < size();
  }

  @Override
  public int hashCode() {
    return errors == null ? EMPTY_MAP.hashCode() : errors.hashCode();
  }

  @Override
  public boolean equals(Object that) {
    if (this == that) {
      return true;
    }
    if (!(that instanceof VoyagerErrors)) {
      return false;
    }
    return this.errors == null
      ? ((VoyagerErrors) that).errors == null
      : this.errors.equals(((VoyagerErrors) that).errors);
  }

  @Override
  public String toString() {
    if (errors == null) {
      return "no errors";
    }
    StringBuilder buf = new StringBuilder();
    int num = 1;
    String separator = "";
    for (String error : errors.keySet()) {
      buf.append(separator);
      buf.append(num);
      buf.append(") ");
      buf.append(error);
      buf.append(": ");
      buf.append(errors.get(error));
      num++;
      separator = "\n\n";
    }
    return buf.toString();
  }

  public static boolean checkPrecondition(
    boolean condition, String key, String clientVisibleMessage, VoyagerErrors errors) {
    if (!condition) {
      errors.addError(key, clientVisibleMessage);
    }
    return condition;
  }

}