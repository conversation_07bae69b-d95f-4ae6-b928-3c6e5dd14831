package com.wealthfront.voyager.navigation;

import static com.twolattes.json.Json.string;

import java.lang.reflect.Field;

import org.jetbrains.annotations.NotNull;

import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.common.Strings;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(VoyagerStepId.Converter.class)
@MarshalledBy(VoyagerStepId.JsonType.class)
public class VoyagerStepId extends AbstractIdentifier<String> {

  public VoyagerStepId(@NotNull String stepId) {
    super(validate(stepId));
  }

  private static String validate(String id) {
    if (id.contains("{") || id.contains("}")) {
      throw new IllegalArgumentException(Strings.format("Not all placeholders were replaced in %s.", id));
    }
    return id;
  }

  public static <V extends VoyagerStepArguments> VoyagerStepId of(String stepId) {
    return new VoyagerStepId(stepId);
  }

  public static <V extends VoyagerStepArguments> VoyagerStepId of(VoyagerRoute route, V arguments) {
    String stepPath = route.getStepPath();
    for (Field field : arguments.getClass().getDeclaredFields()) {
      field.setAccessible(true);
      try {
        Object value = field.get(arguments);
        if (value != null) {
          String placeholder = Strings.format("{%s}", field.getName());
          if (stepPath.contains(placeholder)) {
            stepPath = stepPath.replace(placeholder, value.toString());
          } else {
            throw new IllegalArgumentException(Strings.format("Argument %s not found in %s.", placeholder, route.getStepPath()));
          }
        }
      } catch (IllegalAccessException e) {
        throw new IllegalStateException("Failed to access field value", e);
      }
    }

    return VoyagerStepId.of(stepPath);
  }

  public static class JsonType extends NullSafeType<VoyagerStepId, Json.String> {

    @Override
    protected Json.String nullSafeMarshall(VoyagerStepId voyagerStepId) {
      return string(voyagerStepId.toString());
    }

    @Override
    protected VoyagerStepId nullSafeUnmarshall(Json.String string) {
      return new VoyagerStepId(string.getString());
    }

  }

  public static class Converter extends NullHandlingConverter<VoyagerStepId> {

    @Override
    protected VoyagerStepId fromNonNullableString(String representation) {
      return new VoyagerStepId(representation);
    }

    @Override
    protected String nonNullableToString(VoyagerStepId value) {
      return value.getId();
    }

  }

}