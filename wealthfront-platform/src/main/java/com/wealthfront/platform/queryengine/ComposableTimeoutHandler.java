package com.wealthfront.platform.queryengine;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.kaching.platform.common.Option;
import com.kaching.platform.queryengine.Query;
import com.wealthfront.platform.queryengine.InMemoryOnlineQueryTimeoutHandler.PagingSchedule;
import com.wealthfront.util.objects.Builder;

public interface ComposableTimeoutHandler {

  // is this handler always applicable?
  boolean isWildcard();

  record Result(
      Option<PagingSchedule> pagingSchedule,
      boolean shouldSkipRemainingAlerting
  ) {

    Builder<Result> copy() {
      return new Builder<>(Result.class).from(this);
    }

    Result merge(Result other) {
      Option<PagingSchedule> mergedPagingSchedule;
      if (pagingSchedule.isEmpty() && other.pagingSchedule.isEmpty()) {
        mergedPagingSchedule = Option.none();
      } else if (pagingSchedule.isDefined() && other.pagingSchedule.isEmpty()) {
        mergedPagingSchedule = pagingSchedule;
      } else if (pagingSchedule.isEmpty() && other.pagingSchedule.isDefined()) {
        mergedPagingSchedule = other.pagingSchedule;
      } else {
        mergedPagingSchedule = Option.some(pagingSchedule.getOrThrow().max(other.pagingSchedule.getOrThrow()));
      }
      return new Result(
          mergedPagingSchedule,
          shouldSkipRemainingAlerting || other.shouldSkipRemainingAlerting
      );
    }

  }

  // None -> handler is inapplicable
  Option<Result> handle(Class<? extends Query<?>> queryClazz);

  default ComposableTimeoutHandler forQuery(Class<? extends Query<?>> query) {
    return forQueries(List.of(query));
  }

  default ComposableTimeoutHandler forQueries(Collection<Class<? extends Query<?>>> queries) {
    Set<Class<? extends Query<?>>> querySet = Set.copyOf(queries);
    ComposableTimeoutHandler outer = this;
    return new ComposableTimeoutHandler() {
      @Override
      public boolean isWildcard() {
        return false;
      }

      @Override
      public Option<Result> handle(Class<? extends Query<?>> queryClazz) {
        if (!querySet.contains(queryClazz)) {
          return Option.none();
        }
        return outer.handle(queryClazz);
      }
    };
  }

  default ComposableTimeoutHandler withPagingSchedule(PagingSchedule pagingSchedule) {
    ComposableTimeoutHandler outer = this;
    return new ComposableTimeoutHandler() {
      @Override
      public boolean isWildcard() {
        return outer.isWildcard();
      }

      @Override
      public Option<Result> handle(Class<? extends Query<?>> queryClazz) {
        return outer.handle(queryClazz).transform(result ->
            result.copy()
                .update(Result::pagingSchedule).with(maybePrevious -> maybePrevious.transform(prev -> pagingSchedule))
                .build()
        );
      }
    };
  }

  default ComposableTimeoutHandler shouldSkipRemainingAlertingIfApplicable(boolean shouldSkipRemainingAlerting) {
    ComposableTimeoutHandler outer = this;
    return new ComposableTimeoutHandler() {
      @Override
      public boolean isWildcard() {
        return outer.isWildcard();
      }

      @Override
      public Option<Result> handle(Class<? extends Query<?>> queryClazz) {
        return outer.handle(queryClazz).transform(result ->
            result.copy()
                .set(Result::shouldSkipRemainingAlerting).to(shouldSkipRemainingAlerting)
                .build()
        );
      }
    };
  }

  default ComposableTimeoutHandler shouldSkipRemainingAlertingIfApplicable() {
    return shouldSkipRemainingAlertingIfApplicable(true);
  }

  default InMemoryOnlineQueryTimeoutHandler toInMemoryOnlineQueryTimeoutHandler() {
    if (!isWildcard()) {
      throw new IllegalStateException("a timeout handler must handle possible all timeouts");
    }
    return new InMemoryOnlineQueryTimeoutHandler() {
      @Override
      public synchronized Option<PagingSchedule> handleQueryTimeout(Class<? extends Query<?>> queryClazz) {
        return ComposableTimeoutHandler.this.handle(queryClazz).flatTransform(Result::pagingSchedule);
      }
    };
  }

}
