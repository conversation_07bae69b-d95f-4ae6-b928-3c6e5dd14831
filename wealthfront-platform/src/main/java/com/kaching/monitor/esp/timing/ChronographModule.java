package com.kaching.monitor.esp.timing;

import static com.kaching.platform.queryengine.QueryEngineModule.registerQueries;

import org.collectd.mx.MBeanCollector;

import com.google.inject.AbstractModule;
import com.google.inject.matcher.Matchers;
import com.google.inject.multibindings.Multibinder;
import com.kaching.platform.jcollectd.JCollectd;
import com.kaching.platform.timing.Timed;
import com.kaching.platform.timing.TimedInterceptor;

public class ChronographModule extends AbstractModule {

  @SuppressWarnings("unchecked")
  @Override
  protected void configure() {
    registerQueries(binder(), GetTimingInformation.class);
    install(new InternalChronographModule());
  }

  static class InternalChronographModule extends AbstractModule {

    @Override
    protected void configure() {
      TimedInterceptor ti = new TimedInterceptor();
      requestInjection(ti);
      bindInterceptor(Matchers.any(), Matchers.annotatedWith(Timed.class), ti);
      Multibinder.newSetBinder(
          binder(), MBeanCollector.class, JCollectd.class).addBinding().to(CollectdChronographPublisher.class);
    }

  }

}
