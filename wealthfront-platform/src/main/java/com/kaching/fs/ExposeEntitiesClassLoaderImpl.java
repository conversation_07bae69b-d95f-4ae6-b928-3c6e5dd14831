package com.kaching.fs;

import static com.kaching.api.EntityType.TWO_LATTES;

import java.io.IOException;
import java.nio.file.FileSystem;
import java.util.Collections;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.google.inject.Inject;
import com.kaching.api.EntityType;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;

public class ExposeEntitiesClassLoaderImpl implements ExposeEntitiesClassLoader {

  private static final String AUTO_TYPES_PACKAGE = "com.wealthfront.auto.types";

  @Inject FileSystemClassLoader fileSystemClassLoader;

  @Override
  public Set<Class<?>> getExposedTwolattesFrontendEntities(
      FileSystem fs,
      String buildDirectory)
      throws IOException {
    return getExposedEntities(fs, buildDirectory, Set.of(ExposeTo.FRONTEND), Set.of(TWO_LATTES));
  }

  @Override
  public Set<Class<?>> getExposedEntities(FileSystem fs, String buildDirectory) throws IOException {
    return getExposedEntities(fs, buildDirectory, Set.of(ExposeTo.values()), Set.of(EntityType.values()));
  }

  @Override
  public Set<Class<?>> getExposedEntities(
      FileSystem fs,
      String buildDirectory,
      Set<ExposeTo> exposeTo,
      Set<EntityType> entityTypes)
      throws IOException {
    return fileSystemClassLoader.walkDirectoryForClasses(fs, buildDirectory, (clazz) -> {
      ExposeType exposeType = clazz.getAnnotation(ExposeType.class);
      if (clazz.getPackageName().startsWith(AUTO_TYPES_PACKAGE)) {
        return false;
      }

      if (exposeType == null) {
        return false;
      }

      Set<ExposeTo> exposeTypeValues = Set.of(exposeType.value());
      if (Collections.disjoint(exposeTo, exposeTypeValues)) {
        return false;
      }

      if (entityTypes.contains(TWO_LATTES) && clazz.getAnnotation(Entity.class) != null) {
        return true;
      } else if (entityTypes.contains(EntityType.JACKSON) && clazz.getAnnotation(JsonClassDescription.class) != null) {
        return true;
      }

      return clazz.isEnum();
    });
  }
} 