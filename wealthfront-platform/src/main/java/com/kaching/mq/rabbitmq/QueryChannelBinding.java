package com.kaching.mq.rabbitmq;

import static java.lang.String.format;

import java.util.Map;

import com.google.common.collect.ImmutableMap;
import com.kaching.platform.queryengine.Query;

/**
 * Binds a direct exchange to a queue named "query.&lt;query.getName()&gt;" for a
 * {@link Query} class. The routing key for a {@link Query} instance is the
 * same as the exchange and queue names.
 */
public class QueryChannelBinding extends ChannelBinding {

  public QueryChannelBinding(Class<? extends Query<?>> query, Map<String, Object> queueArgs) {
    this(query.getName(), queueArgs);
  }

  public QueryChannelBinding(String queryClassName, Map<String, Object> queueArgs) {
    super(format("query.%s", queryClassName),
        "direct",
        format("query.%s", queryClassName),
        format("query.%s", queryClassName),
        queueArgs);
  }

  public static QueryChannelBinding of(Class<? extends Query<?>> query, Map<String, Object> queueArgs) {
    return new QueryChannelBinding(query, queueArgs);
  }

  public static QueryChannelBinding of(Class<? extends Query<?>> query) {
    return new QueryChannelBinding(query, ImmutableMap.of());
  }

  public static QueryChannelBinding of(String queryClassName, Map<String, Object> queueArgs) {
    return new QueryChannelBinding(queryClassName, queueArgs);
  }

  public static QueryChannelBinding of(String queryClassName) {
    return new QueryChannelBinding(queryClassName, ImmutableMap.of());
  }

}
