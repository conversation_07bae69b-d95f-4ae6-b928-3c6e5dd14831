package com.kaching.mq.rabbitmq;

import static com.google.common.collect.Iterables.addAll;
import static com.google.common.collect.Iterables.toArray;
import static com.google.common.collect.Iterables.transform;
import static com.google.common.collect.Lists.newArrayList;
import static com.google.inject.multibindings.Multibinder.newSetBinder;
import static com.kaching.platform.common.logging.Log.getLog;
import static java.lang.String.format;
import static java.util.Arrays.asList;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import com.google.common.base.Function;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.inject.AbstractModule;
import com.google.inject.Binder;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.multibindings.Multibinder;
import com.google.inject.name.Named;
import com.kaching.mq.Consumer;
import com.kaching.mq.MessageQueue;
import com.kaching.mq.Producer;
import com.kaching.mq.QueryConsumerEngine;
import com.kaching.mq.QueryPublisher;
import com.kaching.platform.bus.impl.FakeInboxSignalerImpl;
import com.kaching.platform.bus.impl.FakeOutboxSignalerImpl;
import com.kaching.platform.bus.impl.InboxSignaler;
import com.kaching.platform.bus.impl.OutboxSignaler;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.NamedThreadFactory;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.util.Resettable;
import com.rabbitmq.client.Address;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.ConnectionParameters;

public class RabbitMQModule extends AbstractModule {

  private static final Log log = getLog(RabbitMQModule.class);
  final List<Class<? extends Query<?>>> producerQueries = newArrayList();
  final List<String> producerQueryNames = newArrayList();
  final List<QueryConfiguration> producerQueryConfigurations = newArrayList();
  final List<Class<? extends Query<?>>> consumerQueries = newArrayList();
  final List<QueryConfiguration> consumerQueryConfigurations = new ArrayList<>();
  final Set<ChannelBinding> producerChannelBindings = new LinkedHashSet<>();
  final Set<ChannelBinding> consumerChannelBindings = new LinkedHashSet<>();
  boolean isFake = false;
  int numConsumerThreads = 4;
  int prefetch = 10;

  public static class Builder {

    RabbitMQModule module = new RabbitMQModule();

    public RabbitMQModule build() {
      return module;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public Builder withProducerQueries(Class... queries) {
      checkBounds(queries);
      addAll(
          module.producerQueries,
          Lists.<Class<? extends Query<?>>>newArrayList(queries));
      return this;
    }

    public Builder withProducerQueryNames(String... queryNames) {
      addAll(
          module.producerQueryNames,
          asList(queryNames));
      return this;
    }

    public Builder withProducerQueryConfigurations(QueryConfiguration... queryConfigurations) {
      addAll(
          module.producerQueryConfigurations,
          asList(queryConfigurations)
      );
      return this;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public Builder withConsumerQueries(Class... queries) {
      checkBounds(queries);
      addAll(
          module.consumerQueries,
          Lists.<Class<? extends Query<?>>>newArrayList(queries));
      return this;
    }

    public Builder withConsumerQueryConfigurations(QueryConfiguration... queryConfigurations) {
      addAll(
          module.consumerQueryConfigurations,
          asList(queryConfigurations)
      );
      return this;
    }

    public Builder withProducerChannelBindings(ChannelBinding... bindings) {
      module.producerChannelBindings.addAll(asList(bindings));
      return this;
    }

    public Builder withConsumerChannelBindings(ChannelBinding... bindings) {
      module.consumerChannelBindings.addAll(asList(bindings));
      return this;
    }

    public Builder withChannelBindings(ChannelBinding... bindings) {
      return this
          .withProducerChannelBindings(bindings)
          .withConsumerChannelBindings(bindings);
    }

    private void checkBounds(Class<?>... queries) {
      for (Class<?> klass : queries) {
        if (!Query.class.isAssignableFrom(klass)) {
          throw new IllegalArgumentException(format("%s must be a query", klass));
        }
      }
    }

    public Builder withConsumerThreads(int numConsumerThreads) {
      module.numConsumerThreads = numConsumerThreads;
      return this;
    }

    public Builder withPrefetch(int prefetch) {
      module.prefetch = prefetch;
      return this;
    }

    public Builder asFake() {
      module.isFake = true;
      return this;
    }
  }

  public static Builder builder() {
    return new Builder();
  }

  @Override
  protected void configure() {
    if (isFake) {
      log.warn("RabbitMQModule running with fake message queue!");
      bind(OutboxSignaler.class).to(FakeOutboxSignalerImpl.class);
      bind(InboxSignaler.class).to(FakeInboxSignalerImpl.class);
    }

    bind(MessageQueue.class).to(RabbitQueue.class);
    bind(QueryPublisher.class).to(RabbitPublisher.class);

    bindQueryChannelBindings(newSetBinder(binder(), ChannelBinding.class, Producer.class), producerQueries);
    bindQueryChannelBindingsWithStrings(newSetBinder(binder(), ChannelBinding.class, Producer.class), producerQueryNames);
    bindQueryChannelBindingsWithConfigurations(newSetBinder(binder(), ChannelBinding.class, Producer.class), producerQueryConfigurations);
    bindQueryChannelBindings(newSetBinder(binder(), ChannelBinding.class, Consumer.class), consumerQueries);
    bindQueryChannelBindingsWithConfigurations(newSetBinder(binder(), ChannelBinding.class, Consumer.class), consumerQueryConfigurations);
    bindChannelBindings(newSetBinder(binder(), ChannelBinding.class, Producer.class), producerChannelBindings);
    bindChannelBindings(newSetBinder(binder(), ChannelBinding.class, Consumer.class), consumerChannelBindings);
  }

  public static void addProducer(Binder binder, Class<? extends Query<?>> producer) {
    addProducer(binder, QueryChannelBinding.of(producer));
  }

  public static void addConsumer(Binder binder, Class<? extends Query<?>> consumer) {
    addConsumer(binder, QueryChannelBinding.of(consumer));
  }

  public static void addProducer(Binder binder, ChannelBinding producer) {
    RabbitMQModule.bindChannelBindings(
        newSetBinder(binder, ChannelBinding.class, Producer.class), Collections.singleton(producer));
  }

  public static void addConsumer(Binder binder, ChannelBinding consumer) {
    RabbitMQModule.bindChannelBindings(
        newSetBinder(binder, ChannelBinding.class, Consumer.class), Collections.singleton(consumer));
  }

  public static void addProducer(Binder binder, Provider<ChannelBinding> producer) {
    RabbitMQModule.bindChannelBindingProviders(
        newSetBinder(binder, ChannelBinding.class, Producer.class), Collections.singleton(producer));
  }

  public static void addConsumer(Binder binder, Provider<ChannelBinding> consumer) {
    RabbitMQModule.bindChannelBindingProviders(
        newSetBinder(binder, ChannelBinding.class, Consumer.class), Collections.singleton(consumer));
  }

  private static void bindQueryChannelBindings(Multibinder<ChannelBinding> bindings, Iterable<Class<? extends Query<?>>> queries) {
    for (Class<? extends Query<?>> query : queries) {
      bindings.addBinding().toInstance(QueryChannelBinding.of(query));
    }
  }

  private static void bindQueryChannelBindingsWithStrings(Multibinder<ChannelBinding> bindings, Iterable<String> queryNames) {
    for (String queryName : queryNames) {
      bindings.addBinding().toInstance(QueryChannelBinding.of(queryName));
    }
  }

  private static void bindQueryChannelBindingsWithConfigurations(Multibinder<ChannelBinding> bindings, Iterable<QueryConfiguration> queryConfigurations) {
    for (QueryConfiguration queryConfiguration : queryConfigurations) {
      bindings.addBinding().toInstance(QueryChannelBinding.of(
          queryConfiguration.getQuery(),
          queryConfiguration.getQueueArgs()));
    }
  }

  private static void bindChannelBindings(Multibinder<ChannelBinding> bindings, Iterable<ChannelBinding> channelBindings) {
    for (ChannelBinding channelBinding : channelBindings) {
      bindings.addBinding().toInstance(channelBinding);
    }
  }

  private static void bindChannelBindingProviders(Multibinder<ChannelBinding> bindings, Iterable<Provider<ChannelBinding>> providers) {
    for (Provider<ChannelBinding> provider : providers) {
      bindings.addBinding().toProvider(provider);
    }
  }

  @Provides
  @Singleton
  Resettable<RabbitContext> context(final Provider<RabbitContext> delegate, StackTraceMonitor stm) {
    return new Resettable<RabbitContext>() {
      @Override
      protected RabbitContext create() {
        return delegate.get();
      }

      @Override
      protected void resetFailed(Exception e) {
        log.error(e, "Error creating RabbitContext");
        stm.add(e);

        try {
          Thread.sleep(5000);
        } catch (InterruptedException e1) {
          throw new RuntimeException(e1);
        }
      }

      @Override
      protected int numRetries() {
        return 20;
      }
    };
  }

  @Provides
  ConnectionParameters parameters() {
    ConnectionParameters parameters = new ConnectionParameters();
    parameters.setRequestedHeartbeat(30);
    return parameters;
  }

  @Provides
  Connection connection(@Named("rabbitmq.hosts") String hosts, ConnectionParameters params) throws IOException {
    try {
      log.info("Connecting to RabbitMQ on hosts %s", hosts);
      Connection connection = isFake
          ? new FakeConnection()
          : new ConnectionFactory(params).newConnection(toArray(transform(
          Splitter.on(',').split(hosts),
          new Function<String, Address>() {
            @Override
            public Address apply(String from) {
              return new Address(from);
            }
          }), Address.class));
      log.info("Connected to RabbitMQ on hosts %s", hosts);
      return connection;
    } catch (IOException e) {
      log.error(e, "Failed to connect to RabbitMQ on hosts %s", hosts);
      throw e;
    }
  }

  @Provides
  @Consumer
  @Singleton
  ThreadPoolExecutor executor() {
    return new ThreadPoolExecutor(
        numConsumerThreads,
        numConsumerThreads,
        1,
        TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(),
        new NamedThreadFactory(QueryConsumerEngine.class.getSimpleName(), true),
        new ThreadPoolExecutor.AbortPolicy());
  }

  @Provides
  @Singleton
  @Named("prefetch")
  AtomicInteger prefetch() {
    return new AtomicInteger(prefetch);
  }

  /**
   * This is used to indicate whether the MQ consumers are shutting down. If true, then consumers should shut down
   * once there are no more items in the queue.
   */
  @Provides
  @Singleton
  @Named("shutting-down")
  AtomicBoolean shuttingDown() {
    return new AtomicBoolean(false);
  }

}
