package com.kaching.entities;

import static com.kaching.platform.common.Strings.format;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.google.common.base.Preconditions;
import com.kaching.platform.common.Option;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.kaching.util.functional.Either;
import com.twolattes.json.Json;
import com.twolattes.json.JsonVisitor;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(CleanPrice.Converter.class)
@MarshalledBy(CleanPrice.JsonType.class)
public class CleanPrice extends AbstractNumber<CleanPrice> implements EquityPriceOrCleanPrice {

  public static final CleanPrice ZERO_PRICE = cleanPrice(BigDecimal.ZERO);
  public static final CleanPrice ONE_PRICE = cleanPrice(BigDecimal.ONE);
  public static final String CLEAN_PRICE_PREFIX = "cp:";

  protected CleanPrice(BigDecimal cleanPrice) {
    super(cleanPrice);
  }

  private CleanPrice(String cleanPrice) {
    this(new BigDecimal(cleanPrice));
  }

  @Override
  protected CleanPrice createNewInstance(BigDecimal cleanPrice) {
    return cleanPrice(cleanPrice);
  }

  public static CleanPrice cleanPrice(BigDecimal cleanPrice) {
    return cleanPrice == null ? null : new CleanPrice(cleanPrice);
  }

  public static CleanPrice cleanPrice(String cleanPrice) {
    return cleanPrice(new BigDecimal(cleanPrice));
  }

  public static CleanPrice cleanPrice(long cleanPrice) {
    return cleanPrice(BigDecimal.valueOf(cleanPrice));
  }

  public static CleanPrice cleanPrice(double cleanPrice) {
    Preconditions.checkArgument(!Double.isInfinite(cleanPrice), "infinite");
    Preconditions.checkArgument(!Double.isNaN(cleanPrice), "NaN");
    return cleanPrice(BigDecimal.valueOf(cleanPrice));
  }

  public static CleanPrice fromCleanPercentOfPar(CleanPercentOfPar cleanPercentOfPar) {
    return cleanPercentOfPar.toCleanPrice();
  }

  public CleanPercentOfPar toCleanPercentOfPar() {
    return CleanPercentOfPar.fromCleanPrice(this);
  }

  public CleanPrice roundToCents() {
    return this.setScale(2, RoundingMode.HALF_EVEN);
  }

  public CleanPrice roundToMils() {
    return this.setScale(4, RoundingMode.HALF_EVEN);
  }

  public boolean isLessThan(CleanPrice other) {
    return super.isLessThan(other);
  }

  public boolean isLessThanOrEqualTo(CleanPrice other) {
    return super.isLessThanOrEqualTo(other);
  }

  public boolean isGreaterThan(CleanPrice other) {
    return super.isGreaterThan(other);
  }

  public boolean isGreaterThanOrEqualTo(CleanPrice other) {
    return super.isGreaterThanOrEqualTo(other);
  }

  @Override
  public EquityPriceOrCleanPercentOfPar toEquityPriceOrCleanPercentOfPar() {
    return toCleanPercentOfPar();
  }

  @Override
  public Money multiply(Quantity quantity) {
    return Money.money(this.value.multiply(quantity.toBigDecimal()));
  }

  @Override
  public Option<Price> getMaybeEquityPrice() {
    return Option.none();
  }

  @Override
  public Option<CleanPrice> getMaybeCleanPrice() {
    return Option.some(this);
  }

  public Price add(AccruedInterest accruedInterest) {
    return Price.price(this.value.add(accruedInterest.value));
  }

  @Override
  public <T> T visit(Visitor<T> visitor) {
    return visitor.caseCleanPrice(this);
  }

  @Override
  public Either<Price, CleanPrice> getEitherPriceOrCleanPrice() {
    return Either.right(this);
  }

  public static class Converter extends NullHandlingConverter<CleanPrice> {

    protected CleanPrice fromNonNullableString(String representation) {
      return new JsonType().unmarshall(Json.string(representation));
    }

    protected String nonNullableToString(CleanPrice value) {
      return CLEAN_PRICE_PREFIX + value.toBigDecimal().toPlainString();
    }

  }

  public static class NumberConverter extends NullHandlingConverter<CleanPrice> {

    protected CleanPrice fromNonNullableString(String representation) {
      return cleanPrice(representation);
    }

    protected String nonNullableToString(CleanPrice value) {
      return value.toBigDecimal().toPlainString();
    }

  }

  public static class JsonType extends NullSafeType<CleanPrice, Json.Value> {

    protected Json.Value nullSafeMarshall(CleanPrice cleanPrice) {
      return Json.string(CLEAN_PRICE_PREFIX + cleanPrice.toBigDecimal().toPlainString());
    }

    protected CleanPrice nullSafeUnmarshall(Json.Value value) {
      return value.visit(new JsonVisitor.Default<Option<CleanPrice>>(Option.none()) {
        @Override
        public Option<CleanPrice> caseNumber(Json.Number number) {
          return Option.some(cleanPrice(number.getNumber()));
        }

        @Override
        public Option<CleanPrice> caseString(Json.String string) {
          return Option.some(string.getString().startsWith(CLEAN_PRICE_PREFIX) ?
              cleanPrice(string.getString().replaceFirst(CLEAN_PRICE_PREFIX, "")) :
              cleanPrice(string.getString()));
        }
      }).getOrThrow(format("invalid %s representation: %s", CleanPrice.class.getSimpleName(), value));
    }

  }

}
