package com.kaching.entities;

import static com.kaching.platform.common.Strings.format;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.google.common.base.Preconditions;
import com.kaching.platform.common.Option;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.kaching.util.functional.Either;
import com.twolattes.json.Json;
import com.twolattes.json.JsonVisitor;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(CleanPercentOfPar.Converter.class)
@MarshalledBy(CleanPercentOfPar.JsonType.class)
public class CleanPercentOfPar extends AbstractNumber<CleanPercentOfPar> implements EquityPriceOrCleanPercentOfPar {

  public static final String CLEAN_PERCENT_OF_PAR_PREFIX = "cpp:";
  private static final BigDecimal HUNDRED_PAR_VALUE = new BigDecimal(100);

  protected CleanPercentOfPar(BigDecimal cleanPercentOfPar) {
    super(cleanPercentOfPar);
  }

  @Override
  protected CleanPercentOfPar createNewInstance(BigDecimal cleanPercentOfPar) {
    return new CleanPercentOfPar(cleanPercentOfPar);
  }

  public static CleanPercentOfPar cleanPercentOfPar(BigDecimal cleanPercentOfPar) {
    return new CleanPercentOfPar(cleanPercentOfPar);
  }

  public static CleanPercentOfPar cleanPercentOfPar(String cleanPercentOfPar) {
    return new CleanPercentOfPar(new BigDecimal(cleanPercentOfPar));
  }

  public static CleanPercentOfPar cleanPercentOfPar(double cleanPercentOfPar) {
    Preconditions.checkArgument(!Double.isInfinite(cleanPercentOfPar), "infinite");
    Preconditions.checkArgument(!Double.isNaN(cleanPercentOfPar), "NaN");
    return cleanPercentOfPar(BigDecimal.valueOf(cleanPercentOfPar));
  }

  public static CleanPercentOfPar fromCleanPrice(CleanPrice cleanPrice) {
    return cleanPrice == null ? null : new CleanPercentOfPar(cleanPrice.toBigDecimal().multiply(HUNDRED_PAR_VALUE));
  }

  public CleanPrice toCleanPrice() {
    int newScale = this.toBigDecimal().scale() + 2;
    return CleanPrice.cleanPrice(this.toBigDecimal().divide(HUNDRED_PAR_VALUE, newScale, RoundingMode.HALF_UP));
  }

  @Deprecated
  public static CleanPercentOfPar fromPrice(Price price) {
    return price == null ? null : new CleanPercentOfPar(price.toBigDecimal().multiply(HUNDRED_PAR_VALUE));
  }

  @Deprecated
  public Price toPrice() {
    int newScale = this.toBigDecimal().scale() + 2;
    return Price.price(this.toBigDecimal().divide(HUNDRED_PAR_VALUE, newScale, RoundingMode.HALF_UP));
  }

  public CleanPercentOfPar roundToCents() {
    return this.setScale(2, RoundingMode.HALF_EVEN);
  }

  @Override
  public EquityPriceOrCleanPrice toEquityPriceOrCleanPrice() {
    return toCleanPrice();
  }

  @Override
  public Money multiply(Quantity quantity) {
    return toCleanPrice().multiply(quantity);
  }

  @Override
  public <T> T visit(EquityPriceOrCleanPercentOfPar.Visitor<T> visitor) {
    return visitor.caseCleanPercentOfPar(this);
  }

  @Override
  public Option<Price> getMaybeEquityPrice() {
    return Option.none();
  }

  @Override
  public Option<CleanPercentOfPar> getMaybeCleanPercentOfPar() {
    return Option.some(this);
  }

  @Override
  public Either<Price, CleanPercentOfPar> getEitherPriceOrCleanPercentOfPar() {
    return Either.right(this);
  }

  public static class Converter extends NullHandlingConverter<CleanPercentOfPar> {

    protected CleanPercentOfPar fromNonNullableString(String representation) {
      return new JsonType().unmarshall(Json.string(representation));
    }

    protected String nonNullableToString(CleanPercentOfPar value) {
      return CLEAN_PERCENT_OF_PAR_PREFIX + value.toBigDecimal().toPlainString();
    }

  }

  public static class JsonType extends NullSafeType<CleanPercentOfPar, Json.Value> {

    protected Json.Value nullSafeMarshall(CleanPercentOfPar cleanPercentOfPar) {
      return Json.string(CLEAN_PERCENT_OF_PAR_PREFIX + cleanPercentOfPar.toBigDecimal().toPlainString());
    }

    protected CleanPercentOfPar nullSafeUnmarshall(Json.Value value) {
      return value.visit(new JsonVisitor.Default<Option<CleanPercentOfPar>>(Option.none()) {
        @Override
        public Option<CleanPercentOfPar> caseNumber(Json.Number number) {
          return Option.some(cleanPercentOfPar(number.getNumber()));
        }

        @Override
        public Option<CleanPercentOfPar> caseString(Json.String string) {
          return Option.some(string.getString().startsWith(CLEAN_PERCENT_OF_PAR_PREFIX) ?
              CleanPercentOfPar.cleanPercentOfPar(string.getString().replaceFirst(CLEAN_PERCENT_OF_PAR_PREFIX, "")) :
              CleanPercentOfPar.cleanPercentOfPar(string.getString()));
        }
      }).getOrThrow(format("invalid %s representation: %s", CleanPercentOfPar.class.getSimpleName(), value));
    }
  }

}
