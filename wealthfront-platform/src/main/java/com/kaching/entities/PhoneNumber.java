package com.kaching.entities;

import static com.twolattes.json.Json.string;
import static java.lang.String.format;

import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@MarshalledBy(PhoneNumber.JsonType.class)
public class PhoneNumber extends AbstractIdentifier<String> {

  private static final long serialVersionUID = 1L;

  private static final String PHONE_PATTERN = "^\\d{10}$";
  /*
    North American Numbering Plan: https://nationalnanpa.com/about_us/index.html
   */
  private static final String NANP_PHONE_PATTERN = "^[2-9]\\d{2}[2-9]\\d{6}$";

  public PhoneNumber(String number) {
    super(normalize(number));
  }

  public static PhoneNumber of(String number) {
    return new PhoneNumber(number);
  }

  private static String normalize(String possibleNumber) {
    return possibleNumber.toLowerCase()
        .replace('a', '2')
        .replace('b', '2')
        .replace('c', '2')
        .replace('d', '3')
        .replace('e', '3')
        .replace('f', '3')
        .replace('g', '4')
        .replace('h', '4')
        .replace('i', '4')
        .replace('j', '5')
        .replace('k', '5')
        .replace('l', '5')
        .replace('m', '6')
        .replace('n', '6')
        .replace('o', '6')
        .replace('p', '7')
        .replace('q', '7')
        .replace('r', '7')
        .replace('s', '7')
        .replace('t', '8')
        .replace('u', '8')
        .replace('v', '8')
        .replace('w', '9')
        .replace('x', '9')
        .replace('y', '9')
        .replace('z', '9')
        .replaceAll("[^\\d]", "");
  }

  public String normalize() {
    return normalize(getId());
  }

  public String getLastFourDigits() {
    String normalized = normalize();
    return normalized.substring(normalized.length() - 4);
  }

  public static boolean validate(String possibleNumber) {
    return normalize(possibleNumber).matches(PHONE_PATTERN);
  }

  public static boolean validateNANP(String possibleNumber) {
    return normalize(possibleNumber).matches(NANP_PHONE_PATTERN);
  }

  public boolean validate() {
    return validate(this.normalize());
  }

  public boolean validateNANP() {
    return validateNANP(this.normalize());
  }

  public static String getNumberWithCountryCode(String phoneNumber) {
    if (phoneNumber.startsWith("+1")) {
      return phoneNumber;
    }
    if (phoneNumber.length() == 11 && phoneNumber.startsWith("1")) {
      return format("+%s", phoneNumber);
    }
    return format("+1%s", phoneNumber);
  }

  public String getNumberWithCountryCode() {
    return getNumberWithCountryCode(this.normalize());
  }

  public static String getNumberWithoutCountryCode(String phoneNumber) {
    String normalized = normalize(phoneNumber);
    if (normalized.startsWith("+1")) {
      return normalized.substring(2);
    }
    if (normalized.length() == 11 && normalized.startsWith("1")) {
      return normalized.substring(1);
    }
    return normalized;
  }

  public String getNumberWithoutCountryCode() {
    return getNumberWithoutCountryCode(this.normalize());
  }

  public static class JsonType extends NullSafeType<PhoneNumber, Json.String> {

    @Override
    protected Json.String nullSafeMarshall(PhoneNumber number) {
      return string(number.toString());
    }

    @Override
    protected PhoneNumber nullSafeUnmarshall(Json.String string) {
      return new PhoneNumber(string.getString());
    }

  }

  public static class Converter extends NullHandlingConverter<PhoneNumber> {

    @Override
    protected PhoneNumber fromNonNullableString(String representation) {
      return new PhoneNumber(representation);
    }

    @Override
    protected String nonNullableToString(PhoneNumber value) {
      return value.getId();
    }

  }

}
