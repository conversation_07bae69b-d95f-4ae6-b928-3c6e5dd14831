package com.kaching.entities;

import static java.lang.String.format;
import static java.util.Collections.emptyList;
import static org.hibernate.criterion.Restrictions.eq;
import static org.hibernate.criterion.Restrictions.in;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Projections;

import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.PersistentCriteria;
import com.kaching.platform.hibernate.PersistentSQLQuery;
import com.kaching.platform.queryengine.exceptions.NotFoundException;

/**
 * An abstract repository interacting with the database.
 */
public abstract class PersistentRepository<E extends HibernateEntity> implements Repository<E> {

  private final Class<E> entityClass;
  protected final DbSession session;

  public PersistentRepository(Class<E> clazz, DbSession session) {
    this.entityClass = clazz;
    this.session = session;
  }

  @Override
  public E get(Id<E> id) {
    return session.get(entityClass, id).getOrNull();
  }

  @Override
  public E getOrThrow(Id<E> id) {
    return session.get(entityClass, id).getOrThrow(new NotFoundException(entityClass, id));
  }

  public Class<E> getEntityClass() {
    return entityClass;
  }

  @Override
  @SuppressWarnings("unchecked")
  public E load(Id<E> id) {
    return (E) session.getSession().load(entityClass, id);
  }

  @Override
  @SuppressWarnings("unchecked")
  public Id<E> persist(E entity) {
    session.save(entity);
    return (Id<E>) entity.getId();
  }

  /**
   * Gets the session associated with this repository.
   */
  public Session getSession() {
    return session.getSession();
  }

  /**
   * Creates a Query object that returns the
   * Entities associated with the Repository
   *
   * @param hql HQL query
   */
  public Query createQuery(String hql) {
    return getSession().createQuery(hql);
  }

  /**
   * Creates a type-safe Criteria object that returns the
   * Entities associated with the Repository
   */
  public PersistentCriteria<E> createCriteria() {
    return new PersistentCriteria<>(getSession(), entityClass);
  }

  public PersistentCriteria<E> createCriteria(String alias) {
    return new PersistentCriteria<>(getSession(), entityClass, alias);
  }

  /**
   * Creates a type-safe Criteria object that returns the
   * Entities associated with the Repository
   *
   * @param criteria list of criteria to apply
   */
  public PersistentCriteria<E> createCriteria(Criterion... criteria) {
    PersistentCriteria<E> result = createCriteria();
    for (Criterion criterion : criteria) {
      result.add(criterion);
    }
    return result;
  }

  public <T extends E> PersistentCriteria<T> createCriteria(Class<T> clazz) {
    return new PersistentCriteria<>(getSession(), clazz);
  }

  public PersistentSQLQuery<E> createSQLQuery(String queryString) {
    return new PersistentSQLQuery<>(getSession(), entityClass, queryString);
  }

  public <T extends E> PersistentSQLQuery<T> createSQLQuery(
      Class<T> clazz, String queryString) {
    return new PersistentSQLQuery<>(getSession(), clazz, queryString);
  }

  @Override
  public List<E> get(Collection<Id<E>> ids) {
    if (ids.isEmpty()) {
      return emptyList();
    } else {
      return createCriteria()
          .add(in("id", ids))
          .list();
    }
  }

  @Override
  public List<E> getAll() {
    return createCriteria().list();
  }

  @SuppressWarnings({"UnnecessaryLocalVariable"})
  @Override
  public List<Id<E>> getAllIds() {
    List<Id<E>> list = createCriteria()
        .<Id<E>>setProjection(Projections.id())
        .list();
    return list;
  }

  @Override
  @SuppressWarnings("unchecked")
  public Id<E> merge(E entity) {
    getSession().merge(entity);
    return (Id<E>) entity.getId();
  }

  @Override
  public void delete(E entity) {
    session.delete(entity);
    session.flush();
  }

  @Override
  public void deleteByCriteria(Criterion... criteria) {
    List<E> entities = createCriteria(criteria).list();
    for (E entity : entities) {
      session.delete(entity);
    }
    session.flush();
  }

  protected <T> T error(String name, Object value, Exception e) {
    throw new RuntimeException(format("Can't find %s %s", name, value), e);
  }

  // TODO(vlad): add it to Repository interface
  public E getOrThrow(String key, Object value) {
    List<E> results;
    try {
      results = createCriteria(eq(key, value)).list();
    } catch (Exception e) {
      throw new RuntimeException(format("Failed to find %s %s", key, value), e);
    }
    if (results == null || results.isEmpty()) {
      throw new NotFoundException(entityClass, key, value);
    }
    if (results.size() > 1) {
      throw new RuntimeException(format("Failed to find unique %s for %s %s", entityClass, key, value));
    }
    return results.get(0);
  }

  public <K> Set<E> findS(String key, Collection<K> values) {
    try {
      if (values.isEmpty()) {
        return Collections.emptySet();
      } else {
        return setOf(createCriteria(in(key, values)).list());
      }
    } catch (Exception e) {
      return error(key, values, e);
    }
  }

  public static <S, T extends S> Set<S> setOf(Iterable<T> i) {
    Set<S> set = new HashSet<>();
    for (T t : i) {
      set.add(t);
    }
    return set;
  }

}
