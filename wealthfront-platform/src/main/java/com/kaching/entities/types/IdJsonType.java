package com.kaching.entities.types;

import static com.twolattes.json.Json.number;

import com.kaching.platform.hibernate.Id;
import com.twolattes.json.Json;
import com.twolattes.json.types.NullSafeType;

public class IdJsonType extends NullSafeType<Id<?>, Json.Number> {

  @Override
  protected Json.Number nullSafeMarshall(Id<?> entity) {
    return number(entity.getId());
  }

  @Override
  protected Id<?> nullSafeUnmarshall(Json.Number number) {
    return Id.of(number.getNumber().longValue());
  }

}
