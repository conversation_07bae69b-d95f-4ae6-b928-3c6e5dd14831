package com.kaching.entities.converters;

import org.joda.time.Months;

import com.kaching.platform.converters.NullHandlingConverter;

public class MonthsConverter extends NullHandlingConverter<Months> {

  @Override
  protected Months fromNonNullableString(String representation) {
    return Months.months(Integer.parseInt(representation, 10));
  }

  @Override
  protected String nonNullableToString(Months value) {
    return Integer.toString(value.getMonths());
  }

}

