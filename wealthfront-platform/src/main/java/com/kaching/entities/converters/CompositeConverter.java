package com.kaching.entities.converters;

import com.kaching.platform.converters.Converter;
import com.kaching.platform.converters.NullHandlingConverter;

public abstract class CompositeConverter<T> extends NullHandlingConverter<T> {

  private final Converter<T> primary;
  private final Converter<T> secondary;

  public CompositeConverter(Converter<T> primary, Converter<T> secondary) {
    this.primary = primary;
    this.secondary = secondary;
  }

  @Override
  protected T fromNonNullableString(String representation) {
    T value = primary.fromString(representation);
    return value != null ? value : secondary.fromString(representation);
  }

  @Override
  protected String nonNullableToString(T value) {
    return primary.toString(value);
  }

}
