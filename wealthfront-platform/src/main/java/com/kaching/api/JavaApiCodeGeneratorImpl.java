package com.kaching.api;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.kaching.api.ExposeType.RewriteNamespace.DO_NOT_COPY;
import static com.kaching.api.JavaApiType.JavaObjectTypeSource.GLOBAL_DEFINITIONS;
import static com.kaching.api.JavaApiType.JavaObjectTypeSource.LENDING;
import static com.kaching.api.JavaApiType.JavaObjectTypeSource.MODELS;
import static com.kaching.api.JavaApiType.JavaObjectTypeSource.getPopulatedConstructorSources;
import static com.wealthfront.util.stream.WFCollectors.toMapWithKey;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Deque;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Modifier;

import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.TypeUtils;
import org.joda.time.LocalDate;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.CaseFormat;
import com.google.common.base.Converter;
import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Streams;
import com.google.inject.Inject;
import com.kaching.DefaultKachingMarshallers;
import com.kaching.api.JavaApiQuerySchema.JavaQueryParameter;
import com.kaching.api.JavaApiType.JavaEnumType;
import com.kaching.api.JavaApiType.JavaObjectType;
import com.kaching.api.JavaApiTypeNamespaceTransformer.FullyQualifiedTypeName;
import com.kaching.monitor.esp.ResponsibleParty;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.Strings;
import com.kaching.platform.converters.Instantiate;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.DeprecatedStubQueryConstructorGenerator;
import com.kaching.platform.queryengine.Owned;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.SecureField;
import com.kaching.platform.queryengine.StubQuery;
import com.kaching.util.Enums;
import com.kaching.util.functional.Pointer;
import com.kaching.util.functional.Tuple3;
import com.kaching.util.types.HyphenatedLocalDateJsonType;
import com.squareup.javapoet.AnnotationSpec;
import com.squareup.javapoet.ArrayTypeName;
import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.CodeBlock;
import com.squareup.javapoet.FieldSpec;
import com.squareup.javapoet.JavaFile;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.ParameterSpec;
import com.squareup.javapoet.ParameterizedTypeName;
import com.squareup.javapoet.TypeName;
import com.squareup.javapoet.TypeSpec;
import com.squareup.javapoet.TypeVariableName;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;
import com.twolattes.json.types.NullSafeType;

public class JavaApiCodeGeneratorImpl implements JavaApiCodeGenerator {

  private static final String WEALTHFRONT_MODELS_PACKAGE = "com.wealthfront.model";

  @Inject JavaApiValidator validator;
  @Inject DeprecatedStubQueryConstructorGenerator deprecatedStubQueryConstructorGenerator;

  @Override
  public List<JavaFile> generateFiles(
      Errors errors,
      Class<? extends ServiceKind> serviceKind,
      JavaApiTypesBuilder allTypes,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    checkForNamespaceCollisions(errors, serviceKind, allTypes, namespaceTransformer);
    if (errors.hasErrors()) {
      return emptyList();
    }
    List<TypeWithName> generatedTypes = new ArrayList<>();

    for (Class<? extends Query<?>> queryClass : allTypes.getQueries().keySet()) {
      JavaApiQuerySchema queryType = allTypes.getQueries().get(queryClass);
      FullyQualifiedTypeName resolved = namespaceTransformer.resolveQuery(serviceKind, queryType);
      generatedTypes.add(
          new TypeWithName(resolved, generateStubQueryTypeSpec(errors, serviceKind, queryType, namespaceTransformer)));
      generatedTypes.addAll(generateTypesForQuery(errors, serviceKind, queryClass, allTypes, namespaceTransformer));
    }

    return groupTypeDefinitionsIntoFiles(generatedTypes);
  }

  @Override
  public List<JavaFile> generateFilesFromJsonEntities(
      Errors errors,
      Class<? extends ServiceKind> serviceKind,
      JavaApiTypesBuilder allTypes,
      GlobalEntityNamespaceTransformer namespaceTransformer) {
    checkForJacksonNamespaceCollisions(errors, serviceKind, allTypes, namespaceTransformer);
    if (errors.hasErrors()) {
      return emptyList();
    }

    List<TypeWithName> generatedTypes = new ArrayList<>();

    allTypes.getAllTypes().values().forEach(type -> type.visit(new JavaApiTypeVisitor.DefaultVisitor<>(Unit.unit) {
      @Override
      public Unit caseObjectType(JavaApiType.JavaObjectType objectType) {
        Option<ExposeType> maybeExposeType = validator.getAndValidateExposeType(errors, objectType);
        JavaApiQuerySchema querySchema = new JavaApiQuerySchema(GlobalTypeTempQuery.class, emptyList(), objectType);

        if (maybeExposeType.isEmpty()) {
          errors.addMessage("Entity %s does not have @ExposeType.", objectType.getEntityClass());
          return Unit.unit;
        }

        FullyQualifiedTypeName resolved = namespaceTransformer.resolveObject(serviceKind, querySchema, objectType);
        generatedTypes.add(new TypeWithName(resolved,
            generateObjectTypeSpec(errors, serviceKind, querySchema, objectType, namespaceTransformer)));
        return Unit.unit;
      }

      @Override
      public Unit caseEnumType(JavaEnumType enumType) {
        JavaApiQuerySchema querySchema = new JavaApiQuerySchema(GlobalTypeTempQuery.class, emptyList(), enumType);
        ExposeType exposeType = enumType.getEnumClass().getAnnotation(ExposeType.class);

        if (exposeType == null) {
          errors.addMessage("Enum %s does not have @ExposeType.", enumType.getEnumClass());
          return Unit.unit;
        }

        FullyQualifiedTypeName resolved = namespaceTransformer.resolveEnum(serviceKind, querySchema, enumType);
        generatedTypes.add(new TypeWithName(resolved, generateEnumTypeSpec(errors, enumType, resolved)));
        return Unit.unit;
      }
    }));

    errors.throwIfHasErrors();
    return groupTypeDefinitionsIntoFiles(generatedTypes).stream()
        .filter(file -> !file.typeSpec.name.contains("TempJacksonGlobalTypeQuery"))
        .collect(Collectors.toList());
  }

  private List<TypeWithName> generateTypesForQuery(
      Errors errors,
      Class<? extends ServiceKind> serviceKind,
      Class<? extends Query<?>> queryClass,
      JavaApiTypesBuilder allTypes,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    JavaApiQuerySchema querySchema = allTypes.getQueries().get(queryClass);
    List<TypeWithName> generatedTypes = new ArrayList<>();
    for (Class<? extends Enum<?>> enumClass : allTypes.getQueryEnumReferences(queryClass)) {
      if (enumClass.getAnnotation(ExposeType.class).namespace() == DO_NOT_COPY) {
        continue;
      }
      JavaEnumType enumType = allTypes.getEnumTypes().get(enumClass);
      FullyQualifiedTypeName resolved = namespaceTransformer.resolveEnum(serviceKind, querySchema, enumType);
      generatedTypes.add(new TypeWithName(resolved, generateEnumTypeSpec(errors, enumType, resolved)));
    }
    for (Class<?> objectClass : allTypes.getQueryObjectReferences(queryClass)) {
      JavaObjectType objectType = allTypes.getObjectType(objectClass).getOrThrow();
      Option<ExposeType> maybeAnnotation = validator.getAndValidateExposeType(errors, objectType);
      if (maybeAnnotation.isEmpty()) {
        throw new RuntimeException(errors.toString());
      }
      if (maybeAnnotation.getOrThrow().namespace() == DO_NOT_COPY) {
        continue;
      }
      FullyQualifiedTypeName resolved = namespaceTransformer.resolveObject(serviceKind, querySchema, objectType);
      generatedTypes.add(new TypeWithName(resolved,
          generateObjectTypeSpec(errors, serviceKind, querySchema, objectType, namespaceTransformer)));
    }
    return generatedTypes;
  }

  @VisibleForTesting
  List<JavaFile> groupTypeDefinitionsIntoFiles(List<TypeWithName> types) {
    Multimap<Pair<String, String>, TypeWithName> typesByTopLevelClassName = HashMultimap.create();
    types.forEach((typeWithName) -> {
      typesByTopLevelClassName.put(
          Pair.of(typeWithName.name.packageName, typeWithName.name.getTopLevelName()),
          typeWithName
      );
    });
    return typesByTopLevelClassName.asMap().values().stream().map(typesForSingleFile -> {
      Map<FullyQualifiedTypeName, TypeWithName> typesToLinkByFullyQualified = typesForSingleFile.stream()
          .collect(toMapWithKey(t -> t.name, (existing, replacement) -> existing));

      Pointer<TypeWithName> topLevelPtr = Pointer.pointer();
      while (!typesToLinkByFullyQualified.isEmpty()) {
        for (FullyQualifiedTypeName typeName : ImmutableList.sortedCopyOf(
            comparing(name -> -name.nestedClassNames.size()),
            typesToLinkByFullyQualified.keySet())) {
          TypeWithName currentType = typesToLinkByFullyQualified.get(typeName);
          if (currentType.name.isTopLevel()) {
            topLevelPtr.set(currentType);
            typesToLinkByFullyQualified.remove(currentType.name);
          } else {
            FullyQualifiedTypeName outerClassName = new FullyQualifiedTypeName(currentType.name.packageName,
                removeLastElement(currentType.name.nestedClassNames));
            TypeWithName outerClass;
            if (typesToLinkByFullyQualified.containsKey(outerClassName)) {
              outerClass = typesToLinkByFullyQualified.get(outerClassName);
              outerClass.spec.addType(currentType.spec.build());
              typesToLinkByFullyQualified.remove(currentType.name);
            } else {
              outerClass = new TypeWithName(outerClassName, TypeSpec.classBuilder(outerClassName.getJavaPoetClassName())
                  .addModifiers(Modifier.STATIC, Modifier.PUBLIC));
              outerClass.spec.addType(currentType.spec.build());
              typesToLinkByFullyQualified.put(outerClassName, outerClass);
              typesToLinkByFullyQualified.remove(currentType.name);
              break;
            }
          }
        }
      }

      TypeWithName topLevelType = checkNotNull(topLevelPtr.get(),
          "Should not be reachable- a top level class should always be found or generated");

      return JavaFile.builder(topLevelType.name.packageName, topLevelType.spec.build())
          .skipJavaLangImports(true)
          .indent("  ")
          .build();
    }).collect(toList());
  }

  private static <T> List<T> removeLastElement(List<T> input) {
    checkArgument(!input.isEmpty(), "Empty list given");
    List<T> output = new ArrayList<>(input.size() - 1);
    for (int i = 0; i < input.size() - 1; i++) {
      output.add(input.get(i));
    }
    return output;
  }

  private void checkForNamespaceCollisions(
      Errors errors,
      Class<? extends ServiceKind> serviceKind,
      JavaApiTypesBuilder allTypes,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    Multimap<FullyQualifiedTypeName, Class<?>> resolutions = HashMultimap.create();
    for (Class<? extends Query<?>> queryClass : allTypes.getQueries().keySet()) {
      JavaApiQuerySchema querySchema = allTypes.getQueries().get(queryClass);
      allTypes.getQueryObjectReferences(queryClass).forEach(objectClass -> {
        JavaObjectType objectType = allTypes.getObjectType(objectClass).getOrThrow(Strings.format(
            "Class %s wasn't introspected despite being a dependency of %s", objectClass, queryClass));
        resolutions.put(namespaceTransformer.resolveObject(serviceKind, querySchema, objectType), objectClass);
      });
      allTypes.getQueryEnumReferences(queryClass).forEach(enumClass -> {
        JavaEnumType enumType = checkNotNull(allTypes.getEnumTypes().get(enumClass), Strings.format(
            "Class %s wasn't introspected despite being a dependency of %s", enumClass, queryClass));
        resolutions.put(namespaceTransformer.resolveEnum(serviceKind, querySchema, enumType), enumClass);
      });
    }

    resolutions.asMap().entrySet().stream()
        .filter(entry -> entry.getValue().size() > 1)
        .forEach(entry -> errors.addMessage("Multiple classes resolve to %s: %s", entry.getKey(), entry.getValue()));
  }

  private void checkForJacksonNamespaceCollisions(
      Errors errors,
      Class<? extends ServiceKind> serviceKind,
      JavaApiTypesBuilder allTypes,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    Multimap<FullyQualifiedTypeName, Class<?>> resolutions = HashMultimap.create();

    allTypes.getObjectTypes().entrySet().stream()
        .forEach(objectEntry -> {
          Class<?> objectClass = objectEntry.getKey();
          JavaObjectType objectType = checkNotNull(objectEntry.getValue(), Strings.format(
              "Class %s wasn't introspected", objectClass));
          JavaApiQuerySchema querySchema = new JavaApiQuerySchema(GlobalTypeTempQuery.class, emptyList(), objectType);
          resolutions.put(namespaceTransformer.resolveObject(serviceKind, querySchema, objectType), objectClass);
        });
    allTypes.getEnumTypes().entrySet().stream()
        .forEach(enumEntry -> {
          Class<? extends Enum<?>> enumClass = enumEntry.getKey();
          JavaEnumType enumType = checkNotNull(enumEntry.getValue(), Strings.format(
              "Class %s wasn't introspected", enumClass));
          JavaApiQuerySchema querySchema = new JavaApiQuerySchema(GlobalTypeTempQuery.class, emptyList(), enumType);
          resolutions.put(namespaceTransformer.resolveEnum(serviceKind, querySchema, enumType), enumClass);
        });
    resolutions.asMap().entrySet().stream()
        .filter(entry -> entry.getValue().size() > 1)
        .forEach(entry -> errors.addMessage("Multiple classes resolve to %s: %s", entry.getKey(), entry.getValue()));
  }

  private TypeSpec.Builder generateEnumTypeSpec(
      Errors errors,
      JavaEnumType enumType,
      FullyQualifiedTypeName newTypeName) {
    TypeSpec.Builder builder = TypeSpec.enumBuilder(newTypeName.getJavaPoetClassName())
        .addModifiers(Modifier.PUBLIC)
        .addAnnotation(AnnotationSpec.get(enumType.getEnumClass().getAnnotation(ExposeType.class)));
    Option<Method> maybeGetValueMethod = Option.from(Arrays.stream(enumType.getEnumClass().getDeclaredMethods())
        .filter(method -> method.getName().equals("getValue"))
        .findFirst());
    TypeVariableName tVar = TypeVariableName.get("T");
    ClassName visitorClassName = newTypeName.getJavaPoetClassName().nestedClass("Visitor");
    TypeName interfaceName = ParameterizedTypeName.get(visitorClassName, tVar);
    TypeSpec.Builder interfaceBuilder = TypeSpec.interfaceBuilder(visitorClassName)
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .addAnnotation(Deprecated.class)
        .addTypeVariable(tVar);

    TypeName supplierOfT = ParameterizedTypeName.get(ClassName.get(Supplier.class), tVar);
    TypeName parameterizedVisitorName = ParameterizedTypeName.get(visitorClassName, tVar);

    TypeSpec.Builder defaultVisitorBuilder = TypeSpec.classBuilder("Default" + visitorClassName.simpleName())
        .addSuperinterface(parameterizedVisitorName)
        .addAnnotation(Deprecated.class)
        .addTypeVariable(tVar)
        .addField(supplierOfT, "defaultValue", Modifier.PRIVATE, Modifier.FINAL)
        .addMethod(MethodSpec.constructorBuilder().addModifiers(Modifier.PUBLIC)
            .addParameter(supplierOfT, "defaultValue")
            .addStatement("this.defaultValue = defaultValue")
            .build())
        .addMethod(MethodSpec.constructorBuilder().addModifiers(Modifier.PUBLIC)
            .addParameter(tVar, "defaultValue")
            .addStatement("this.defaultValue = () -> defaultValue")
            .build())
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC);
    Converter<String, String> enumConverter = CaseFormat.UPPER_UNDERSCORE.converterTo(CaseFormat.UPPER_CAMEL);
    for (Enum<?> constant : enumType.getEnumConstants()) {
      String constantName = constant.name();
      if (maybeGetValueMethod.isDefined()) {
        try {
          String value = (String) maybeGetValueMethod.getOrThrow().invoke(constant);
          TypeSpec typeSpec = TypeSpec.anonymousClassBuilder("$S", value).build();
          builder.addEnumConstant(constantName, typeSpec);
        } catch (IllegalAccessException e) {
          throw new RuntimeException(Strings.format(
              "Unable to invoke %s.%s:getValue() method because it isn't public.", enumType.getEnumClass(),
              constantName));
        } catch (InvocationTargetException e) {
          throw new RuntimeException(Strings.format(
              "%s.%s:getValue() threw an exception %s", enumType.getEnumClass(), constantName,
              e.getTargetException().getMessage()));
        }
      } else {
        builder.addEnumConstant(constantName);
      }

      interfaceBuilder.addMethod(MethodSpec.methodBuilder("case" + enumConverter.convert(constantName))
          .returns(tVar)
          .addModifiers(Modifier.ABSTRACT, Modifier.PUBLIC)
          .build());

      defaultVisitorBuilder.addMethod(MethodSpec.methodBuilder("case" + enumConverter.convert(constantName))
          .addModifiers(Modifier.PUBLIC)
          .addAnnotation(Override.class)
          .addStatement("return defaultValue.get()")
          .returns(tVar)
          .build());
    }

    if (maybeGetValueMethod.isDefined()) {
      FieldSpec valueField = FieldSpec.builder(String.class, "value", Modifier.PRIVATE, Modifier.FINAL)
          .build();

      MethodSpec constructor = MethodSpec.constructorBuilder()
          .addParameter(String.class, "value")
          .addStatement("this.value = value")
          .build();

      MethodSpec getValueMethod = MethodSpec.methodBuilder("getValue")
          .addModifiers(Modifier.PUBLIC)
          .returns(String.class)
          .addStatement("return this.value")
          .build();
      builder.addField(valueField);
      builder.addMethod(constructor);
      builder.addMethod(getValueMethod);

      TypeSpec jsonType = TypeSpec.classBuilder("JsonType")
          .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
          .superclass(ParameterizedTypeName.get(
              ClassName.get(NullSafeType.class),
              newTypeName.getJavaPoetClassName(),
              ClassName.get(Json.String.class)
          ))
          .addMethod(MethodSpec.methodBuilder("nullSafeMarshall")
              .addModifiers(Modifier.PROTECTED)
              .addAnnotation(Override.class)
              .returns(ClassName.get("Json", "String"))
              .addParameter(newTypeName.getJavaPoetClassName(), "type")
              .addStatement("return Json.string(type.getValue())")
              .build())
          .addMethod(MethodSpec.methodBuilder("nullSafeUnmarshall")
              .addModifiers(Modifier.PROTECTED)
              .addAnnotation(Override.class)
              .returns(newTypeName.getJavaPoetClassName())
              .addParameter(ClassName.get(Json.String.class), "string")
              .addCode(CodeBlock.builder()
                  .add("return $T.stream(values())\n", ClassName.get(Arrays.class))
                  .add("    .filter(val -> val.getValue().equals(string.getString()))\n")
                  .add("    .findFirst()\n")
                  .add("    .get();\n")
                  .build())
              .build())
          .build();

      AnnotationSpec marshalledBy = AnnotationSpec.builder(ClassName.get(MarshalledBy.class))
          .addMember("value", "$T.class", newTypeName.getJavaPoetClassName().nestedClass("JsonType"))
          .build();

      builder.addType(jsonType);
      builder.addAnnotation(marshalledBy);
    }

    MethodSpec.Builder visitMethod = MethodSpec.methodBuilder("visit")
        .addTypeVariable(tVar)
        .returns(tVar)
        .addModifiers(Modifier.PUBLIC)
        .addParameter(ParameterSpec.builder(interfaceName, "visitor")
            .build());
    CodeBlock.Builder visitMethodBody = CodeBlock.builder()
        .add("switch (this) {\n")
        .indent();
    for (String constant : enumType.getEnumConstantNames()) {
      visitMethodBody.add("case $N:\n", constant)
          .indent()
          .addStatement("return visitor.$N()", "case" + enumConverter.convert(constant))
          .unindent();
    }
    visitMethodBody.add("default:\n")
        .indent()
        .addStatement("throw new IllegalStateException()")
        .unindent()
        .unindent()
        .add("}\n");
    visitMethod.addCode(visitMethodBody.build());

    builder.addMethod(visitMethod.build());
    builder.addType(interfaceBuilder.build());
    builder.addType(defaultVisitorBuilder.build());
    return builder;
  }

  private TypeSpec.Builder generateStubQueryTypeSpec(
      Errors errors,
      Class<? extends ServiceKind> serviceKind,
      JavaApiQuerySchema querySchema,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    List<FieldSpec> fieldSpecs = new ArrayList<>();
    List<ParameterSpec> parameterSpecs = new ArrayList<>();
    List<Constructor<?>> constructors = asList(querySchema.getQueryClass().getDeclaredConstructors());
    Constructor<?> constructor = constructors.size() == 1 ? constructors.get(0) :
        constructors.stream()
            .filter(c -> c.getAnnotation(Instantiate.class) != null)
            .findAny()
            .get();
    List<Parameter> constructorParameters = asList(constructor.getParameters());
    for (JavaQueryParameter parameter : querySchema.getParameters()) {
      TypeName typeName = rewriteJavaType(parameter.getType(), serviceKind, querySchema, namespaceTransformer);
      List<AnnotationSpec> fieldAnnotations = parameter.getFlags().stream()
          .sorted()
          .flatMap(flag -> flag.visit(new JavaQueryParameter.FlagVisitor<Stream<AnnotationSpec>>() {
            @Override
            public Stream<AnnotationSpec> caseSecureField() {
              return Stream.of(AnnotationSpec.builder(SecureField.class).build());
            }

            @Override
            public Stream<AnnotationSpec> caseOwnedEnforcing() {
              return Stream.of();
            }

            @Override
            public Stream<AnnotationSpec> caseOptional() {
              return Stream.of();
            }
          })).collect(toList());
      fieldSpecs.add(FieldSpec.builder(typeName, parameter.getName(), Modifier.PRIVATE, Modifier.FINAL)
          .addAnnotations(fieldAnnotations)
          .build());
      List<AnnotationSpec> parameterAnnotations = parameter.getFlags().stream()
          .sorted()
          .flatMap(flag -> flag.visit(new JavaQueryParameter.FlagVisitor<Stream<AnnotationSpec>>() {
            @Override
            public Stream<AnnotationSpec> caseSecureField() {
              return Stream.empty();
            }

            @Override
            public Stream<AnnotationSpec> caseOwnedEnforcing() {
              return Stream.of(AnnotationSpec.builder(Owned.class).build());
            }

            @Override
            public Stream<AnnotationSpec> caseOptional() {
              return Stream.of();
            }
          })).collect(toList());
      parameterSpecs.add(ParameterSpec.builder(typeName, parameter.getName())
          .addAnnotations(parameterAnnotations)
          .build());
    }

    List<MethodSpec> deprecatedConstructorMethodSpecs =
        deprecatedStubQueryConstructorGenerator.generate(constructorParameters, parameterSpecs);

    CodeBlock.Builder fieldAssignment = CodeBlock.builder();
    querySchema.getParameters()
        .forEach(param -> fieldAssignment.addStatement("this.$N = $N", param.getName(), param.getName()));
    MethodSpec.Builder constructorMethodSpecBuilder = MethodSpec.constructorBuilder()
        .addModifiers(Modifier.PUBLIC)
        .addParameters(parameterSpecs)
        .addCode(fieldAssignment.build());

    if (deprecatedConstructorMethodSpecs.size() > 0) {
      constructorMethodSpecBuilder.addAnnotation(Instantiate.class);
    }
    MethodSpec constructorMethodSpec = constructorMethodSpecBuilder.build();
    TypeName returnTypeName =
        rewriteJavaType(querySchema.getReturnType(), serviceKind, querySchema, namespaceTransformer);
    TypeName superTypeName =
        ParameterizedTypeName.get(ClassName.get(StubQuery.class), returnTypeName, TypeName.get(serviceKind));

    Map<String, Boolean> paramNameToIsNullableMap = constructorParameters.stream()
        .collect(toMap(
            Parameter::getName,
            parameter -> Option.of(parameter.getAnnotation(Optional.class))
                .transform(annotation -> annotation.value().equals(Optional.VALUE_DEFAULT) &&
                    annotation.constant().isEmpty())
                .getOrElse(false)
        ));

    List<MethodSpec> fieldGetterMethodSpecs = fieldSpecs.stream()
        .map(fieldSpec -> {
          String getterName = String.format(
              "get%c%s",
              Character.toUpperCase(fieldSpec.name.charAt(0)), fieldSpec.name.substring(1)
          );
          if (Option.of(paramNameToIsNullableMap.get(fieldSpec.name)).getOrElse(false)) {
            return MethodSpec.methodBuilder(getterName)
                .addModifiers(Modifier.PUBLIC)
                .returns(ParameterizedTypeName.get(ClassName.get(Option.class), fieldSpec.type))
                .addStatement("return $T.of($N)", Option.class, fieldSpec.name)
                .build();
          }
          return MethodSpec.methodBuilder(getterName)
              .addModifiers(Modifier.PUBLIC)
              .returns(fieldSpec.type)
              .addStatement("return $N", fieldSpec.name)
              .build();
        })
        .toList();

    Option<ExposeQuery> queryAnnotation = Option.of(querySchema.getQueryClass().getAnnotation(ExposeQuery.class));
    Option<ResponsibleParty> responsiblePartyAnnotation = Option.of(
        querySchema.getQueryClass().getAnnotation(ResponsibleParty.class)
    );
    Option<Deprecated> deprecatedAnnotation = Option.of(querySchema.getQueryClass().getAnnotation(Deprecated.class));
    return TypeSpec.classBuilder(namespaceTransformer.resolveQuery(serviceKind, querySchema).getJavaPoetClassName())
        .superclass(superTypeName)
        .addAnnotations(queryAnnotation.transform(AnnotationSpec::get))
        .addAnnotations(responsiblePartyAnnotation.transform(AnnotationSpec::get))
        .addAnnotations(deprecatedAnnotation.transform(annotation -> AnnotationSpec.builder(Deprecated.class).build()))
        .addModifiers(Modifier.PUBLIC)
        .addMethod(constructorMethodSpec)
        .addMethods(deprecatedConstructorMethodSpecs)
        .addMethods(fieldGetterMethodSpecs)
        .addFields(fieldSpecs);
  }

  private TypeSpec.Builder generateObjectTypeSpec(
      Errors errors,
      Class<? extends ServiceKind> serviceKind,
      JavaApiQuerySchema querySchema,
      JavaObjectType objectType,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    FullyQualifiedTypeName className = namespaceTransformer.resolveObject(serviceKind, querySchema, objectType);
    AnnotationSpec.Builder entityAnnotation = AnnotationSpec.builder(Entity.class);
    TypeSpec.Builder baseBuilder = TypeSpec.classBuilder(className.getJavaPoetClassName());
    boolean populateConstructorFields = getPopulatedConstructorSources().contains(objectType.getSource());

    ExposeType actualAnnotation = objectType.getEntityClass().getAnnotation(ExposeType.class);
    if (actualAnnotation != null) {
      baseBuilder.addAnnotation(AnnotationSpec.get(actualAnnotation));
    }

    if (objectType.getEntityClass().isAnnotationPresent(SecureField.class)) {
      baseBuilder.addAnnotation(SecureField.class);
    }

    Option<JavaObjectType> visitorOwnerType;
    if (objectType.getDiscriminatorName().isDefined() && hasNoParentOrParentIsNonPolymorphic(objectType)) {
      visitorOwnerType = Option.some(objectType);
    } else if (objectType.getTopPolymorphicAncestor().isDefined()) {
      visitorOwnerType = objectType.getTopPolymorphicAncestor();
    } else {
      visitorOwnerType = Option.none();
    }

    for (JavaObjectType polymorphicAncestor : visitorOwnerType) {
      Tuple3<TypeSpec, TypeSpec, TypeName> visitorClassTuple =
          generateVisitorSpec(namespaceTransformer, serviceKind, querySchema, polymorphicAncestor);
      TypeVariableName tVar = TypeVariableName.get("T");
      MethodSpec.Builder visitMethod = MethodSpec.methodBuilder("visit")
          .addTypeVariable(tVar)
          .returns(tVar)
          .addParameter(visitorClassTuple._3, "visitor")
          .addModifiers(Modifier.PUBLIC);
      if (objectType.getDiscriminatorName().isDefined() && hasNoParentOrParentIsNonPolymorphic(objectType)) {
        baseBuilder.addType(visitorClassTuple._1);
        baseBuilder.addType(visitorClassTuple._2);
        if (objectType.isAbstract()) {
          visitMethod.addModifiers(Modifier.ABSTRACT);
        } else {
          visitMethod.addStatement("return visitor.case" + objectType.getEntityClass().getSimpleName() + "(this)");
        }
        baseBuilder.addMethod(visitMethod.build());
      } else if (objectType.getDiscriminatorValue().isDefined() && !objectType.isAbstract()) {
        if (objectType.isAbstract()) {
          visitMethod.addModifiers(Modifier.ABSTRACT);
        } else {
          visitMethod.addStatement("return visitor.case" + objectType.getEntityClass().getSimpleName() + "(this)");
        }
        visitMethod.addAnnotation(Override.class);
        baseBuilder.addMethod(visitMethod.build());
      }
    }

    if (objectType.getDiscriminatorName().isDefined() && hasNoParentOrParentIsNonPolymorphic(objectType)) {
      entityAnnotation.addMember("discriminatorName",
          CodeBlock.builder().add("$S", objectType.getDiscriminatorName().getOrThrow()).build());
      CodeBlock classArray = objectType.getAllDescendents()
          .stream()
          .filter(type -> !type.isAbstract())
          .sorted(comparing(type -> type.getEntityClass().getSimpleName()))
          .map(objectSchema -> namespaceTransformer.resolveObject(serviceKind, querySchema, objectSchema))
          .map(resolvedName -> CodeBlock.of("$T.class", resolvedName.getJavaPoetClassName()))
          .collect(CodeBlock.joining(", ", "{", "}"));
      entityAnnotation.addMember("subclasses", classArray);
    }
    if (objectType.getDiscriminatorValue().isDefined()) {
      entityAnnotation.addMember("discriminator",
          CodeBlock.builder().add("$S", objectType.getDiscriminatorValue().getOrThrow()).build());
    }

    baseBuilder
        .addAnnotation(entityAnnotation.build())
        .addModifiers(Modifier.PUBLIC);
    if (className.nestedClassNames.size() > 1) {
      baseBuilder.addModifiers(Modifier.STATIC);
    }

    if (!objectType.getComponentInterfaces().isEmpty()) {
      objectType.getComponentInterfaces()
          .forEach(baseBuilder::addSuperinterface);
    }

    if (objectType.getParent().isDefined()) {
      baseBuilder.superclass(
          namespaceTransformer.resolveObject(serviceKind, querySchema, objectType.getParent().getOrThrow())
              .getJavaPoetClassName());
    }
    if (objectType.isAbstract()) {
      baseBuilder.addModifiers(Modifier.ABSTRACT);
    }
    if (objectType.getSource() == LENDING && hasNoParentOrParentIsNonPolymorphic(objectType)) {
      baseBuilder.addField(FieldSpec.builder(
              ParameterizedTypeName.get(ClassName.get(Marshaller.class), className.getJavaPoetClassName()),
              "MARSHALLER",
              Modifier.PUBLIC,
              Modifier.STATIC,
              Modifier.FINAL)
          .initializer("$T.createEntityMarshaller($T.class)", ClassName.get(DefaultKachingMarshallers.class),
              className.getJavaPoetClassName())
          .build());
    }

    for (JavaApiType.JavaObjectField field : objectType.getFields()) {
      AnnotationSpec.Builder annotationBuilder = AnnotationSpec.builder(Value.class);
      if (field.isOptional()) {
        annotationBuilder.addMember("optional", "true");
      }
      annotationBuilder.addMember("nullable", field.getApiType().isNullable() ? "true" : "false");
      if (!field.getApiName().equals(field.getJavaName())) {
        annotationBuilder.addMember("name", CodeBlock.of("$S", field.getApiName()));
      }
      if (objectType.getSource() == LENDING && field.getJavaType().equals(LocalDate.class)) {
        annotationBuilder.addMember("type",
            CodeBlock.builder().add("$T.class", ClassName.get(HyphenatedLocalDateJsonType.class)).build());
      }
      if (field.getConverters().size() == 1) {
        annotationBuilder.addMember("type",
            CodeBlock.builder().add("$T.class", TypeName.get(field.getConverters().get(0))).build());
      } else if (field.getConverters().size() > 1) {
        CodeBlock classArray = field.getConverters()
            .stream()
            .map(TypeName::get)
            .map(typeName -> CodeBlock.of("$T.class", typeName))
            .collect(CodeBlock.joining(", ", "{", "}"));
        annotationBuilder.addMember("types", classArray);
      }

      TypeName rewrittenType = rewriteJavaType(field.getApiType(), serviceKind, querySchema, namespaceTransformer);
      baseBuilder.addField(FieldSpec.builder(rewrittenType, field.getJavaName())
          .addModifiers(Modifier.PRIVATE)
          .addAnnotation(annotationBuilder.build())
          .build());

      String getterName = "get" + StringUtils.capitalize(field.getJavaName());
      if (field.getApiType().isNullable()) {
        MethodSpec.Builder builder = MethodSpec.methodBuilder(getterName)
            .returns(ParameterizedTypeName.get(ClassName.get(Option.class), rewrittenType))
            .addStatement("return $T.of($N)", Option.class, field.getJavaName())
            .addModifiers(Modifier.PUBLIC);
        if (validEntityDataMethod(objectType, getterName, field, errors)) {
          builder.addAnnotation(Override.class);
        }

        baseBuilder.addMethod(builder.build());
      } else {
        MethodSpec.Builder builder = MethodSpec.methodBuilder(getterName)
            .returns(rewrittenType)
            .addStatement("return $N", field.getJavaName())
            .addModifiers(Modifier.PUBLIC);
        if (validEntityDataMethod(objectType, getterName, field, errors)) {
          builder.addAnnotation(Override.class);
        }
        baseBuilder.addMethod(builder
            .build());
      }

      String setterName = "set" + StringUtils.capitalize(field.getJavaName());
      if (!populateConstructorFields) {
        baseBuilder.addMethod(MethodSpec.methodBuilder(setterName)
            .returns(void.class)
            .addParameter(rewrittenType, field.getJavaName())
            .addStatement("this.$N = $N", field.getJavaName(), field.getJavaName())
            .addModifiers(Modifier.PROTECTED)
            .build());
      }
    }
    List<JavaObjectType> allAncestorsDesc = Lists.reverse(objectType.getAllAncestorsAsc());
    boolean allAncestorsCodeGenerated = allAncestorsDesc.stream()
        .allMatch(type -> {
          boolean isNotDoNotCopy =
              validator.getAndValidateExposeType(errors, type).getOrThrow().namespace() != DO_NOT_COPY;
          boolean isGeneratingFilesFromJacksonEntities = querySchema.getQueryClass().equals(GlobalTypeTempQuery.class);

          return isNotDoNotCopy || isGeneratingFilesFromJacksonEntities;
        });

    MethodSpec.Builder constructorBuilder = MethodSpec.constructorBuilder().addModifiers(Modifier.PUBLIC);
    boolean hasParameters = false;
    List<String> superKeys = new ArrayList<>();
    if (populateConstructorFields) {
      for (JavaObjectType superclass : allAncestorsDesc) {
        for (JavaApiType.JavaObjectField field : superclass.getFields()) {
          TypeName typeName = rewriteJavaType(field.getApiType(), serviceKind, querySchema, namespaceTransformer);
          constructorBuilder.addParameter(typeName, field.getJavaName());
          hasParameters = true;
          superKeys.add(field.getJavaName());
        }
      }
      if (!superKeys.isEmpty()) {
        constructorBuilder.addStatement(superKeys.stream()
            .map(CodeBlock::of)
            .collect(CodeBlock.joining(", ", "super(", ")")));
      }
      for (JavaApiType.JavaObjectField field : objectType.getFields()) {
        TypeName typeName = rewriteJavaType(field.getApiType(), serviceKind, querySchema, namespaceTransformer);
        constructorBuilder.addParameter(typeName, field.getJavaName());
        constructorBuilder.addStatement("this.$N = $N", field.getJavaName(), field.getJavaName());
        hasParameters = true;
      }
    }

    if (hasParameters) {
      baseBuilder.addMethod(MethodSpec.constructorBuilder()
          .addModifiers(Modifier.PUBLIC)
          .addComment("JSON")
          .build());
    }
    baseBuilder.addMethod(constructorBuilder.build());
    if (allAncestorsDesc.isEmpty() || allAncestorsCodeGenerated) {
      if (objectType.getSource() != MODELS) {
        baseBuilder.addMethod(generateValidateSpec(objectType));
        baseBuilder.addMethod(generateHashCodeSpec(objectType));
        baseBuilder.addMethod(generateEqualsSpec(className, objectType));
        baseBuilder.addMethod(generateToStringSpec(className, objectType));

        if (!objectType.isAbstract()) {
          baseBuilder.addMethod(generateCopySpec(objectType));
        }
      }

      if (!objectType.isAbstract()) {
        if (objectType.getSource() == GLOBAL_DEFINITIONS) {
          baseBuilder.addMethod(generateToGlobalSpec(className, objectType, querySchema, namespaceTransformer));
          baseBuilder.addMethod(generateToWfModelSpec(objectType));
        }
        baseBuilder.addMethod(generateBuilderMethodSpec());
        baseBuilder.addType(
            generateBuilderSpec(serviceKind, querySchema, className, objectType, namespaceTransformer));
      } else {
        if (objectType.getSource() == GLOBAL_DEFINITIONS) {
          baseBuilder.addMethod(
              generateAbstractParentToGlobalMethod(className, objectType, querySchema, namespaceTransformer));
          baseBuilder.addMethod(generateAbstractToWFModelsMethod(className));
        }
      }
    }

    return baseBuilder;
  }

  private boolean validEntityDataMethod(
      JavaObjectType objectType, String getterName, JavaApiType.JavaObjectField field, Errors errors) {
    if (objectType.getEntityDataMethods().containsKey(getterName)) {
      Value methodValue = objectType.getEntityDataMethods().get(getterName).getAnnotation(Value.class);
      if (methodValue.nullable() != field.getApiType().isNullable()) {
        errors.addMessage(
            "Nullable mismatch for field '%s': method annotation has nullable=%s but field type has nullable=%s",
            field.getJavaName(), methodValue.nullable(), field.getApiType().isNullable());
      }
      if (methodValue.optional() != field.isOptional()) {
        errors.addMessage(
            "Optional mismatch for field '%s': method annotation has optional=%s but field is optional=%s",
            field.getJavaName(), methodValue.optional(), field.isOptional());
      }

      if (field.getConverters().size() == 1 && field.getConverters().get(0) != methodValue.type()) {
        errors.addMessage(
            "Type converter mismatch for field '%s': method annotation has type=%s but field has converter=%s",
            field.getJavaName(), methodValue.type().getName(), field.getConverters().get(0).getName());
      } else if (field.getConverters().size() > 1 && !asList(methodValue.types()).equals(field.getConverters())) {
        errors.addMessage(
            "Multiple type converters mismatch for field '%s': method annotation types don't match field converters",
            field.getJavaName());
      }

      return !errors.hasErrors();
    }
    return false;
  }

  private Option<ClassName> getMaybeNonAbstractParentClassName(
      Class<? extends ServiceKind> serviceKind,
      JavaApiQuerySchema querySchema,
      JavaObjectType objectType,
      JavaApiTypeNamespaceTransformer namespaceTransformer
  ) {
    if (objectType.getParent().isDefined()) {
      if (!objectType.getParent().getOrThrow().isAbstract()) {
        return Option.some(
            namespaceTransformer.resolveObject(serviceKind, querySchema, objectType.getParent().getOrThrow())
                .getJavaPoetClassName());
      } else {
        return getMaybeNonAbstractParentClassName(serviceKind, querySchema, objectType.getParent().getOrThrow(),
            namespaceTransformer);
      }
    }
    return Option.none();
  }

  private MethodSpec generateValidateSpec(JavaObjectType type) {
    MethodSpec.Builder builder = MethodSpec.methodBuilder("validate")
        .returns(TypeName.VOID)
        .addModifiers(Modifier.PUBLIC);
    if (type.getParent().isDefined()) {
      builder.addStatement(CodeBlock.of("super.validate()"));
      builder.addAnnotation(Override.class);
    }
    type.getFields().stream()
        .filter(field -> !field.getApiType().isNullable())
        .filter(field -> !isPrimitiveType(field.getJavaType()))
        .map(field -> CodeBlock.of("$T.checkNotNull($N, $S)", Preconditions.class, field.getJavaName(),
            String.format("field '%s' should not be null", field.getJavaName())))
        .forEach(builder::addStatement);
    return builder.build();
  }

  Tuple3<TypeSpec, TypeSpec, TypeName> generateVisitorSpec(
      JavaApiTypeNamespaceTransformer namespaceTransformer,
      Class<? extends ServiceKind> serviceKind,
      JavaApiQuerySchema querySchema,
      JavaObjectType ancestorType) {
    ClassName visitorName =
        namespaceTransformer.resolveObject(serviceKind, querySchema, ancestorType).getInnerClass("Visitor")
            .getJavaPoetClassName();
    TypeVariableName typeT = TypeVariableName.get("T");
    TypeName supplierOfT = ParameterizedTypeName.get(ClassName.get(Supplier.class), typeT);
    TypeSpec.Builder visitorBuilder = TypeSpec.interfaceBuilder(visitorName)
        .addTypeVariable(typeT)
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC);

    TypeName parameterizedVisitorName = ParameterizedTypeName.get(visitorName, typeT);

    TypeSpec.Builder defaultVisitorBuilder = TypeSpec.classBuilder("Default" + visitorName.simpleName())
        .addSuperinterface(parameterizedVisitorName)
        .addTypeVariable(typeT)
        .addField(supplierOfT, "defaultValue", Modifier.PRIVATE, Modifier.FINAL)
        .addMethod(MethodSpec.constructorBuilder().addModifiers(Modifier.PUBLIC)
            .addParameter(supplierOfT, "defaultValue")
            .addStatement("this.defaultValue = defaultValue")
            .build())
        .addMethod(MethodSpec.constructorBuilder().addModifiers(Modifier.PUBLIC)
            .addParameter(typeT, "defaultValue")
            .addStatement("this.defaultValue = () -> defaultValue")
            .build())
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC);
    List<JavaObjectType> allVisitedTypes = Stream.concat(
            ancestorType.getAllDescendents().stream(),
            ancestorType.isAbstract() ? Stream.empty() : Stream.of(ancestorType)
        )
        .sorted(Comparator.comparing(type -> type.getEntityClass().getSimpleName()))
        .collect(toList());
    allVisitedTypes.stream()
        .filter(type -> !type.isAbstract())
        .forEach(descendent -> {
          ClassName descendentName =
              namespaceTransformer.resolveObject(serviceKind, querySchema, descendent).getJavaPoetClassName();
          String simpleName = descendent.getEntityClass().getSimpleName();
          String parameterName = WordUtils.uncapitalize(simpleName);
          if (SourceVersion.isKeyword(parameterName)) {
            parameterName += "Val";
          }
          MethodSpec visitMethodSpec = MethodSpec.methodBuilder("case" + simpleName)
              .addParameter(descendentName, parameterName)
              .addModifiers(Modifier.PUBLIC, Modifier.ABSTRACT)
              .returns(typeT)
              .build();

          MethodSpec defaultVisitMethodSpec = MethodSpec.methodBuilder("case" + simpleName)
              .addParameter(descendentName, parameterName)
              .addModifiers(Modifier.PUBLIC)
              .addAnnotation(Override.class)
              .addStatement("return defaultValue.get()")
              .returns(typeT)
              .build();
          visitorBuilder.addMethod(visitMethodSpec);
          defaultVisitorBuilder.addMethod(defaultVisitMethodSpec);
        });
    return Tuple3.of(visitorBuilder.build(), defaultVisitorBuilder.build(), parameterizedVisitorName);
  }

  @VisibleForTesting
  MethodSpec generateEqualsSpec(FullyQualifiedTypeName typeName, JavaObjectType type) {
    String paramName = "o";
    String castedName = "that";

    ClassName javaType = typeName.getJavaPoetClassName();
    MethodSpec.Builder result = MethodSpec.methodBuilder("equals")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(boolean.class)
        .addParameter(Object.class, paramName);
    List<JavaApiType.JavaObjectField> fields = type.getFields();

    if (fields.isEmpty() && type.getParent().isEmpty()) {
      result.addStatement("return $N instanceof $T", paramName, javaType);
      return result.build();
    }

    result.addCode(CodeBlock.builder()
        .beginControlFlow("if ($N == this)", paramName)
        .addStatement("return true")
        .endControlFlow()
        .build());
    result.addCode(CodeBlock.builder()
        .beginControlFlow("if ($1N == null || getClass() != $2N.getClass())", paramName, paramName)
        .addStatement("return false")
        .endControlFlow()
        .build());

    result.addStatement("$T $N = ($T) $N", javaType, castedName, javaType, paramName);
    result.addCode("$[return ");
    if (type.getParent().isDefined()) {
      result.addCode("super.equals($N)", castedName);
      if (fields.isEmpty()) {
        result.addCode(";\n$]");
        return result.build();
      } else {
        result.addCode(" &&\n");
      }
    }

    for (int i = 0; i < fields.size(); i++) {
      String fieldName = fields.get(i).getJavaName();
      Object equalsClass = Objects.class;
      if (fields.get(i).getApiType() instanceof JavaApiType.JavaArrayType) {
        equalsClass = Arrays.class;
      }
      if (i == fields.size() - 1) {
        result.addCode("$1T.equals($2L, $3N.$2L);\n$]", equalsClass, fieldName, castedName);
      } else {
        result.addCode("$1T.equals($2L, $3N.$2L) &&\n", equalsClass, fieldName, castedName);
      }
    }

    return result.build();
  }

  @VisibleForTesting
  MethodSpec generateToStringSpec(FullyQualifiedTypeName typeName, JavaObjectType type) {
    MethodSpec.Builder result = MethodSpec.methodBuilder("toString")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(String.class);
    List<JavaApiType.JavaObjectField> fields = type.getFields();

    result.addStatement("$T sb = new $T()", StringBuilder.class, StringBuilder.class);
    result.addStatement("boolean isExactClass = this.getClass().equals($T.class)", typeName.getJavaPoetClassName());
    result.addCode(CodeBlock.builder()
        .beginControlFlow("if (isExactClass)")
        .addStatement("sb.append(\"$T {\\n\")", typeName.getJavaPoetClassName())
        .endControlFlow()
        .build());

    if (type.getParent().isDefined()) {
      result.addStatement("sb.append(super.toString())");
    }

    for (JavaApiType.JavaObjectField field : fields) {
      if (type.getEntityClass().isAnnotationPresent(SecureField.class)) {
        result.addStatement("sb.append(\"  $1N: \").append(\"XXXXXX\").append(\"\\n\")", field.getJavaName());
      } else if (shouldPadNewLines(field.getApiType())) {
        result.addStatement(
            "sb.append(\"  $1N: \").append($1N == null ? \"null\" : $1N.toString().replaceAll(\"\\n\", \"\\n  \")).append(\"\\n\")",
            field.getJavaName());
      } else {
        result.addStatement("sb.append(\"  $1N: \").append($1N).append(\"\\n\")", field.getJavaName());
      }
    }

    result.addCode(CodeBlock.builder()
        .beginControlFlow("if (isExactClass)", typeName.getJavaPoetClassName())
        .addStatement("sb.append(\"}\")")
        .endControlFlow()
        .build());

    result.addStatement("return sb.toString()");

    return result.build();
  }

  private boolean shouldPadNewLines(JavaApiType apiType) {
    return apiType.visit(new JavaApiTypeVisitor.DefaultVisitor<Boolean>(true) {
      @Override
      public Boolean caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        return false;
      }

      @Override
      public Boolean caseEnumType(JavaEnumType enumType) {
        return false;
      }
    });
  }

  @VisibleForTesting
  MethodSpec generateHashCodeSpec(JavaObjectType type) {
    MethodSpec.Builder result = MethodSpec.methodBuilder("hashCode")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(int.class);
    List<JavaApiType.JavaObjectField> fields = type.getFields();

    if (fields.isEmpty()) {
      if (type.getParent().isEmpty()) {
        result.addStatement("return 0");
      } else {
        result.addStatement("return super.hashCode()", Objects.class);
      }
      return result.build();
    }

    result.addCode("$[return $1T.hash(", Objects.class);

    if (type.getParent().isDefined()) {
      result.addCode("super.hashCode(), ");
    }

    for (int i = 0; i < fields.size(); i++) {
      JavaApiType.JavaObjectField field = fields.get(i);
      String fieldName = field.getJavaName();
      if (field.getApiType() instanceof JavaApiType.JavaArrayType) {
        result.addCode("$T.hashCode($N)", Arrays.class, fieldName);
      } else {
        result.addCode("this.$N", fieldName);
      }

      if (i == fields.size() - 1) {
        result.addCode(");\n$]");
      } else {
        result.addCode(", ");
      }
    }

    return result.build();
  }

  @VisibleForTesting
  MethodSpec generateBuilderMethodSpec() {
    return MethodSpec.methodBuilder("builder")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .addStatement("return new Builder()")
        .returns(ClassName.get("", "Builder"))
        .build();
  }

  @VisibleForTesting
  MethodSpec generateCopySpec(JavaObjectType type) {
    CodeBlock.Builder buildSpec = CodeBlock.builder();
    buildSpec.add("return builder()");
    List<JavaApiType.JavaObjectField> fields = getAllTypeFields(type);
    for (JavaApiType.JavaObjectField field : fields) {
      String capitalizedField = StringUtils.capitalize(field.getJavaName());
      if (field.getApiType().isNullable()) {
        buildSpec.add("\n  .with$N(get$N().getOrNull())", capitalizedField, capitalizedField);
      } else {
        buildSpec.add("\n  .with$N(get$N())", capitalizedField, capitalizedField);
      }
    }
    buildSpec.add(";\n");
    return MethodSpec.methodBuilder("copy")
        .addModifiers(Modifier.PUBLIC)
        .addCode(buildSpec.build())
        .returns(ClassName.get("", "Builder"))
        .build();
  }

  @VisibleForTesting
  MethodSpec generateAbstractParentToGlobalMethod(
      FullyQualifiedTypeName className,
      JavaObjectType type,
      JavaApiQuerySchema querySchema,
      JavaApiTypeNamespaceTransformer namespaceTransformer
  ) {
    MethodSpec.Builder methodBuilder = MethodSpec.methodBuilder("toGlobal")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .returns(className.getJavaPoetClassName())
        .addParameter(ClassName.get(WEALTHFRONT_MODELS_PACKAGE, type.getEntityClass().getSimpleName()), "from");

    CodeBlock.Builder codeBlockBuilder = CodeBlock.builder();
    codeBlockBuilder.beginControlFlow("if (from == null)")
        .addStatement("return null")
        .endControlFlow();

    type.getAllDescendents().stream()
        .sorted(comparing(type2 -> type2.getEntityClass().getSimpleName()))
        .forEach(subclass -> {
          ClassName subclassModelClassName =
              ClassName.get(WEALTHFRONT_MODELS_PACKAGE, subclass.getEntityClass().getSimpleName());
          ClassName subclassGlobalClassName =
              namespaceTransformer.resolveObject(JavaJsonEntityGenerator.NoOpServiceKind.class, querySchema, subclass)
                  .getJavaPoetClassName();
          codeBlockBuilder.beginControlFlow("if (from instanceof $T)", subclassModelClassName)
              .addStatement("return $T.toGlobal(($T) from)", subclassGlobalClassName, subclassModelClassName)
              .endControlFlow();
        });

    codeBlockBuilder.addStatement("throw new IllegalArgumentException(\"Unknown type: \" + from)");

    methodBuilder.addCode(codeBlockBuilder.build());
    return methodBuilder.build();
  }

  @VisibleForTesting
  MethodSpec generateAbstractToWFModelsMethod(FullyQualifiedTypeName className) {
    return MethodSpec.methodBuilder("toWFModel")
        .addModifiers(Modifier.PUBLIC, Modifier.ABSTRACT)
        .returns(ClassName.get(WEALTHFRONT_MODELS_PACKAGE, className.getSimpleName()))
        .build();
  }

  @VisibleForTesting
  MethodSpec generateToGlobalSpec(
      FullyQualifiedTypeName className,
      JavaObjectType type,
      JavaApiQuerySchema querySchema,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    ClassName modelClassName = ClassName.get(WEALTHFRONT_MODELS_PACKAGE, type.getEntityClass().getSimpleName());
    String modelName = "model";

    ParameterSpec.Builder parameterSpec = ParameterSpec.builder(modelClassName, modelName);
    CodeBlock.Builder buildSpec = CodeBlock.builder();
    buildSpec.add("return builder()");

    List<JavaApiType.JavaObjectField> fields = getAllTypeFields(type);

    for (JavaApiType.JavaObjectField field : fields) {
      JavaApiType apiType = field.getApiType();
      String capitalizedField = StringUtils.capitalize(field.getJavaName());

      StringBuilder builder = new StringBuilder();
      List<Object> arguments = new ArrayList<>();
      AtomicBoolean canBeSimplified = new AtomicBoolean(false);
      String variableName;
      if (apiType.isNullable()) {
        variableName = "j";
      } else {
        variableName = modelName + ".get" + capitalizedField + "()";
      }
      addFieldToGlobalBuilder(builder, arguments, apiType, variableName, querySchema,
          namespaceTransformer,
          new AtomicInteger(),
          canBeSimplified);
      if (canBeSimplified.get()) {
        buildSpec.add("\n  .with$N($N.get$N())", capitalizedField, modelName, capitalizedField);
      } else {
        buildSpec.add("\n  .with$N(", capitalizedField);
        if (apiType.isNullable()) {
          buildSpec.add("$T.of($N.get$N()).transform(j -> ", Option.class, modelName, capitalizedField);
          buildSpec.add(builder.toString(), arguments.toArray());
          buildSpec.add(").getOrNull()");
        } else {
          buildSpec.add(builder.toString(), arguments.toArray());
        }
        buildSpec.add(")");

      }
    }

    buildSpec.add("\n  .build();\n");

    return MethodSpec.methodBuilder("toGlobal")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .addParameter(parameterSpec.build())
        .addCode(buildSpec.build())
        .returns(className.getJavaPoetClassName())
        .build();
  }

  private void addFieldToGlobalBuilder(
      StringBuilder builder,
      List<Object> arguments,
      JavaApiType type,
      String currentVariable,
      JavaApiQuerySchema querySchema,
      JavaApiTypeNamespaceTransformer namespaceTransformer,
      AtomicInteger listIndex,
      AtomicBoolean canBeSimplfiedToSimple
  ) {
    String iterVar = "i" + listIndex.get();
    type.visit(new JavaApiTypeVisitor<Unit>() {
      @Override
      public Unit caseMapType(JavaApiType.JavaMapType mapType) {
        listIndex.incrementAndGet();

        builder.append("$N.entrySet().stream().collect($T.toMap(Map.Entry::getKey, $N -> ");
        arguments.add(currentVariable);
        arguments.add(Collectors.class);
        arguments.add(iterVar);

        addFieldToGlobalBuilder(builder, arguments, mapType.getApiValueType(), iterVar + ".getValue()",
            querySchema, namespaceTransformer, listIndex, canBeSimplfiedToSimple);

        builder.append("))");

        return Unit.unit;
      }

      @Override
      public Unit caseArrayType(JavaApiType.JavaArrayType arrayType) {
        listIndex.incrementAndGet();
        if (listIndex.get() > 1) {
          throw new RuntimeException("Nested arrays are not supported");
        }

        builder.append("$N.stream().map($N -> ");
        arguments.add(currentVariable);
        arguments.add(iterVar);

        addFieldToGlobalBuilder(builder, arguments, arrayType.getApiValueType(), iterVar, querySchema,
            namespaceTransformer,
            listIndex, canBeSimplfiedToSimple);
        builder.append(").toArray($T[]::new)");
        if (arrayType.getApiValueType().isSimpleType()) {
          canBeSimplfiedToSimple.set(true);
        } else if (arrayType.getApiValueType() instanceof JavaEnumType) {
          arguments.add(namespaceTransformer.resolveEnum(
              JavaJsonEntityGenerator.NoOpServiceKind.class,
              querySchema,
              (JavaEnumType) arrayType.getApiValueType()).getJavaPoetClassName());
        } else {
          arguments.add(namespaceTransformer.resolveObject(
              JavaJsonEntityGenerator.NoOpServiceKind.class,
              querySchema,
              (JavaObjectType) arrayType.getApiValueType()).getJavaPoetClassName());
        }

        return Unit.unit;
      }

      @Override
      public Unit caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        if (isIdentifierType(simpleType.getJavaType())) {
          builder.append("new $T(");
          arguments.add(simpleType.getJavaType());
          builder.append(currentVariable);
          builder.append(")");
        } else {
          canBeSimplfiedToSimple.set(true);
        }
        return Unit.unit;
      }

      @Override
      public Unit caseObjectType(JavaObjectType objectType) {
        builder.append("$T.toGlobal($N)");
        arguments.add(namespaceTransformer.resolveObject(
            JavaJsonEntityGenerator.NoOpServiceKind.class,
            querySchema,
            objectType).getJavaPoetClassName());
        arguments.add(currentVariable);
        return Unit.unit;
      }

      @Override
      public Unit caseOptionType(JavaApiType.JavaOptionType optionType) {
        throw new RuntimeException("Option types are not supported in two-lattes value");
      }

      @Override
      public Unit caseCollectionType(JavaApiType.JavaCollectionType collectionType) {
        listIndex.incrementAndGet();

        builder.append("$N.stream().map($N -> ");
        arguments.add(currentVariable);
        arguments.add(iterVar);

        addFieldToGlobalBuilder(builder, arguments, collectionType.getApiValueType(), iterVar, querySchema,
            namespaceTransformer, listIndex, canBeSimplfiedToSimple);
        builder.append(").collect($T.toList())");
        arguments.add(Collectors.class);

        return Unit.unit;
      }

      @Override
      public Unit caseEnumType(JavaEnumType enumType) {
        builder.append("$T.convert($N, $T.class)");
        arguments.add(Enums.class);
        arguments.add(currentVariable);
        arguments.add(namespaceTransformer.resolveEnum(
            JavaJsonEntityGenerator.NoOpServiceKind.class,
            querySchema,
            enumType).getJavaPoetClassName());
        return Unit.unit;
      }
    });
  }

  private MethodSpec generateToWfModelSpec(
      JavaObjectType type
  ) {
    ClassName modelClassName = ClassName.get(WEALTHFRONT_MODELS_PACKAGE, type.getEntityClass().getSimpleName());
    CodeBlock.Builder buildSpec = CodeBlock.builder();
    buildSpec.add("return $T.with()", modelClassName);

    List<JavaApiType.JavaObjectField> fields = getAllTypeFields(type);
    for (JavaApiType.JavaObjectField field : fields) {
      JavaApiType apiType = field.getApiType();
      String capitalizedField = StringUtils.capitalize(field.getJavaName());

      StringBuilder builder = new StringBuilder();
      List<Object> arguments = new ArrayList<>();
      AtomicBoolean canBeSimplified = new AtomicBoolean(false);
      String variableName;
      if (apiType.isNullable()) {
        variableName = "j";
      } else {
        variableName = "this.get" + capitalizedField + "()";
      }
      addFieldToWfModelBuilder(builder, arguments, apiType, variableName,
          new AtomicInteger(),
          canBeSimplified);
      if (canBeSimplified.get()) {
        buildSpec.add("\n  .$N(this.get$N()", field.getJavaName(), capitalizedField);
        if (apiType.isNullable()) {
          buildSpec.add(".getOrNull()");
        }
      } else {
        buildSpec.add("\n  .$N(", field.getJavaName());
        if (apiType.isNullable()) {
          buildSpec.add("this.get$N().transform(j -> ", capitalizedField);
          buildSpec.add(builder.toString(), arguments.toArray());
          buildSpec.add(").getOrNull()");
        } else {
          buildSpec.add(builder.toString(), arguments.toArray());
        }
      }
      buildSpec.add(")");
    }

    buildSpec.add("\n  .build();\n");

    MethodSpec.Builder builder = MethodSpec.methodBuilder("toWFModel")
        .addModifiers(Modifier.PUBLIC)
        .returns(modelClassName)
        .addCode(buildSpec.build());

    if (type.getParent().isDefined()) {
      builder.addAnnotation(Override.class);
    }

    return builder.build();
  }

  private void addFieldToWfModelBuilder(
      StringBuilder builder,
      List<Object> arguments,
      JavaApiType type,
      String currentVariable,
      AtomicInteger listIndex,
      AtomicBoolean canBeSimplfiedToSimple
  ) {
    String iterVar = "i" + listIndex.get();
    type.visit(new JavaApiTypeVisitor<Unit>() {
      @Override
      public Unit caseMapType(JavaApiType.JavaMapType mapType) {
        listIndex.incrementAndGet();

        builder.append("$N.entrySet().stream().collect($T.toMap(Map.Entry::getKey, $N -> ");
        arguments.add(currentVariable);
        arguments.add(Collectors.class);
        arguments.add(iterVar);

        addFieldToWfModelBuilder(builder, arguments, mapType.getApiValueType(), iterVar + ".getValue()",
            listIndex, canBeSimplfiedToSimple);
        builder.append("))");

        return Unit.unit;
      }

      @Override
      public Unit caseArrayType(JavaApiType.JavaArrayType arrayType) {
        listIndex.incrementAndGet();
        if (listIndex.get() > 1) {
          throw new RuntimeException("Nested arrays are not supported");
        }

        builder.append("$T.stream($N).map($N -> ");
        arguments.add(Arrays.class);
        arguments.add(currentVariable);
        arguments.add(iterVar);

        addFieldToWfModelBuilder(builder, arguments, arrayType.getApiValueType(), iterVar,
            listIndex, canBeSimplfiedToSimple);
        builder.append(").collect($T.toList())");
        arguments.add(Collectors.class);
        if (arrayType.getApiValueType().isSimpleType()) {
          canBeSimplfiedToSimple.set(true);
        }

        return Unit.unit;
      }

      @Override
      public Unit caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        if (isIdentifierType(simpleType.getJavaType())) {
          builder.append(currentVariable);
          builder.append(".getId()");
          if (simpleType.getSerializedType() == JavaApiType.JavaSimpleType.SerializedType.INTEGER) {
            builder.append(".intValue()");
          }
        } else {
          canBeSimplfiedToSimple.set(true);
        }
        return Unit.unit;
      }

      @Override
      public Unit caseObjectType(JavaObjectType objectType) {
        builder.append("$N.toWFModel()");
        arguments.add(currentVariable);
        return Unit.unit;
      }

      @Override
      public Unit caseOptionType(JavaApiType.JavaOptionType optionType) {
        throw new RuntimeException("Option types are not supported in two-lattes value");
      }

      @Override
      public Unit caseCollectionType(JavaApiType.JavaCollectionType collectionType) {
        listIndex.incrementAndGet();

        builder.append("$N.stream().map($N -> ");
        arguments.add(currentVariable);
        arguments.add(iterVar);

        addFieldToWfModelBuilder(builder, arguments, collectionType.getApiValueType(), iterVar, listIndex,
            canBeSimplfiedToSimple);
        builder.append(").collect($T.toList())");
        arguments.add(Collectors.class);

        return Unit.unit;
      }

      @Override
      public Unit caseEnumType(JavaEnumType enumType) {
        builder.append("$T.convert($N, $T.class)");
        arguments.add(Enums.class);
        arguments.add(currentVariable);
        arguments.add(ClassName.get(WEALTHFRONT_MODELS_PACKAGE, enumType.getEnumClass().getSimpleName()));
        return Unit.unit;
      }
    });
  }

  private boolean isIdentifierType(Type javaType) {
    return javaType instanceof Class && Identifier.class.isAssignableFrom((Class<?>) javaType)
        || javaType instanceof ParameterizedType &&
        Identifier.class.isAssignableFrom((Class<?>) ((ParameterizedType) javaType).getRawType());
  }

  @VisibleForTesting
  TypeSpec generateBuilderSpec(
      Class<? extends ServiceKind> serviceKind,
      JavaApiQuerySchema querySchema,
      FullyQualifiedTypeName className,
      JavaObjectType type,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    TypeSpec.Builder builderClassBuilder = TypeSpec.classBuilder("Builder")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC);

    Option<ClassName> maybeBuilderParentClassName =
        getMaybeNonAbstractParentClassName(serviceKind, querySchema, type, namespaceTransformer);

    maybeBuilderParentClassName.ifDefined(parentClassName ->
        builderClassBuilder.superclass(ClassName.get("", parentClassName.simpleName() + ".Builder")));

    List<JavaApiType.JavaObjectField> fields = getAllTypeFields(type);

    for (JavaApiType.JavaObjectField field : fields) {
      Type javaType = field.getJavaType();
      Class<?> rawType = TypeUtils.getRawType(javaType, null);
      TypeName typeName = rewriteJavaType(field.getApiType(), serviceKind, querySchema, namespaceTransformer);
      CodeBlock defaultInitializer = type.getSource() == LENDING ?
          CodeBlock.builder().add("null").build() : getDefaultInitializer(rawType);
      FieldSpec fieldSpec = FieldSpec.builder(getBoxedType(typeName), field.getJavaName())
          .addAnnotation(getNullableAnnotation(field.getApiType().isNullable()))
          .addModifiers(Modifier.PRIVATE)
          .initializer(defaultInitializer)
          .build();
      builderClassBuilder.addField(fieldSpec);

      ParameterSpec.Builder parameterSpec = ParameterSpec.builder(typeName, field.getJavaName());

      if (!isPrimitiveType(javaType)) {
        parameterSpec.addAnnotation(getNullableAnnotation(field.getApiType().isNullable()));
      }

      MethodSpec setter = MethodSpec.methodBuilder("with" + StringUtils.capitalize(field.getJavaName()))
          .addModifiers(Modifier.PUBLIC)
          .returns(ClassName.get("", "Builder"))
          .addParameter(parameterSpec.build())
          .addStatement("this.$N = $N", field.getJavaName(), field.getJavaName())
          .addStatement("return this")
          .build();
      builderClassBuilder.addMethod(setter);
    }

    ClassName javaType = className.getJavaPoetClassName();
    CodeBlock.Builder constructorCall = CodeBlock.builder();
    CodeBlock.Builder constructFieldsWithSetters = CodeBlock.builder();
    boolean hasPopulatedConstructor = getPopulatedConstructorSources().contains(type.getSource());

    constructorCall.add("$T obj1 = new $T(", javaType, javaType);
    if (hasPopulatedConstructor) {
      buildBuilderConstructorParameters(constructorCall, fields);
    } else {
      buildBuilderFieldsWithSetters(constructFieldsWithSetters, fields);
    }
    constructorCall.add(");\n");

    MethodSpec.Builder buildMethod = MethodSpec.methodBuilder("build")
        .addModifiers(Modifier.PUBLIC)
        .returns(javaType)
        .addCode(constructorCall.build())
        .addCode(constructFieldsWithSetters.build());

    if (type.getSource() == MODELS) {
      buildMethod.addStatement("return obj1");
    } else {
      buildMethod
          .addStatement("obj1.validate()")
          .addStatement("return obj1");
    }

    MethodSpec buildForTestingMethod = MethodSpec.methodBuilder("buildForTesting")
        .addModifiers(Modifier.PUBLIC)
        .returns(javaType)
        .addCode(constructorCall.build())
        .addAnnotation(VisibleForTesting.class)
        .addStatement("return obj1")
        .build();
    builderClassBuilder.addMethod(buildMethod.build());
    builderClassBuilder.addMethod(buildForTestingMethod);

    return builderClassBuilder.build();
  }

  private void buildBuilderConstructorParameters(
      CodeBlock.Builder constructorCall, List<JavaApiType.JavaObjectField> fields) {
    for (int i = 0; i < fields.size(); i++) {
      String fieldName = fields.get(i).getJavaName();
      if (i == fields.size() - 1) {
        constructorCall.add("$N", fieldName);
      } else {
        constructorCall.add("$N, ", fieldName);
      }
    }
  }

  private void buildBuilderFieldsWithSetters(
      CodeBlock.Builder constructFieldsWithSetters, List<JavaApiType.JavaObjectField> fields) {
    for (int i = 0; i < fields.size(); i++) {
      String fieldName = fields.get(i).getJavaName();
      constructFieldsWithSetters.add("obj1.set$N($N);\n", StringUtils.capitalize(fieldName), fieldName);
    }
  }

  @VisibleForTesting
  static List<JavaApiType.JavaObjectField> getAllTypeFields(JavaObjectType type) {
    return Streams.concat(type.getParent()
                .transform(JavaApiCodeGeneratorImpl::getAllTypeFields)
                .getOrElse(emptyList())
                .stream(),
            type.getFields().stream())
        .collect(toList());
  }

  private Class<?> getNullableAnnotation(boolean nullable) {
    if (nullable) {
      return Nullable.class;
    } else {
      return Nonnull.class;
    }
  }

  private CodeBlock getDefaultInitializer(Class<?> clazz) {
    CodeBlock.Builder initializerBuilder = CodeBlock.builder();
    if (List.class.isAssignableFrom(clazz)) {
      initializerBuilder.add("new $T<>()", ArrayList.class);
    } else if (Set.class.isAssignableFrom(clazz)) {
      initializerBuilder.add("new $T<>()", HashSet.class);
    } else if (Queue.class.isAssignableFrom(clazz)) {
      initializerBuilder.add("new $T<>()", LinkedList.class);
    } else if (Deque.class.isAssignableFrom(clazz)) {
      initializerBuilder.add("new $T<>()", ArrayDeque.class);
    } else if (SortedMap.class.isAssignableFrom(clazz)) {
      initializerBuilder.add("new $T<>()", TreeMap.class);
    } else if (Map.class.isAssignableFrom(clazz)) {
      initializerBuilder.add("new $T<>()", HashMap.class);
    } else {
      initializerBuilder.add("null");
    }
    return initializerBuilder.build();
  }

  private boolean isPrimitiveType(Type type) {
    TypeName typeName = TypeName.get(type);
    return typeName.isPrimitive();
  }

  private TypeName getBoxedType(TypeName typeName) {
    if (typeName.isPrimitive()) {
      return typeName.box();
    }
    return typeName;
  }

  @VisibleForTesting
  static TypeName rewriteJavaType(
      JavaApiType apiType,
      Class<? extends ServiceKind> serviceKind,
      JavaApiQuerySchema querySchema,
      JavaApiTypeNamespaceTransformer namespaceTransformer) {
    return apiType.visit(new JavaApiTypeVisitor<TypeName>() {
      @Override
      public TypeName caseMapType(JavaApiType.JavaMapType mapType) {
        Class<?> rawType = TypeUtils.getRawType(mapType.getJavaType(), null);
        TypeName keyType = rewriteJavaType(mapType.getApiKeyType(), serviceKind, querySchema, namespaceTransformer);
        TypeName valueType = rewriteJavaType(mapType.getApiValueType(), serviceKind, querySchema, namespaceTransformer);
        return ParameterizedTypeName.get(ClassName.get(rawType), keyType, valueType);
      }

      @Override
      public TypeName caseArrayType(JavaApiType.JavaArrayType arrayType) {
        TypeName valueName =
            rewriteJavaType(arrayType.getApiValueType(), serviceKind, querySchema, namespaceTransformer);
        return ArrayTypeName.of(valueName);
      }

      @Override
      public TypeName caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        return TypeName.get(simpleType.getJavaType());
      }

      @Override
      public TypeName caseObjectType(JavaObjectType objectType) {
        return namespaceTransformer.resolveObject(serviceKind, querySchema, objectType).getJavaPoetClassName();
      }

      @Override
      public TypeName caseOptionType(JavaApiType.JavaOptionType optionType) {
        TypeName valueTypeName =
            rewriteJavaType(optionType.getApiValueType(), serviceKind, querySchema, namespaceTransformer);
        return ParameterizedTypeName.get(ClassName.get(Option.class), valueTypeName);
      }

      @Override
      public TypeName caseCollectionType(JavaApiType.JavaCollectionType collectionType) {
        TypeName valueTypeName =
            rewriteJavaType(collectionType.getApiValueType(), serviceKind, querySchema, namespaceTransformer);
        Class<?> rawType = TypeUtils.getRawType(collectionType.getJavaType(), null);
        return ParameterizedTypeName.get(ClassName.get(rawType), valueTypeName);
      }

      @Override
      public TypeName caseEnumType(JavaEnumType enumType) {
        FullyQualifiedTypeName fqn = namespaceTransformer.resolveEnum(serviceKind, querySchema, enumType);
        return fqn.getJavaPoetClassName();
      }
    });
  }

  private boolean hasNoParentOrParentIsNonPolymorphic(JavaObjectType objectType) {
    return objectType.getParent().isEmptyOr(parent -> parent.getDiscriminatorName().isEmpty());
  }

  @VisibleForTesting
  static class TypeWithName {

    private final FullyQualifiedTypeName name;
    private final TypeSpec.Builder spec;

    TypeWithName(FullyQualifiedTypeName name, TypeSpec.Builder spec) {
      this.name = name;
      this.spec = spec;
    }

    @Override
    public String toString() {
      return MoreObjects.toStringHelper(this)
          .add("name", name)
          .add("spec", spec)
          .toString();
    }

  }

  static class GlobalTypeTempQuery extends AbstractQuery<Unit> {

    @Override
    public Unit process() {
      return Unit.unit;
    }

  }

}
