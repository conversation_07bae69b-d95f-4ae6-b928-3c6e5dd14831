package com.kaching.util.cobol;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.collect.Lists.newArrayList;

import java.util.List;

class AST {

  interface TreeVisitor<T> {

    T caseCopybook(AST.Copybook copybook);

    T casePicture(AST.Picture picture);

    T caseCondition(AST.Condition condition);

    T caseGroup(AST.Group group);

    T caseRedefiningGroup(AST.RedefiningGroup group);
  }

  interface Tree {

    <T> T visit(TreeVisitor<T> visitor);

  }

  interface EntryVisitor<T> {

    T caseCopybook(AST.Copybook copybook);

    T casePicture(AST.Picture picture);

    T caseGroup(AST.Group group);

    T caseRedefiningGroup(AST.RedefiningGroup group);
  }

  interface Entry extends Tree {

    <T> T visit(EntryVisitor<T> visitor);

  }

  static class Copybook implements AST.Entry {

    final List<Entry> entries;

    Copybook(List<Entry> entries) {
      this.entries = entries;
    }

    @Override
    public <T> T visit(TreeVisitor<T> visitor) {
      return visitor.caseCopybook(this);
    }

    @Override
    public <T> T visit(EntryVisitor<T> visitor) {
      return visitor.caseCopybook(this);
    }

  }

  static class Picture implements AST.Entry {

    final String name;
    final DataType type;
    final String value;
    final List<Condition> conditions = newArrayList();

    List<Integer> offsets = newArrayList(); // set during semantic analysis

    Picture(String name, DataType type) {
      this(name, type, null);
    }

    Picture(String name, DataType type, String value) {
      this.name = name;
      this.type = type;
      this.value = value;
    }

    void addCondition(AST.Condition condition) {
      conditions.add(condition);
    }

    int getLength() {
      return type.getLength();
    }

    @Override
    public <T> T visit(TreeVisitor<T> visitor) {
      return visitor.casePicture(this);
    }

    @Override
    public <T> T visit(EntryVisitor<T> visitor) {
      return visitor.casePicture(this);
    }

  }

  static class Condition implements AST.Tree {

    final String name;
    final String literal;

    Condition(String name, String literal) {
      this.name = name;
      this.literal = literal;
    }

    @Override
    public <T> T visit(TreeVisitor<T> visitor) {
      return visitor.caseCondition(this);
    }

  }

  static class Group implements AST.Entry {

    final String name;
    final int occurs;
    final List<Entry> entries = newArrayList();

    List<Integer> offsets = newArrayList(); // set during semantic analysis

    Group(String name, int occurs) {
      checkArgument(occurs > 0);
      this.name = name;
      this.occurs = occurs;
    }

    void addEntry(AST.Entry entry) {
      entries.add(entry);
    }

    @Override
    public <T> T visit(TreeVisitor<T> visitor) {
      return visitor.caseGroup(this);
    }

    @Override
    public <T> T visit(EntryVisitor<T> visitor) {
      return visitor.caseGroup(this);
    }

  }

  static class RedefiningGroup extends AST.Group {

    final String redefined;

    AST.Group referenceToRedefined; // set during semantic analysis

    RedefiningGroup(String name, String redefined) {
      super(name, 1);
      this.redefined = redefined;
    }

    @Override
    public <T> T visit(TreeVisitor<T> visitor) {
      return visitor.caseRedefiningGroup(this);
    }

    @Override
    public <T> T visit(EntryVisitor<T> visitor) {
      return visitor.caseRedefiningGroup(this);
    }

  }

}
