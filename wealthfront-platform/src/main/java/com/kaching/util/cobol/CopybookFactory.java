package com.kaching.util.cobol;

import static com.kaching.util.io.Closeables.closeQuietly;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;

import com.google.common.io.ByteSource;

public class CopybookFactory {

  public static Copybook create(InputStream layoutStream, ByteSource source) {
    return create(
        new BufferedReader(new InputStreamReader(layoutStream)),
        source);
  }

  public static Copybook create(Reader layoutReader, ByteSource source) {
    try {
      return new CopybookGenerator().generate(
          new CopybookAnalyzer().analyze(
              new CopybookParser().parse(
                  new CopybookTokenizer().tokenize(layoutReader))),
          source);
    } finally {
      closeQuietly(layoutReader);
    }
  }
}
