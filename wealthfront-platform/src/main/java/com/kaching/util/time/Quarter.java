package com.kaching.util.time;

import static com.google.common.base.Preconditions.checkArgument;

import org.joda.time.LocalDate;

import com.kaching.platform.common.values.NumberedValue;

public enum Quarter implements NumberedValue {
  Q1(0, 1),
  Q2(1, 4),
  Q3(2, 7),
  Q4(3, 10);

  final int number;
  final int startMonth;

  Quarter(int number, int startMonth) {
    this.number = number;
    this.startMonth = startMonth;
  }

  @Override
  public int getNumber() {
    return this.number;
  }

  public LocalDate getStart(int year) {
    return new LocalDate(year, startMonth, 1);
  }

  public int getStartMonth() {
    return startMonth;
  }

  public LocalDate getEnd(int year) {
    return getStart(year).plusMonths(3).minusDays(1);
  }

  public static Quarter forMonth(int month) {
    checkArgument(month >= 1 && month <= 12, "Month must be between 1 and 12, inclusive.");
    switch ((month - 1) / 3) {
      case 0:
        return Quarter.Q1;
      case 1:
        return Quarter.Q2;
      case 2:
        return Quarter.Q3;
      case 3:
        return Quarter.Q4;
      default:
        throw new RuntimeException("Not a real month, somehow.");
    }
  }

  public Quarter next() {
    switch (this) {
      case Q1:
        return Q2;
      case Q2:
        return Q3;
      case Q3:
        return Q4;
      case Q4:
        return Q1;
      default: break;
    }
    throw new RuntimeException("You added more than four quarters, somehow.");
  }

  public Quarter previous() {
    switch (this) {
      case Q1:
        return Q4;
      case Q2:
        return Q1;
      case Q3:
        return Q2;
      case Q4:
        return Q3;
      default: break;
    }
    throw new RuntimeException("You added more than four quarters, somehow.");
  }

}
