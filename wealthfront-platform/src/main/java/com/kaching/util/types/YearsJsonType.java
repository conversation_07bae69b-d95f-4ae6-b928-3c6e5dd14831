package com.kaching.util.types;

import org.joda.time.Years;

import com.twolattes.json.Json;
import com.twolattes.json.types.NullSafeType;

public class YearsJsonType extends NullSafeType<Years, Json.Number> {

  @Override
  protected Json.Number nullSafeMarshall(Years years) {
    return Json.number(years.getYears());
  }

  @Override
  protected Years nullSafeUnmarshall(Json.Number number) {
    return Years.years(number.getNumber().intValue());
  }

}
