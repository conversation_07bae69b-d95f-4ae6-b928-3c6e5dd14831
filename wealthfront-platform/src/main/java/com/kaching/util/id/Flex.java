package com.kaching.util.id;

import com.google.common.base.CharMatcher;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(Flex.Converter.class)
@MarshalledBy(Flex.JsonType.class)
public class Flex<T extends Identifier<Long>> extends AbstractIdentifier<String> {
  
  public Flex(String id) {
    super(id);
  }
  
  public Flex(long id) {
    super(Long.toString(id));
  }
  
  public <R> R visit(Visitor<R, T> visitor) {
    if (isInternalId()) {
      return visitor.caseInternal(Long.parseLong(getId()));
    }
    return visitor.caseExternal(new External<>(getId()));
  }
  
  public interface Visitor<R, T extends Identifier<Long>> {
    
    R caseInternal(long id);
    
    R caseExternal(External<T> id);
    
  }

  public boolean isInternalId() {
    return CharMatcher.inRange('0', '9').matchesAllOf(getId());
  }
  
  public static class Converter extends NullHandlingConverter<Flex<?>> {

    @Override
    public Flex<?> fromNonNullableString(String representation) {
      return new Flex<>(representation);
    }

    @Override
    public String nonNullableToString(Flex<?> value) {
      return value.getId();
    }
    
  } 
  
  public static class JsonType extends NullSafeType<Flex<?>, Json.String> {

    @Override
    public Json.String nullSafeMarshall(Flex<?> entity) {
      return Json.string(entity.getId());
    }

    @Override
    public Flex<?> nullSafeUnmarshall(Json.String object) {
      return new Flex<>(object.getString());
    }
    
  }
  
}
