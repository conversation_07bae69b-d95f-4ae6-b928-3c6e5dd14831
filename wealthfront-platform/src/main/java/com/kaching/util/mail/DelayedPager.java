package com.kaching.util.mail;

import com.google.inject.ImplementedBy;
import com.kaching.platform.common.Option;
import com.kaching.util.schedule.TimeSchedule;

@ImplementedBy(DelayedPagerImpl.class)
public interface DelayedPager {

  Option<QueuedPageId> alert(String subject, String message, Pager.Device device, TimeSchedule schedule);

  Option<QueuedPageId> alert(String subject, String message, Throwable t, Pager.Device device, TimeSchedule schedule);

}
