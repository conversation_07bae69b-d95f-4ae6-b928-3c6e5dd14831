package com.kaching.util.mail;

import javax.mail.Session;

import org.apache.commons.mail.Email;
import org.apache.commons.mail.EmailException;

import com.google.inject.Inject;
import com.kaching.entities.EmailAddress;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.multicolo.MultiColoStatusProvider;

public abstract class DefaultEmailSender implements EmailSender {

  static final Log log = Log.getLog(DefaultEmailSender.class);

  @Inject MultiColoStatusProvider multiColoStatusProvider;

  @Override
  public String send(TestableHtmlEmail email) throws EmailException {
    if (multiColoStatusProvider.getStatus() == MultiColoStatusProvider.ColoStatus.MOCK_FAILOVER_FAKE_MASTER) {
      throw new EmailException("Shouldn't send emails in mock failover master");
    }

    String token = sendWithCustomBounceAddr(email, new EmailAddress(email.getFromAddress().getAddress()));
    log.info("mail sent -- MessageID: %s", token);
    return token;
  }

  @Override
  public String send(Email email) throws EmailException {
    if (multiColoStatusProvider.getStatus() == MultiColoStatusProvider.ColoStatus.MOCK_FAILOVER_FAKE_MASTER) {
      throw new EmailException("Shouldn't send emails in mock failover master");
    }

    return sendApacheEmailWithCustomBounceAddr(
        email, new EmailAddress(email.getFromAddress().getAddress()));
  }

  protected String sendWithCustomBounceAddr(TestableHtmlEmail email, EmailAddress address)
      throws EmailException {
    return sendApacheEmailWithCustomBounceAddr(email, address);
  }

  protected abstract Session getSession(EmailAddress address);

  private String sendApacheEmailWithCustomBounceAddr(Email email, EmailAddress address)
      throws EmailException {
    email.setMailSession(getSession(address));
    return email.send();
  }
}
