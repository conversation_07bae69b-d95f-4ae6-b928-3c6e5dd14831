package com.kaching.util.push;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;

import com.google.common.base.Function;
import com.kaching.platform.common.Option;
import com.kaching.util.functional.Result;
import com.kaching.util.http.ResultBasedHttpClient;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

@Entity
public class FirebaseResponse {

  public static final Marshaller<FirebaseResponse> MARSHALLER = createEntityMarshaller(FirebaseResponse.class);

  @Value(name = "multicast_id") private Long multicastId;
  @Value private Long success;
  @Value private Long failure;
  @Value(name = "canonical_ids") private Long canonicalIds;
  @Value(optional = true) private Results[] results;

  public static Function<ResultBasedHttpClient.Response, Result<FirebaseResponse>> responseToFirebaseResponse() {
    return response -> Result.compute(() -> MARSHALLER.unmarshall(Json.fromString(response.getBody())));
  }

  public Long getCanonicalIds() {
    return canonicalIds;
  }

  public void setCanonicalIds(Long canonicalIds) {
    this.canonicalIds = canonicalIds;
  }

  public Long getFailure() {
    return failure;
  }

  public void setFailure(Long failure) {
    this.failure = failure;
  }

  public Long getMulticastId() {
    return multicastId;
  }

  public void setMulticastId(Long multicastId) {
    this.multicastId = multicastId;
  }

  public Long getSuccess() {
    return success;
  }

  public Option<Results[]> getResults() {
    return Option.of(results);
  }

  public void setResults(Results[] results) {
    this.results = results;
  }

  public boolean isSuccess() {
    return success > 0 && failure == 0;
  }

  public void setSuccess(Long success) {
    this.success = success;
  }

  @Entity
  public static class Results {

    @Value(optional = true, name = "message_id") private String messageId;
    @Value(optional = true, name = "registration_id") private String registrationId;
    @Value(optional = true) private String error;

    public Option<String> getError() {
      return Option.of(error);
    }

    public void setError(String error) {
      this.error = error;
    }

    public Option<String> getMessageId() {
      return Option.of(messageId);
    }

    public void setMessageId(String messageId) {
      this.messageId = messageId;
    }

    public Option<String> getRegistrationId() {
      return Option.of(registrationId);
    }

    public void setRegistrationId(String registrationId) {
      this.registrationId = registrationId;
    }
  }
}
