package com.kaching.util.schedule;

import static com.wealthfront.util.time.DateTimeZones.toLocalDate;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;

import com.google.common.base.Preconditions;
import com.google.common.collect.Range;
import com.wealthfront.util.objects.DerivedMethods;

public class LocalTimeRangeOfDay {

  private static final DerivedMethods<LocalTimeRangeOfDay> DERIVED_METHODS =
      new DerivedMethods<>(LocalTimeRangeOfDay.class);

  private static void validate(LocalTime startTime, LocalTime endTime, DateTimeZone timeZone) {
    Preconditions.checkNotNull(startTime, "startTime is null");
    Preconditions.checkNotNull(endTime, "endTime is null");
    Preconditions.checkState(!startTime.isAfter(endTime), "start time %s is after end time %s", startTime, endTime);
    Preconditions.checkNotNull(timeZone, "timeZone is null");
  }

  private final LocalTime startTime;
  private final LocalTime endTime;
  private final DateTimeZone timeZone;

  public LocalTimeRangeOfDay(LocalTime startTime, LocalTime endTime, DateTimeZone timeZone) {
    validate(startTime, endTime, timeZone);
    this.startTime = startTime;
    this.endTime = endTime;
    this.timeZone = timeZone;
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

  @Override
  public boolean equals(Object obj) {
    return DERIVED_METHODS.equals(this, obj);
  }

  @Override
  public String toString() {
    return DERIVED_METHODS.toString(this);
  }

  public LocalTime getStartTime() {
    return startTime;
  }

  public LocalTime getEndTime() {
    return endTime;
  }

  public DateTimeZone getTimeZone() {
    return timeZone;
  }

  public boolean containsInstant(DateTime instant) {
    return validRange(toLocalDate(instant, timeZone)).contains(instant);
  }

  public Range<DateTime> validRange(LocalDate date) {
    return Range.closed(date.toDateTime(startTime, timeZone), date.toDateTime(endTime, timeZone));
  }

}
