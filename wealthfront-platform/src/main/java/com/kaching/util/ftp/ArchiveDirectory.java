package com.kaching.util.ftp;

import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.security.Ciphers.aes;

import java.io.File;
import java.io.IOException;
import java.net.SocketException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.kaching.platform.common.logging.Log;
import com.kaching.security.Cipher;
import com.kaching.util.Base64;

public class ArchiveDirectory {

  private static final Log log = getLog(ArchiveDirectory.class);

  private final String localPath;
  private final String toPath;
  private final FtpsClient ftpTo;
  private Predicate<String> inclusionPredicate = Predicates.alwaysTrue();

  public ArchiveDirectory(String localPath, FtpsClient ftpTo, String toPath) {
    this.localPath = localPath;
    this.ftpTo = ftpTo;
    this.toPath = toPath;
  }

  public ArchiveDirectory(String localPath, FtpsClient ftpTo, String toPath, Predicate<String> inclusionPredicate) {
    this(localPath, ftpTo, toPath);
    this.inclusionPredicate = inclusionPredicate;
  }

  public Boolean process() {
    try {
      return processUnhecked();
    } catch (NoSuchAlgorithmException e) {
      log.warn(e);
    } catch (SocketException e) {
      log.warn(e);
    } catch (IOException e) {
      log.warn(e);
    }
    return null;
  }

  public Boolean processUnhecked() throws NoSuchAlgorithmException, SocketException, IOException {
    ftpTo.open();
    Set<String> toBackup = filesWeShouldBackup();

    for (String file : toBackup) {
      log.debug("Backing up " + file + "\n");
      backupFile(file);
    }

    return true;
  }

  public void backupFile(String file) throws IOException {
    byte[] dataToBackup = readFileFromDisk(file);
    byte[] encrypted = encryptForBackup(dataToBackup);
    ftpTo.uploadBinary(toPath + "/" + file, encrypted);
  }

  public byte[] readFileFromDisk(String file) throws IOException {
    return FileUtils.readFileToByteArray(new File(Joiner.on(File.separator).join(localPath, file)));
  }

  public byte[] encryptForBackup(byte[] dataToBackup) {
    Cipher c = aes(Base64.decode("IVQD7kL59nyDRQxESwvESXePyX95OEY2UdlYgk+GZC0="), new SecureRandom());
    return c.encryptOntoBytes(dataToBackup);
  }

  public Set<String> filesWeShouldBackup() throws IOException, NoSuchAlgorithmException {
    Set<String> fromSet = new HashSet<>(listLocalDirectory(getLocalDir()));
    Set<String> toSet = ftpTo.listDirectory(toPath);

    fromSet.removeAll(toSet);
    return fromSet;
  }

  @VisibleForTesting
  File getLocalDir() {
    return new File(localPath);
  }

  public Set<String> listLocalDirectory(File localDir) {
    String[] contents = localDir.list();
    if (contents == null) {
      return Collections.emptySet();
    }
    return Arrays.stream(contents)
        .filter(input -> !(new File(input)).isDirectory())
        .filter(inclusionPredicate)
        .collect(Collectors.toSet());
  }
}
