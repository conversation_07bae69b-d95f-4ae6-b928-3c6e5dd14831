package com.kaching.util;

public class Path {

  public static String getDirectoryFromPath(String path) {
    String dirPath = "";
    if (path.lastIndexOf("/") != -1) {
      dirPath = path.substring(0, path.lastIndexOf("/"));
    }
    return dirPath;
  }

  public static String getFilenameFromPath(String path) {
    String filename = path;
    if (path.lastIndexOf("/") != -1) {
      filename = path.concat(" ").substring(path.lastIndexOf("/") + 1);
    }
    return filename.trim();
  }
}