package com.kaching.security.securitymanager;

import static com.google.common.base.Preconditions.checkState;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;

import java.io.FilePermission;
import java.net.SocketPermission;
import java.security.Permission;
import java.util.ArrayList;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;
import java.util.function.Supplier;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.kaching.platform.common.logging.Log;

public class FakeServiceSecurityManager extends SecurityManager {

  private static final Log LOG = Log.getLog(FakeServiceSecurityManager.class);

  private final ThreadLocal<Map<Permission, Integer>> contextPermissions = ThreadLocal.withInitial(IdentityHashMap::new);

  private final List<Predicate<SocketPermission>> socketPredicates;
  private final List<Predicate<FilePermission>> filePredicates;

  public FakeServiceSecurityManager(
      List<Predicate<SocketPermission>> socketPredicates,
      List<Predicate<FilePermission>> filePredicates) {
    this.socketPredicates = ImmutableList.copyOf(socketPredicates);
    this.filePredicates = ImmutableList.copyOf(filePredicates);
  }

  public static Builder builder() {
    return new Builder();
  }

  @Override
  public void checkPermission(Permission perm, Object context) {
    checkPermission(perm);
  }

  @Override
  public void checkPermission(Permission perm) {
    if (getThreadLocalPermissions().keySet().stream().anyMatch(p -> p.implies(perm))) {
      LOG.debug("Matches context-aware permission %s", perm);
      return;
    }
    if (perm instanceof SocketPermission) {
      LOG.debug("Checking socket permission %s", perm);
      SocketPermission socketPermission = (SocketPermission) perm;
      if (socketPredicates.stream().noneMatch(p -> p.test(socketPermission))) {
        LOG.error("Rejecting socket permission %s", perm);
        throw new SecurityException("Denying socket permission: " + perm);
      }
    } else if (perm instanceof FilePermission) {
      LOG.debug("Checking file permission %s", perm);
      FilePermission filePermission = (FilePermission) perm;
      if (filePredicates.stream().noneMatch(p -> p.test(filePermission))) {
        LOG.error("Rejecting file permission %s", perm);
        throw new SecurityException("Denying file permission: " + perm);
      }
    }
  }

  @VisibleForTesting
  Map<Permission, Integer> getThreadLocalPermissions() {
    return contextPermissions.get();
  }

  public PermissionContext executeWithPermission(Permission... permissions) {
    return executeWithPermission(permissions != null ? asList(permissions) : emptyList());
  }

  public PermissionContext executeWithPermission(List<Permission> permissions) {
    return new PermissionContext(permissions);
  }

  public boolean installSecurityManager() {
    SecurityManager existing = System.getSecurityManager();
    if (existing != null) {
      LOG.error("System is using existing security manager: ", existing.getClass());
      return false;
    }
    try {
      System.setSecurityManager(this);
      return true;
    } catch (SecurityException e) {
      LOG.error(e, "Unable to install security manager");
      return false;
    }
  }

  public static class Builder {

    private final List<Predicate<SocketPermission>> socketPredicates = new ArrayList<>();
    private final List<Predicate<FilePermission>> filePredicates = new ArrayList<>();

    public Builder socketPermission(Predicate<SocketPermission> perm) {
      socketPredicates.add(perm);
      return this;
    }

    public Builder filePermission(Predicate<FilePermission> perm) {
      filePredicates.add(perm);
      return this;
    }

    public FakeServiceSecurityManager build() {
      return new FakeServiceSecurityManager(socketPredicates, filePredicates);
    }

  }

  public class PermissionContext {

    private final AtomicBoolean entered = new AtomicBoolean(false);
    private final List<Permission> permissions;

    private PermissionContext(List<Permission> permissions) {
      this.permissions = permissions != null ? permissions : emptyList();
    }

    private void enter() {
      checkState(entered.compareAndSet(false, true));
      Map<Permission, Integer> context = getThreadLocalPermissions();
      for (Permission perm : permissions) {
        context.put(perm, context.getOrDefault(perm, 0) + 1);
      }
    }

    private void exit() {
      Map<Permission, Integer> context = getThreadLocalPermissions();
      for (Permission perm : permissions) {
        int count = context.getOrDefault(perm, 0) - 1;
        if (count > 0) {
          context.put(perm, count);
        } else {
          context.remove(perm);
        }
      }
    }

    private <T> T wrap(Supplier<T> supplier) {
      enter();
      try {
        return supplier.get();
      } finally {
        exit();
      }
    }

    public <T> T get(Supplier<T> supplier) {
      return wrap(supplier);
    }

    public void run(Runnable runnable) {
      wrap(() -> {
        runnable.run();
        return null;
      });
    }

  }

}
