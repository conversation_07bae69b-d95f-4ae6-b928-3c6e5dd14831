package com.kaching.security;

import static com.google.common.base.Preconditions.checkArgument;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.AbstractIterator;
import com.google.inject.Inject;
import com.kaching.platform.common.Pair;
import com.kaching.platform.guice.MutableSingleton;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.BatchItemId;
import com.kaching.platform.hibernate.queue.BatchItemOutcome;
import com.kaching.platform.hibernate.queue.BatchQueue;
import com.kaching.security.CipherMigrator.CipherMigrationResult;
import com.kaching.util.mail.Pager;
import com.twolattes.json.Entity;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

@MutableSingleton
public class CipherMigrationBatchQueue implements BatchQueue<CipherMigrationBatchQueue.BatchData, CipherMigrationBatchQueue.Data> {

  public enum Mode {
    VALIDATION,
    PRODUCTION
  }

  @Inject CipherMigrator migrator;

  @Override
  public int getParallelism() {
    return 8;
  }

  @Override
  public Marshaller<BatchData> getBatchDataMarshaller() {
    return BatchData.MARSHALLER;
  }

  @Override
  public Marshaller<Data> getItemDataMarshaller() {
    return Data.MARSHALLER;
  }

  public Iterable<Data> getChunkGenerator(String hibernateEntityClassName, int chunkSize) {
    Pair<Long, Long> bounds = migrator.getMinMaxIdBounds(hibernateEntityClassName);
    return new Iterable<>() {
      @NotNull
      @Override
      public Iterator<Data> iterator() {
        return new ChunkIterator(bounds.left, bounds.right, chunkSize);
      }
    };
  }

  public static class ChunkIterator extends AbstractIterator<Data> {

    private final long maxId;
    private final int chunkSize;
    private long current;

    public ChunkIterator(long minId, long maxId, int chunkSize) {
      checkArgument(chunkSize > 0);
      checkArgument(minId <= maxId);
      this.maxId = maxId;
      current = minId;
      this.chunkSize = chunkSize;
    }

    @Nullable
    @Override
    protected Data computeNext() {
      long start = current;
      long end = current + chunkSize - 1;
      current += chunkSize;
      if (start > maxId) {
        return endOfData();
      } else if (end > maxId) {
        return new Data(Id.of(start), Id.of(maxId));
      } else {
        return new Data(Id.of(start), Id.of(end));
      }
    }

  }

  @Override
  public Map<BatchItemId, BatchItemOutcome> processBatch(BatchData batchData, Map<BatchItemId, Data> items) {
    Map<BatchItemId, BatchItemOutcome> results = new HashMap<>();
    if (batchData.getMode() == CipherMigrationBatchQueue.Mode.PRODUCTION) {
      return migrateBatch(batchData, items);
    } else if (batchData.getMode() == Mode.VALIDATION) {
      return validateBatch(batchData, items);
    } else {
      throw new UnsupportedOperationException("Unsupported mode: " + Objects.toString(batchData.getMode()));
    }
  }

  @VisibleForTesting
  Map<BatchItemId, BatchItemOutcome> migrateBatch(BatchData batchData, Map<BatchItemId, Data> items) {
    Map<BatchItemId, BatchItemOutcome> results = new HashMap<>();
    migrator.migrate(batchData.getTableName(), batchData.getColumnName(), batchData.getCipherNameKey(), migration ->
      items.forEach((batchItemId, data) ->
          results.put(batchItemId, toBatchItemOutcome(migration.migrate(data.getGreaterThanOrEqualToId(), data.getLessThanOrEqualToId())))));
    return results;
  }

  @VisibleForTesting
  Map<BatchItemId, BatchItemOutcome> validateBatch(BatchData batchData, Map<BatchItemId, Data> items) {
    Map<BatchItemId, BatchItemOutcome> results = new HashMap<>();
    migrator.validate(batchData.getTableName(), batchData.getColumnName(), batchData.getCipherNameKey(), validation ->
        items.forEach((batchItemId, data) ->
            results.put(batchItemId, toBatchItemOutcome(validation.validate(data.getGreaterThanOrEqualToId(), data.getLessThanOrEqualToId())))));
    return results;
  }

  @VisibleForTesting
  static BatchItemOutcome toBatchItemOutcome(CipherMigrationResult result) {
    if (result.success()) {
      return new BatchItemOutcome.Success();
    } else {
      return new BatchItemOutcome.ErrorFlag(result.message());
    }
  }

  @Override
  public Pager.Device getPagerDevice() {
    return Pager.Device.PAGER_FRAUD_AND_RISK;
  }

  @Entity
  public static class BatchData {

    static final Marshaller<BatchData> MARSHALLER = createMarshaller(BatchData.class);

    @Value(nullable = false)
    String tableName;

    @Value(nullable = false)
    String columnName;

    @Value(nullable = false)
    String cipherNameKey;

    @Value(nullable = false)
    Mode mode;

    public BatchData() {/* json */}

    public BatchData(String tableName, String columnName, String cipherNameKey, Mode mode) {
      this.tableName = tableName;
      this.columnName = columnName;
      this.cipherNameKey = cipherNameKey;
      this.mode = mode;
    }

    public String getTableName() {
      return tableName;
    }

    public String getColumnName() {
      return columnName;
    }

    public String getCipherNameKey() {
      return cipherNameKey;
    }

    public Mode getMode() {
      return mode;
    }

  }

  @Entity
  public static class Data {

    static final Marshaller<Data> MARSHALLER = createMarshaller(Data.class);

    @Value(nullable = false)
    Id<?> gteId;

    @Value(nullable = false)
    Id<?> lteId;

    public Data() {/* json */}

    public Data(Id<?> gteId, Id<?> lteId) {
      this.gteId = gteId;
      this.lteId = lteId;
    }

    public Id<?> getGreaterThanOrEqualToId() {
      return gteId;
    }

    public Id<?> getLessThanOrEqualToId() {
      return lteId;
    }

  }

}
