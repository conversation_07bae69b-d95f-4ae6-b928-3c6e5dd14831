package com.kaching.security.pgp.keys;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;

import com.kaching.util.io.UncheckedIo;

/*
  Inspired by / adapted from <PERSON><PERSON> Neuhalfen's bouncy-gpg. See https://github.com/neuhalje/bouncy-gpg
  Original license:

  Copyright 2017 Jens Neuhalfen

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
*/
public class FileBasedKeyringConfig extends InputStreamBasedKeyringConfig {

  public FileBasedKeyringConfig(Path publicKeyring, Path secretKeyring) {
    super(getFileStream(publicKeyring), getFileStream(secretKeyring));
  }

  private static InputStream getFileStream(Path path) {
    return path == null ? null : UncheckedIo.get(() -> Files.newInputStream(path));
  }

}
