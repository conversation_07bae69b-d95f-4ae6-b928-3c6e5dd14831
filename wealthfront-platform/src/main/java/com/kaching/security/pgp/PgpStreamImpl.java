package com.kaching.security.pgp;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collections;
import java.util.Set;

import org.bouncycastle.openpgp.PGPPrivateKey;
import org.bouncycastle.openpgp.PGPPublicKey;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.security.pgp.keys.KeyringConfig;

public class PgpStreamImpl implements PgpStream {

  private final KeyringConfig keyring;

  @Inject
  public PgpStreamImpl(KeyringConfig keyring) {
    this.keyring = keyring;
  }

  @Override
  public OutputStream encrypt(OutputStream encryptedOutput, long encryptingKeyId) {
    return SigningEncryptingPgpStream.createWithDefaults(encryptedOutput, Option.some(getEncryptingKey(encryptingKeyId)), Option.none(), Option.none());
  }

  @Override
  public OutputStream sign(OutputStream signedOutput, long signingKeyId) {
    return SigningEncryptingPgpStream.createWithDefaults(signedOutput, Option.none(), Option.some(getSigningKey(signingKeyId)), Option.none());
  }

  @Override
  public OutputStream encryptWithFileName(OutputStream encryptedOutput, long encryptingKeyId, String fileName) {
    return SigningEncryptingPgpStream.createWithDefaults(encryptedOutput, Option.some(getEncryptingKey(encryptingKeyId)), Option.none(), Option.some(fileName));
  }

  @Override
  public OutputStream encryptAndSign(OutputStream signedEncryptedOutput, long encryptionKeyId, long signingKeyId) {
    return SigningEncryptingPgpStream.createWithDefaults(signedEncryptedOutput,
        Option.some(getEncryptingKey(encryptionKeyId)), Option.some(getSigningKey(signingKeyId)), Option.none());
  }

  private PGPPublicKey getEncryptingKey(long encryptingKeyId) {
    return PgpUtilities.findFirstPublicKey(keyring.getPublicKeyRings(),
        key -> key.isEncryptionKey() && key.getKeyID() == encryptingKeyId);
  }

  private PGPPrivateKey getSigningKey(long signingKeyId) {
    String password = keyring.getSecretKeyPassword(signingKeyId)
        .getOrThrow("Password not found in keyring for key: " + signingKeyId);
    return PgpUtilities.findSecretKey(keyring.getSecretKeyRings(), signingKeyId, password);
  }

  @Override
  public InputStream decrypt(InputStream encryptedInput) {
    DecryptionStreamFactory factory = new DecryptionStreamFactory(keyring, Collections.emptySet());
    return factory.wrap(encryptedInput);
  }

  @Override
  public InputStream verify(InputStream signedInput, Set<Long> requiredSigningKeyIds) {
    DecryptionStreamFactory factory = new DecryptionStreamFactory(keyring, requiredSigningKeyIds);
    return factory.wrap(signedInput);
  }

  @Override
  public InputStream decryptAndVerify(InputStream encryptedSignedInput, Set<Long> requiredSigningKeyIds) {
    DecryptionStreamFactory factory = new DecryptionStreamFactory(keyring, requiredSigningKeyIds);
    return factory.wrap(encryptedSignedInput);
  }

}
