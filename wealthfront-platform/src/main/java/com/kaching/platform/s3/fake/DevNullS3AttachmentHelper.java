package com.kaching.platform.s3.fake;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;

import org.jets3t.service.acl.AccessControlList;
import org.jets3t.service.model.S3Object;
import org.jets3t.service.model.StorageObject;

import com.kaching.platform.common.Option;
import com.kaching.platform.s3.S3AttachmentHelper;

public class DevNullS3AttachmentHelper implements S3AttachmentHelper {

  @Override
  public InputStream fetchObject(String key) {
    return new ByteArrayInputStream(new byte[0]);
  }

  @Override
  public StorageObject fetchObjectDetails(String key) {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean uploadObjectRetry(String key, InputStream input, int tries) {
    return true;
  }

  @Override
  public boolean uploadObjectRetry(String key, byte[] bytes, int tries) {
    return true;
  }

  @Override
  public boolean uploadEncryptedObjectRetry(String key, byte[] bytes, int tries) {
    return true;
  }

  @Override
  public boolean uploadEncryptedObjectRetry(String key, InputStream input, int tries) {
    return true;
  }

  @Override
  public boolean uploadObjectWithAclRetry(
      String key, InputStream input, AccessControlList accessControlList, int tries) {
    return true;
  }

  @Override
  public List<S3Object> listObjects(Option<String> maybePrefix) {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean deleteObject(String key) {
    return true;
  }

}
