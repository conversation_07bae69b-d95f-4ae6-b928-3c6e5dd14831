package com.kaching.platform.components;

import java.util.Objects;

import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.discovery.Status;

public abstract class StartupResult {

  private final Status status;

  protected StartupResult(Status status) {
    this.status = status;
  }

  public Status getStatus() {
    return status;
  }

  public Option<String> getMaybeReason() {
    return visit(new DefaultStartupResultVisitor<>(Option.none()) {
      @Override
      public Option<String> caseFailed(String reason) {
        return Option.some(reason);
      }

      @Override
      public Option<String> caseDegraded(String reason) {
        return Option.some(reason);
      }
    });
  }

  public abstract <T> T visit(StartupResultVisitor<T> visitor);

  public static Init init() {
    return new Init();
  }

  public static Up up() {
    return new Up();
  }

  public static Down down() {
    return new Down();
  }

  public static Failed failed(String reason) {
    return new Failed(reason);
  }

  public static Degraded degraded(String reason) {
    return new Degraded(reason);
  }

  @Override
  public String toString() {
    return Strings.format("StartupResult<%s, maybeReason=%s>", getStatus(), getMaybeReason());
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }
    if (!getClass().equals(obj.getClass())) {
      return false;
    }
    return getMaybeReason().equals(((StartupResult) obj).getMaybeReason());
  }

  @Override
  public int hashCode() {
    return Objects.hash(getStatus(), getMaybeReason());
  }

  public static class Init extends StartupResult {

    private Init() {
      super(Status.INIT);
    }

    @Override
    public <T> T visit(StartupResultVisitor<T> visitor) {
      return visitor.caseInit();
    }

  }

  public static class Up extends StartupResult {

    private Up() {
      super(Status.UP);
    }

    @Override
    public <T> T visit(StartupResultVisitor<T> visitor) {
      return visitor.caseUp();
    }

  }

  public static class Down extends StartupResult {

    private Down() {
      super(Status.DOWN);
    }

    @Override
    public <T> T visit(StartupResultVisitor<T> visitor) {
      return visitor.caseDown();
    }

  }

  public static class Failed extends StartupResult {

    private final String reason;

    private Failed(String reason) {
      super(Status.FAILED);
      this.reason = reason;
    }

    public String getReason() {
      return reason;
    }

    @Override
    public <T> T visit(StartupResultVisitor<T> visitor) {
      return visitor.caseFailed(reason);
    }

  }

  public static class Degraded extends StartupResult {

    private final String reason;

    private Degraded(String reason) {
      super(Status.DEGRADED);
      this.reason = reason;
    }

    public String getReason() {
      return reason;
    }

    @Override
    public <T> T visit(StartupResultVisitor<T> visitor) {
      return visitor.caseDegraded(reason);
    }

  }

}
