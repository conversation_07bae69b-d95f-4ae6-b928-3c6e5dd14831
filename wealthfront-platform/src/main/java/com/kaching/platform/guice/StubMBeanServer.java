package com.kaching.platform.guice;

import java.io.ObjectInputStream;
import java.util.Set;

import javax.management.*;
import javax.management.loading.ClassLoaderRepository;

public class StubMBeanServer implements MBeanServer {

  @Override
  public void addNotificationListener(
      ObjectName name,
      NotificationListener listener, NotificationFilter filter, Object handback)
      throws InstanceNotFoundException {
  }

  @Override
  public void addNotificationListener(
      ObjectName name, ObjectName listener,
      NotificationFilter filter, Object handback)
      throws InstanceNotFoundException {
  }

  @Override
  public ObjectInstance createMBean(String className, ObjectName name)
      throws ReflectionException, InstanceAlreadyExistsException,
      MBeanRegistrationException, MBeanException, NotCompliantMBeanException {
    return null;
  }

  @Override
  public ObjectInstance createMBean(
      String className, ObjectName name,
      ObjectName loaderName) throws ReflectionException,
      InstanceAlreadyExistsException, MBeanRegistrationException,
      MBeanException, NotCompliantMBeanException, InstanceNotFoundException {
    return null;
  }

  @Override
  public ObjectInstance createMBean(
      String className, ObjectName name,
      Object[] params, String[] signature) throws ReflectionException,
      InstanceAlreadyExistsException, MBeanRegistrationException,
      MBeanException, NotCompliantMBeanException {
    return null;
  }

  @Override
  public ObjectInstance createMBean(
      String className, ObjectName name,
      ObjectName loaderName, Object[] params, String[] signature)
      throws ReflectionException, InstanceAlreadyExistsException,
      MBeanRegistrationException, MBeanException, NotCompliantMBeanException,
      InstanceNotFoundException {
    return null;
  }

  @Override
  public ObjectInputStream deserialize(ObjectName name, byte[] data)
      throws InstanceNotFoundException, OperationsException {
    return null;
  }

  @Override
  public ObjectInputStream deserialize(String className, byte[] data)
      throws OperationsException, ReflectionException {
    return null;
  }

  @Override
  public ObjectInputStream deserialize(
      String className, ObjectName loaderName,
      byte[] data) throws InstanceNotFoundException, OperationsException,
      ReflectionException {
    return null;
  }

  @Override
  public Object getAttribute(ObjectName name, String attribute)
      throws MBeanException, AttributeNotFoundException,
      InstanceNotFoundException, ReflectionException {
    return null;
  }

  @Override
  public AttributeList getAttributes(ObjectName name, String[] attributes)
      throws InstanceNotFoundException, ReflectionException {
    return null;
  }

  @Override
  public ClassLoader getClassLoader(ObjectName loaderName)
      throws InstanceNotFoundException {
    return null;
  }

  @Override
  public ClassLoader getClassLoaderFor(ObjectName mbeanName)
      throws InstanceNotFoundException {
    return null;
  }

  @Override
  public ClassLoaderRepository getClassLoaderRepository() {
    return null;
  }

  @Override
  public String getDefaultDomain() {
    return null;
  }

  @Override
  public String[] getDomains() {
    throw new UnsupportedOperationException();
  }

  @Override
  public Integer getMBeanCount() {
    throw new UnsupportedOperationException();
  }

  @Override
  public MBeanInfo getMBeanInfo(ObjectName name)
      throws InstanceNotFoundException, IntrospectionException,
      ReflectionException {
    throw new UnsupportedOperationException();
  }

  @Override
  public ObjectInstance getObjectInstance(ObjectName name)
      throws InstanceNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public Object instantiate(String className) throws ReflectionException,
      MBeanException {
    throw new UnsupportedOperationException();
  }

  @Override
  public Object instantiate(String className, ObjectName loaderName)
      throws ReflectionException, MBeanException, InstanceNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public Object instantiate(
      String className, Object[] params,
      String[] signature) throws ReflectionException, MBeanException {
    throw new UnsupportedOperationException();
  }

  @Override
  public Object instantiate(
      String className, ObjectName loaderName,
      Object[] params, String[] signature) throws ReflectionException,
      MBeanException, InstanceNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public Object invoke(
      ObjectName name, String operationName, Object[] params,
      String[] signature) throws InstanceNotFoundException, MBeanException,
      ReflectionException {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isInstanceOf(ObjectName name, String className)
      throws InstanceNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isRegistered(ObjectName name) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Set<ObjectInstance> queryMBeans(ObjectName name, QueryExp query) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Set<ObjectName> queryNames(ObjectName name, QueryExp query) {
    throw new UnsupportedOperationException();
  }

  @Override
  public ObjectInstance registerMBean(Object object, ObjectName name)
      throws InstanceAlreadyExistsException, MBeanRegistrationException,
      NotCompliantMBeanException {
    return null;
  }

  @Override
  public void removeNotificationListener(ObjectName name, ObjectName listener)
      throws InstanceNotFoundException, ListenerNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void removeNotificationListener(
      ObjectName name,
      NotificationListener listener) throws InstanceNotFoundException,
      ListenerNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void removeNotificationListener(
      ObjectName name, ObjectName listener,
      NotificationFilter filter, Object handback)
      throws InstanceNotFoundException, ListenerNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void removeNotificationListener(
      ObjectName name,
      NotificationListener listener, NotificationFilter filter, Object handback)
      throws InstanceNotFoundException, ListenerNotFoundException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void setAttribute(ObjectName name, Attribute attribute)
      throws InstanceNotFoundException, AttributeNotFoundException,
      InvalidAttributeValueException, MBeanException, ReflectionException {
    throw new UnsupportedOperationException();
  }

  @Override
  public AttributeList setAttributes(ObjectName name, AttributeList attributes)
      throws InstanceNotFoundException, ReflectionException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void unregisterMBean(ObjectName name)
      throws InstanceNotFoundException, MBeanRegistrationException {
    throw new UnsupportedOperationException();
  }

}
