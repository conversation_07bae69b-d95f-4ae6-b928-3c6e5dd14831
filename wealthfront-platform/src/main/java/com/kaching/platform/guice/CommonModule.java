package com.kaching.platform.guice;

import static com.google.inject.multibindings.MapBinder.newMapBinder;
import static com.google.inject.name.Names.named;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.lang.Thread.currentThread;
import static java.util.Map.entry;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.management.ManagementFactory;
import java.nio.file.FileSystems;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.Timer;
import java.util.regex.Pattern;

import javax.management.MBeanServer;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.reflections.Reflections;
import org.reflections.scanners.ResourcesScanner;
import org.weakref.jmx.guice.MBeanModule;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.multibindings.MapBinder;
import com.google.inject.name.Named;
import com.kaching.Author;
import com.kaching.platform.common.Option;
import com.kaching.platform.memcached.MemcachedModule;
import com.kaching.platform.monitoring.JvmStatsModule;
import com.kaching.platform.monitoring.icinga.ExtraJvmMetricsModule;
import com.kaching.platform.monitoring.nscaspooler.NscaspoolerClientModule;
import com.kaching.platform.queryengine.Trace;
import com.kaching.platform.queryengine.xray.BestEffortAwsXRayRecorderModule;

import gs.hitchin.hitchfs.DefaultFileSystem;
import gs.hitchin.hitchfs.FakeFile;
import gs.hitchin.hitchfs.FileSystem;
import gs.hitchin.hitchfs.StubFileSystem;

public class CommonModule extends AbstractModule {

  private static final Map<Author, List<String>> AUTHOR_TO_PACKAGES = ImmutableMap.<Author, List<String>>builder()
      .put(entry(
          Author.BROKERAGE_PLATFORM_TEAM,
          List.of(
              "com.kaching.apex",
              "com.kaching.user.audit",
              "com.kaching.user.documents",
              "com.kaching.user.inteliclear",
              "com.kaching.user.ira",
              "com.kaching.user.iraadmin",
              "com.kaching.user.omni",
              "com.kaching.user.penson",
              "com.kaching.user.tax",
              "com.kaching.user.toggle",
              "com.kaching.trading.bi.cax",
              "com.kaching.trading.core.balances",
              "com.kaching.trading.master",
              "com.kaching.trading.market")))
      .put(entry(
          Author.FRAUD_RISK_ENG_TEAM,
          List.of(
              "com.kaching.user.idv",
              "com.wealthfront.risk",
              "com.wealthfront.banking.wellsfargo.logicbox")))
      .put(entry(
          Author.BANKING_PLATFORM_TEAM,
          List.of(
              "com.kaching.ascensus.client",
              "com.kaching.risk",
              "com.kaching.user.ascensus",
              "com.kaching.user.riskmetrics",
              "com.kaching.user.withdrawals",
              "com.wealthfront.brokerage.funding.accounttransfers")))
      .put(entry(
          Author.INVESTMENT_SERVICES_TEAM,
          List.of(
              "com.kaching.historical",
              "com.kaching.investing",
              "com.kaching.trading")))
      .put(entry(
          Author.INVESTMENT_PRODUCTS_TEAM,
          List.of(
              "com.kaching.trading.market.stockcollections",
              "com.kaching.trading.market.polygon",
              "com.kaching.trading.market.logos",
              "com.kaching.trading.bi.bondladders",
              "com.kaching.trading.bi.compatibleaccounts",
              "com.kaching.trading.bi.crossaccounttransfer",
              "com.kaching.trading.bi.otcaffiliate",
              "com.kaching.user.account",
              "com.kaching.user.customportfolios",
              "com.kaching.user.fpsl",
              "com.kaching.user.lostsecurityholder",
              "com.kaching.user.risk",
              "com.kaching.user.riskassessment",
              "com.kaching.user.sp500di",
              "com.kaching.user.stockportfolios",
              "com.kaching.user.investing",
              "com.kaching.user.zendesk",
              "com.kaching.user.phone",
              "com.kaching.user.push",
              "com.kaching.user.email",
              "com.kaching.util.mail",
              "com.kaching.link.aggregation.smartystreets"
          )))
      .put(entry(
          Author.ONLINE_SERVICES_TEAM,
          List.of(
              "com.kaching.user.callouts",
              "com.kaching.user.cash",
              "com.kaching.user.cashdrag",
              "com.kaching.user.cashpaymentincentives",
              "com.kaching.user.conversions",
              "com.kaching.user.coworker",
              "com.kaching.user.cross",
              "com.kaching.user.depositmatch",
              "com.kaching.user.drops",
              "com.kaching.user.experiment",
              "com.kaching.user.financialincentives",
              "com.kaching.user.funding",
              "com.kaching.user.householdviews",
              "com.kaching.user.incentives",
              "com.kaching.user.invitations",
              "com.kaching.user.merchandise",
              "com.kaching.user.npm",
              "com.kaching.user.referrals",
              "com.kaching.user.survey",
              "com.kaching.user.tbi",
              "com.kaching.user.transfer"
          )))
      .put(entry(
          Author.TRADE_VALIDATION_TEAM,
          List.of("com.kaching.trading.bi.tradevalidation")))
      .put(entry(
          Author.TRADING_PRODUCTS_TEAM,
          List.of(
              "com.kaching.trading.direct",
              "com.kaching.trading.migration",
              "com.kaching.trading.bi.ascensus",
              "com.kaching.trading.master.sandp",
              "com.kaching.trading.master.crsp",
              "com.kaching.xignite",
              "com.kaching.user.passiveplus",
              "com.wealthfront.investing")))
      .put(entry(
          Author.ADVICE_AUTOMATION_TEAM,
          List.of("com.kaching.autopilot")))
      .put(entry(
          Author.LINK_SERVICE_TEAM,
          List.of(
              "com.kaching.link",
              "com.kaching.user.balancesheet",
              "com.kaching.user.link",
              "com.kaching.path",
              "com.wealthfront.integration.fbank.autopilot",
              "com.wealthfront.integration.link")))
      .build();

  private final ApplicationOptions options;

  public CommonModule(ApplicationOptions options) {
    this.options = options;
  }

  @Override
  protected void configure() {
    // Wealthfront specific
    install(new PropertiesModule(options));
    install(new Apache4HttpClientModule());
    install(new ResultBasedHttpClientModule());
    install(new RetryingResultBasedHttpClientModule());
    install(new KachingHttpClientModule());
    install(new ProtobufHttpClientModule());
    install(new QueryEngineClientModule());
    install(new JvmStatsModule());
    install(new ExtraJvmMetricsModule());
    install(new MemcachedModule(getMemcacheMode(), getMemcachedTimeout()));
    install(new UUIDModule());
    install(new BestEffortAwsXRayRecorderModule());
    install(new NscaspoolerClientModule());

    bind(Timer.class).toProvider(new ThrowingProvider<>(Timer.class));

    // Wealthfront specific
    install(new MBeanModule());
    if (options.fakeJmx) {
      bind(MBeanServer.class).toInstance(new StubMBeanServer());
    } else {
      bind(MBeanServer.class).toInstance(ManagementFactory.getPlatformMBeanServer());
    }

    // Wealthfront specific
    if (options.clojureShellPort != 0) {
      bind(Integer.class).annotatedWith(named("clojure-shell-port")).toInstance(options.clojureShellPort);
    }

    // Wealthfront specific
    if (options.scalaShellPort != 0) {
      bind(Integer.class).annotatedWith(named("scala-shell-port")).toInstance(options.scalaShellPort);
    }

    bind(java.nio.file.FileSystem.class).toInstance(FileSystems.getDefault());
    // hitchfs
    if (options.fakeFileSystem) {
      bind(FileSystem.class).toInstance(new StubFileSystem() {
        @Override
        public InputStream getInputStream(FakeFile fake) throws IOException {
          return new ByteArrayInputStream(new byte[0]);
        }
      });
    } else {
      bind(FileSystem.class).to(DefaultFileSystem.class);
    }

    bindOnCallPackageMapping();
  }

  public MemcachedModule.Mode getMemcacheMode() {
    if (options.fakeMemcached) {
      return MemcachedModule.Mode.FAKE_CACHE;
    } else if (options.testMemcached) {
      return MemcachedModule.Mode.TEST_CACHE;
    }
    return MemcachedModule.Mode.PROD_CACHE;
  }

  public int getMemcachedTimeout() {
    return options.memcachedTimeout;
  }

  @Provides
  DateTime dateTime() {
    return new DateTime(ET);
  }

  @Provides
  LocalDate localDate() {
    return new LocalDate(ET);
  }

  // Wealthfront specific
  @Provides
  @Singleton
  @Version
  public String version() throws IOException {
    Properties properties = new Properties();
    properties.load(CommonModule.class.getResourceAsStream("/com/kaching/version.properties"));
    return properties.getProperty("version");
  }

  // Wealthfront specific
  @Provides
  @Singleton
  public Revision revisionVersion(@Version String version) throws IOException {
    return new Revision(version);
  }

  @Provides
  @Singleton
  @Named("META-INF.maven-pom.properties-jarNames")
  public List<String> metaInfMavenPomPropertiesJarNames() {
    Reflections reflections = new Reflections("META-INF.maven", new ResourcesScanner());
    Set<String> resourceList = reflections.getResources(Pattern.compile(".*pom\\.properties"));
    ClassLoader loader = currentThread().getContextClassLoader();
    List<String> jarNames = resourceList.stream()
        .map(pomPropertiesFilePath -> {
          InputStream stream = loader.getResourceAsStream(pomPropertiesFilePath);
          Properties prop = new Properties();
          try {
            prop.load(stream);
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
          String artifactId = prop.getProperty("artifactId");
          String version = prop.getProperty("version");
          if (artifactId == null || version == null) {
            return Option.<String>none();
          }
          return Option.some(String.format("%s-%s.jar", artifactId, version));
        })
        .flatMap(Option::stream)
        .sorted()
        .toList();
    return ImmutableList.copyOf(jarNames);
  }

  // Wealthfront specific (TEST-52)
  @Provides
  @Singleton
  java.lang.ThreadLocal<Trace> traces() {
    return new java.lang.ThreadLocal<Trace>() {
      @Override
      protected Trace initialValue() {
        return Trace.newTrace();
      }
    };
  }

  private void bindOnCallPackageMapping() {
    MapBinder<String, Author> onCallPackageMappingBinder = newMapBinder(binder(), String.class, Author.class);
    AUTHOR_TO_PACKAGES.forEach((author, packages) ->
        packages.forEach(aPackage -> onCallPackageMappingBinder.addBinding(aPackage).toInstance(author)));
  }

}
