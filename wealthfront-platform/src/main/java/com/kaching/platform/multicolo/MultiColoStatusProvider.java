package com.kaching.platform.multicolo;

import com.google.inject.ImplementedBy;

@ImplementedBy(MultiColoStatusProviderImpl.class)
public interface MultiColoStatusProvider {
  
  enum ColoStatus {
    MASTER {
      @Override
      public <T> T visit(ColoStatusVisitor<T> visitor) {
        return visitor.caseMaster();
      }
    },
    FOLLOWER {
      @Override
      public <T> T visit(ColoStatusVisitor<T> visitor) {
        return visitor.caseFollower();
      }
    },
    MASTER_TEST {
      @Override
      public <T> T visit(ColoStatusVisitor<T> visitor) {
        return visitor.caseMasterTest();
      }
    },
    MOCK_FAILOVER_FAKE_MASTER {
      @Override
      public <T> T visit(ColoStatusVisitor<T> visitor) {
        return visitor.caseMockFailoverFakeMaster();
      }
    };
    
    public abstract <T> T visit(ColoStatusVisitor<T> visitor);
    
  }
  
  interface ColoStatusVisitor<T> {
    
    T caseMaster();
    
    T caseFollower();
    
    T caseMasterTest();
    
    T caseMockFailoverFakeMaster();
    
  }
  
  ColoStatus getStatus();

}
