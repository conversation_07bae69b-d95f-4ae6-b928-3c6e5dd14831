package com.kaching.platform.hibernate.queue.impl;

import static com.google.common.collect.ImmutableSortedMap.toImmutableSortedMap;
import static com.kaching.util.Preconditions.checkQueryArgument;
import static java.util.stream.Collectors.toList;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

import com.google.inject.Inject;
import com.kaching.platform.common.Pair;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.queue.InSessionBatchEnqueuer;
import com.kaching.platform.hibernate.queue.BatchItemId;
import com.kaching.platform.hibernate.queue.BatchQueue;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.queryengine.AbstractQuery;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

public class BatchQueueManuallyEnqueueItems extends AbstractQuery<Json.Object> {
  
  private final String queueName;
  private final Json.Value batchData;
  private final List<Json.Value> items;

  public BatchQueueManuallyEnqueueItems(String queueName, Json.Value batchData, List<Json.Value> items) {
    this.queueName = queueName;
    this.batchData = batchData;
    this.items = items;
  }

  @Inject BatchQueueBinder binder;
  @Inject RetryingTransacter transacter;

  @Override
  public Json.Object process() {
    BatchQueue<?, ?> queue = binder.getQueue(queueName);
    checkQueryArgument(!items.isEmpty(), "Must specify at least one item to enqueue");
    Map<BatchItemId, Json.Value> map = processImpl(queue);
    
    Json.Object result = Json.object();
    map.forEach((id, value) -> result.put(Json.string(id.toString()), value));
    return result;
  }
  
  private <Q extends BatchQueue<B, I>, B, I> Map<BatchItemId, Json.Value> processImpl(Q queue) {
    Class<Q> queueClass = (Class<Q>) queue.getClass();
    Marshaller<B> batchDataMarshaller = queue.getBatchDataMarshaller();
    Marshaller<I> marshaller = queue.getItemDataMarshaller();
    B batchDataUnmarshalled = batchDataMarshaller.unmarshall(batchData);
    List<I> itemsMarshalled = items.stream()
        .map(item -> {
          I unmarshalled = marshaller.unmarshall(item);
          Json.Value remarshalled = marshaller.marshall(unmarshalled);
          checkQueryArgument(item.equals(remarshalled), "Remarshalling item '%s' produces '%s'. Data may be lost", item, remarshalled);
          return unmarshalled;
        }).collect(toList());
    return transacter.executeWithSessionExpression(session -> {
      InSessionBatchEnqueuer<Q, B, I> enqueuer = binder.getInSessionEnqueuer(queueClass);
      return enqueuer.enqueue(batchDataUnmarshalled, itemsMarshalled).entrySet()
          .stream()
          .map(entry -> Pair.of(entry.getKey(), marshaller.marshall(entry.getValue())))
          .collect(toImmutableSortedMap(Comparator.naturalOrder(), Pair::getLeft, Pair::getRight));
    });
  }
  
}
