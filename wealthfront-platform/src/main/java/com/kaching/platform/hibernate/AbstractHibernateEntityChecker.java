package com.kaching.platform.hibernate;

import static com.kaching.util.GenericLists.list;
import static com.kaching.util.Preconditions.checkNotInstanceOf;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.util.functional.Tuple;
import com.kaching.util.functional.Tuple1;
import com.kaching.util.functional.Tuple2;
import com.kaching.util.functional.Tuple3;
import com.kaching.util.functional.Tuple4;
import com.kaching.util.functional.Tuple5;
import com.kaching.util.functional.Tuple6;
import com.kaching.util.functional.Tuple7;
import com.kaching.util.functional.Tuple8;
import com.kaching.util.functional.Tuple9;
import com.twolattes.json.Entity;

abstract class AbstractHibernateEntityChecker implements HibernateEntityChecker {

  private static final List<String> IGNORED_CLASSNAME_PREFIXES = list("Fake", "Partial", "Test");
  private static final List<String> IGNORED_CLASSNAME_SUFFIXES = list("Factory", "Test", "TestBase");
  private static final List<Class<?>> IGNORED_CLASSES = list(Synced.class);
  private static final List<Class<? extends Annotation>> IGNORED_ANNOTATIONS = list(Entity.class);
  private boolean forced = false;

  private static Class<?> getCallerClass(IllegalArgumentException e) {
    for (int i = e.getStackTrace().length - 1; i >= 0; i--) {
      for (Class<?> myClass : classForName(e.getStackTrace()[i].getClassName())) {
        if (Transacter.class.isAssignableFrom(myClass) || HibernateEntityChecker.class.isAssignableFrom(myClass)) {
          return classForName(e.getStackTrace()[i + 1].getClassName()).getOrThrow();
        }
      }
    }
    throw new NoSuchElementException();
  }

  @VisibleForTesting
  static Option<Class<?>> classForName(String className) {
    try {
      return Option.some(Class.forName(className));
    } catch (ClassNotFoundException e) {
      return Option.none();
    }
  }

  @VisibleForTesting
  static boolean isIgnoredClass(Class<?> myClass, boolean recursive) {
    if (myClass == null) {
      return false;
    }
    for (Class<? extends Annotation> ignoredAnnotation : IGNORED_ANNOTATIONS) {
      if (myClass.getAnnotation(ignoredAnnotation) != null) {
        return true;
      }
    }
    for (Class<?> ignoredClass : IGNORED_CLASSES) {
      if (myClass.equals(ignoredClass)) {
        return true;
      }
    }
    for (String ignoredPrefix : IGNORED_CLASSNAME_PREFIXES) {
      if (myClass.getSimpleName().startsWith(ignoredPrefix)) {
        return true;
      }
    }
    for (String ignoredSuffix : IGNORED_CLASSNAME_SUFFIXES) {
      if (myClass.getSimpleName().endsWith(ignoredSuffix)) {
        return true;
      }
    }
    return recursive && isIgnoredClass(myClass.getEnclosingClass(), true);
  }

  public <T> T check(T t) {
    if (t == null) {
      return null;
    }
    if (t instanceof CharSequence || t instanceof Number || t instanceof Identifier) {
      return t;
    }
    if (isIgnoredClass(t.getClass(), false)) {
      return t;
    }
    try {
      if (t instanceof Collection) {
        ((Collection<?>) t).forEach(this::check);
      } else if (t instanceof Map) {
        check(((Map) t).keySet());
        check(((Map) t).values());
      } else if (t instanceof Pair) {
        check(((Pair) t).left);
        check(((Pair) t).right);
      } else if (t instanceof Tuple) {
        if (t instanceof Tuple9) {
          check(((Tuple9) t)._9);
        }
        if (t instanceof Tuple8) {
          check(((Tuple8) t)._8);
        }
        if (t instanceof Tuple7) {
          check(((Tuple7) t)._7);
        }
        if (t instanceof Tuple6) {
          check(((Tuple6) t)._6);
        }
        if (t instanceof Tuple5) {
          check(((Tuple5) t)._5);
        }
        if (t instanceof Tuple4) {
          check(((Tuple4) t)._4);
        }
        if (t instanceof Tuple3) {
          check(((Tuple3) t)._3);
        }
        if (t instanceof Tuple2) {
          check(((Tuple2) t)._2);
        }
        if (t instanceof Tuple1) {
          check(((Tuple1) t)._1);
        }
      } else {
        checkNotInstanceOf(HibernateEntity.class, t);
      }
    } catch (IllegalArgumentException e) {
      if ((!(t instanceof HibernateEntity) || ((HibernateEntity) t).getId() != null) && (!isIgnoredClass(getCallerClass(e), true) || forced)) {
        handleException(e);
      }
    }
    return t;
  }

  @VisibleForTesting
  void setForced(boolean forced) {
    this.forced = forced;
  }

  abstract void handleException(IllegalArgumentException e);

}
