package com.kaching.platform.hibernate.queue;

import static com.kaching.platform.monitoring.icinga.IcingaMetadata.Interval.SIX_HOURS;
import static com.kaching.platform.monitoring.icinga.IcingaMetadata.TimePeriod.OFFICE_HOURS;
import static com.kaching.platform.monitoring.icinga.IcingaMetadata.icingaMetadataBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.kaching.platform.common.Strings;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.monitoring.icinga.IcingaCheck;
import com.kaching.platform.monitoring.icinga.IcingaMetadata;
import com.kaching.platform.monitoring.icinga.IcingaOutput;
import com.kaching.util.functional.Tuple3;
import com.kaching.util.pagerduty.ServiceKindToPagerDuty;

public class CheckHybridQueuesProcessingIgnoredEntities implements IcingaCheck {

  private static final int IGNORED_PROCESSED_ID_COUNT_THRESHOLD = 50;
  private static final int IGNORED_PROCESSED_COUNT_THRESHOLD = 500;

  @Inject Map<String, HybridQueue<?>> allHybridQueues;
  @Inject ServiceKind serviceKind;

  @Override
  public IcingaMetadata getIcingaMetadata() {
    return icingaMetadataBuilder(this.getClass())
        .withInterval(SIX_HOURS)
        .withTimePeriod(OFFICE_HOURS)
        .withIcingaGroup(ServiceKindToPagerDuty.getPagerDutyDevices(serviceKind).get(0))
        .withLeaderOnly(true)
        .build();
  }

  @Override
  public IcingaOutput getIcingaOutput() {
    List<Tuple3<String, Integer, Long>> violations = new ArrayList<>();
    for (Map.Entry<String, HybridQueue<?>> nameAndQueue : allHybridQueues.entrySet()) {
      String name = nameAndQueue.getKey();
      HybridQueue<?> queue = nameAndQueue.getValue();

      int ignoredProcessedIdsCount = queue.getProcessedIgnoredIds().size();
      long ignoredProcessedCount = queue.getProcessedIgnoredCount();

      if (ignoredProcessedIdsCount > getIgnoredProcessedIdCountThreshold() ||
          ignoredProcessedCount > getIgnoredProcessedCountThreshold()) {
        violations.add(new Tuple3<>(name, ignoredProcessedIdsCount, ignoredProcessedCount));
      }
      queue.resetProcessedIgnoredIds();
    }

    if (violations.isEmpty()) {
      return IcingaOutput.OK;
    }
    StringBuilder messageBuilder = new StringBuilder("The following HybridQueues have processed a large number " +
        "of ignored entities in the past 6 hours:\n");
    for (Tuple3<String, Integer, Long> violation : violations) {
      messageBuilder.append(Strings.format("Queue %s. Unique ignored processed id count: %s. " +
          "Total count of ignored ids: %s\n", violation._1, violation._2, violation._3));
    }
    messageBuilder.append("The priority for fixing this issues is relatively low in that the Queue is likely operating " +
        "without issue. Over time it will chew up CPU resources. Fixing this issue is usually very straightforward. " +
        "See BEINFRA-1171 for context on how to fix this problem.");

    return IcingaOutput.icingaOutputBuilder()
        .withExitCode(IcingaOutput.ExitCode.CRITICAL)
        .withMessage(messageBuilder.toString())
        .build();
  }

  @VisibleForTesting
  int getIgnoredProcessedIdCountThreshold() {
    return IGNORED_PROCESSED_ID_COUNT_THRESHOLD;
  }

  @VisibleForTesting
  int getIgnoredProcessedCountThreshold() {
    return IGNORED_PROCESSED_COUNT_THRESHOLD;
  }

}
