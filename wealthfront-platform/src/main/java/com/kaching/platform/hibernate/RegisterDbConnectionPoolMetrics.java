package com.kaching.platform.hibernate;

import static com.kaching.platform.multicolo.MultiColoSchedule.ALL;

import java.sql.SQLException;

import org.collectd.mx.MBeanSender;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.jcollectd.CollectorBuilder;
import com.kaching.platform.multicolo.MultiColo;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.mchange.v2.c3p0.C3P0Registry;
import com.mchange.v2.c3p0.PooledDataSource;

@MultiColo(enabledOn = ALL, reason = "Db Connection monitoring should be available on all colos")
public class RegisterDbConnectionPoolMetrics extends AbstractQuery<Boolean> {

  private static final Log log = Log.getLog(RegisterDbConnectionPoolMetrics.class);

  @Inject MBeanSender sender;
  @Inject C3P0PooledDataSourceFetcher c3P0PooledDataSourceFetcher;
  @Inject C3P0Metrics metrics;
  @Inject StackTraceMonitor monitor;

  @Override
  public Boolean process() {
    Option<PooledDataSource> pooledDataSourceOption = c3P0PooledDataSourceFetcher.get();
    if (pooledDataSourceOption.isDefined()) {
      PooledDataSource pooledDataSource = pooledDataSourceOption.getOrThrow();
      String objectName = getPdsObjectNameStr(pooledDataSource);
      sender.schedule(new CollectorBuilder("c3p0")
          .addMBean(objectName, "c3p0")
          .withGauge("numConnectionsAllUsers")
          .withGauge("numBusyConnectionsAllUsers")
          .withGauge("numIdleConnectionsAllUsers")
          .withGauge("minPoolSize")
          .withGauge("maxPoolSize")
          .withGauge("numUnclosedOrphanedConnectionsAllUsers")
          .withGauge("numThreadsAwaitingCheckoutDefaultUser")
          .withGauge("threadPoolNumActiveThreads")
          .withGauge("threadPoolNumIdleThreads")
          .withGauge("threadPoolSize")
          .buildMBean()
          .setInterval(1)
          .build());

      try {
        C3P0ConnectionCustomizer connectionCustomizer =
            (C3P0ConnectionCustomizer) C3P0Registry.getConnectionCustomizer(C3P0ConnectionCustomizer.class.getName());
        connectionCustomizer.setMetrics(metrics);
      } catch (SQLException e) {
        log.error(e, "Failed to retrieve the connection customizer. Some C3P0 metrics won't be available.");
        monitor.add(e);
      }
      return true;
    } else {
      return false;
    }
  }

  // from https://www.mchange.com/projects/c3p0/#jmx_configuration_and_management
  private static String getPdsObjectNameStr(PooledDataSource pds) {
    return Strings.format("com.mchange.v2.c3p0:type=PooledDataSource,identityToken=%s,name=%s",
        pds.getIdentityToken(),
        pds.getDataSourceName());
  }

}
