package com.kaching.platform.hibernate.queue;

import org.joda.time.Duration;

import com.twolattes.json.Json;

public interface BatchSizer {
  
  int getTargetBatchSize();
  
  void observeSuccessfulBatch(int targetBatchSize, int actualBatchSize, Duration processTime);
  
  int observeFailedBatchAndGetNewSize(int targetBatchSize, int actualBatchSize, Duration processTime, Exception exception);

  default Json.Value debugState() {
    return Json.object();
  }

}
