package com.kaching.platform.hibernate.queue.impl;

import java.util.Map;

import com.google.common.collect.RangeSet;
import com.google.inject.ImplementedBy;
import com.kaching.entities.Repository;
import com.kaching.platform.hibernate.Id;
import com.twolattes.json.Json;

@ImplementedBy(PersistentBatchQueueItemRepository.class)
public interface BatchQueueItemRepository extends Repository<BatchQueueItem> {
  
  void deleteRanges(RangeSet<Id<BatchQueueItem>> rangeSet);
  
  Map<Id<BatchQueueItem>, Json.Value> getPayloads(RangeSet<Id<BatchQueueItem>> rangeSet);
  
}
