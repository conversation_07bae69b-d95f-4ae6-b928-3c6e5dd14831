package com.kaching.platform.hibernate.interceptor;

import static com.kaching.platform.hibernate.interceptor.HibernateInterceptorStatusSwitch.Status.INTERCEPTOR_ENABLED;

import com.google.inject.Singleton;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;

@Singleton
class HibernateInterceptorStatusSwitch {

  @ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
  enum Status {
    INTERCEPTOR_ENABLED,
    INTERCEPTOR_DISABLED,
  }

  private volatile Status status = INTERCEPTOR_ENABLED;

  Status getStatus() {
    return status;
  }

  void setStatus(Status status) {
    this.status = status;
  }

}
