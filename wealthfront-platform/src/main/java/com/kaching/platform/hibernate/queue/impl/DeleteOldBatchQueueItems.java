package com.kaching.platform.hibernate.queue.impl;

import static com.wealthfront.util.stream.WFCollectors.pairsToMap;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import org.joda.time.DateTime;
import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Range;
import com.google.common.collect.RangeSet;
import com.google.common.collect.TreeRangeSet;
import com.google.inject.Inject;
import com.kaching.entities.Scalar;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.hibernate.queue.BatchQueue;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.hibernate.queue.BatchQueueDeletionPolicy;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.util.Sleeper;
import com.kaching.util.time.NanoTimeProvider;

public class DeleteOldBatchQueueItems extends AbstractQuery<Unit> {
  
  private static final Log log = Log.getLog(DeleteOldBatchQueueItems.class);
  private static final Scalar SLEEP_BETWEEN_BATCHES = Scalar.scalar(2);
  
  private static final int SELECT_SIZE = 5000;
  private static final int MAX_ITEMS_IN_SINGLE_TRANSACTION = 5000;
  
  @Inject RetryingTransacter transacter;
  @Inject BatchQueueBinder binder;
  @Inject DateTime now;
  @Inject NanoTimeProvider nanoTimeProvider;
  @Inject Sleeper sleeper;
  
  public DeleteOldBatchQueueItems() {}

  @Override
  public Unit process() {
    Map<Id<BatchQueueName>, BatchQueueDeletionPolicy> queueIds = getQueueIdsToPurge();
    if (queueIds.isEmpty()) {
      return Unit.unit;
    }
    for (Map.Entry<Id<BatchQueueName>, BatchQueueDeletionPolicy> queueEntry : queueIds.entrySet()) {
      Id<BatchQueueName> queueId = queueEntry.getKey();
      BatchQueueDeletionPolicy policy = queueEntry.getValue();
      for (Duration sentDuration : policy.getDeleteSentAfter()) {
        DateTime cutoff = now.minus(sentDuration);
        deleteAllBefore(queueId, cutoff, State.SENT);
      }
      for (Duration ignoredDuration : policy.getDeleteIgnoredAfter()) {
        DateTime cutoff = now.minus(ignoredDuration);
        deleteAllBefore(queueId, cutoff, State.IGNORED);
      }
    }
    return Unit.unit;
  }
  
  private boolean deleteAllBefore(Id<BatchQueueName> queueId, DateTime cutoff, State state) {
    List<ToDelete> toDelete;
    do {
      toDelete = getSomeToDelete(queueId, cutoff, state);
      try {
        for (List<ToDelete> toDeleteBatch : partition(toDelete, getMaxItemsInSingleTransaction())) {
          deleteBatch(queueId, toDeleteBatch, state);
        }
      } catch (BatchQueueBatchRepository.StaleUpdate exception) {
        log.error(exception, "Some batches in queue %s from before %s are not longer %s. Trying again.", queueId, cutoff, state);
      }
    } while (toDelete.size() == getSelectSize());
    return !toDelete.isEmpty();
  }
  
  @VisibleForTesting
  Map<Id<BatchQueueName>, BatchQueueDeletionPolicy> getQueueIdsToPurge() {
    return binder.getAllQueueNames().stream()
        .flatMap(name -> {
          BatchQueue<?, ?> queue = binder.getQueue(name);
          Id<BatchQueueName> queueId = binder.getRunner(name).getQueueId();
          return Stream.of(Pair.of(queueId, queue.getDeletionPolicy()));
        }).collect(pairsToMap());
  }
  
  @VisibleForTesting
  void deleteBatch(Id<BatchQueueName> queueId, List<ToDelete> toDelete, State state) {
    Set<Id<BatchQueueBatch>> batchIds = toDelete.stream()
        .map(td -> td.batchId)
        .collect(toSet());
    RangeSet<Id<BatchQueueItem>> itemIdSet = TreeRangeSet.create();
    toDelete.forEach(td -> itemIdSet.add(Range.closed(td.itemIdFrom, td.itemIdTo)));
    long nanoStart = nanoTimeProvider.get();
    transacter.execute(new WithSession() {
      
      @Inject BatchQueueBatchRepository batchRepository;
      @Inject BatchQueueItemRepository itemRepository;
      
      @Override
      public void run(DbSession session) {
        if (state == State.SENT) {
          batchRepository.deleteSentBatches(batchIds);
        } else if (state == State.IGNORED) {
          batchRepository.deleteIgnoredBatches(batchIds);
        } else {
          throw new IllegalArgumentException();
        }
        itemRepository.deleteRanges(itemIdSet);
      }
      
    });
    long elapsedNanos = nanoTimeProvider.get() - nanoStart;
    long totalDeleted = toDelete.stream().mapToLong(td -> td.size).sum();
    log.info("Deleted %s items in %s batches from queue %s in %s ms", totalDeleted, batchIds.size(), queueId, elapsedNanos / 1_000_000L);
    sleepToNotOverloadDatabase(elapsedNanos);
  }
  
  private void sleepToNotOverloadDatabase(long elapsedNanos) {
    long sleepMillis = SLEEP_BETWEEN_BATCHES.toBigDecimal().multiply(new BigDecimal(elapsedNanos)).longValue() / 1_000_000L;
    sleeper.sleep(Duration.millis(sleepMillis));
  }
  
  private List<ToDelete> getSomeToDelete(Id<BatchQueueName> queueId, DateTime cutoff, State state) {
    return transacter.execute(new WithReadOnlySessionExpression<List<ToDelete>>() {
      @Inject BatchQueueBatchRepository repository;
      
      @Override
      public List<ToDelete> run(DbSession session) {
        List<BatchQueueBatch> batches;
        if (state == State.SENT) {
          batches = repository.getSentOlderThan(queueId, cutoff, getSelectSize());
        } else if (state == State.IGNORED) {
          batches = repository.getIgnoredOlderThan(queueId, cutoff, getSelectSize());
        } else {
          throw new IllegalArgumentException();
        }
        return batches.stream()
            .map(batch -> new ToDelete(batch.getId(), batch.getItemIdFrom(), batch.getItemIdTo(), batch.getSize()))
            .collect(toList());
      }
    });
  }
  
  @VisibleForTesting
  static List<List<ToDelete>> partition(List<ToDelete> inputList, int size) {
    List<List<ToDelete>> output = new ArrayList<>();
    
    List<ToDelete> newPartition = new ArrayList<>();
    int numItems = 0;
    for (ToDelete input : inputList) {
      if (numItems > 0 && numItems + input.size > size) {
        numItems = 0;
        output.add(newPartition);
        newPartition = new ArrayList<>();
      }
      numItems += input.size;
      newPartition.add(input);
    }
    if (!newPartition.isEmpty()) {
      output.add(newPartition);
    }
    return output;
  }

  @VisibleForTesting
  enum State {
    SENT,
    IGNORED
  }
  
  @VisibleForTesting
  static class ToDelete {
    
    private final Id<BatchQueueBatch> batchId;
    private final Id<BatchQueueItem> itemIdFrom;
    private final Id<BatchQueueItem> itemIdTo;
    private final int size;

    ToDelete(Id<BatchQueueBatch> batchId, Id<BatchQueueItem> itemIdFrom, Id<BatchQueueItem> itemIdTo, int size) {
      this.batchId = batchId;
      this.itemIdFrom = itemIdFrom;
      this.itemIdTo = itemIdTo;
      this.size = size;
    }
    
  }
  
  @VisibleForTesting
  int getSelectSize() {
    return SELECT_SIZE;
  }
  
  @VisibleForTesting
  int getMaxItemsInSingleTransaction() {
    return MAX_ITEMS_IN_SINGLE_TRANSACTION;
  }
  
}
