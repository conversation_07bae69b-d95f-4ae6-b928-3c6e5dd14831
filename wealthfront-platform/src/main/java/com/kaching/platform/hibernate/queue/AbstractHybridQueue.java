package com.kaching.platform.hibernate.queue;

import static com.kaching.platform.common.logging.Log.logContextPut;
import static com.kaching.platform.functional.Unit.unit;
import static com.kaching.platform.hibernate.RetryingTransacter.retrying;
import static com.kaching.platform.hibernate.queue.AbstractHybridQueue.ProcessOutcome.DO_NOT_REENQUEUE;
import static com.kaching.platform.hibernate.queue.AbstractHybridQueue.ProcessOutcome.INCREMENT_UNKNOWN_AND_REENQUEUE_LONG_DELAY_WITH_BACKOFF;
import static com.kaching.platform.hibernate.queue.AbstractHybridQueue.ProcessOutcome.REENQUEUE_LONG_DELAY;
import static com.kaching.platform.hibernate.queue.AbstractHybridQueue.ProcessOutcome.REENQUEUE_SHORT_DELAY;
import static com.kaching.platform.hibernate.queue.AbstractHybridQueue.ProcessOutcome.REENQUEUE_SPECIFIED_DELAY;
import static com.kaching.platform.hibernate.queue.AbstractHybridQueue.ProcessOutcome.UNSET;
import static com.kaching.platform.multicolo.MultiColoStatusProvider.ColoStatus.MOCK_FAILOVER_FAKE_MASTER;
import static java.lang.Integer.max;
import static java.lang.Long.max;
import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static java.util.concurrent.TimeUnit.SECONDS;
import static java.util.stream.Collectors.toMap;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import org.apache.http.conn.ConnectTimeoutException;
import org.joda.time.DateTime;
import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Striped;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.Author;
import com.kaching.monitor.esp.ResponsiblePartyFinder;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.Leader;
import com.kaching.platform.discovery.LocalAnnouncement;
import com.kaching.platform.discovery.ResolutionException;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.multicolo.MultiColoStatusProvider;
import com.kaching.platform.queryengine.OriginInfo;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.util.Preconditions;
import com.kaching.util.Sleeper;
import com.kaching.util.time.DateTimes;
import com.wealthfront.util.collections.Expiring;
import com.wealthfront.util.collections.ExpiringSet;

public abstract class AbstractHybridQueue<E extends HybridQueueEntity, S> implements HybridQueue<E> {

  enum ProcessOutcome {
    UNSET,
    DO_NOT_REENQUEUE,
    REENQUEUE_IMMEDIATELY,
    REENQUEUE_SPECIFIED_DELAY,
    REENQUEUE_SHORT_DELAY,
    REENQUEUE_LONG_DELAY,
    INCREMENT_UNKNOWN_AND_REENQUEUE_LONG_DELAY_WITH_BACKOFF,
  }

  private static final Log log = Log.getLog(AbstractHybridQueue.class);

  private static final int DEFAULT_UNKNOWN_EXCEPTION_LIMIT = 3;
  private static final int DEFAULT_NUM_IDS_TO_LOAD = 5000;

  private static final List<Class<? extends Throwable>> ERROR_FLAG_EXCEPTIONS =
      ImmutableList.<Class<? extends Throwable>>builder()
          .add(HybridQueueErrorFlagException.class)
          .build();

  private static final List<Class<? extends Throwable>> SHOULD_PAUSE_QUEUE_EXCEPTIONS =
      ImmutableList.<Class<? extends Throwable>>builder()
          .add(ResolutionException.class)
          .add(ConnectTimeoutException.class)
          .build();

  private final AtomicBoolean shouldWakeUp = new AtomicBoolean(false);
  private final AtomicReference<DateTime> checkDbAt = new AtomicReference<>();
  private final AtomicReference<DateTime> checkDelayQueueAt = new AtomicReference<>();
  private final Striped<Lock> idLocks = Striped.lock(4096);
  @VisibleForTesting final HybridQueueThreadPoolExecutor executor;
  @VisibleForTesting final DelayQueue<Expiring<ProcessSingleItem<E, S>>> delayedEnqueues = new DelayQueue<>();
  @VisibleForTesting final ExpiringSet<Object> pausedCategories = new ExpiringSet<>();
  private final Set<Id<E>> processedIgnoredIds = Sets.newConcurrentHashSet();
  private final AtomicLong processedIgnoredIdCounts = new AtomicLong(0);

  @VisibleForTesting public volatile boolean hasStarted = false;
  private volatile boolean shouldShutdown = false;
  private volatile boolean hasShutdownSuccessfully = false;

  /**
   * Only add or remove ids from this collection while holding the id lock.
   */
  @VisibleForTesting final Set<Id<E>> enqueuedIds = Sets.newConcurrentHashSet();

  final int initialWorkers;

  protected AbstractHybridQueue(int initialWorkers) {
    this.initialWorkers = initialWorkers;
    executor = new HybridQueueThreadPoolExecutor(initialWorkers);
  }

  @Inject public Transacter transacter;
  @Inject public Provider<DateTime> clock;
  @Inject @Leader public Provider<Boolean> isLeader;
  @Inject public MultiColoStatusProvider multiColoStatusProvider;
  @Inject public LocalAnnouncement localAnnouncement;
  @Inject public Sleeper sleeper;
  @Inject public StackTraceMonitor stackTraceMonitor;
  @Inject public ServiceKind serviceKind;
  @Inject public ResponsiblePartyFinder responsiblePartyFinder;

  @Override
  public Class<E> getEntityClass() {
    return getEntityRepository(null).getEntityClass();
  }

  protected abstract HybridQueueEntityRepository<E> getEntityRepository(DbSession session);

  /**
   * Do not return null from this method. To retry or set the error flag, throw an appropriate Exception.
   * See {@link #isIgnoredException(Exception)} or {@link #isErrorFlagException(Exception)}
   */
  protected abstract S transform(E entity, DbSession session);

  protected abstract void send(S sendValue, Id<E> id);

  /**
   * Entities with their error flag set will not be processed until it is cleared. This will most likely require
   * manual intervention. External monitoring might be appropriate, but if you want notification as soon as this
   * occurs, you can override this method.
   * <p>
   * If you want to make a network call e.g. through Pager, use session.onCommit() to avoid doing it in a transaction.
   */
  protected void onSetErrorFlag(Exception e, E entity, DbSession session) {
    if (stackTraceMonitor != null) {
      session.onCommit(() -> addToStm(e));
    }
  }

  /**
   * By default, certain exceptions in send() will cause a temporary halt to avoid overloading any alerting
   * mechanisms and causing pointless load. However, some queues might send to multiple destinations or otherwise
   * want this throttling to only apply to certain categories of entity on the queue.
   * <p>
   * Overload this method to divide your queue results into categories. Throttling will happen within each category of
   * transformedValues where the return value of this method matches by hashCode() and equals().
   */
  @SuppressWarnings("unused")
  protected Object categorizeForPausing(S transformedValue) {
    return unit;
  }

  /**
   * This is provided in case you need to lock more than just the queue entity itself while processing it.
   * <p>
   * The queue will not poll, transform(), or send() without acquiring this lock first, though it may set
   * the error flag without holding this if necessary.
   */
  @SuppressWarnings("unused")
  protected boolean extraTryLock(E entity, DbSession session) {
    return true;
  }

  /**
   * If you override extraTryLock you must override extraUnlock as well.
   */
  @SuppressWarnings("unused")
  protected void extraUnlock(E entity, DbSession session) {
  }

  @Override
  public final boolean trySafeExtraUnlock(Id<E> entityId, DbSession session) {
    E entity = getEntityRepository(session).getOrThrow(entityId);
    idLocks.get(entityId).lock();
    try {
      if (entity.isSendable()) {
        return false;
      }
      extraUnlock(entity, session);
      return true;
    } finally {
      idLocks.get(entityId).unlock();
    }
  }

  /**
   * If you override getCustomComparable you must override bulkGetCustomComparables as well.
   * Repository methods to poll unsent items should likely follow a similar ordering.
   * This method will only be called after the entity is saved (has an ID).
   * <p>
   * The hybrid queue only guarantees approximate order, as many items may execute simultaneously.
   * If strict ordering is required, it should be implemented with locking and retrying.
   */
  protected Comparable<?> getCustomComparable(E entity, DbSession session) {
    return null;
  }

  /**
   * If you override getCustomComparable you must override bulkGetCustomComparables as well
   */
  protected Map<Id<E>, Comparable<?>> bulkGetCustomComparables(Set<Id<E>> ids, DbSession session) {
    return Collections.emptyMap();
  }

  /**
   * The extra lock is held when calling this
   */
  protected void onTransformException(Exception e, E entity, DbSession session) {
  }

  /**
   * The extra lock is held when calling this
   */
  protected void onSendException(Exception e, Id<E> id, S sendValue) {
  }

  protected boolean leaderOnly() {
    return true;
  }

  /**
   * Override this method to add specific handling for whether an entity should be enqueued.
   * Returning true means the entity will be enqueued, returning false means the entity will just be saved.
   */
  protected boolean shouldEnqueueEntity(E entity) {
    return true;
  }

  @Override
  public final QueueMonitoringParams getMonitoringParams() {
    QueueMonitoringParams params = new QueueMonitoringParams();
    configureMonitoring(params);
    return params;
  }

  protected void configureMonitoring(QueueMonitoringParams params) {
  }

  /**
   * Each queue item requires 3 database transactions: one to persist it; one to poll and transform it;
   * and one to mark it as sent. However, you may batch the persisting by keeping a session open for multiple enqueues.
   * <p>
   * Do not enqueue too many entities in a single transaction. Sending the entities cannot begin until the
   * database transaction has finished.
   */
  @Override
  public Id<E> persistAndEnqueue(E entity, DbSession session) {
    log.debug("starting persistAndEnqueue");
    if (entity.getId() != null) {
      throw new IllegalArgumentException("Do not enqueue an entity that has already been persisted");
    }
    if (!entity.isSendable()) {
      throw new IllegalArgumentException("Do not enqueue an entity that cannot be sent");
    }

    if (shouldNotExecute()) {
      Id<E> id = session.save(entity);
      log.debug("just saving id " + id + ", not enqueueing it." +
          " hasStarted: " + hasStarted + "." +
          " isLeader: " + (isLeader != null ? isLeader.get() : null) + "." +
          " multiColoStatus: " + multiColoStatusProvider.getStatus() + "." +
          " shouldShutDown:" + shouldShutdown());
      return id;
    }

    if (!shouldEnqueueEntity(entity)) {
      Id<E> id = session.save(entity);
      log.debug("just saving id " + id + ", not enqueueing it. shouldEnqueueEntity returned false");
      return id;
    }

    entity.setPolledTime(clock.get());
    Id<E> id = session.save(entity);
    Comparable<?> comparable = getCustomComparable(entity, session);

    log.debug("just saved id " + id);
    session.onCommit(() -> {
      if (enqueuedIds.add(id)) {
        log.debug("about to add id " + id + " to executor");
        enqueueSingleItem(new ProcessSingleItem<>(id, 0, this, new WrappedComparable(comparable, id.getId())));
      }
    });

    return id;
  }

  /**
   * This method should be called from outside a transaction, and is meant to signal that a queue should add a given
   * entity to its execution queue. This method will throw if the entity in question is not already persisted.
   * <p>
   * This method is most useful if an entity is being enqueued on one service but will be processed on another, and
   * will almost always be used in a cross service call to the SignalEnqueueHybridQueueEntity query.
   */
  @Override
  public boolean signalEnqueue(Id<E> entityId) {
    if (shouldNotExecute()) {
      log.debug("not enqueueing id " + entityId + "." +
          " hasStarted: " + hasStarted + "." +
          " isLeader: " + (isLeader != null ? isLeader.get() : null) + "." +
          " multiColoStatus: " + multiColoStatusProvider.getStatus() + "." +
          " shouldShutDown:" + shouldShutdown());
      return false;
    }

    Comparable<?> comparable = transacter.executeWithReadOnlySessionExpression(session -> {
      E entity = session.get(getEntityClass(), entityId)
          .getOrThrow("Do not signal enqueue an entity that has not already been persisted");

      return getCustomComparable(entity, session);
    });

    if (enqueuedIds.add(entityId)) {
      log.debug("about to add id " + entityId + " to executor");
      enqueueSingleItem(
          new ProcessSingleItem<>(entityId, 0, this, new WrappedComparable(comparable, entityId.getId())));
    }
    return true;
  }

  void addToStm(Exception e) {
    stackTraceMonitor.add(e, getResponsibleParties());
  }

  Set<Author> getResponsibleParties() {
    return new HashSet<>(responsiblePartyFinder.findResponsibleParties(serviceKind.getClass(), this.getClass()));
  }

  @Override
  public void run() {
    logContextPut("originType", OriginInfo.Type.HYBRID_QUEUE.name());
    logContextPut("originIdentifier", this.getClass().getName());
    DateTime lastCheckedDbAt = clock.get().minusDays(1);
    checkDbAt.set(clock.get());
    checkDelayQueueAt.set(clock.get());
    hasStarted = true;
    while (true) {
      try {
        DateTime now = clock.get();
        if (!shouldNotExecute()) {
          DateTime checkTimeSaneUpperBound = now.plusMillis(getMaxPollIntervalMillis());

          DateTime checkDelayQueueAtTime = checkDelayQueueAt.updateAndGet(nextCheckTime ->
              DateTimes.earliest(nextCheckTime, checkTimeSaneUpperBound));
          if (now.isAfter(checkDelayQueueAtTime)) {
            Expiring<ProcessSingleItem<E, S>> polled;
            while ((polled = delayedEnqueues.poll()) != null) {
              log.trace("pulling id " + polled.getValue().id + " off the delay queue and onto the regular queue");
              enqueueSingleItem(polled.getValue());
            }
            checkDelayQueueAt.updateAndGet(unused -> {
              Expiring<ProcessSingleItem<E, S>> nextDelayedItem = delayedEnqueues.peek();
              if (nextDelayedItem == null) {
                return now.plusMillis(getMaxPollIntervalMillis());
              } else {
                return now.plusMillis(max(1, (int) nextDelayedItem.getDelay(MILLISECONDS)));
              }
            });
          }

          lastCheckedDbAt = DateTimes.earliest(lastCheckedDbAt, checkTimeSaneUpperBound);
          DateTime checkDbAtTime = checkDbAt.updateAndGet(currentCheckDbAtTime ->
              DateTimes.earliest(currentCheckDbAtTime, checkTimeSaneUpperBound));
          log.trace("now:" + now + ". checkDbAtTime:" + checkDbAtTime);
          if (now.isAfter(checkDbAtTime)) {
            if (executor.getQueue().isEmpty() ||
                now.isAfter(lastCheckedDbAt.plusMillis(getMinDbPollIntervalMillis()))) {
              log.trace("checking db");
              lastCheckedDbAt = now;
              checkDbAt.set(now.plusMillis(getMaxPollIntervalMillis()));
              Map<Id<E>, WrappedComparable> ids;
              ids = loadUnsentFromDatabase(DEFAULT_NUM_IDS_TO_LOAD);
              for (Map.Entry<Id<E>, WrappedComparable> entry : ids.entrySet()) {
                log.trace("enqueueing id " + entry + " from database");
                enqueueSingleItem(new ProcessSingleItem<>(entry.getKey(), 0, this, entry.getValue()));
              }
            } else {
              checkDbAt.set(lastCheckedDbAt.plus(getMinDbPollIntervalMillis()));
            }
          }
        } else {
          checkDelayQueueAt.set(now.plusMillis(getMaxPollIntervalMillis()));
          checkDbAt.set(now.plusMillis(getMaxPollIntervalMillis()));
        }

        awaitNotifyEnqueue();

        if (shouldShutdown()) {
          while (!hasShutdownSuccessfully) {
            try {
              executor.shutdownNow();
              hasShutdownSuccessfully = executor.awaitTermination(30, SECONDS);
            } catch (InterruptedException ignored) {
              log.trace("ignored InterruptedException in shutdown");
            }
          }
          break;
        }
      } catch (RuntimeException e) {
        addToStm(new RuntimeException("Unhandled exception in queue main thread." +
            " Queue execution will continue but this should be investigated.", e));
        sleeper.sleep(Duration.millis(getShortDelayLengthMillis()));
      }
    }
  }

  private boolean shouldNotExecute() {
    return !hasStarted ||
        (leaderOnly() && !isLeader.get()) ||
        shouldShutdown() ||
        !localAnnouncement.isAnnounced() ||
        (shouldNotExecuteOnMockFailoverFakeMaster() &&
            multiColoStatusProvider.getStatus() == MOCK_FAILOVER_FAKE_MASTER);
  }

  protected boolean shouldNotExecuteOnMockFailoverFakeMaster() {
    return true;
  }

  @Override
  @Deprecated
  public void notifyEnqueue() {
    checkDatabaseNow();
  }

  void checkDatabaseNow() {
    synchronized (shouldWakeUp) {
      checkDbAt.set(clock.get());
      shouldWakeUp.set(true);
      shouldWakeUp.notifyAll();
    }
  }

  public void shutdownGracefully(boolean blockUntilFinished) {
    shouldShutdown = true;
    checkDatabaseNow();
    if (blockUntilFinished) {
      while (!hasShutdownSuccessfully) {
        sleeper.sleep(Duration.millis(100));
        checkDatabaseNow();
      }
    }
  }

  @VisibleForTesting
  Map<Id<E>, WrappedComparable> loadUnsentFromDatabase(int count) {
    log.trace("loadUnsentFromDatabase. " + enqueuedIds.size() + " enqueued ids.");
    Map<Id<E>, WrappedComparable> someIds = transacter.executeWithReadOnlySessionExpression(session -> {
      HybridQueueEntityRepository<E> repo = getEntityRepository(session);
      List<Id<E>> ids = repo.getSomeUnpolledIds(count);
      if (ids.size() < count) {
        ids.addAll(repo.getSomePolledUnsentIds(count - ids.size()));
      }
      return bulkGetAndWrapComparableIdentifiers(ImmutableSet.copyOf(ids), session);
    });
    Striped<Lock> idLocks = getIdLocks();
    ImmutableMap.Builder<Id<E>, WrappedComparable> idsToEnqueue =
        ImmutableMap.<Id<E>, WrappedComparable>builderWithExpectedSize(someIds.size())
            .orderEntriesByValue(Comparator.naturalOrder());
    for (Map.Entry<Id<E>, WrappedComparable> entry : someIds.entrySet()) {
      if (idLocks.get(entry.getKey()).tryLock()) {
        try {
          if (enqueuedIds.add(entry.getKey())) {
            idsToEnqueue.put(entry.getKey(), entry.getValue());
          }
        } finally {
          idLocks.get(entry.getKey()).unlock();
        }
      }
    }
    ImmutableMap<Id<E>, WrappedComparable> result = idsToEnqueue.buildOrThrow();
    log.trace("loadUnsentFromDatabase. " + someIds.size() + " ids from db. added " + result.size() + " new ids.");
    return result;
  }

  @VisibleForTesting
  Map<Id<E>, WrappedComparable> bulkGetAndWrapComparableIdentifiers(Set<Id<E>> ids, DbSession session) {
    Map<Id<E>, Comparable<?>> result = bulkGetCustomComparables(ids, session);
    return ids.stream().collect(toMap(id -> id, id -> new WrappedComparable(result.get(id), id.getId())));
  }

  @VisibleForTesting
  protected int getMinDbPollIntervalMillis() {
    return 10_000;
  }

  @VisibleForTesting
  protected int getMaxPollIntervalMillis() {
    return 60_000;
  }

  @VisibleForTesting
  protected int getShortDelayLengthMillis() {
    return 100;
  }

  @VisibleForTesting
  protected int getLongDelayLengthMillis() {
    return 30_000;
  }

  @VisibleForTesting
  protected int getPauseLengthMillis() {
    return 30_000;
  }

  @VisibleForTesting
  protected int getUnknownExceptionLimit() {
    return DEFAULT_UNKNOWN_EXCEPTION_LIMIT;
  }

  @VisibleForTesting
  Striped<Lock> getIdLocks() {
    return idLocks;
  }

  @VisibleForTesting
  boolean shouldShutdown() {
    return shouldShutdown;
  }

  @VisibleForTesting
  void enqueueSingleItem(ProcessSingleItem<E, S> processSingleItem) {
    log.trace("enqueueing id " + processSingleItem.id + " directly");
    executor.execute(processSingleItem);
  }

  @VisibleForTesting
  void enqueueSingleItemWithDelay(ProcessSingleItem<E, S> processSingleItem, int delayMillis) {
    DateTime thisItemReadyTime = clock.get().plusMillis(delayMillis);
    delayedEnqueues.add(new Expiring<>(processSingleItem, clock, thisItemReadyTime));
    DateTime nextCheckTime = checkDelayQueueAt.getAndUpdate(checkAt -> DateTimes.earliest(thisItemReadyTime, checkAt));
    if (thisItemReadyTime.isBefore(nextCheckTime)) {
      synchronized (shouldWakeUp) {
        shouldWakeUp.notifyAll();
      }
    }
  }

  private void awaitNotifyEnqueue() {
    synchronized (shouldWakeUp) {
      if (!shouldWakeUp.get()) {
        try {
          DateTime now = clock.get();
          DateTime nextWakeUpTime = DateTimes.earliest(checkDbAt.get(), checkDelayQueueAt.get());
          long sleepMillis = max(1, nextWakeUpTime.getMillis() - now.getMillis());
          log.trace("going to sleep. now:" + now + " nextWakeUpTime:" + nextWakeUpTime + " sleepMillis:" + sleepMillis);
          shouldWakeUp.wait(sleepMillis);
        } catch (InterruptedException ignored) {
          log.trace("ignored InterruptedException");
        }
      }
      shouldWakeUp.set(false);
    }
  }

  /**
   * Must hold the idLock before calling this
   */
  private void onUnhandledThreadException(Exception e, Id<E> id) {
    log.error(e, "Unhandled exception in an executor thread! Setting error flag on " + id + ".");
    setErrorFlag(e, id);
  }

  /**
   * Must hold the idLock and extra lock before calling this
   */
  @SuppressWarnings("unchecked")
  private ProcessOutcome handleExceptionInTransform(
      Exception e, E entity, DbSession session, int previousUnknownExceptionCount) {
    onTransformException(e, entity, session);

    Id<E> id = (Id<E>) entity.getId();
    if (isIgnoredException(e)) {
      log.info(e, "Ignored exception on transform of id " + id);
      return REENQUEUE_LONG_DELAY;
    } else if (isErrorFlagException(e)) {
      log.error(e, "Error flag exception on transform of id " + id);
      setErrorFlag(e, entity, session);
      return DO_NOT_REENQUEUE;
    } else {
      int unknownExceptionCount = previousUnknownExceptionCount + 1;
      log.warn(e, "Unknown exception " +
          "(" + unknownExceptionCount + " of " + getUnknownExceptionLimit() + " before setting error flag) " +
          "on transform of id " + id);
      if (unknownExceptionCount >= getUnknownExceptionLimit()) {
        setErrorFlag(e, entity, session);
        return DO_NOT_REENQUEUE;
      } else {
        return INCREMENT_UNKNOWN_AND_REENQUEUE_LONG_DELAY_WITH_BACKOFF;
      }
    }
  }

  /**
   * Must hold the idLock and extra lock before calling this
   */
  @VisibleForTesting
  ProcessOutcome handleExceptionInSend(Exception e, Id<E> id, S sendValue, int previousUnknownExceptionCount) {
    onSendException(e, id, sendValue);

    if (exceptionShouldPauseQueue(e)) {
      log.info(e, "Exception on id " + id + " should pause queue");
      Object category = categorizeForPausing(sendValue);
      pausedCategories.add(category, clock, clock.get().plusMillis(getPauseLengthMillis()));
      return REENQUEUE_LONG_DELAY;
    } else if (isIgnoredException(e)) {
      log.info(e, "Ignored exception on send of id " + id);
      return REENQUEUE_LONG_DELAY;
    } else if (isErrorFlagException(e)) {
      log.error(e, "Error flag exception on send of id " + id);
      setErrorFlag(e, id);
      return DO_NOT_REENQUEUE;
    } else {
      int unknownExceptionCount = previousUnknownExceptionCount + 1;
      log.warn(e, "Unknown exception " +
          "(" + unknownExceptionCount + " of " + getUnknownExceptionLimit() + " before setting error flag) " +
          "on send of id " + id);
      if (unknownExceptionCount >= getUnknownExceptionLimit()) {
        setErrorFlag(e, id);
        return DO_NOT_REENQUEUE;
      } else {
        return INCREMENT_UNKNOWN_AND_REENQUEUE_LONG_DELAY_WITH_BACKOFF;
      }
    }
  }

  private void markAsSent(E entity, DbSession session) {
    try {
      entity.setSentTime(clock.get());
      session.update(entity);
    } catch (Exception e) {
      log.error(e, "failed to markAsSent for entity id " + entity.getId());
    }
  }

  /**
   * Must hold the idLock before calling this
   */
  @VisibleForTesting
  void setErrorFlag(Exception e, E entity, DbSession session) {
    entity.setErrorFlag(true);
    onSetErrorFlag(e, entity, session);
  }

  /**
   * Must hold the idLock before calling this
   */
  private void setErrorFlag(Exception e, Id<E> id) {
    try {
      transacter.executeWithSession(session -> {
        E entity = getEntityRepository(session).getOrThrow(id);
        if (entity.getSentTime() == null && !entity.isIgnored()) {
          setErrorFlag(e, entity, session);
        } else {
          log.warn("Not setting error flag for entity id " + id + " because it has been sent or ignored.");
        }
      });
    } catch (RuntimeException ex) {
      log.warn(ex, "Failed to set error flag for entity id " + id);
    }
  }

  /**
   * These exceptions will pause the queue for a short time. They should represent conditions that would typically
   * affect the entire queue rather than an individual entity.
   * <p>
   * Because these are queue-wide, the error flag will never be set on an entity as a result of these exceptions.
   * <p>
   * If you override it to add more, you probably want to call super.exceptionShouldPauseQueue() as well.
   */
  protected boolean exceptionShouldPauseQueue(Exception e) {
    return Throwables.getCausalChain(e).stream().map(Throwable::getClass)
        .anyMatch(SHOULD_PAUSE_QUEUE_EXCEPTIONS::contains);
  }

  /**
   * The error flag will never be set on an entity as a result of these exceptions.
   */
  protected boolean isIgnoredException(Exception e) {
    return false;
  }

  /**
   * The error flag will be set on an entity right away as a result of these exceptions, instead of retrying
   * a few times first.
   * <p>
   * If you override it to add more, you probably want to call super.isErrorFlagException() as well.
   * <p>
   * Note that any exceptions that extend HybridQueueErrorFlagException are considered error flag exceptions.
   */
  protected boolean isErrorFlagException(Exception e) {
    return Throwables.getCausalChain(e).stream().map(Throwable::getClass).anyMatch(ERROR_FLAG_EXCEPTIONS::contains);
  }

  public int getWorkerCount() {
    return executor.getCorePoolSize();
  }

  public void setWorkerCount(int count) {
    Preconditions.checkQueryArgument(count > 0, "worker count must be positive");
    if (count < executor.getMaximumPoolSize()) {
      executor.setCorePoolSize(count);
      executor.setMaximumPoolSize(count);
    } else {
      executor.setMaximumPoolSize(count);
      executor.setCorePoolSize(count);
    }
  }

  public int getActiveCount() {
    return executor.getActiveCount();
  }

  public int getExecutorQueueSize() {
    return executor.getQueue().size();
  }

  @Override
  public boolean tryLockAndExecute(Id<E> id, DbSession session, Consumer<E> execute) {
    if (!getIdLocks().get(id).tryLock()) {
      return false;
    }
    try {
      E entity = getEntityRepository(session).getOrThrow(id);
      if (!extraTryLock(entity, session)) {
        return false;
      }
      try {
        execute.accept(entity);
        return true;
      } finally {
        extraUnlock(entity, session);
      }
    } finally {
      getIdLocks().get(id).unlock();
    }
  }

  @Override
  public void suspend() {
    executor.suspend();
  }

  @Override
  public void unsuspend() {
    executor.unsuspend();
  }

  @Override
  public boolean isSuspended() {
    return executor.isSuspended;
  }

  @Override
  public String getDebugState(Id<E> itemId, DbSession session) {
    boolean wasUnlocked = idLocks.get(itemId).tryLock();
    if (wasUnlocked) {
      idLocks.get(itemId).unlock();
    }

    E item = getEntityRepository(session).get((Id<E>) itemId);
    boolean wasExtraUnlocked = extraTryLock(item, session);
    if (wasExtraUnlocked) {
      extraUnlock(item, session);
    }

    boolean isEnqueued = enqueuedIds.contains(itemId);
    List<Expiring<ProcessSingleItem<E, S>>> delayedEntries = delayedEnqueues.stream()
        .filter(enqueue -> enqueue.getValue().id.equals(itemId))
        .collect(Collectors.toList());
    List<Long> secondsOfDelay = delayedEntries.stream()
        .map(enqueue -> enqueue.getDelay(SECONDS))
        .collect(Collectors.toList());
    List<Integer> unknownExceptionCount = delayedEntries.stream()
        .map(enqueue -> enqueue.getValue().previousUnknownExceptionCount)
        .collect(Collectors.toList());
    String itemDetailInfo = "Item " + itemId + " is " + (wasUnlocked ? "not " : "") + "locked, "
        + "is " + (wasExtraUnlocked ? "not " : "") + "extra-locked, "
        + "is " + (secondsOfDelay.isEmpty() ? "not " : "") + "delayed"
        + (secondsOfDelay.isEmpty() ? "" : " for " + secondsOfDelay + " seconds") + ", "
        + "has had " + unknownExceptionCount + " unknown exceptions, "
        + "and is " + (!isEnqueued ? "not " : "") + "enqueued.\n";

    return itemDetailInfo + getDebugState();
  }

  @Override
  public String getDebugState() {
    List<String> allDelayedEnqueues = delayedEnqueues.stream()
        .map(enqueue -> enqueue.getValue().id + ":" + enqueue.getDelay(SECONDS) + "s")
        .collect(Collectors.toList());
    int lockedLocks = 0;
    for (int i = 0; i < idLocks.size(); i++) {
      Lock lock = idLocks.getAt(i);
      boolean gotLock = lock.tryLock();
      if (gotLock) {
        lock.unlock();
      } else {
        lockedLocks++;
      }
    }
    return "Current time: " + clock.get() + "\n"
        + "enqueuedIds (" + enqueuedIds.size() + "): " + enqueuedIds + "\n"
        + "delayedEnqueues (" + allDelayedEnqueues.size() + "): " + allDelayedEnqueues + "\n"
        + "locked locks: " + lockedLocks + " of " + idLocks.size() + "\n"
        + "checkDbAt: " + checkDbAt + "\n"
        + "checkDelayQueueAt: " + checkDelayQueueAt + "\n"
        + "pausedCategories: " + pausedCategories + "\n"
        + "executor.getQueue().size(): " + executor.getQueue().size() + "\n"
        + "executor.getActiveCount(): " + executor.getActiveCount() + "\n"
        + "executor.getTaskCount(): " + executor.getTaskCount() + "\n"
        + "shouldNotExecute(): " + shouldNotExecute();
  }

  @VisibleForTesting
  public final void recordProcessedIgnoredId(Id<E> id) {
    if (processedIgnoredIds.size() < DEFAULT_NUM_IDS_TO_LOAD) {
      this.processedIgnoredIds.add(id);
    }
    this.processedIgnoredIdCounts.incrementAndGet();
  }

  @Override
  public final void resetProcessedIgnoredIds() {
    this.processedIgnoredIds.clear();
    this.processedIgnoredIdCounts.set(0);
  }

  @Override
  public final Set<Id<E>> getProcessedIgnoredIds() {
    return this.processedIgnoredIds;
  }

  @Override
  public final long getProcessedIgnoredCount() {
    return this.processedIgnoredIdCounts.get();
  }

  /**
   * Suspending features by Doug Lea. See PausableThreadPoolExecutor in JavaDoc for ThreadPoolExecutor.
   */
  class HybridQueueThreadPoolExecutor extends ThreadPoolExecutor {

    private boolean isSuspended = false;
    private final ReentrantLock suspendLock = new ReentrantLock();
    private final Condition unsuspended = suspendLock.newCondition();

    HybridQueueThreadPoolExecutor(int workerCount) {
      super(
          workerCount,
          workerCount,
          0, SECONDS,
          new PriorityBlockingQueue<>(1000, Comparator.comparing(o -> ((ProcessSingleItem<?, ?>) o).comparable))
      );
    }

    @Override
    protected void beforeExecute(Thread t, Runnable r) {
      super.beforeExecute(t, r);
      suspendLock.lock();
      try {
        while (isSuspended) {
          unsuspended.await();
        }
      } catch (InterruptedException ie) {
        t.interrupt();
      } finally {
        suspendLock.unlock();
      }
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void afterExecute(Runnable r, Throwable t) {
      if (t != null && Exception.class.isAssignableFrom(t.getClass())) {
        Exception e = (Exception) t;
        ProcessSingleItem<E, S> processSingleItem = (ProcessSingleItem<E, S>) r;
        onUnhandledThreadException(e, processSingleItem.id);
      }
      if (getActiveCount() <= 1 && getQueue().isEmpty()) {
        checkDatabaseNow();
      }
    }

    public void suspend() {
      suspendLock.lock();
      try {
        isSuspended = true;
      } finally {
        suspendLock.unlock();
      }
    }

    public void unsuspend() {
      suspendLock.lock();
      try {
        isSuspended = false;
        unsuspended.signalAll();
      } finally {
        suspendLock.unlock();
      }
    }

  }

  protected static class ProcessSingleItem<E extends HybridQueueEntity, S> implements Runnable {

    private static final Log log = Log.getLog(AbstractHybridQueue.class);

    private final Id<E> id;
    private final int previousUnknownExceptionCount;
    private final AbstractHybridQueue<E, S> queue;
    private final WrappedComparable comparable;

    @VisibleForTesting
    ProcessSingleItem(
        Id<E> id,
        int previousUnknownExceptionCount,
        AbstractHybridQueue<E, S> queue,
        WrappedComparable comparable) {
      this.id = id;
      this.previousUnknownExceptionCount = previousUnknownExceptionCount;
      this.queue = queue;
      this.comparable = comparable;
    }

    @VisibleForTesting
    Id<E> getId() {
      return id;
    }

    /**
     * handleOutcome() must be called immediately before any return point from this method.
     */
    @SuppressWarnings("UnnecessaryReturnStatement")
    @Override
    public void run() {
      logContextPut("query", this.getClass().getSimpleName());
      logContextPut("trace", UUID.randomUUID());
      logContextPut("originType", OriginInfo.Type.HYBRID_QUEUE.name());
      logContextPut("originIdentifier", queue.getClass().getName());
      if (queue.shouldNotExecute()) {
        log.trace("Not reenqueueing id " + id + " (should not execute)");
        handleOutcome(DO_NOT_REENQUEUE);
        return;
      }
      try {
        /* Try lock */
        if (!queue.getIdLocks().get(id).tryLock()) {
          log.trace("Reenqueueing id " + id + " (couldn't get id lock)");
          handleOutcome(REENQUEUE_SHORT_DELAY);
          return;
        }
        log.trace("Got lock of id " + id + " in run().");
        try {
          AtomicReference<ProcessOutcome> processOutcome = new AtomicReference<>(UNSET);

          /* Try extra lock and mark as polled, and transform */
          AtomicBoolean extraLockHeld = new AtomicBoolean(false);
          try {
            S sendValue = retrying(queue.transacter).executeWithSessionExpression(session -> {
              E entity = queue.getEntityRepository(session).get(id);

              if (entity == null) {
                log.debug("Not reenqueueing id " + id + " (doesn't exist)");
                processOutcome.set(DO_NOT_REENQUEUE);
                return null;
              }

              if (entity.isIgnored()) {
                log.debug("Not reenqueueing id " + id + " (ignored)");
                queue.recordProcessedIgnoredId(id);
                processOutcome.set(DO_NOT_REENQUEUE);
                return null;
              }

              if (!entity.isSendable()) {
                log.debug("Not reenqueueing id " + id + " (already sent or error flag set)");
                processOutcome.set(DO_NOT_REENQUEUE);
                return null;
              }

              /* Try extra lock */
              extraLockHeld.set(queue.extraTryLock(entity, session));
              if (!extraLockHeld.get()) {
                log.debug("Reenqueueing id " + id + " (couldn't get extra lock)");
                processOutcome.set(REENQUEUE_SHORT_DELAY);
                return null;
              }
              entity.setPolledTime(queue.clock.get());

              try {
                return queue.transform(entity, session);
              } catch (Exception e) {
                processOutcome.set(queue.handleExceptionInTransform(e, entity, session, previousUnknownExceptionCount));
                return null;
              }
            });
            if (processOutcome.get() != UNSET) {
              handleOutcome(processOutcome.get());
              return;
            }

            Object category = queue.categorizeForPausing(sendValue);
            synchronized (queue.pausedCategories) {
              if (queue.pausedCategories.contains(category)) {
                Long delayTime = queue.pausedCategories.getDelayTime(category, TimeUnit.MILLISECONDS);
                handleOutcome(REENQUEUE_SPECIFIED_DELAY, Option.some(delayTime.intValue()));
                return;
              }
            }

            /* Send */
            if (queue.shouldNotExecute()) {
              handleOutcome(DO_NOT_REENQUEUE);
              return;
            }
            try {
              log.debug("About to send id " + id);
              queue.send(sendValue, id);
            } catch (Exception e) {
              handleOutcome(queue.handleExceptionInSend(e, id, sendValue, previousUnknownExceptionCount));
              return;
            }

            /* Mark as sent*/
            retrying(queue.transacter).execute(3, (WithSession) session -> {
              HybridQueueEntityRepository<E> repo = queue.getEntityRepository(session);
              E entity = repo.getOrThrow(id);
              queue.markAsSent(entity, session);
              queue.extraUnlock(entity, session);
            });
            extraLockHeld.set(false);
            handleOutcome(DO_NOT_REENQUEUE);
            return;
          } catch (Exception e) {
            queue.onUnhandledThreadException(e, id);
            handleOutcome(DO_NOT_REENQUEUE);
            return;
          } finally {
            log.debug("Unlocking extra lock for id " + id);
            if (extraLockHeld.get()) {
              queue.transacter.executeWithSession(session -> {
                HybridQueueEntityRepository<E> repo = queue.getEntityRepository(session);
                queue.extraUnlock(repo.getOrThrow(id), session);
              });
            }
          }
        } finally {
          log.debug("Unlocking id " + id);
          queue.getIdLocks().get(id).unlock();
        }
      } catch (Exception e) {
        queue.getIdLocks().get(id).lock();
        try {
          log.debug("Got lock on id " + id + " in final catch");
          queue.onUnhandledThreadException(e, id);
          log.debug("Unlocking id " + id + " in final catch");
          handleOutcome(DO_NOT_REENQUEUE);
          return;
        } finally {
          queue.getIdLocks().get(id).unlock();
        }
      }
    }

    /**
     * Must hold the id lock before calling this
     */
    void handleOutcome(ProcessOutcome processOutcome) {
      handleOutcome(processOutcome, Option.none());
    }

    /**
     * Must hold the id lock before calling this
     */
    void handleOutcome(ProcessOutcome processOutcome, Option<Integer> delayMillis) {
      switch (processOutcome) {
        case UNSET:
          log.error(new RuntimeException("fake exception to capture stack trace"),
              "Not allowed to exit with processOutcome UNSET! id " + id);
          break;
        case DO_NOT_REENQUEUE:
          queue.enqueuedIds.remove(id);
          break;
        case REENQUEUE_IMMEDIATELY:
          log.trace("enqueueing id " + id + " immediately");
          queue.enqueueSingleItem(this);
          break;
        case REENQUEUE_SPECIFIED_DELAY:
          log.trace("enqueueing id " + id + " with short delay");
          queue.enqueueSingleItemWithDelay(this, delayMillis.getOrThrow());
          break;
        case REENQUEUE_SHORT_DELAY:
          log.trace("enqueueing id " + id + " with short delay");
          queue.enqueueSingleItemWithDelay(this, queue.getShortDelayLengthMillis());
          break;
        case REENQUEUE_LONG_DELAY:
          log.trace("enqueueing id " + id + " with long delay");
          queue.enqueueSingleItemWithDelay(this, queue.getLongDelayLengthMillis());
          break;
        case INCREMENT_UNKNOWN_AND_REENQUEUE_LONG_DELAY_WITH_BACKOFF:
          int delayForRetryMillis = queue.getLongDelayLengthMillis() * Math.min(4, previousUnknownExceptionCount + 1);
          log.trace("enqueueing id " + id + " with %sms delay and incrementing unknown", delayForRetryMillis);
          queue.enqueueSingleItemWithDelay(
              new ProcessSingleItem<>(id, previousUnknownExceptionCount + 1, queue, comparable),
              delayForRetryMillis
          );
          break;
        default:
          throw new IllegalArgumentException("Unexpected enum value " + processOutcome);
      }
    }

    @Override
    public String toString() {
      return id.toString();
    }

  }

  @VisibleForTesting
  protected static class WrappedComparable implements Comparable<WrappedComparable> {

    private final Comparable<?> customComparable;
    private final long id;

    public WrappedComparable(@Nullable Comparable<?> customComparable, long id) {
      this.customComparable = customComparable;
      this.id = id;
    }

    public Comparable<?> getCustomComparable() {
      return customComparable;
    }

    public long getId() {
      return id;
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      WrappedComparable that = (WrappedComparable) o;
      return id == that.id && Objects.equal(customComparable, that.customComparable);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(customComparable, id);
    }

    @Override
    public int compareTo(WrappedComparable o) {
      if (customComparable != null && o.customComparable != null) {
        return ((Comparable<Object>) customComparable).compareTo(o.customComparable);
      }
      if (customComparable == null && o.customComparable == null) {
        return Long.compare(id, o.id);
      }
      return customComparable == null ? 1 : -1;
    }

  }

}
