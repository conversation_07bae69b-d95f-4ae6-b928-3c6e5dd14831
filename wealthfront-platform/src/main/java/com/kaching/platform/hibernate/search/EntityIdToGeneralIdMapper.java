package com.kaching.platform.hibernate.search;

import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.kaching.util.id.IdExternalizerFetcher;

public interface EntityIdToGeneralIdMapper {

  default Set<Class<? extends AbstractHibernateEntity>> getAllSearchableEntities() {
    return Arrays.stream(this.getClass().getMethods())
        .filter(method -> method.isAnnotationPresent(EntitySearch.class))
        .map(method -> method.getAnnotation(EntitySearch.class).entity())
        .collect(Collectors.toSet());
  }

  default Option<Long> searchForId(String entitySimpleClassName, IdExternalizerFetcher fetcher, String entityFlexId) {
    for (Long entityId : getLong(entityFlexId)) {
      return searchForId(entitySimpleClassName, Id.of(entityId));
    }
    Class<?> entityClass = Arrays.stream(this.getClass().getMethods())
        .filter(method -> method.isAnnotationPresent(EntitySearch.class) &&
            method.getAnnotation(EntitySearch.class).entity().getSimpleName().equals(entitySimpleClassName))
        .map(method -> method.getAnnotation(EntitySearch.class).entity())
        .findFirst().orElseThrow(
            () -> new InvalidArgumentException(Strings.format("Entity %s has no matching method", entitySimpleClassName)));

    Id<?> internalId =
        fetcher.getIdExternalizer(entityClass)
            .getOrThrow(new InvalidArgumentException(
                Strings.format("Id externalizer for %s is not bound", entitySimpleClassName)))
            .internalize(entityFlexId)
            .getOrThrow(new InvalidArgumentException(
                Strings.format("search input is not a valid external id for %s", entitySimpleClassName)));
    return searchForId(entitySimpleClassName, internalId);
  }

  @SuppressWarnings("unchecked")
  default Option<Long> searchForId(
      String entitySimpleClassName,
      Id<?> entityId) {
    List<Method> matchingMethods = Arrays.stream(this.getClass().getMethods())
        .filter(method -> method.isAnnotationPresent(EntitySearch.class) &&
            method.getAnnotation(EntitySearch.class).entity().getSimpleName().equals(entitySimpleClassName))
        .collect(Collectors.toList());
    if (matchingMethods.isEmpty()) {
      throw new InvalidArgumentException(
          Strings.format("Entity %s has no matching method", entitySimpleClassName));
    } else if (matchingMethods.size() == 1) {
      try {
        return (Option<Long>) matchingMethods.get(0).invoke(this, entityId);
      } catch (IllegalAccessException | InvocationTargetException e) {
        throw new RuntimeException(e);
      }
    } else {
      throw new InvalidArgumentException(
          Strings.format("Entity %s has multiple matching methods: %s", entitySimpleClassName,
              matchingMethods.stream().map(Method::getName).collect(Collectors.toList())));
    }
  }

  @VisibleForTesting
  default void checkEntitySearchMethodsValid() {
    Set<String> invalidMethods = Arrays.stream(this.getClass().getDeclaredMethods())
        .filter(method -> method.isAnnotationPresent(EntitySearch.class))
        .filter(
            method -> method.getParameterTypes().length != 1 || method.getParameterTypes()[0] != Id.class ||
                !method.getGenericReturnType().getTypeName()
                    .equals("com.kaching.platform.common.Option<java.lang.Long>"))
        .map(Method::getName)
        .collect(Collectors.toSet());
    if (!invalidMethods.isEmpty()) {
      throw new InvalidArgumentException(Strings.format(
          "All @EntitySearch methods must have parameter type Id and return type Option<Long>. The following are in violation: %s",
          invalidMethods));
    }

    Map<Class<? extends AbstractHibernateEntity>, Long> allClasses = Arrays.stream(this.getClass().getDeclaredMethods())
        .filter(method -> method.isAnnotationPresent(EntitySearch.class))
        .map(method -> method.getAnnotation(EntitySearch.class).entity())
        .collect(groupingBy(p -> p, counting()));
    Map<Class<? extends AbstractHibernateEntity>, Long> duplicateClasses = allClasses.entrySet().stream()
        .filter(entry -> entry.getValue() > 1)
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    if (duplicateClasses.size() > 0) {
      throw new InvalidArgumentException(Strings.format(
          "There must be at most 1 @EntitySearch method for a given hibernate entity. The following are in violation: %s",
          duplicateClasses.entrySet().stream().map(entry -> Strings.format("%s:%s", entry.getKey(), entry.getValue()))
              .collect(Collectors.toList())));
    }
  }

  @VisibleForTesting
  default Option<Long> getLong(String flexId) {
    try {
      return Option.some(Long.parseLong(flexId));
    } catch (NumberFormatException e) {
      return Option.none();
    }
  }

}
