package com.kaching.platform.hibernate;

import static com.kaching.platform.common.logging.Log.getLog;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.logging.Log;

class TransacterLoggerStore {

  private static final String CONNECTION_POOL_PERF_LOG_NAME =
      TransacterLoggerStore.class.getName() + ".connection-pool-perf";
  private static final String SUPPLEMENTAL_PERF_LOG_NAME =
      TransacterLoggerStore.class.getName() + ".supplemental-perf";
  private static final String TRANSACTION_PERF_LOG_NAME = TransacterLoggerStore.class.getName() + ".transaction-perf";
  private static final String ELASTIC_SEARCH_TRANSACTION_PERF_LOG_NAME =
      TransacterLoggerStore.class.getName() + ".elastic-search-transaction-perf";

  @VisibleForTesting
  static final Log connectionPoolPerfLog = getLog(CONNECTION_POOL_PERF_LOG_NAME);

  @VisibleForTesting
  static final Log supplementalPerfLog = getLog(SUPPLEMENTAL_PERF_LOG_NAME);

  @VisibleForTesting
  static final Log transactionPerfLog = getLog(TRANSACTION_PERF_LOG_NAME);

  @VisibleForTesting
  static final Log elasticSearchTransactionPerfLog = getLog(ELASTIC_SEARCH_TRANSACTION_PERF_LOG_NAME);

  static final String getConnectionPoolPerfLogName() {
    return CONNECTION_POOL_PERF_LOG_NAME;
  }

  static final String getSupplementalPerfLogName() {
    return SUPPLEMENTAL_PERF_LOG_NAME;
  }

  static final String getTransactionPerfLogName() {
    return TRANSACTION_PERF_LOG_NAME;
  }

  static final String getElasticSearchTransactionPerfLogName() {
    return ELASTIC_SEARCH_TRANSACTION_PERF_LOG_NAME;
  }

  static final Log getConnectionPoolPerfLog() {
    return connectionPoolPerfLog;
  }

  static final Log getSupplementalPerfLog() {
    return supplementalPerfLog;
  }

  static final Log getTransactionPerfLog() {
    return transactionPerfLog;
  }

  static final Log getElasticSearchTransactionPerfLog() {
    return elasticSearchTransactionPerfLog;
  }

}
