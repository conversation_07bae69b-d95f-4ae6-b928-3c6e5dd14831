package com.kaching.platform.hibernate.queue.impl;

import java.util.List;
import java.util.Map;

import org.joda.time.DateTime;

import com.google.common.collect.ImmutableList;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.platform.hibernate.queue.BatchQueue;
import com.kaching.platform.hibernate.queue.BatchQueueUnsentItemCache;
import com.kaching.platform.hibernate.queue.BatchQueueUnsentItemGroup;
import com.kaching.platform.monitoring.RuntimeVarzGenerator;
import com.kaching.platform.monitoring.RuntimeVarzValue;

public class BatchQueueBatchServiceVarz implements RuntimeVarzGenerator {

  @Inject Map<Class<BatchQueue<?, ?>>, BatchQueueRunner<?, ?>> runnersByClass;
  @Inject Provider<DateTime> clock;
  @Inject BatchQueueUnsentItemCache unsentItemCache;
  @Inject BatchQueueOnlineServiceVarz onlineServiceVarz;

  @Override
  public List<RuntimeVarzValue> calculateValues() {
    ImmutableList.Builder<RuntimeVarzValue> values = ImmutableList.builder();
    values.addAll(onlineServiceVarz.calculateValues());
    for (Map.Entry<Class<BatchQueue<?, ?>>, BatchQueueRunner<?, ?>> entry : runnersByClass.entrySet()) {
      BatchQueueRunner<?, ?> runner = entry.getValue();
      values.addAll(getUnsentItemValues(entry.getKey(), runner.getQueue().getQueueName()));
    }
    return values.build();
  }

  private List<RuntimeVarzValue> getUnsentItemValues(Class<BatchQueue<?, ?>> batchQueueClass, String categoryName) {
    List<BatchQueueUnsentItemGroup> unsentItems = unsentItemCache.getMaybeStaleUnsentItems(batchQueueClass);
    DateTime oldestSendableSince = null;
    int numUnpolledBatches = 0;
    int numUnpolledItems = 0;
    int numPolledUnsentBatches = 0;
    int numPolledUnsentItems = 0;
    int numErroredBatches = 0;
    int numErroredItems = 0;
    for (BatchQueueUnsentItemGroup unsentGroup : unsentItems) {
      if (oldestSendableSince == null || (unsentGroup.getSendableSince().isDefined() && unsentGroup.getSendableSince().getOrThrow().isBefore(oldestSendableSince))) {
        oldestSendableSince = unsentGroup.getSendableSince().getOrNull();
      }
      if (unsentGroup.getErroredAt().isDefined()) {
        numErroredBatches += unsentGroup.getNumBatches();
        numErroredItems += unsentGroup.getNumItems();
      } else if (unsentGroup.getPolledAt().isDefined()) {
        numPolledUnsentBatches += unsentGroup.getNumBatches();
        numPolledUnsentItems += unsentGroup.getNumItems();
      } else {
        numUnpolledBatches += unsentGroup.getNumBatches();
        numUnpolledItems += unsentGroup.getNumItems();
      }
    }
    long criticalTimeMillis = oldestSendableSince == null ? 0 : clock.get().getMillis() - oldestSendableSince.getMillis();
    if (criticalTimeMillis < 0) {
      criticalTimeMillis = 0;
    }
    return ImmutableList.of(
        new RuntimeVarzValue.GaugeValue(categoryName, "NumUnpolledBatches", numUnpolledBatches),
        new RuntimeVarzValue.GaugeValue(categoryName, "NumUnpolledItems", numUnpolledItems),
        new RuntimeVarzValue.GaugeValue(categoryName, "NumPolledUnsentBatches", numPolledUnsentBatches),
        new RuntimeVarzValue.GaugeValue(categoryName, "NumPolledUnsentItems", numPolledUnsentItems),
        new RuntimeVarzValue.GaugeValue(categoryName, "NumErroredBatches", numErroredBatches),
        new RuntimeVarzValue.GaugeValue(categoryName, "NumErroredItems", numErroredItems),
        new RuntimeVarzValue.GaugeValue(categoryName, "CriticalTimeMillis", criticalTimeMillis)
    );
  }
  
}
