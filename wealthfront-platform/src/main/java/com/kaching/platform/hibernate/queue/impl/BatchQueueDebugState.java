package com.kaching.platform.hibernate.queue.impl;

import com.google.inject.Inject;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.queryengine.AbstractQuery;

public class BatchQueueDebugState extends AbstractQuery<String> {

  private final String queueName;

  public BatchQueueDebugState(String queueName) {
    this.queueName = queueName;
  }

  @Inject BatchQueueBinder binder;

  @Override
  public String process() {
    return binder.getRunner(queueName).debugState().toPrettyPrintString();
  }
  
}
