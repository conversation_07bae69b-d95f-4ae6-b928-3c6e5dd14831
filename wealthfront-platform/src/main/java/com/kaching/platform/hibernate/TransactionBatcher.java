package com.kaching.platform.hibernate;

import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;

public class TransactionBatcher {

  private static final Log log = Log.getLog(TransactionBatcher.class);

  RetryingTransacter transacter;
  private final int retryCount;

  @Inject
  public TransactionBatcher(Transacter transacter) {
    this(new Retrying<PERSON>ran<PERSON>cter(transacter), 0);
  }

  public TransactionBatcher(RetryingTransacter retryingTransacter, int retryCount) {
    this.transacter = retryingTransacter;
    this.retryCount = retryCount;
  }

  public <T> Batch<T> batch(final int batchSize, final BatchingQuery<T> batchingQuery) {
    return new Batch<>(transacter, batchSize, retryCount, batchingQuery);
  }

  public interface BatchingQuery<T> {

    List<T> fetch(DbSession session, int offset, int limit);

    int count(DbSession session);

  }

  /**
   * The callback object that processes one batches worth of objects.
   * <p/>
   * Usage note: although this looks quite similar to the callback for {@link WithSession} and similar, the
   * TransactionBatcher does not inject members of a BatchProcessor, and modifying it to do so is not trivial
   * because of the reuse of BatchProcessor instances across multiple transactions.
   */
  public interface BatchProcessor<T> {

    void process(DbSession session, List<T> items);
  }

  public static class Batch<T> {

    private final RetryingTransacter transacter;
    private final int batchSize;
    private final int retryCount;
    private final BatchingQuery<T> query;
    private int index = 0;

    public Batch(RetryingTransacter transacter, int batchSize, BatchingQuery<T> query) {
      this(transacter, batchSize, 0, query);
    }

    public Batch(RetryingTransacter transacter, int batchSize, int retryCount, BatchingQuery<T> query) {
      this.transacter = transacter;
      this.batchSize = batchSize;
      this.query = query;
      this.retryCount = retryCount;
    }

    @VisibleForTesting
    public BatchingQuery<T> getQuery(BatchingQuery<T> tapQuery) {
      return tapQuery;
    }

    public int process(final BatchProcessor<T> processor) {
      int total = transacter.execute(retryCount, new WithReadOnlySessionExpression<Integer>() {
        @Override
        public Integer run(DbSession session) {
          return getQuery(query).count(session);
        }
      });
      while (transacter.execute(retryCount, new WithSessionExpression<Boolean>() {
        @Override
        public Boolean run(DbSession session) {
          List<T> result = getQuery(query).fetch(session, index, batchSize);
          if (result.isEmpty()) {
            return false;
          }
          index += result.size();
          processor.process(session, result);
          if (result.size() < batchSize) {
            return false;
          }
          return true;
        }
      })) {
      }
      if (total != index) {
        log.error(new IllegalArgumentException(
            Strings.format("TransactionBatcher processed %s out of %s items.", index, total)));
      }
      return index;
    }

  }

}
