package com.kaching.platform.hibernate;

import static com.google.common.base.Joiner.on;
import static com.google.common.collect.Iterables.transform;
import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static com.google.common.collect.Sets.newHashSet;
import static com.wealthfront.util.stream.Predicates.not;
import static java.lang.String.format;
import static java.util.Arrays.asList;
import static java.util.regex.Pattern.compile;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.hibernate.cfg.Environment.DIALECT;
import static org.hibernate.criterion.Order.asc;
import static org.hibernate.criterion.Order.desc;
import static org.hibernate.criterion.Restrictions.lt;

import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.inject.Inject;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.cfg.Configuration;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.metadata.ClassMetadata;
import org.hibernate.persister.entity.AbstractEntityPersister;
import org.hibernate.persister.entity.EntityPersister;
import org.hibernate.persister.entity.OuterJoinLoadable;
import org.hibernate.persister.entity.SingleTableEntityPersister;
import org.hibernate.type.CollectionType;
import org.hibernate.type.CustomType;
import org.hibernate.type.Type;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.CaseFormat;
import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Sets;
import com.google.common.collect.Streams;
import com.google.inject.Singleton;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.logging.Log;
import com.kaching.util.Batcher;
import com.kaching.util.types.EnumTypeAdapter;

/**
 * Runtime checker which compares the Hibernate O/R mapping to the database
 * schema to report configuration and compatibility problems.
 */
@Singleton
public class HbmDbSchemaSyncCheck {

  private static final Log log = Log.getLog(HbmDbSchemaSyncCheck.class);
  private static final Pattern VARCHAR_PATTERN = compile("varchar\\((\\d+)\\)");

  @Inject Transacter transacter;
  @Inject Configuration configuration;
  @Inject Batcher batcher;

  public static HbmDbSchemaSyncCheck forExplicitTransacter(Transacter transacter, Configuration configuration, Batcher batcher) {
    HbmDbSchemaSyncCheck check = new HbmDbSchemaSyncCheck();
    check.transacter = transacter;
    check.configuration = configuration;
    check.batcher = batcher;
    return check;
  }

  /**
   * Checks Hibernate entities.
   */
  public Errors check(Class<?>... clazzez) {
    return check(asList(clazzez));
  }

  /**
   * Checks Hibernate entities.
   */
  public Errors check(final Iterable<Class<?>> clazzez) {
    final Errors errors = new Errors();
    String dialect = configuration.getProperty(DIALECT);
    boolean skipWellFormedVerification = !dialect.toLowerCase().contains("mysql");
    Set<String> classesToVerify = new HashSet<>();
    log.info("loading entities from db");
    batcher.splitList(clazzez).forEach(group -> transacter.executeWithReadOnlySession(dbSession -> {
      Session session = dbSession.getSession();
      loadEntitiesFromDb(session, errors, group);
      // We stop depending on dialect
      if (!skipWellFormedVerification) {
        classesToVerify.addAll(getAllPersistentClassNames(group, session.getSessionFactory()));
      }
    }));

    if (!skipWellFormedVerification) {
      Set<String> allGeneratedClassNames = getAllGeneratedClassNames(
          transacter.executeWithReadOnlySessionExpression(dbSession ->
              dbSession.getSession().getSessionFactory().getAllClassMetadata().keySet()));
      classesToVerify.addAll(getMatchingGeneratedVersionClassNames(clazzez, allGeneratedClassNames));

      batcher.splitList(classesToVerify).forEach(group -> transacter.executeWithReadOnlySession(dbSession -> {
        log.info("checking entities: %s", on(", ").join(group));
        Session session = dbSession.getSession();
        SessionFactory sessionFactory = session.getSessionFactory();
        for (String className : group) {
          try {
            AbstractEntityPersister metadata =
                (AbstractEntityPersister) sessionFactory.getClassMetadata(className);
            String superclass = metadata.getMappedSuperclass();
            AbstractEntityPersister parentClassMetadata = (AbstractEntityPersister) sessionFactory.getClassMetadata(
                superclass == null ? className : superclass);

            String[] propertyNames = getPropertyNames(metadata, parentClassMetadata);
            Pair<String, String> tableNameAndDiscriminator = getTableNameAndDiscriminatorColumnName(
                metadata, parentClassMetadata, Arrays.asList(propertyNames));
            String tableName = tableNameAndDiscriminator.getLeft();
            String discriminator = tableNameAndDiscriminator.getRight();
            Map<String, DbColumn> dbColumns = getDbColumns(tableName, session);
            log.info("checking table well-formed-ness: %s %s", className, tableName);
            checkTableWellFormedness(
                errors, className, metadata, discriminator, tableName, propertyNames, dbColumns);
          } catch (Exception e) {
            errors.addMessage("entity %s: unable to run well-formedness check, cause %s", className,
                e.getMessage());
          }
        }
      }));
    }
    return errors;
  }

  @VisibleForTesting
  public static Pair<String, String> getTableNameAndDiscriminatorColumnName(
      AbstractEntityPersister metadata, AbstractEntityPersister parentClassMetadata, List<String> propertyNames) {
    //noinspection OptionalGetWithoutIsPresent
    return metadata.isMultiTable() && !parentClassMetadata.isMultiTable()
        ? Pair.of(metadata.getPropertyTableName(propertyNames.stream().findFirst().get()), null)
        : Pair.of(metadata.getTableName(), removeQuotesFromColumn(metadata.getDiscriminatorColumnName()));
  }

  @VisibleForTesting
  public static String[] getPropertyNames(
      AbstractEntityPersister metadata, AbstractEntityPersister parentClassMetadata) {
    boolean notJoinedSubclassOrJoinedTable = !metadata.isMultiTable()
        || metadata.getEntityName().equals(parentClassMetadata.getEntityName());
    if (notJoinedSubclassOrJoinedTable) {
      return metadata.getPropertyNames();
    }

    boolean hasJoinedTable = metadata.isMultiTable() && !parentClassMetadata.isMultiTable();
    if (hasJoinedTable) {
      return Arrays.stream(metadata.getPropertyNames())
          .filter(property -> !Sets.newHashSet(parentClassMetadata.getPropertyNames()).contains(property))
          .filter(property -> !parentClassMetadata.getTableName().equals(metadata.getPropertyTableName(property)))
          .toArray(String[]::new);
    }

    return Arrays.stream(metadata.getPropertyNames())
        .filter(name -> !Sets.newHashSet(parentClassMetadata.getPropertyNames()).contains(name))
        .toArray(String[]::new);
  }

  @VisibleForTesting
  public static Set<String> getAllPersistentClassNames(
      Iterable<Class<?>> clazzez,
      SessionFactory sessionFactory) {
    return Streams.stream(clazzez)
        .flatMap(clazz -> {
          AbstractEntityPersister metadata = (AbstractEntityPersister) sessionFactory.getClassMetadata(clazz);
          //noinspection unchecked
          Set<String> subclasses = metadata.getEntityMetamodel().getSubclassEntityNames();
          return subclasses.stream().flatMap(subclass -> {
            try {
              Class.forName(subclass);
              return Stream.of(subclass);
            } catch (ClassNotFoundException e) {
              return Stream.empty();
            }
          });
        })
        .collect(toSet());
  }

  @VisibleForTesting
  public static Set<String> getAllGeneratedClassNames(Set<String> classNames) {
    return classNames.stream()
        .filter(s -> !s.contains("."))
        .collect(Collectors.toSet());
  }

  @VisibleForTesting
  public static Set<String> getMatchingGeneratedVersionClassNames(
      Iterable<Class<?>> clazzez,
      Set<String> allGeneratedClassNames) {
    Set<String> matchingGeneratedClassNames = new HashSet<>();
    clazzez.forEach(subclass -> {
      String subclassName = subclass.getName();
      subclassName = subclassName.contains(".")
          ? subclassName.substring(subclassName.lastIndexOf('.') + 1)
          : subclassName;
      subclassName = CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, subclassName);
      String matchingClassesRegex = "version_" + subclassName + "[s]?_[a-z_]+";
      allGeneratedClassNames.stream()
          .filter(s -> s.matches(matchingClassesRegex))
          .forEach(matchingGeneratedClassNames::add);
    });
    return matchingGeneratedClassNames;
  }

  @VisibleForTesting
  void loadEntitiesFromDb(Session session, Errors errors, Class<?>... clazzez) {
    loadEntitiesFromDb(session, errors, asList(clazzez));
  }

  private void loadEntitiesFromDb(Session session, final Errors errors, final Iterable<Class<?>> clazzez) {
    for (Class<?> persistentClass : clazzez) {
      try {
        loadEntitiesFromDb(persistentClass, session);
      } catch (Exception e) {
        log.warn(e, "unable to read instance of '%s' from the database", persistentClass);
        errors
            .addMessage("entity %s: unable to read instances from database, cause %s", persistentClass, e.getMessage());
      }
    }
  }

  private void loadEntitiesFromDb(final Class<?> clazz, Session session) {
    loadEntitiesFromDb(clazz, session, asc("id"), null);
    Id<?> lastId = loadEntitiesFromDb(clazz, session, desc("id"), null);
    if (null != lastId) {
      long aboutMiddle = lastId.getId() / 2;
      loadEntitiesFromDb(clazz, session, desc("id"), Id.of(aboutMiddle));
    }
    loadEntitiesFromDb(clazz, session, getRandomIdsForEntitiesToLoad(clazz, session));
  }

  private Id<?> loadEntitiesFromDb(final Class<?> clazz, Session session, Order order, Id<?> start) {
    return loadEntitiesFromDb(clazz, session, getOrderedIdsForEntitiesToLoad(clazz, session, order, start));
  }

  @SuppressWarnings({"unchecked"})
  private Id<?> loadEntitiesFromDb(final Class<?> clazz, Session session, List<Id<?>> idsForEntitiesToLoad) {
    Criteria criteria = session.createCriteria(clazz);
    if (idsForEntitiesToLoad.isEmpty()) {
      log.debug("no ids could be found for entities of class %s, anything in the table?", clazz);
      return null;
    }

    log.debug("loading up to entities of class %s with ids %s", clazz.getName(), idsForEntitiesToLoad.toString());
    List<HibernateEntity> entities = newArrayList(criteria.add(Restrictions.in("id", idsForEntitiesToLoad)).list());
    if (entities.isEmpty()) {
      log.debug("no entities of class %s, anything in the table?", clazz);
      return null;
    }
    log.debug("loaded %s entities of class %s: %s", entities.size(), clazz.getName(),
        transform(entities, new Function<HibernateEntity, String>() {
          @Override
          public String apply(HibernateEntity from) {
            return from.getId().toString();
          }
        }));
    return entities.get(0).getId();
  }

  private List<Id<?>> getOrderedIdsForEntitiesToLoad(final Class<?> clazz, Session session, Order order, Id<?> start) {
    Criteria criteria = session.createCriteria(clazz);
    if (start != null) {
      criteria = criteria.add(lt(
          "id",
          // Hack: we're dewrapping the ID based on whether it's a custom type
          // or not. This works in our codebase since we use Id<?> or long
          // but is _far_ from a general purpose solution.
          session.getSessionFactory().getClassMetadata(clazz).getIdentifierType().getClass().equals(CustomType.class) ?
              start :
              start.getId()));
    }
    criteria = criteria.setProjection(Projections.id());
    log.debug("loading up to 10 entities of class %s ; start is %s", clazz.getName(), start);
    return criteria
        .setProjection(Projections.id())
        .addOrder(order)
        .setMaxResults(10)
        .list();
  }

  @VisibleForTesting
  List<Id<?>> getRandomIdsForEntitiesToLoad(Class<?> clazz, Session session) {
    Criteria criteria = session.createCriteria(clazz);

    Object maybeMinId = criteria.setProjection(Projections.min("id")).uniqueResult();
    Object maybeMaxId = criteria.setProjection(Projections.max("id")).uniqueResult();
    if (maybeMinId == null || maybeMaxId == null) {
      return Collections.emptyList();
    }

    long minId = maybeMinId instanceof Id<?> ? ((Id<?>) maybeMinId).getId() : ((long) maybeMinId);
    long maxId = maybeMaxId instanceof Id<?> ? ((Id<?>) maybeMaxId).getId() : ((long) maybeMaxId);

    Set<Long> randomIds = new HashSet<>();
    for (int i = 0; i < 10; ++i) {
      long randomId = (long) (Math.random() * (maxId - minId) + minId);
      randomIds.add(randomId);
    }

    log.debug("loading up to %s entities of class %s", randomIds.size(), clazz.getName());
    //noinspection unchecked
    return criteria
        .add(Restrictions.in("id", maybeMinId instanceof Id<?>
            ? randomIds.stream().map(Id::of).collect(toSet())
            : randomIds))
        .list();
  }

  @VisibleForTesting
  public <P extends ClassMetadata & EntityPersister & OuterJoinLoadable> void checkTableWellFormedness(
      Errors errors,
      String entityClass,
      P metadata,
      String discriminatorColumn,
      String tableName,
      String[] propertyNames,
      Map<String, DbColumn> dbColumns) {
    boolean[] insertability = metadata.getPropertyInsertability();

    Set<String> allDbColumns = newHashSet(dbColumns.keySet());

    List<String> keyColumns = metadata instanceof SingleTableEntityPersister ?
        Arrays.stream(((SingleTableEntityPersister) metadata)
                .getContraintOrderedTableKeyColumnClosure())
            .flatMap(Arrays::stream)
            .toList() :
        asList(metadata.getKeyColumnNames());
    Set<String> keyColumnsWithoutQuotes = keyColumns.stream()
        .map(HbmDbSchemaSyncCheck::removeQuotesFromColumn)
        .collect(toSet());

    allDbColumns.removeAll(keyColumnsWithoutQuotes);

    int offset = 0;
    for (String name : metadata.getPropertyNames()) {
      if (name.equals(propertyNames[0])) {
        break;
      }
      ++offset;
    }

    for (int index = 0; index < propertyNames.length; index++) {
      String propertyName = propertyNames[index];
      Type propertyType = metadata.getSubclassPropertyType(index + offset);
      String[] columns = metadata.getPropertyColumnNames(propertyName);

      if (propertyType != null) {
        List<Integer> sqlTypes = Arrays.stream(propertyType.sqlTypes(metadata.getFactory()))
            .boxed()
            .collect(toList());
        log.debug("For property %s, type %s has %s sqlTypes: %s",
            propertyName, propertyType.getName(), sqlTypes.size(), sqlTypes);
      } else {
        log.debug("For property %s, type is null", propertyName);
      }

      for (int i = 0; i < columns.length; i++) {
        columns[i] = removeQuotesFromColumn(columns[i]);
      }

      String columnName = columns[0];
      asList(columns).forEach(allDbColumns::remove);

      if (propertyName.startsWith("_") || !insertability[index + offset] || "id".equals(columnName) ||
          (propertyType instanceof CollectionType && ((CollectionType) propertyType).getLHSPropertyName() != null)) {
        continue;
      }

      DbColumn dbColumn = dbColumns.get(columnName);
      if (dbColumn == null) {
        errors.addMessage(
            "entity %s: missing db column in table %s for hbm property %s",
            entityClass, tableName, propertyName);
        return;
      }

      boolean isJoinedTable = discriminatorColumn == null;
      if (!isJoinedTable) {
        boolean[] nullables = metadata.getPropertyNullability();
        if (nullables[index + offset] && !dbColumn.isNullable()) {
          errors.addMessage(
              "entity %s: property %s is nullable but %s.%s is not",
              entityClass, propertyName, tableName, columnName);
        }
      }

      checkEnumMapping(errors, entityClass, tableName, propertyType, dbColumn, propertyName, columnName);
    }
    if (discriminatorColumn != null && !discriminatorColumn.equals("clazz_") &&
        !allDbColumns.contains(discriminatorColumn)) {
      errors.addMessage(
          "entity %s: missing db column in table %s for hbm discriminator %s",
          entityClass, tableName, discriminatorColumn);
    }

    for (String unmappedDbColumn : allDbColumns) {
      if (unmappedDbColumn.equals(discriminatorColumn)) {
        if (dbColumns.get(unmappedDbColumn).isNullable()) {
          errors.addMessage(
              "entity %s: discriminator %s must not be nullable but %s.%s is",
              entityClass, discriminatorColumn, tableName, discriminatorColumn);
        }
      } else if (!dbColumns.get(unmappedDbColumn).isIgnorable()) {
        errors.addMessage(
            "entity %s: %s.%s is not mapped and is not ignorable",
            entityClass, tableName, unmappedDbColumn);
      }
    }
  }

  private static void checkEnumMapping(
      Errors errors,
      String entityClass,
      String tableName,
      Type propertyType,
      DbColumn dbColumn,
      String propertyName,
      String columnName) {
    if (propertyType instanceof CustomType customType &&
        customType.getUserType() instanceof EnumTypeAdapter<?> enumType) {
      Object[] enumConstants = enumType.returnedClass().getEnumConstants();
      String longestEnumValue = Arrays.stream(enumConstants)
          .filter(not(hasCodeField()))
          .map(Object::toString)
          .max(Comparator.comparing(String::length, Comparator.naturalOrder()))
          .orElse("");
      String columnType = dbColumn.type;
      if (columnType == null || columnType.equals("text") || columnType.equals("mediumtext")) {
        return;
      }
      Matcher varcharMatcher = VARCHAR_PATTERN.matcher(columnType);
      if (!varcharMatcher.matches()) {
        errors.addMessage("entity %s: property %s must have a corresponding varchar column, but was: %s",
            entityClass, propertyName, columnType);
      } else {
        int dbColumnLength = Integer.parseInt(varcharMatcher.group(1));
        if (longestEnumValue.length() > dbColumnLength) {
          errors.addMessage(
              "entity %s: property %s has the enum value %s which is longer than the max allowed for %s.%s (%s > %s)",
              entityClass, propertyName, longestEnumValue, tableName, columnName, longestEnumValue.length(), dbColumnLength);
        }
      }
    }
  }

  private static Predicate<Object> hasCodeField() {
    return o -> Arrays.stream(o.getClass().getDeclaredFields()).anyMatch(f -> f.getName().equals("code") || f.getName().equals("codes"));
  }

  // TODO: test this when we have Connector/MXJ hooked into the CI
  Map<String, DbColumn> getDbColumns(final String tableName, Session session) {
    Map<String, DbColumn> columns = newHashMap();
    try (Statement st = session.connection().createStatement();
         ResultSet rs = st.executeQuery(format("desc %s", tableName))) {
      while (rs.next()) {
        // desc TABLENAME columns:
        // 1. Field
        // 2. Type
        // 3. Null
        // 4. Key
        // 5. Default
        // 6. Extra
        String columnName = rs.getString(1);
        boolean nullable = "YES".equals(rs.getString(3));
        String defaultValue = rs.getString(5);
        columns.put(columnName, new DbColumn(nullable, defaultValue));
      }
      return columns;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @VisibleForTesting
  public static String removeQuotesFromColumn(String columnName) {
    if (columnName == null) {
      return null;
    }
    Pattern p = Pattern.compile("[\"'`]");
    return p.matcher(columnName).replaceAll("");
  }

  /**
   * Represents a database column.
   */
  public static class DbColumn {

    private final boolean nullable;
    private final String defaultValue;
    private final String type;
    private final String name;
    private final Type propertyType;

    DbColumn(boolean nullable, String defaultValue) {
      this.nullable = nullable;
      this.defaultValue = defaultValue;
      this.type = null;
      this.name = null;
      this.propertyType = null;
    }

    DbColumn(boolean nullable, String defaultValue, Type propertyType) {
      this.nullable = nullable;
      this.defaultValue = defaultValue;
      this.type = null;
      this.name = null;
      this.propertyType = propertyType;
    }

    public DbColumn(boolean nullable, String defaultValue, String type, String name) {
      this.nullable = nullable;
      this.defaultValue = defaultValue;
      this.type = type;
      this.name = name;
      this.propertyType = null;
    }

    public DbColumn(boolean nullable, String defaultValue, String type, String name, Type propertyType) {
      this.nullable = nullable;
      this.defaultValue = defaultValue;
      this.type = type;
      this.name = name;
      this.propertyType = propertyType;
    }

    /**
     * Returns whether this column is nullable.
     */
    public boolean isNullable() {
      return nullable;
    }

    /**
     * Returns whether this column has a default value.
     */
    public boolean hasDefaultValue() {
      return defaultValue != null;
    }

    /**
     * Returns whether this column can be ignored. An ignorable column is one
     * whose presence will not affect {@code insert} statements which do
     * not mention it. It must therefore be nullable or have a default value.
     */
    public boolean isIgnorable() {
      return isNullable() || hasDefaultValue();
    }

    public String getName() {
      return name;
    }

    public String getType() {
      return type;
    }

    public Option<Type> getPropertyType() {
      return Option.of(propertyType);
    }

  }

}
