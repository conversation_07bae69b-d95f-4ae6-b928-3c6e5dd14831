package com.kaching.platform.hibernate.queue.impl;

import static com.google.common.base.Preconditions.checkArgument;
import static java.util.stream.Collectors.toList;

import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Set;

import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ContiguousSet;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Range;
import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.MultiDbInserter;

public class BatchQueueItemInserterImpl implements BatchQueueItemInserter {
  
  private static final LoadingCache<Integer, String> INSERT_SQL_CACHE = CacheBuilder.newBuilder()
      .maximumSize(32)
      .build(new CacheLoader<Integer, String>() {
        @Override
        public String load(Integer numItems) {
          return generateInsertSql(numItems);
        }
      });

  @Inject MultiDbInserter inserter;
  
  @Override
  public Option<ContiguousSet<Id<BatchQueueItem>>> insertAndGetNewIds(List<BatchQueueItem> items) {
    if (items.isEmpty()) {
      return Option.none();
    }
    List<Long> ids = inserter.bulkInsertAndGetIds(INSERT_SQL_CACHE.getUnchecked(items.size()), ps -> {
      int i = 1;
      for (BatchQueueItem item : items) {
        ps.setLong(i++, item.getQueueId().getId());
        ps.setString(i++, item.getPayload().toString());
      }
    });
    checkArgument(ids.size() == items.size(), "Expected %s IDs, got %s", items.size(), ids.size());
    for (int i = 0; i < ids.size(); i++) {
      items.get(i).setId(Id.of(ids.get(i)));
    }
    return Option.some(toContiguousSet(items.stream().map(BatchQueueItem::getId).collect(toList())));
  }
  
  private static String generateInsertSql(int numItems) {
    checkArgument(numItems > 0, "numItems must be positive");
    
    String placeholder = "(?, ?)";
    StringBuilder sql = new StringBuilder("INSERT INTO batch_queue_items (batch_queue_name_id, payload) VALUES ");
    sql.append(Strings.repeat(placeholder + ", ", numItems - 1));
    sql.append(placeholder);
    return sql.toString();
  }

  private static ContiguousSet<Id<BatchQueueItem>> toContiguousSet(List<Id<BatchQueueItem>> items) {
    Set<Id<BatchQueueItem>> set = ImmutableSet.copyOf(items);
    checkArgument(items.size() == set.size(), "Duplicate items found: %s", items);
    LongSummaryStatistics stats = items.stream()
        .mapToLong(Id::getId)
        .summaryStatistics();
    checkArgument(stats.getMax() - stats.getMin() + 1 == items.size(), "IDs not contiguous: %s", items);
    return ContiguousSet.create(Range.closed(
        Id.of(stats.getMin()),
        Id.of(stats.getMax())
    ), BatchQueueItem.DISCRETE_DOMAIN);
  }
  
}
