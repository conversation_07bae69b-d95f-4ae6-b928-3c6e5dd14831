package com.kaching.platform.hibernate;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.queryengine.progress.FakeProgressMonitor;
import com.kaching.platform.queryengine.progress.ProgressMonitor;

public class EntityIdsBatcherProviderImpl implements EntityIdsBatcherProvider {

  private static final int DEFAULT_NUM_BATCHES = 100;

  @Inject public ProgressMonitor progressMonitor = new FakeProgressMonitor();
  @Inject public Transacter transacter;

  @Override
  public <E extends HibernateEntity> EntityIdsBatcher<E> get(Class<? extends E> clazz) {
    return get(DEFAULT_NUM_BATCHES, clazz);
  }

  @Override
  public <E extends HibernateEntity> EntityIdsBatcher<E> get(Class<? extends E> clazz, String stageName) {
    return get(DEFAULT_NUM_BATCHES, clazz, stageName);
  }

  @Override
  public <E extends HibernateEntity> EntityIdsBatcher<E> get(int numBatches, Class<? extends E> clazz) {
    return get(numBatches, clazz, null);
  }

  @Override
  public <E extends HibernateEntity> EntityIdsBatcher<E> get(
      int numBatches, Class<? extends E> clazz, String stageName) {
    return new EntityIdsBatcher<>(numBatches, transacter, clazz, progressMonitor, Option.of(stageName));
  }

}
