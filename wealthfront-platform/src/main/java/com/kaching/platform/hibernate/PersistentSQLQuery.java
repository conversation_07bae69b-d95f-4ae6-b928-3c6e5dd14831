package com.kaching.platform.hibernate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import org.hibernate.CacheMode;
import org.hibernate.FlushMode;
import org.hibernate.LockMode;
import org.hibernate.SQLQuery;
import org.hibernate.ScrollMode;
import org.hibernate.ScrollableResults;
import org.hibernate.Session;
import org.hibernate.transform.ResultTransformer;
import org.hibernate.type.Type;

import com.google.common.collect.ImmutableSet;

/**
 * A type-safe wrapper around a Hibernate {@link SQLQuery}.
 */
public class PersistentSQLQuery<E> {

  private final Class<E> clazz;
  private final SQLQuery query;

  public PersistentSQLQuery(Session session, Class<E> clazz, String queryString) {
    this.query = session.createSQLQuery(queryString);
    this.clazz = clazz;
    this.query.addEntity(this.clazz);
  }

  /**
   * Get the query string.
   *
   * @return the query string
   */
  public String getQueryString() {
    return query.getQueryString();
  }

  /**
   * Return the Hibernate types of the query result set.
   *
   * @return an array of types
   */
  public Type[] getReturnTypes() {
    return query.getReturnTypes();
  }

  /**
   * Return the HQL select clause aliases (if any)
   *
   * @return an array of aliases as strings
   */
  public String[] getReturnAliases() {
    return query.getReturnAliases();
  }

  /**
   * Return the names of all named parameters of the query.
   *
   * @return the parameter names, in no particular order
   */
  public String[] getNamedParameters() {
    return query.getNamedParameters();
  }

  /**
   * Return the query results as an <tt>Iterator</tt>. If the query contains multiple
   * results pre row, the results are returned in an instance of <tt>Object[]</tt>.
   * <p/>
   * <p>Entities returned as results are initialized on demand. The first SQL query
   * returns identifiers only.
   *
   * @return the result iterator
   * @throws HibernateException if the <tt>Session</tt> is disconnected
   */
  @SuppressWarnings("unchecked")
  public Iterator<E> iterate() {
    return query.iterate();
  }

  /**
   * Return the query results as <tt>ScrollableResults</tt>. The scrollability of the
   * returned results depends upon JDBC driver support for scrollable <tt>ResultSet</tt>s.
   *
   * @return the result iterator
   * @throws HibernateException if the <tt>Session</tt> is disconnected
   * @see ScrollableResults
   */
  public ScrollableResults scroll() {
    return query.scroll();
  }

  /**
   * Return the query results as <tt>ScrollableResults</tt>. The scrollability of the
   * returned results depends upon JDBC driver support for scrollable <tt>ResultSet</tt>s.
   *
   * @return the result iterator
   * @throws HibernateException if the <tt>Session</tt> is disconnected
   * @see ScrollableResults
   * @see ScrollMode
   */
  public ScrollableResults scroll(ScrollMode scrollMode) {
    return query.scroll(scrollMode);
  }

  /**
   * Return the query results as a <tt>List</tt>. If the query contains multiple results
   * per row, the results are returned in an instance of <tt>Object[]</tt>.
   *
   * @return the result list
   * @throws HibernateException if the <tt>Session</tt> is disconnected
   */
  @SuppressWarnings("unchecked")
  public List<E> list() {
    return query.list();
  }

  /**
   * Return the query results as a <tt>Set</tt>. If the query contains multiple results
   * per row, the results are returned in an instance of <tt>Object[]</tt>.
   *
   * @return the result list
   * @throws HibernateException if the <tt>Session</tt> is disconnected
   */
  @SuppressWarnings("unchecked")
  public Set<E> set() {
    return ImmutableSet.copyOf(query.list());
  }

  /**
   * Convenience method to return a single instance that matches the query, or null if the
   * query returns no results.
   *
   * @return the single result or <tt>null</tt>
   * @throws NonUniqueResultException if there is more than one matching result
   */
  @SuppressWarnings("unchecked")
  public E uniqueResult() {
    return (E) query.uniqueResult();
  }

  /**
   * Execute the update or delete statement.
   * <p/>
   * <p>The semantics are compliant with the ejb3 Query.executeUpdate() method.
   *
   * @return The number of entities updated or deleted.
   * @throws HibernateException if the <tt>Session</tt> is disconnected
   */
  public int executeUpdate() {
    return query.executeUpdate();
  }

  /**
   * Set the maximum number of rows to retrieve. If not set, there is no limit to the
   * number of rows retrieved.
   *
   * @param maxResults the maximum number of rows
   */
  public PersistentSQLQuery<E> setMaxResults(int maxResults) {
    query.setMaxResults(maxResults);
    return this;
  }

  /**
   * Set the first row to retrieve. If not set, rows will be retrieved beginning from
   * row <tt>0</tt>.
   *
   * @param firstResult a row number, numbered from <tt>0</tt>
   */
  public PersistentSQLQuery<E> setFirstResult(int firstResult) {
    query.setFirstResult(firstResult);
    return this;
  }

  /**
   * Entities retrieved by this query will be loaded in a read-only mode where Hibernate
   * will never dirty-check them or make changes persistent.
   */
  public PersistentSQLQuery<E> setReadOnly(boolean readOnly) {
    query.setReadOnly(readOnly);
    return this;
  }

  /**
   * Enable caching of this query result set.
   *
   * @param cacheable Should the query results be cacheable?
   */
  public PersistentSQLQuery<E> setCacheable(boolean cacheable) {
    query.setCacheable(cacheable);
    return this;
  }

  /**
   * Set the name of the cache region.
   *
   * @param cacheRegion the name of a query cache region, or <tt>null</tt>
   *                    for the default query cache
   */
  public PersistentSQLQuery<E> setCacheRegion(String cacheRegion) {
    query.setCacheRegion(cacheRegion);
    return this;
  }

  /**
   * Set a timeout for the underlying JDBC query.
   *
   * @param timeout the timeout in seconds
   */
  public PersistentSQLQuery<E> setTimeout(int timeout) {
    query.setTimeout(timeout);
    return this;
  }

  /**
   * Set a fetch size for the underlying JDBC query.
   *
   * @param fetchSize the fetch size
   */
  public PersistentSQLQuery<E> setFetchSize(int fetchSize) {
    query.setFetchSize(fetchSize);
    return this;
  }

  /**
   * Set the lockmode for the objects identified by the given alias that appears in
   * the <tt>FROM</tt> clause.
   *
   * @param alias a query alias, or <tt>this</tt> for a collection filter
   */
  public PersistentSQLQuery<E> setLockMode(String alias, LockMode lockMode) {
    query.setLockMode(alias, lockMode);
    return this;
  }

  /**
   * Add a comment to the generated SQL.
   *
   * @param comment a human-readable string
   */
  public PersistentSQLQuery<E> setComment(String comment) {
    query.setComment(comment);
    return this;
  }

  /**
   * Override the current session flush mode, just for this query.
   *
   * @see FlushMode
   */
  public PersistentSQLQuery<E> setFlushMode(FlushMode flushMode) {
    query.setFlushMode(flushMode);
    return this;
  }

  /**
   * Override the current session cache mode, just for this query.
   *
   * @see CacheMode
   */
  public PersistentSQLQuery<E> setCacheMode(CacheMode cacheMode) {
    query.setCacheMode(cacheMode);
    return this;
  }

  /**
   * Bind a value to a JDBC-style query parameter.
   *
   * @param position the position of the parameter in the query string, numbered from <tt>0</tt>.
   * @param val      the possibly-null parameter value
   * @param type     the Hibernate type
   */
  public PersistentSQLQuery<E> setParameter(int position, Object val, Type type) {
    query.setParameter(position, val, type);
    return this;
  }

  /**
   * Bind a value to a named query parameter.
   *
   * @param name the name of the parameter
   * @param val  the possibly-null parameter value
   * @param type the Hibernate type
   */
  public PersistentSQLQuery<E> setParameter(String name, Object val, Type type) {
    query.setParameter(name, val, type);
    return this;
  }

  /**
   * Bind a value to a JDBC-style query parameter. The Hibernate type of the parameter is
   * first detected via the usage/position in the query and if not sufficient secondly
   * guessed from the class of the given object.
   *
   * @param position the position of the parameter in the query string, numbered from <tt>0</tt>.
   * @param val      the non-null parameter value
   * @throws HibernateException if no type could be determined
   */
  public PersistentSQLQuery<E> setParameter(int position, Object val) {
    query.setParameter(position, val);
    return this;
  }

  /**
   * Bind a value to a named query parameter. The Hibernate type of the parameter is
   * first detected via the usage/position in the query and if not sufficient secondly
   * guessed from the class of the given object.
   *
   * @param name the name of the parameter
   * @param val  the non-null parameter value
   * @throws HibernateException if no type could be determined
   */
  public PersistentSQLQuery<E> setParameter(String name, Object val) {
    query.setParameter(name, val);
    return this;
  }

  /**
   * Bind values and types to positional parameters.
   */
  public PersistentSQLQuery<E> setParameters(Object[] values, Type[] types) {
    query.setParameters(values, types);
    return this;
  }

  /**
   * Bind multiple values to a named query parameter. This is useful for binding a
   * list of values to an expression such as <tt>foo.bar in (:value_list)</tt>.
   *
   * @param name the name of the parameter
   * @param vals a collection of values to list
   * @param type the Hibernate type of the values
   */
  @SuppressWarnings("rawtypes")
  public PersistentSQLQuery<E> setParameterList(String name, Collection vals, Type type) {
    query.setParameterList(name, vals, type);
    return this;
  }

  /**
   * Bind multiple values to a named query parameter. The Hibernate type of the parameter
   * is first detected via the usage/position in the query and if not sufficient secondly
   * guessed from the class of the first object in the collection. This is useful for
   * binding a list of values to an expression such as <tt>foo.bar in (:value_list)</tt>.
   *
   * @param name the name of the parameter
   * @param vals a collection of values to list
   */
  @SuppressWarnings("rawtypes")
  public PersistentSQLQuery<E> setParameterList(String name, Collection vals) {
    query.setParameterList(name, vals);
    return this;
  }

  /**
   * Bind multiple values to a named query parameter. This is useful for binding
   * a list of values to an expression such as <tt>foo.bar in (:value_list)</tt>.
   *
   * @param name the name of the parameter
   * @param vals a collection of values to list
   * @param type the Hibernate type of the values
   */
  public PersistentSQLQuery<E> setParameterList(String name, Object[] vals, Type type) {
    query.setParameterList(name, vals, type);
    return this;
  }

  /**
   * Bind multiple values to a named query parameter. The Hibernate type of the parameter is
   * first detected via the usage/position in the query and if not sufficient secondly
   * guessed from the class of the first object in the array. This is useful for binding a list
   * of values to an expression such as <tt>foo.bar in (:value_list)</tt>.
   *
   * @param name the name of the parameter
   * @param vals a collection of values to list
   */
  public PersistentSQLQuery<E> setParameterList(String name, Object[] vals) {
    query.setParameterList(name, vals);
    return this;
  }

  /**
   * Bind the property values of the given bean to named parameters of the query, matching
   * property names with parameter names and mapping property types to Hibernate types
   * using heuristics.
   *
   * @param bean any JavaBean or POJO
   */
  public PersistentSQLQuery<E> setProperties(Object bean) {
    query.setProperties(bean);
    return this;
  }

  /**
   * Bind the values of the given Map for each named parameters of the query, matching key
   * names with parameter names and mapping value types to Hibernate types using heuristics.
   *
   * @param bean a java.util.Map
   */
  @SuppressWarnings("rawtypes")
  public PersistentSQLQuery<E> setProperties(Map bean) {
    query.setProperties(bean);
    return this;
  }

  public PersistentSQLQuery<E> setBigDecimal(int position, BigDecimal number) {
    query.setBigDecimal(position, number);
    return this;
  }

  public PersistentSQLQuery<E> setBigDecimal(String name, BigDecimal number) {
    query.setBigDecimal(name, number);
    return this;
  }

  public PersistentSQLQuery<E> setBigInteger(int position, BigInteger number) {
    query.setBigInteger(position, number);
    return this;
  }

  public PersistentSQLQuery<E> setBigInteger(String name, BigInteger number) {
    query.setBigInteger(name, number);
    return this;
  }

  public PersistentSQLQuery<E> setBinary(int position, byte[] val) {
    query.setBinary(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setBinary(String name, byte[] val) {
    query.setBinary(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setBoolean(int position, boolean val) {
    query.setBoolean(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setBoolean(String name, boolean val) {
    query.setBoolean(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setByte(int position, byte val) {
    query.setByte(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setByte(String name, byte val) {
    query.setByte(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setCalendar(int position, Calendar calendar) {
    query.setCalendar(position, calendar);
    return this;
  }

  public PersistentSQLQuery<E> setCalendar(String name, Calendar calendar) {
    query.setCalendar(name, calendar);
    return this;
  }

  public PersistentSQLQuery<E> setCalendarDate(int position, Calendar calendar) {
    query.setCalendarDate(position, calendar);
    return this;
  }

  public PersistentSQLQuery<E> setCalendarDate(String name, Calendar calendar) {
    query.setCalendarDate(name, calendar);
    return this;
  }

  public PersistentSQLQuery<E> setCharacter(int position, char val) {
    query.setCharacter(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setCharacter(String name, char val) {
    query.setCharacter(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setDate(int position, Date date) {
    query.setDate(position, date);
    return this;
  }

  public PersistentSQLQuery<E> setDate(String name, Date date) {
    query.setDate(name, date);
    return this;
  }

  public PersistentSQLQuery<E> setDouble(int position, double val) {
    query.setDouble(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setDouble(String name, double val) {
    query.setDouble(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setFloat(int position, float val) {
    query.setFloat(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setFloat(String name, float val) {
    query.setFloat(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setInteger(int position, int val) {
    query.setInteger(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setInteger(String name, int val) {
    query.setInteger(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setLocale(int position, Locale locale) {
    query.setLocale(position, locale);
    return this;
  }

  public PersistentSQLQuery<E> setLocale(String name, Locale locale) {
    query.setLocale(name, locale);
    return this;
  }

  public PersistentSQLQuery<E> setLong(int position, long val) {
    query.setLong(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setLong(String name, long val) {
    query.setLong(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setSerializable(int position, Serializable val) {
    query.setSerializable(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setSerializable(String name, Serializable val) {
    query.setSerializable(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setShort(int position, short val) {
    query.setShort(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setShort(String name, short val) {
    query.setShort(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setString(int position, String val) {
    query.setString(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setString(String name, String val) {
    query.setString(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setText(int position, String val) {
    query.setText(position, val);
    return this;
  }

  public PersistentSQLQuery<E> setText(String name, String val) {
    query.setText(name, val);
    return this;
  }

  public PersistentSQLQuery<E> setTime(int position, Date date) {
    query.setTime(position, date);
    return this;
  }

  public PersistentSQLQuery<E> setTime(String name, Date date) {
    query.setTime(name, date);
    return this;
  }

  public PersistentSQLQuery<E> setTimestamp(int position, Date date) {
    query.setTimestamp(position, date);
    return this;
  }

  public PersistentSQLQuery<E> setTimestamp(String name, Date date) {
    query.setTimestamp(name, date);
    return this;
  }

  /**
   * Bind an instance of a mapped persistent class to a JDBC-style query parameter.
   *
   * @param position the position of the parameter in the query string, numbered from <tt>0</tt>.
   * @param val      a non-null instance of a persistent class
   */
  public PersistentSQLQuery<E> setEntity(int position, Object val) {
    query.setEntity(position, val);
    return this;
  }

  /**
   * Bind an instance of a mapped persistent class to a named query parameter.
   *
   * @param name the name of the parameter
   * @param val  a non-null instance of a persistent class
   */
  public PersistentSQLQuery<E> setEntity(String name, Object val) {
    query.setEntity(name, val);
    return this;
  }

  /**
   * Set a strategy for handling the query results. This can be used to change "shape"
   * of the query result.
   *
   * @param transformer The transformer to apply
   * @return this (for method chaining)
   */
  public PersistentSQLQuery<E> setResultTransformer(ResultTransformer transformer) {
    query.setResultTransformer(transformer);
    return this;
  }

  /**
   * Use a predefined named ResultSetMapping.
   */
  public PersistentSQLQuery<E> setResultSetMapping(String name) {
    query.setResultSetMapping(name);
    return this;
  }

}
