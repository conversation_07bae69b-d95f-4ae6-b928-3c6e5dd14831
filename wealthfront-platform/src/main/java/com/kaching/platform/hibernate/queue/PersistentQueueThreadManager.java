package com.kaching.platform.hibernate.queue;

import static java.lang.Thread.NORM_PRIORITY;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.Leader;

public class PersistentQueueThreadManager {

  private static final Log log = Log.getLog(PersistentQueueThreadManager.class);

  @VisibleForTesting final Set<Thread> startedQueueThreads = new HashSet<>();

  @Inject @Leader Provider<Boolean> isLeader;
  @Inject ThreadManager threadManager;

  public <T extends PersistentQueue> void process(Provider<T> queueProvider, int workerThreads, String threadPrefix) {
    if (isLeader.get()) {
      removeDeadThreads();
      int numThreadsToStart = workerThreads - startedQueueThreads.size();
      for (int i = 0; i < numThreadsToStart; i++) {
        Thread t = threadManager.startWorkerAndGetThread(threadPrefix + i, queueProvider.get());
        if (t != null) {
          startedQueueThreads.add(t);
        }
      }
    } else {
      if (!startedQueueThreads.isEmpty()) {
        for (Iterator<Thread> iterator = startedQueueThreads.iterator(); iterator.hasNext();) {
          iterator.next().interrupt();
          iterator.remove();
        }
      }
    }
  }

  private void removeDeadThreads() {
    for (Iterator<Thread> iterator = startedQueueThreads.iterator(); iterator.hasNext();) {
      Thread thread = iterator.next();
      if (thread.getState() == Thread.State.TERMINATED) {
        thread.interrupt();
        iterator.remove();
      }
    }
  }

  @Singleton
  static class ThreadManager {
    Thread startWorkerAndGetThread(String name, Runnable worker) {
      log.info("Scheduling worker thread %s.", name);
      Thread t = new Thread(worker, name);
      t.setPriority(NORM_PRIORITY - 1);
      t.start();
      return t;
    }
  }

}