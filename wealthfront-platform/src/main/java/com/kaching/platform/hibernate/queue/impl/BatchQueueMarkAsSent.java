package com.kaching.platform.hibernate.queue.impl;

import java.util.ArrayList;
import java.util.List;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.queryengine.AbstractQuery;

public class BatchQueueMarkAsSent extends AbstractQuery<BatchQueueLockAndExecuteResult> {

  private final String queueName;
  private final boolean newIsSent;
  private final List<Id<BatchQueueBatch>> batchIds;

  public BatchQueueMarkAsSent(String queueName, boolean newIsSent, List<Id<BatchQueueBatch>> batchIds) {
    this.queueName = queueName;
    this.newIsSent = newIsSent;
    this.batchIds = batchIds;
  }
  
  @Inject BatchQueueBinder binder;
  @Inject DateTime now;

  @Override
  public BatchQueueLockAndExecuteResult process() {
    return binder.getRunner(queueName).tryLockAndExecute(batchIds, (session, batches) -> {
      List<Id<BatchQueueBatch>> changed = new ArrayList<>();
      List<Id<BatchQueueBatch>> unchanged = new ArrayList<>();
      batches.forEach(batch -> {
        if (newIsSent) {
          if (batch.getSentAt().isDefined()) {
            unchanged.add(batch.getId());
          } else {
            batch.markAsSent(now);
            changed.add(batch.getId());
          }
        } else {
          if (batch.getSentAt().isDefined()) {
            batch.markAsNotSent(now);
            changed.add(batch.getId());
          } else {
            unchanged.add(batch.getId());
          }
        }
      });
      return Strings.format("Changed: %s,\n Unchanged: %s", changed, unchanged);
    });
  }
  
}
