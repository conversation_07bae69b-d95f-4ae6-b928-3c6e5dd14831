package com.kaching.platform.monitoring;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.BooleanUtils.isFalse;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.queryengine.Ping;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.QueryExecutorService;
import com.kaching.platform.queryengine.QueryExecutorService.QueryKeyResult;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.queryengine.authorization.Impersonator;
import com.kaching.platform.queryengine.client.SmartClient;
import com.kaching.user.UserId;
import com.kaching.util.UncheckedInterruptedException;
import com.kaching.util.http.LongOnlineTimeoutPooling;

public class QueryHealthSelfTestRunnerImpl implements QueryHealthSelfTestRunner {
  
  private static final Log log = Log.getLog(QueryHealthSelfTestRunnerImpl.class);

  @VisibleForTesting
  static final String OK = "OK";
  
  @Inject Provider<Impersonator> impersonatorProvider;
  @Inject QueryExecutorService qes;
  @Inject StackTraceMonitor stm;
  @Inject ServiceKind serviceKind;
  @Inject @LongOnlineTimeoutPooling SmartClient<KachingServices.UM> umSmartClient;
  @Inject @LongOnlineTimeoutPooling SmartClient<KachingServices.BANK> bankSmartClient;
  @Inject @LongOnlineTimeoutPooling SmartClient<KachingServices.LINK> linkSmartClient;
  @Inject @LongOnlineTimeoutPooling SmartClient<KachingServices.CBUS> cbusSmartClient;

  @Override
  public <P, R, Q extends Query<R>> Errors runSelfTest(QueryHealthSelfTest<Q, P> selfTest) {
    log.info("Starting query health self test for %s", selfTest.getQueryClazz().getSimpleName());

    ExecutorService executor = createExecutor();
    Set<P> parameters = selfTest.generateParameters();
    Map<P, UserId> ownerMap = selfTest.extractOwnersForAuthentication(parameters);

    Set<Class<? extends ServiceKind>> unavailableServices = getUnavailableServices(selfTest);

    if (isFalse(unavailableServices.isEmpty())) {
      stm.add(new IllegalStateException(Strings.format(
          "Could not run QueryHealthSelfTest for %s because the following dependent services were unavailable: %s",
          selfTest.getQueryClazz().getSimpleName(),
          unavailableServices.stream().map(Class::getSimpleName).collect(toList()))));
      return new Errors();
    }

    int round = 0;
    while (true) {
      round++;
      List<QueryTask<R, P>> tasks = parameters.stream()
          .map(p -> new QueryTask<>(p, selfTest.instantiateQuery(p), Option.of(ownerMap.get(p))))
          .collect(toList());
      List<QueryKeyResult<P, Query<R>, R>> results = executeQueries(executor, selfTest.getQueryConcurrency(), tasks);

      parameters = results.stream()
          .filter(QueryKeyResult::hasError)
          .map(QueryKeyResult::getKey)
          .collect(toSet());
      if (parameters.isEmpty()) {
        log.info("Query health self test for %s (round %s) succeeded.",
            selfTest.getQueryClazz().getSimpleName(), round);
        return new Errors();
      } else if (round <= selfTest.getNumRetries()) {
        log.info("Query health self test for %s (round %s) threw %s exceptions. Retrying.",
            selfTest.getQueryClazz().getSimpleName(), round, parameters.size());
      } else {
        Errors errors = new Errors();
        results.stream()
            .filter(QueryKeyResult::hasError)
            .limit(5)
            .forEach(result -> {
              String message = Strings.format("Query health self test for %s(%s) threw a(n) %s",
                  selfTest.getQueryClazz().getSimpleName(), result.getKey(), result.getError().getClass().getSimpleName());
              log.error(result.getError(), message);
              errors.addMessage(message);
            });
        return errors;
      }
    }
  }
  
  private <P, R, Q extends Query<R>> List<QueryKeyResult<P, Q, R>> executeQueries(
      ExecutorService executor, int queryConcurrency, List<QueryTask<R, P>> queryTasks) {
    ConcurrentLinkedQueue<QueryTask<R, P>> queue = new ConcurrentLinkedQueue<>(queryTasks);
    ConcurrentLinkedQueue<QueryKeyResult<P, Q, R>> results = new ConcurrentLinkedQueue<>();
    List<Future<?>> futures = new ArrayList<>();
    
    for (int i = 0; i < queryConcurrency; i++) {
      futures.add(executor.submit(() -> {
        Impersonator impersonator = impersonatorProvider.get();
        QueryTask<R, P> task = queue.poll();
        while (task != null) {
          if (Thread.interrupted()) {
            break;
          }
          try {
            R result;
            if (task.maybeOwnerId.isDefined()) {
              QueryTask<R, P> taskPtr =  task;
              result = impersonator.doAs(task.maybeOwnerId.getOrThrow(), getClass(), () -> qes.submitAndGetResult(taskPtr.query));
            } else {
              result = qes.submitAndGetResult(task.query);
            }
            
            results.add(QueryKeyResult.success(task.parameters, null, result));
          } catch (Exception e) {
            if (e instanceof UncheckedInterruptedException) {
              break;
            }
            results.add(QueryKeyResult.error(task.parameters, null, e));
          }
          task  = queue.poll();
        }
      }));
    }
    
    futures.forEach(future -> Unchecked.run(future::get));
    return ImmutableList.copyOf(results);
  }

  @VisibleForTesting
  <P, R, Q extends Query<R>> Set<Class<? extends ServiceKind>> getUnavailableServices(QueryHealthSelfTest<Q, P> selfTest) {
    Map<Class<? extends ServiceKind>, SmartClient<? extends ServiceKind>> serviceKindToClient = getServiceKindToClient();
    Set<Class<? extends ServiceKind>> toReturn = new HashSet<>();
    selfTest.getDependentServices().forEach(kind -> {
      if (serviceKind.getClass() != kind) {
        try {
          if (isFalse(serviceKindToClient.containsKey(kind))) {
            stm.add(new IllegalStateException(Strings.format(
                "Cannot ping %s because it isn't configured yet as a checkable service in QueryHealthSelfTestRunnerImpl.",
                kind.getSimpleName())));
            toReturn.add(kind);
          } else {
            String status = serviceKindToClient.get(kind).invoke(new Ping());
            if (isFalse(status.equals(OK))) {
              toReturn.add(kind);
            }
          }
        } catch (Exception e) {
          log.error(e, Strings.format("Encountered error when trying to ping %s.", kind.getSimpleName()));
          toReturn.add(kind);
        }
      }
    });
    return toReturn;
  }
  
  @VisibleForTesting
  ExecutorService createExecutor() {
    return Executors.newCachedThreadPool();
  }

  private Map<Class<? extends ServiceKind>, SmartClient<? extends ServiceKind>> getServiceKindToClient() {
    return ImmutableMap.of(
        KachingServices.UM.class, umSmartClient,
        KachingServices.BANK.class, bankSmartClient,
        KachingServices.LINK.class, linkSmartClient,
        KachingServices.CBUS.class, cbusSmartClient
    );
  }

  private static class QueryTask<R, P> {

    private final P parameters;
    private final Query<R> query;
    private final Option<UserId> maybeOwnerId;

    QueryTask(P parameters, Query<R> query, Option<UserId> maybeOwnerId) {
      this.parameters = parameters;
      this.query = query;
      this.maybeOwnerId = maybeOwnerId;
    }

  }
  
}
