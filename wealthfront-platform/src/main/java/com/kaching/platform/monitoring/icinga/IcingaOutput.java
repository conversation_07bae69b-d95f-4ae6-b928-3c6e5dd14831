package com.kaching.platform.monitoring.icinga;

import static com.google.common.base.Preconditions.checkNotNull;
import static com.kaching.platform.monitoring.icinga.IcingaOutput.ExitCode.OKAY;

import java.util.HashMap;
import java.util.Map;

import com.kaching.platform.common.Strings;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

@Entity
@ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
public class IcingaOutput {

  public static final IcingaOutput OK = new IcingaOutput("ok", OKAY);

  public enum ExitCode {
    OKAY(0),
    WARNING(1),
    CRITICAL(2),
    UNKNOWN(3);

    private static final Map<Integer, ExitCode> map = new HashMap<>();

    static {
      for (ExitCode code : values()) {
        map.put(code.getOutput(), code);
      }
    }

    private final int output;

    ExitCode(int number) {
      this.output = number;
    }

    public int getOutput() {
      return output;
    }

    public static ExitCode fromOutput(int output) {
      return map.get(output);
    }

  }

  @Value private String message;
  private ExitCode exitCode;

  private IcingaOutput() { /* JSON */ }

  public IcingaOutput(String message, ExitCode exitCode) {
    this.message = message;
    this.exitCode = exitCode;
  }

  public String getMessage() {
    return message;
  }

  @Value(name = "exitCode")
  private void setExitCodeForMarshalling(int exitCode) {
    this.exitCode = ExitCode.fromOutput(exitCode);
  }

  @Value(name = "exitCode")
  int getExitCodeForMarshalling() {
    return exitCode.getOutput();
  }

  public ExitCode getExitCode() {
    return exitCode;
  }

  public static IcingaOutputBuilder icingaOutputBuilder() {
    return new IcingaOutputBuilder();
  }

  public static class IcingaOutputBuilder {

    private String message;
    private ExitCode exitCode;

    public IcingaOutput build() {
      checkNotNull(message, "Message cannot be null");
      checkNotNull(exitCode, "ExitCode cannot be null");

      return new IcingaOutput(message, exitCode);
    }

    public IcingaOutputBuilder withMessage(String message) {
      this.message = message;
      return this;
    }

    public IcingaOutputBuilder withMessage(String message, Object... args) {
      return withMessage(Strings.format(message, args));
    }

    public IcingaOutputBuilder withExitCode(ExitCode exitCode) {
      this.exitCode = exitCode;
      return this;
    }

  }

}
