package com.kaching.platform.monitoring.nscaspooler;

import java.util.List;

import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
public class NscaspoolerServicesResponse {

  @Value
  private Boolean error;

  @Value
  private String message;

  @Value(name = "data")
  private List<NscaspoolerServiceMetadata> serviceMetadata;

  private NscaspoolerServicesResponse() { /* JSON */ }

  NscaspoolerServicesResponse(
      Boolean error,
      String message,
      List<NscaspoolerServiceMetadata> serviceMetadata) {
    this.error = error;
    this.message = message;
    this.serviceMetadata = serviceMetadata;
  }

  public Boolean getError() {
    return error;
  }

  public String getMessage() {
    return message;
  }

  public List<NscaspoolerServiceMetadata> getServiceMetadata() {
    return serviceMetadata;
  }

  public static class Builder {

    private Boolean error;
    private String message;
    private List<NscaspoolerServiceMetadata> serviceMetadata;

    public NscaspoolerServicesResponse.Builder withError(Boolean error) {
      this.error = error;
      return this;
    }

    public NscaspoolerServicesResponse.Builder withMessage(String message) {
      this.message = message;
      return this;
    }

    public Builder withServicesMetadata(List<NscaspoolerServiceMetadata> serviceMetadata) {
      this.serviceMetadata = serviceMetadata;
      return this;
    }

    public NscaspoolerServicesResponse build() {
      return new NscaspoolerServicesResponse(error, message, serviceMetadata);
    }

  }

}
