package com.kaching.platform.monitoring;

import com.kaching.platform.monitoring.VarzValue.VarzType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

@Entity
@ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
public class VarzSnapshot {
  @Value public String value;
  @Value public String name;
  @Value public VarzType type;
  @Value public String packageName;

  /* JSON */ VarzSnapshot() {}

  VarzSnapshot(String packageName, String name, String value, VarzType type) {
    this.value = value;
    this.name = name;
    this.type = type;
    this.packageName = packageName;
  }

  public static VarzSnapshot varzSnapshot(String packageName, String name, String value, VarzType type) {
    return new VarzSnapshot(packageName, name, value, type);
  }
}
