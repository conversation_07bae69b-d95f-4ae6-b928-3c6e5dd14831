package com.kaching.platform.monitoring;

import static com.kaching.platform.monitoring.Varz2Module.getNameForMethod;
import static com.kaching.platform.monitoring.Varz2Module.isMethodVarz2;
import static com.kaching.platform.monitoring.Varz2Module.isMethodVarzMap;
import static com.kaching.platform.monitoring.VarzSnapshot.varzSnapshot;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.inject.Inject;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.AbstractQuery;

public class Varz2 extends AbstractQuery<List<VarzSnapshot>> {

  private static final Log log = Log.getLog(Varz2.class);

  @Inject @VarzSet Set<Object> objects;
  @Inject @VarzSet Map<String, RuntimeVarzGenerator> runtimeGenerators;

  @Override
  public List<VarzSnapshot> process() {
    List<VarzSnapshot> snapshots = snapshotsFromRuntimeObjects();

    for (Object o : objects) {
      Method[] methods = o.getClass().getMethods();
      Arrays.sort(methods, Comparator.comparing(Method::getName));
      for (Method m : methods) {
        if (isMethodVarz2(m)) {
          try {
            m.setAccessible(true);
            Object result = m.invoke(o);
            if (result != null) {
              String packageName = o.getClass().getName();
              VarzValue.VarzType type = m.getAnnotation(VarzValue.class).value();
              String methodName = getNameForMethod(m);
              if (isMethodVarzMap(m)) {
                log.info("creating snapshots for method %s on class %s", m.getName(), o.getClass().getName());
                Map<?, ?> varzMap = (Map<?, ?>) result;
                for (Object key : varzMap.keySet()) {
                  String snapshotName = methodName + "." + key.toString();
                  String value = varzMap.get(key).toString();
                  snapshots.add(
                      varzSnapshot(
                          packageName,
                          snapshotName,
                          value,
                          type));
                  log.info("created snapshot with package name %s name %s value %s and type%",
                      packageName, snapshotName, value, type);
                }
              } else {
                snapshots.add(
                    varzSnapshot(
                        packageName,
                        methodName,
                        result.toString(),
                        type));
              }
            }
          } catch (Exception t) {
            log.warn(t, "unable to access method %s on class %s", m.getName(), o.getClass().getName());
          }
        }
      }
    }

    return snapshots;
  }
  
  private List<VarzSnapshot> snapshotsFromRuntimeObjects() {
    List<VarzSnapshot> result = new ArrayList<>();
    for (Map.Entry<String, RuntimeVarzGenerator> entry : runtimeGenerators.entrySet()) {
      try {
        for (RuntimeVarzValue value : entry.getValue().calculateValues()) {
          result.add(fromRuntimeValue(entry.getKey(), value));
        }
      } catch (RuntimeException ex) {
        log.warn(ex, "Error calculating runtime varz values for %s", entry.getKey());
      }
    }
    Collections.sort(result, Comparator.<VarzSnapshot, String>comparing(snapshot -> snapshot.packageName)
        .thenComparing(snapshot -> snapshot.name));
    return result;
  }
  
  private VarzSnapshot fromRuntimeValue(String topLevelName, RuntimeVarzValue value) {
    return value.visit(new RuntimeVarzValue.Visitor<VarzSnapshot>() {
      
      @Override
      public VarzSnapshot visitGaugeValue(RuntimeVarzValue.GaugeValue gaugeValue) {
        return new VarzSnapshot(
            topLevelName + "." + gaugeValue.getCategory(),
            gaugeValue.getName(),
            Double.toString(gaugeValue.getValue()),
            VarzValue.VarzType.GAUGE
        );
      }

      @Override
      public VarzSnapshot visitCounterValue(RuntimeVarzValue.CounterValue counterValue) {
        return new VarzSnapshot(
            topLevelName + "." + counterValue.getCategory(),
            counterValue.getName(),
            Long.toString(counterValue.getValue()),
            VarzValue.VarzType.COUNTER
        );
      }

      @Override
      public VarzSnapshot visitStringValue(RuntimeVarzValue.StringValue stringValue) {
        return new VarzSnapshot(
            topLevelName + "." + stringValue.getCategory(),
            stringValue.getName(),
            stringValue.getValue(),
            VarzValue.VarzType.STRING
        );
      }
      
    });
  }

}
