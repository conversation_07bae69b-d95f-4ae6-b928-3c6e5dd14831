package com.kaching.platform.monitoring;

import static com.google.common.collect.Maps.newHashMap;
import static com.kaching.platform.common.logging.Log.getLog;

import java.util.Collection;
import java.util.Map;

import com.google.common.collect.Multimap;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.Discovery;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceKind;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

@Entity
@ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
public class Announcements {

  private static final Log log = getLog(Announcements.class);

  @Value Map<String, Collection<ServiceDescriptor>> announcements = newHashMap();

  /* JSON */
  public Announcements() { }

  public Announcements(Discovery discovery) {
    Multimap<Class<? extends ServiceKind>, ServiceDescriptor> announcements = discovery.getAnnouncements();
    for (Class<? extends ServiceKind> kind : announcements.keySet()) {
      // grr
      if (kind == null) {
        log.error("Null kind in Discovery.getAnnouncements(); kinds = %s", announcements.keySet());
        continue;
      } else if (kind.getSimpleName() == null) {
        log.error("Null simpleName for kind %s", kind);
        continue;
      } else if (kind.getSimpleName().toLowerCase() == null) {
        log.error("Null lowercase simpleName for kind %s", kind);
        continue;
      } else if (announcements.get(kind) == null) {
        log.error("Null announcements for kind %s, should be empty", kind);
        continue;
      }
      this.announcements.put(kind.getSimpleName().toLowerCase(), announcements.get(kind));
    }
  }

}
