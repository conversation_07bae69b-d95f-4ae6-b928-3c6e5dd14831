package com.kaching.platform.monitoring;

import static com.kaching.platform.monitoring.SessionStateUtils.dumpState;
import static java.lang.String.format;

import java.util.Set;

import com.google.inject.Inject;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.queryengine.ResponseTime;

@ResponseTime(warn = 10, error = 100)
public class SessionState extends AbstractQuery<String> {

  @Inject public QueryRuntimeMonitor runtimeMonitor;
  private final Long id;

  public SessionState(Long id) {
    this.id = id;
  }

  @Override
  public String process() {
    try {
      Set<QuerySession> set = runtimeMonitor.runningQueries();
      if (set.isEmpty()) {
        return "No Queries Running at the moment\n";
      }
      //yes, we iterate. not optimal but on a small set its faster
      for (QuerySession session : set) {
        if (session.getSessionId() == id) {
          return dumpState(session);
        }
      }
      return "session not found: " + id;
    } catch (Exception e) {
      return format("could not find %s : %s", id, e);
    }
  }

  @Override
  public String toString() {
    return "SessionState [" + id + "]";
  }

}
