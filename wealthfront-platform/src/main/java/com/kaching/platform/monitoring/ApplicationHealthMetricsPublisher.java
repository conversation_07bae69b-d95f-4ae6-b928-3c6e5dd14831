package com.kaching.platform.monitoring;

import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.multicolo.MultiColoSchedule.MASTER_ONLY;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;

import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.guice.KachingServices.DMW;
import com.kaching.platform.monitoring.HealthzReport.Statistics;
import com.kaching.platform.multicolo.MultiColo;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.ServiceRevision;
import com.kaching.platform.queryengine.client.SmartClient;
import com.kaching.util.Sleeper;
import com.kaching.util.http.LongTimeoutPooling;

@MultiColo(
    enabledOn = MASTER_ONLY,
    reason = "Only persisting health metrics on the master colo for now")
@Singleton
public class ApplicationHealthMetricsPublisher implements Runnable {

  private static final Log log = getLog(ApplicationHealthMetricsPublisher.class);

  @Inject ApplicationHealthMetricsPublisherConfig config;
  @Inject Sleeper sleeper;
  @Inject ServiceDescriptor serviceDescriptor;
  @Inject QueryRuntimeMonitor monitor;
  @Inject @LongTimeoutPooling SmartClient<DMW> dmw;
  @Inject @Named("isIntegrationServer") boolean isIntegrationServer;

  @Override
  public void run() {
    if (isIntegrationServer) {
      return;
    }

    while (true) {
      Duration interval = config.getExecutionInterval();
      if (interval == null || interval.getMillis() <= 0) {
        log.warn("Invalid execution interval configured. Stopping worker to prevent tight loop.");
        break;
      }

      publish();

      sleeper.sleep(interval);
    }
  }

  @VisibleForTesting
  void publish() {
    try {
      HealthzReport healthzReport = new HealthzReport();
      monitor.statistics().forEach(entry -> {
        RollingStatistics rollingStatistics = entry.getValue();
        if (rollingStatistics.rollingTotalCount() > 0) {
          healthzReport.getStatistics().put(
              entry.getKey(),
              new Statistics(
                  rollingStatistics.rollingAverage(),
                  rollingStatistics.rollingMedian(),
                  rollingStatistics.rollingMin(),
                  rollingStatistics.rollingMax(),
                  rollingStatistics.rollingTotalCount(),
                  rollingStatistics.rollingFailurePercentage(),
                  rollingStatistics.rollingInvalidPercentage()));
        }
      });
      monitor.downstreamStatistics().forEach(entry -> {
        ServiceRevision serviceRevision = entry.getKey();
        Map<String, Statistics> stats = new HashMap<>();
        ConcurrentMap<String, RollingStatistics> rollingStatistics = entry.getValue();
        rollingStatistics.forEach((queryName, rollingQueryStatistics) -> {
          if (rollingQueryStatistics.rollingTotalCount() > 0) {
            stats.put(queryName, new Statistics(
                rollingQueryStatistics.rollingAverage(),
                rollingQueryStatistics.rollingMedian(),
                rollingQueryStatistics.rollingMin(),
                rollingQueryStatistics.rollingMax(),
                rollingQueryStatistics.rollingTotalCount(),
                rollingQueryStatistics.rollingFailurePercentage(),
                rollingQueryStatistics.rollingInvalidPercentage()));
          }
        });
        healthzReport.getDownstreamReports().add(
            new HealthzReport.DownstreamHealthzReport(serviceRevision, stats));
      });
      dmw.invoke(new PublishApplicationHealthMetrics(
          ServiceRevision.fromDescriptor(serviceDescriptor), healthzReport));
    } catch (Exception e) {
      log.error(e);
    }
  }

}
