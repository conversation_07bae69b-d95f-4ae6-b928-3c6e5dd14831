package com.kaching.platform.monitoring;

import static java.lang.String.format;

import com.kaching.platform.queryengine.QuerySession;

public abstract class SessionStateUtils {

  public static String dumpState(QuerySession session) {
    if (session.running()) {
      StringBuilder sb = new StringBuilder();
      sb.append(format("Session %d of Query %s %s\n",
          session.getSessionId(),
          session.getQuery().getClass().getSimpleName(),
          session.getQuery().toString()));
      sb.append(format("Thread %s State: %s\n",
          session.getQueryMainThread().getName(),
          session.getQueryMainThread().getState().name()));
      StackTraceElement[] trace = session.getQueryMainThread().getStackTrace();
      for (StackTraceElement element : trace) {
        sb.append(format("  %s.%s() : %s\n",
            element.getClassName(),
            element.getMethodName(),
            element.getLineNumber()));
      }
      return sb.toString();
    }
    return format("session %s is no longer running", session.getSessionId());
  }

}
