package com.kaching.platform.pagerduty.api.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EscalationPolicy {

  @JsonProperty("html_url")
  private String htmlUrl;
  @JsonProperty("id")
  private String id;
  @JsonProperty("self")
  private String self;
  @JsonProperty("summary")
  private String summary;
  @JsonProperty("name")
  private String name;
  @JsonProperty("type")
  private String type;

  public EscalationPolicy(String htmlUrl, String id, String self, String summary, String name, String type) {
    this.htmlUrl = htmlUrl;
    this.id = id;
    this.self = self;
    this.summary = summary;
    this.name = name;
    this.type = type;
  }

  public EscalationPolicy() {/* jackson */}

  public String getHtmlUrl() {
    return htmlUrl;
  }

  public String getId() {
    return id;
  }

  public String getSelf() {
    return self;
  }

  public String getSummary() {
    return summary;
  }

  public String getName() {
    return name;
  }

  public String getType() {
    return type;
  }

  public static class Builder {

    private String htmlUrl;
    private String id;
    private String self;
    private String summary;
    private String name;
    private String type;

    public EscalationPolicy build() {
      return new EscalationPolicy(htmlUrl, id, self, summary, name, type);
    }

    public Builder withHtmlUrl(String htmlUrl) {
      this.htmlUrl = htmlUrl;
      return this;
    }

    public Builder withId(String id) {
      this.id = id;
      return this;
    }

    public Builder withSelf(String self) {
      this.self = self;
      return this;
    }

    public Builder withSummary(String summary) {
      this.summary = summary;
      return this;
    }

    public Builder withName(String name) {
      this.name = name;
      return this;
    }

    public Builder withType(String type) {
      this.type = type;
      return this;
    }

  }

}
