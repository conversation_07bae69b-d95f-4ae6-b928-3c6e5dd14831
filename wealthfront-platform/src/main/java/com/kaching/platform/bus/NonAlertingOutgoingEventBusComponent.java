package com.kaching.platform.bus;

import com.kaching.platform.bus.impl.CloneOutgoingEventToSubscriber;
import com.kaching.platform.bus.impl.DeleteDroppedOutgoingEvent;
import com.kaching.platform.bus.impl.DeleteNullReceiverOutgoingEvent;
import com.kaching.platform.bus.impl.DeleteSuccessfulOutgoingEvents;
import com.kaching.platform.bus.impl.DropFailedOutgoingEvent;
import com.kaching.platform.bus.impl.GetSubscriptions;
import com.kaching.platform.bus.impl.OutgoingEvent;
import com.kaching.platform.bus.impl.PublishDummyEvent;
import com.kaching.platform.bus.impl.RepublishEventsWithNoReceiver;
import com.kaching.platform.bus.impl.ResignalOutgoingEvents;
import com.kaching.platform.bus.impl.ResignalSomeUnpolledEvents;
import com.kaching.platform.bus.impl.RetryFailedOutgoingEvent;
import com.kaching.platform.bus.impl.RetryFailedOutgoingEvents;
import com.kaching.platform.bus.impl.Sentinel;
import com.kaching.platform.components.Component;
import com.kaching.platform.components.Cronjob;
import com.kaching.platform.hibernate.IsDatabaseAdminPredicate;
import com.kaching.platform.zk.IsLeaderQueryPredicate;

@Component(
    queries = {
        CloneOutgoingEventToSubscriber.class,
        DeleteDroppedOutgoingEvent.class,
        DeleteNullReceiverOutgoingEvent.class,
        DeleteSuccessfulOutgoingEvents.class,
        DropFailedOutgoingEvent.class,
        GetSubscriptions.class,
        PublishDummyEvent.class,
        RepublishEventsWithNoReceiver.class,
        ResignalOutgoingEvents.class,
        ResignalSomeUnpolledEvents.class,
        RetryFailedOutgoingEvent.class,
        RetryFailedOutgoingEvents.class,
    },
    hibernateEntitites = {
        OutgoingEvent.class,
    },
    crons = {
        @Cronjob(
            query = DeleteSuccessfulOutgoingEvents.class,
            triggers = "0 0 * * * ?",
            conditions = {IsLeaderQueryPredicate.class, IsDatabaseAdminPredicate.class}
        ),
        @Cronjob(
            query = RetryFailedOutgoingEvents.class,
            triggers = "0 */1 * * * ?",
            conditions = {
                IsLeaderQueryPredicate.class,
                ShouldRetryFailedOutgoingEventsPredicate.class
            }),
    },
    startupListener = Sentinel.class
)
public class NonAlertingOutgoingEventBusComponent {

}
