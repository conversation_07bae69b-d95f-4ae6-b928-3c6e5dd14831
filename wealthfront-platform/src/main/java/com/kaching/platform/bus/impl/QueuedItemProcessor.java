package com.kaching.platform.bus.impl;

import static com.kaching.platform.bus.impl.QueuedItemProcessor.ProcessResult.FAILED_WITH_RETRIES;
import static com.kaching.platform.bus.impl.QueuedItemProcessor.ProcessResult.NORMAL;
import static com.kaching.platform.common.logging.Log.getLog;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Function;

import javax.inject.Inject;
import javax.inject.Provider;

import org.joda.time.DateTime;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.Author;
import com.kaching.monitor.esp.ResponsiblePartyFinder;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.queryengine.StackTraceMonitor;

class QueuedItemProcessor {

  enum ProcessResult {
    NORMAL,
    FAILED_WITH_RETRIES
  }

  private static final Log log = getLog(QueuedItemProcessor.class);

  @VisibleForTesting
  static final int EVENT_FAILURE_THROWING_THRESHOLD = 60;

  @Inject Transacter transacter;
  @Inject Provider<DateTime> clock;
  @Inject StackTraceMonitor stackTraceMonitor;
  @Inject ResponsiblePartyFinder responsiblePartyFinder;
  @Inject ServiceKind serviceKind;

  <T extends QueuedItem & HasEventType> ProcessResult process(
      Class<T> clazz,
      Id<T> id,
      boolean dropOnUnknownFailure,
      Function<T, Boolean> action) {
    for (T item : poll(clazz, id)) {
      Set<Author> authors;
      try {
        authors = new HashSet<>(responsiblePartyFinder.findResponsibleParties(
            serviceKind.getClass(),
            Class.forName(item.describeEventType())));
      } catch (ClassNotFoundException classNotFoundException) {
        authors = new HashSet<>();
      }
      try {
        boolean succeeded = action.apply(item);
        if (succeeded) {
          markAsSucceeded(clazz, id);
        } else {
          throw new RuntimeException("the action returned false");
        }
      } catch (BusNonRetryableException e) {
        markAsFailed(clazz, id, true, "a " + e.getClass().getSimpleName() + " was thrown");
        stackTraceMonitor.add(
            new BusNonRetryableException(
                Strings.format(
                    "QueuedItemProcessor failed with a %s for %s %s id: %s",
                    BusNonRetryableException.class.getSimpleName(),
                    clazz.getSimpleName(),
                    item.describeEventType(),
                    id),
                e),
            authors);
      } catch (BusRetryableException e) {
        int failedAttempts = markAsFailed(clazz, id, false, "a " + e.getClass().getSimpleName() + " was thrown");
        if (failedAttempts >= EVENT_FAILURE_THROWING_THRESHOLD) {
          stackTraceMonitor.add(
              new BusRetryableException(
                  Strings.format(
                      "QueuedItemProcessor failed %s times for %s %s id: %s",
                      failedAttempts,
                      clazz.getSimpleName(),
                      item.describeEventType(),
                      id),
                  e),
              authors);
        } else {
          return FAILED_WITH_RETRIES;
        }
      } catch (RuntimeException e) {
        int failedAttempts =
            markAsFailed(clazz, id, dropOnUnknownFailure, "a " + e.getClass().getSimpleName() + " was thrown");
        if (dropOnUnknownFailure || failedAttempts >= EVENT_FAILURE_THROWING_THRESHOLD) {
          stackTraceMonitor.add(
              new BusUnknownException(
                  Strings.format(
                      "QueuedItemProcessor failed %s times for %s %s id: %s",
                      failedAttempts,
                      clazz.getSimpleName(),
                      item.describeEventType(),
                      id),
                  e),
              authors);
        }
      }
    }

    return NORMAL;
  }

  private <T extends QueuedItem> void markAsSucceeded(Class<T> clazz, Id<T> id) {
    transacter.executeWithSession(session -> {
      T loaded = session.getOrThrow(clazz, id);
      loaded.setSucceededAt(clock.get());
      loaded.setInFlight(false);
    });
  }

  @VisibleForTesting
  <T extends QueuedItem> int markAsFailed(Class<T> clazz, Id<T> id, boolean drop, String reason) {
    log.warn("processing %s#%s failed because %s", clazz.getSimpleName(), id, reason);
    return transacter.executeWithSessionExpression(session -> {
      T loaded = session.getOrThrow(clazz, id);
      loaded.setFailedAt(clock.get());
      loaded.setInFlight(false);
      if (drop || loaded.getFailedAttempts() >= EVENT_FAILURE_THROWING_THRESHOLD) {
        loaded.setDropped(true);
      }
      return loaded.getFailedAttempts();
    });
  }

  private <T extends QueuedItem> Option<T> poll(Class<T> clazz, Id<T> id) {
    try {
      return transacter.executeWithSessionExpression(session -> {
        Option<T> maybeItem = session.get(clazz, id);
        if (maybeItem.isEmpty()) {
          log.warn("%s#%s no longer exists", clazz.getSimpleName(), id);
          return Option.none();
        }
        T item = maybeItem.getOrThrow();
        if (item.isDropped()) {
          log.warn("%s#%s is dropped", clazz.getSimpleName(), id);
          return Option.none();
        }
        if (item.getSucceededAt() != null) {
          log.warn("%s#%s has already succeeded", clazz.getSimpleName(), id);
          return Option.none();
        }
        if (item.isInFlight()) {
          log.warn("%s#%s is in-flight", clazz.getSimpleName(), id);
          return Option.none();
        }
        item.setLastPolledAt(clock.get());
        item.unsetFailedAt();
        item.setInFlight(true);
        return Option.some(item);
      });
    } catch (RuntimeException e) {
      if (RetryingTransacter.defaultRetryingExceptions().test(e)) {
        log.warn(e, "database contention occurred when polling %s#%s", clazz.getSimpleName(), id);
        try {
          markAsFailed(clazz, id, false, "database contention");
        } catch (RuntimeException exception) {
          log.error("Failed to mark %s as failed after database contention", id);
          log.error(exception.getMessage());
        }
        return Option.none();
      } else {
        log.error(e, "exception thrown when polling %s#%s", clazz.getSimpleName(), id);
        stackTraceMonitor.add(e);
        return Option.none();
      }
    }
  }

}
