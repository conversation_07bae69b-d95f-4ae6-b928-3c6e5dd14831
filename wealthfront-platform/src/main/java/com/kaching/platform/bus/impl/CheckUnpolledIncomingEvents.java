package com.kaching.platform.bus.impl;

import java.util.Set;

import com.google.inject.Inject;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.monitoring.icinga.IcingaCheck;

public class CheckUnpolledIncomingEvents extends AbstractCheckUnpolledQueuedItems<IncomingEvent> implements IcingaCheck {

  @Override
  Class<IncomingEvent> getEntityClass() {
    return IncomingEvent.class;
  }

  @Override
  Set<Id<IncomingEvent>> getUnpolledIds() {
    return transacter.executeWithReadOnlySessionExpression(new WithReadOnlySessionExpression<Set<Id<IncomingEvent>>>() {
      @Inject IncomingEventRepository incomingEventRepository;

      @Override
      public Set<Id<IncomingEvent>> run(DbSession session) {
        return incomingEventRepository.getUnpolledIncomingEventIds(getCreatedAfter(), getUnpolledSince());
      }
    });
  }

}
