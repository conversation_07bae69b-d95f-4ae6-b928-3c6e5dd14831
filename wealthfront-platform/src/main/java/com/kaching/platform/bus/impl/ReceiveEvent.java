package com.kaching.platform.bus.impl;

import static com.kaching.platform.common.logging.Log.getLog;

import javax.inject.Inject;

import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.LockAcquisitionException;

import com.kaching.platform.common.logging.Log;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.queryengine.AbstractQuery;

public class ReceiveEvent extends AbstractQuery<Boolean> {

  private static final Log log = getLog(ReceiveEvent.class);

  private final Message message;

  ReceiveEvent(Message message) {
    this.message = message;
  }

  @Inject Transacter transacter;

  @Override
  public Boolean process() {
    try {
      transacter.execute(new WithSession() {

        @Inject Receiver receiver;

        @Override
        public void run(DbSession session) {
          receiver.receive(message);
        }
      });
    } catch (ConstraintViolationException e) {
      log.info("event %s has already been received", message.getEventId());
    } catch (LockAcquisitionException e) {
      log.info("LockAcquisitionException while receiving event %s", message.getEventId());
      return false;
    }
    return true;
  }
}
