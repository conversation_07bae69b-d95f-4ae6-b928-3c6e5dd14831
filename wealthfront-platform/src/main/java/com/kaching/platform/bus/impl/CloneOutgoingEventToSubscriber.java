package com.kaching.platform.bus.impl;

import com.google.inject.Inject;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.Transactional;

@Transactional
public class CloneOutgoingEventToSubscriber extends AbstractQuery<Boolean> {

  private static final Log log = Log.getLog(CloneOutgoingEventToSubscriber.class);

  private final Id<OutgoingEvent> outgoingEventId;
  private final ServiceKind subscriber;

  public CloneOutgoingEventToSubscriber(Id<OutgoingEvent> outgoingEventId, ServiceKind subscriber) {
    this.outgoingEventId = outgoingEventId;
    this.subscriber = subscriber;
  }

  @Inject DbSession dbSession;
  @Inject Outbox outbox;

  @Override
  public Boolean process() {
    OutgoingEvent outgoingEventToClone = dbSession.getOrThrow(OutgoingEvent.class, outgoingEventId);
    log.info("Cloned outgoing event: %s", outgoingEventToClone.getId());
    outbox.enqueue(subscriber, outgoingEventToClone.getMessage());
    return true;
  }

}
