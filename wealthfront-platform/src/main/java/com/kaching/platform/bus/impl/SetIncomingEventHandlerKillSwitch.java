package com.kaching.platform.bus.impl;

import com.google.inject.Inject;
import com.kaching.platform.queryengine.AbstractQuery;

public class SetIncomingEventHandlerKillSwitch extends AbstractQuery<Boolean> {

  private final boolean value;

  public SetIncomingEventHandlerKillSwitch(boolean value) {
    this.value = value;
  }

  @Inject IncomingEventHandlerKillSwitch killSwitch;

  @Override
  public Boolean process() {
    killSwitch.set(value);
    return value;
  }

}
