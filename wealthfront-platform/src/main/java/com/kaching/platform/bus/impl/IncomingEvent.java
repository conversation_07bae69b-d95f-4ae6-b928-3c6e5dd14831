package com.kaching.platform.bus.impl;

import org.joda.time.DateTime;

import com.kaching.platform.bus.Handler;
import com.kaching.platform.hibernate.Id;

public class IncomingEvent extends QueuedItem implements HasEventType {

  private Id<IncomingEvent> id;
  private EventId eventId;
  private String handler;
  private Message message;

  IncomingEvent() {
  }

  IncomingEvent(Class<? extends Handler<?>> handler, Message message, DateTime createdAt) {
    super(createdAt);
    this.eventId = message.getEventId();
    this.handler = handler.getName();
    this.message = message;
  }

  @Override
  public Id<IncomingEvent> getId() {
    return id;
  }

  public EventId getEventId() {
    return eventId;
  }

  @SuppressWarnings("unchecked")
  public Class<? extends Handler<?>> getHandler() {
    try {
      return (Class<? extends Handler<?>>) Class.forName(handler);
    } catch (ClassNotFoundException e) {
      throw new RuntimeException(e);
    }
  }

  public Message getMessage() {
    return message;
  }

  @Override
  public String describeEventType() {
    return handler;
  }
  
}
