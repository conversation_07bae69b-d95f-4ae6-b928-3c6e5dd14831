package com.kaching.platform.bus.impl;

import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.util.Preconditions.checkQueryArgument;

import javax.inject.Inject;

import com.kaching.platform.common.logging.Log;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.Transactional;

@Transactional
public class DeleteDroppedIncomingEvent extends AbstractQuery<Boolean> {

  private static final Log log = getLog(DeleteDroppedIncomingEvent.class);

  private final Id<IncomingEvent> id;

  DeleteDroppedIncomingEvent(Id<IncomingEvent> id) {
    this.id = id;
  }

  @Inject DbSession session;

  @Override
  public Boolean process() {
    IncomingEvent event = session.getOrThrow(IncomingEvent.class, id);

    checkQueryArgument(event.isDropped(), "IncomingEvent#%s is not dropped", id);

    log.info("deleting dropped IncomingEvent#%s", id);

    session.delete(event);

    return true;
  }
}
