package com.kaching.platform.bus;

import com.kaching.platform.bus.impl.CheckAllOutgoingEventsHaveValidReceiver;
import com.kaching.platform.bus.impl.CheckUnpolledOutgoingEvents;
import com.kaching.platform.components.Component;

@Component(
    dependsOn = {
      NonAlertingOutgoingEventBusComponent.class,
    },
    icingaChecks =  {
        CheckAllOutgoingEventsHaveValidReceiver.class,
        CheckUnpolledOutgoingEvents.class,
    }
)
public class OutgoingEventBusComponent {
}
