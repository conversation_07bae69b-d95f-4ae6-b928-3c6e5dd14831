package com.kaching.platform.bus.impl;

import static java.util.Objects.nonNull;
import static org.hibernate.criterion.Restrictions.and;
import static org.hibernate.criterion.Restrictions.eq;
import static org.hibernate.criterion.Restrictions.ge;
import static org.hibernate.criterion.Restrictions.isNotNull;
import static org.hibernate.criterion.Restrictions.isNull;
import static org.hibernate.criterion.Restrictions.lt;
import static org.hibernate.criterion.Restrictions.not;
import static org.hibernate.criterion.Restrictions.or;

import java.util.Set;

import javax.inject.Inject;

import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.LogicalExpression;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.SimpleExpression;
import org.joda.time.DateTime;
import org.joda.time.Duration;

import com.kaching.platform.bus.Handler;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.PersistentCriteria;

public class IncomingEventMonitorImpl implements IncomingEventMonitor {

  @Inject DbSession session;
  @Inject DateTime now;

  @Override
  public HandlerStatus monitorHandler(Class<? extends Handler<?>> handler, Duration stuckCutoff) {
    return monitorHandler(handler, stuckCutoff, null);
  }

  @Override
  public HandlerStatus monitorHandler(
      Class<? extends Handler<?>> handler,
      Duration stuckCutoff,
      Duration failedCutoff) {
    PersistentCriteria<IncomingEvent> failedEventsCriteria = session.createCriteria(IncomingEvent.class)
        .add(eq("handler", handler.getName()))
        .add(isNotNull("failedAt"))
        .add(eq("dropped", true));
    if (nonNull(failedCutoff)) {
      failedEventsCriteria.add(ge("failedAt", now.minus(failedCutoff)));
    }

    Set<Id<IncomingEvent>> failedEventIds = failedEventsCriteria
        .<Id<IncomingEvent>>setProjection(Projections.id())
        .set();

    LogicalExpression eventIsDropped = and(isNotNull("failedAt"), eq("dropped", true));
    Criterion eventHasSucceeded = isNotNull("succeededAt");
    Criterion eventIsNotCompleted = not(or(eventIsDropped, eventHasSucceeded));

    PersistentCriteria<IncomingEvent> stuckEventsCriteria = session.createCriteria(IncomingEvent.class)
        .add(eq("handler", handler.getName()))
        .add(eventIsNotCompleted);
    if (nonNull(stuckCutoff)) {
      LogicalExpression eventHasNotBeenAttempted = and(isNull("lastPolledAt"), lt("createdAt", now.minus(stuckCutoff)));
      SimpleExpression eventHasNotBeenAttemptedRecently = lt("lastPolledAt", now.minus(stuckCutoff));
      stuckEventsCriteria.add(or(eventHasNotBeenAttempted, eventHasNotBeenAttemptedRecently));
    }

    Set<Id<IncomingEvent>> stuckEventIds = stuckEventsCriteria
        .<Id<IncomingEvent>>setProjection(Projections.id())
        .set();

    return new HandlerStatus(
        failedEventIds,
        stuckEventIds
    );
  }

}
