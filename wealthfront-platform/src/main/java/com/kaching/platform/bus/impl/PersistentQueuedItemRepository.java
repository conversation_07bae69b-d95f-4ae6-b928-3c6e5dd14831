package com.kaching.platform.bus.impl;

import static org.hibernate.criterion.Restrictions.eq;
import static org.hibernate.criterion.Restrictions.ge;
import static org.hibernate.criterion.Restrictions.isNull;
import static org.hibernate.criterion.Restrictions.le;

import java.util.Set;

import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.joda.time.DateTime;

import com.kaching.entities.PersistentRepository;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;

public abstract class PersistentQueuedItemRepository<T extends QueuedItem> extends PersistentRepository<T> {

  public PersistentQueuedItemRepository(Class<T> clazz, DbSession session) {
    super(clazz, session);
  }

  public Set<Id<T>> getUnpolledQueuedItemIds(DateTime createdAfter, DateTime unpolledSince) {
    return createCriteria(getEntityClass())
        .add(ge("createdAt", createdAfter))
        .add(le("createdAt", unpolledSince))
        .add(isNull("firstPolledAt"))
        .add(eq("dropped", false))
        .<Id<T>>setProjection(Projections.id())
        .addOrder(Order.asc("id"))
        .set();
  }

}
