package com.kaching.platform.bus.impl;

import static com.kaching.platform.common.logging.Log.getLog;

import javax.inject.Inject;
import javax.inject.Provider;

import org.joda.time.DateTime;

import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;

class OutboxImpl implements Outbox {

  private static final Log log = getLog(OutboxImpl.class);

  @Inject DbSession session;
  @Inject Provider<DateTime> clock;
  @Inject OutboxSignaler outboxSignaler;

  @Override
  public void enqueue(ServiceKind receiver, Message message) {
    log.debug("enqueuing %s for %s", message.getEventId(), receiver.getClass().getSimpleName());

    Id<OutgoingEvent> id = session.save(new OutgoingEvent(receiver, message, clock.get()));

    session.onCommit(() -> outboxSignaler.signal(id));
  }

  @Override
  public void enqueueForAlerting(Message message) {
    log.debug("enqueuing %s for %s", message.getEventId());
    session.save(new OutgoingEvent(null, message, clock.get()));
  }
}
