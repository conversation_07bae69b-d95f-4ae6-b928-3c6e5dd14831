package com.kaching.platform.bus.impl;

import com.kaching.platform.bus.Event;
import com.kaching.platform.bus.DataEnvironmentAwareEvent;
import com.kaching.platform.common.DataEnvironment;

public abstract class DataEnvironmentAwareBusImpl extends BusImpl {

  @Override
  public void publish(Event event) {
    if (event instanceof DataEnvironmentAwareEvent) {
      DataEnvironmentAwareEvent dataEnvironmentAwareEvent = (DataEnvironmentAwareEvent) event;
      dataEnvironmentAwareEvent.setDataEnvironment(getDataEnvironmentForEvent(dataEnvironmentAwareEvent));
    }
    super.publish(event);
  }

  protected abstract DataEnvironment getDataEnvironmentForEvent(DataEnvironmentAwareEvent event);

}
