package com.kaching.platform.bus.impl;

import java.util.Set;

import com.google.inject.Inject;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.AbstractQuery;

public class ResignalOutgoingEvents extends AbstractQuery<Boolean> {

  private final Set<Id<OutgoingEvent>> outgoingEvents;

  public ResignalOutgoingEvents(Set<Id<OutgoingEvent>> outgoingEvents) {
    this.outgoingEvents = outgoingEvents;
  }

  @Inject OutboxSignaler outboxSignaler;

  @Override
  public Boolean process() {
    outgoingEvents.forEach(outboxSignaler::signal);
    return true;
  }

}
