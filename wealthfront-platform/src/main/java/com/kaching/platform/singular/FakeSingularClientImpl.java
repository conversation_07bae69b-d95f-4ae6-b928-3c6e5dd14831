package com.kaching.platform.singular;

import com.kaching.util.functional.Result;
import com.kaching.util.http.ResultBasedHttpClient;
import com.twolattes.json.Json;

public class FakeSingularClientImpl implements SingularClient {

  @Override
  public Result<ResultBasedHttpClient.Response> reportSession(Event.Session event) {
    return Result.of(new ResultBasedHttpClient.Response(
        200,
        "OK",
        Json.object("status", "ok").toString()
    ));
  }

  @Override
  public Result<ResultBasedHttpClient.Response> reportEvent(Event.PostSession event) {
    return Result.of(new ResultBasedHttpClient.Response(
        200,
        "OK",
        Json.object("status", "ok").toString()
    ));
  }

  @Override
  public Result<Long> getSKAdNetworkConversionValue(SingularSKAdNetworkConversionApiEvent event) {
    return Result.of(999L);
  }

}
