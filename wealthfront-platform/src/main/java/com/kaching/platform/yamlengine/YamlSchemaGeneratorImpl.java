package com.kaching.platform.yamlengine;

import static java.util.Collections.emptySet;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.apache.commons.lang3.reflect.TypeUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;

import com.kaching.api.JavaApiSchemaIntrospector;
import com.kaching.api.JavaApiType;
import com.kaching.api.JavaApiTypeVisitor;
import com.kaching.api.JavaApiTypesBuilder;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.hibernate.Id;

public class YamlSchemaGeneratorImpl implements YamlSchemaGenerator {

  private static final Map<String, Boolean> ENTITY_PROPERTY_TO_BOOLEAN_DEFAULT_VALUE = Map.ofEntries(
      Map.entry("AccountTransferResponse,success", false),
      Map.entry("Answer,defaultAnswer", false),
      Map.entry("ApplicationConfig,shouldForceShowSoftUpgradePrompt", false),
      Map.entry("ApplicationConfig,priorYearIraContributionsAllowed", false),
      Map.entry("ApplicationConfig,supportsPageLevelForceUpgrade", false),
      Map.entry("BalanceSheetRecommendation,transitionedState", false),
      Map.entry("BalanceSheetRecommendationSummary,shouldDisplayFinancialPictureRecommendations", false),
      Map.entry("BalanceSheetRecommendationSummary,hasCompletedRecommendation", false),
      Map.entry("BmlInputNode,isRequired", true),
      Map.entry("BmlNode,isVisible", true),
      Map.entry("BooleanResponse,success", false),
      Map.entry("CashAccountOverview,isCurrentRateBoosted", false),
      Map.entry("CreateAccountRequestRequest,appSupportsTryBeforeYouBuy", false),
      Map.entry("CreateAccountRequestRequest,appSupportsCafaResumption", false),
      Map.entry("CreateDepositPlanResponse,success", false),
      Map.entry("CreateOneTimeTransferResponse,success", false),
      Map.entry("CreateRecurringTransferResponse,success", false),
      Map.entry("DebitAccountStatus,shouldPoll", false),
      Map.entry("DebitAccountStatus,isUpgradeCancelable", false),
      Map.entry("ExternalAccount,tracked", false),
      Map.entry("ExternalAccountResponse,success", false),
      Map.entry("InvitePreferences,signedInviteAgreement", false),
      Map.entry("IraWithdrawalTaxWithholdingInformation,shouldFederalWithhold", false),
      Map.entry("IraWithdrawalTaxWithholdingInformation,shouldStateWithhold", false),
      Map.entry("LinkResponse,success", false),
      Map.entry("PathUserPreferences,shouldDisplayPathGraph", true),
      Map.entry("PredictDepositPlanActionsResponse,success", false),
      Map.entry("PreviewRedfinResponse,success", false),
      Map.entry("RetirementAccountContribution,userLinked", false),
      Map.entry("RetirementAccountContribution,shouldDisplaySavingsAdvice", true),
      Map.entry("ReviewTransferIntentStep,stepUpAuthRequired", false),
      Map.entry("TimeOffProjectionData,hasOrderedWithdrawals", true),
      Map.entry("TransferIntentChoice,selectable", true),
      Map.entry("TransferIntentQuestion,editable", true),
      Map.entry("TransferIntentQuestion,optional", false),
      Map.entry("TransferIntentTriggerContent,displayAsNotice", false),
      Map.entry("TransferReviewMetadatum,shouldTrack", false),
      Map.entry("UserInfo,signedInviteAgreement", false),
      Map.entry("UserInfo,pushNotificationsEnabled", true),
      Map.entry("UserInfo,geek", false),
      Map.entry("UserInfo,hasBiometricAuth", false),
      Map.entry("UserInfo,riskParityForExistingClients", false),
      Map.entry("UserInfo,hasReusableRiskProfile", false),
      Map.entry("UserInfoMfaSummary,mfaRequired", false),
      Map.entry("UserInfoMfaSummary,nonSmsMfaEnabled", false),
      Map.entry("UserStatus,applicationCompleted", false),
      Map.entry("UserStatus,isTryBeforeYouBuy", false),
      Map.entry("UserStatus,hasPendingJafaInvitation", false),
      Map.entry("ValidateDepositPlanResponse,success", false),
      Map.entry("WithdrawalAccount,eligibleForOneTransfer", false)
  );

  private static final Set<String> ENTITY_PROPERTY_FOR_NULLABLE_ARRAY = Set.of(
      "AbstractApiResponse,errors",
      "AbstractSubmitTransferIntentResponse,errors",
      "MutationResponse,errors"
  );

  @Override
  public Map<String, Object> generate(Set<JavaApiTypesBuilder.TypeKey> typeKeys) {
    Map<String, Object> fullSchema = new LinkedHashMap<>();
    JavaApiTypesBuilder typesBuilder = new JavaApiTypesBuilder();

    TypesData typesData = getAllTypeData(typeKeys, typesBuilder);
    typesData.javaApiTypes().forEach(apiType -> {
      fullSchema.put(getSimpleName(apiType), visitJavaApiType(apiType, typesData.javaTypes(), false,
          false, Option.none()));
    });

    return fullSchema;
  }

  @Override
  public String writeYaml(Map<String, Object> yamlObject) {
    Map<String, Object> fullYamlObject = new LinkedHashMap<>();
    DumperOptions options = new DumperOptions();
    options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
    options.setPrettyFlow(true);
    fullYamlObject.put("swagger", "2.0");
    fullYamlObject.put("definitions", new TreeMap<>(yamlObject));

    Yaml yaml = new Yaml(options);
    return yaml.dump(fullYamlObject);
  }

  private TypesData getAllTypeData(Set<JavaApiTypesBuilder.TypeKey> typeKeys, JavaApiTypesBuilder typesBuilder) {
    Set<JavaApiType> allExpectedJavaApiTypes = new HashSet<>();
    Set<Type> allExpectedJavaTypes = new HashSet<>();
    typeKeys.forEach(typeKey -> {
      JavaApiType apiType = JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, typesBuilder);
      allExpectedJavaApiTypes.add(apiType);
      allExpectedJavaTypes.add(apiType.getJavaType());
      getAllDescendants(apiType).forEach(descendant -> {
        allExpectedJavaApiTypes.add(descendant);
        allExpectedJavaTypes.add(descendant.getJavaType());
      });
    });
    return new TypesData(allExpectedJavaApiTypes, allExpectedJavaTypes);
  }

  private Set<JavaApiType.JavaObjectType> getAllDescendants(JavaApiType apiType) {
    return apiType.visit(new JavaApiTypeVisitor.DefaultVisitor<>(emptySet()) {
      @Override
      public Set<JavaApiType.JavaObjectType> caseObjectType(JavaApiType.JavaObjectType objectType) {
        return objectType.getAllDescendents();
      }
    });
  }

  private String getSimpleName(JavaApiType apiType) {
    return apiType.visit(new JavaApiTypeVisitor<>() {

      @Override
      public String caseMapType(JavaApiType.JavaMapType mapType) {
        throw new UnsupportedOperationException("No simple name for mapType");
      }

      @Override
      public String caseArrayType(JavaApiType.JavaArrayType arrayType) {
        throw new UnsupportedOperationException("No simple name for arrayType");
      }

      @Override
      public String caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        throw new UnsupportedOperationException("No simple name for simpleType");
      }

      @Override
      public String caseObjectType(JavaApiType.JavaObjectType objectType) {
        return objectType.getEntityClass().getSimpleName();
      }

      @Override
      public String caseOptionType(JavaApiType.JavaOptionType optionType) {
        throw new UnsupportedOperationException("No simple name for optionType");
      }

      @Override
      public String caseCollectionType(JavaApiType.JavaCollectionType collectionType) {
        throw new UnsupportedOperationException("No simple name for collectionType");
      }

      @Override
      public String caseEnumType(JavaApiType.JavaEnumType enumType) {
        return enumType.getEnumClass().getSimpleName();
      }
    });
  }

  private Map<String, Object> visitJavaApiType(
      JavaApiType apiType, Set<Type> allExpectedJavaTypes, boolean isField,
      boolean isOptional, Option<String> maybeEntityPropertyDefaultValueKey) {
    Map<String, Object> schema = new LinkedHashMap<>();
    apiType.visit(new JavaApiTypeVisitor<>() {

      @Override
      public Unit caseMapType(JavaApiType.JavaMapType mapType) {
        if (mapType.getJavaKeyType() == String.class) {
          schema.put("type", "object");
          schema.put("additionalProperties", visitJavaApiType(mapType.getApiValueType(), allExpectedJavaTypes, true,
              false, maybeEntityPropertyDefaultValueKey));
        } else {
          throw new IllegalArgumentException("Map keys must be of type string");
        }
        return Unit.unit;
      }

      @Override
      public Unit caseArrayType(JavaApiType.JavaArrayType arrayType) {
        if (arrayType.getJavaValueType().equals(byte.class)) {
          schema.put("type", "string");
          schema.put("format", "byte");
          return Unit.unit;
        }
        if (isOptional && maybeEntityPropertyDefaultValueKey.isDefinedAnd(ENTITY_PROPERTY_FOR_NULLABLE_ARRAY::contains)) {
          schema.put("x-extra-annotation", "@Nullable");
          schema.put("x-custom-default-value", "null");
        }
        schema.put("type", "array");
        schema.put("items", visitJavaApiType(arrayType.getApiValueType(), allExpectedJavaTypes, true,
            false, maybeEntityPropertyDefaultValueKey));
        return Unit.unit;
      }

      @Override
      public Unit caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        schema.putAll(handleJavaSimpleType(simpleType, maybeEntityPropertyDefaultValueKey));
        return Unit.unit;
      }

      @Override
      public Unit caseObjectType(JavaApiType.JavaObjectType objectType) {
        if (!isField) {
          if (objectType.isAbstract() && objectType.getParent().isEmpty()) {
            schema.put("type", "object");
            if (objectType.getDiscriminatorName().isDefined()) {
              schema.put("discriminator", objectType.getDiscriminatorName().getOrThrow());
            }
            PropertiesData propertiesData = getPropertiesFromFields(objectType, allExpectedJavaTypes);
            if (!propertiesData.requiredFields().isEmpty()) {
              schema.put("required", propertiesData.requiredFields());
            }
            if (!propertiesData.optionalFields().isEmpty()) {
              schema.put("x-optional", propertiesData.optionalFields());
            }
            if (!propertiesData.propertiesSchema().isEmpty()) {
              schema.put("properties", propertiesData.propertiesSchema());
            }
          } else if (objectType.getParent().isDefined()) {
            if (objectType.getDiscriminatorValue().isDefined()) {
              schema.put("x-discriminator-value", objectType.getDiscriminatorValue().getOrThrow());
            }
            JavaApiType parentType = objectType.getParent().getOrThrow();
            if (!allExpectedJavaTypes.contains(parentType.getJavaType())) {
              throw new IllegalArgumentException(
                  Strings.format("%s: is not exposed properly for yaml generation in: ", getSimpleName(parentType),
                      getSimpleName(objectType)));
            }
            List<Map<String, Object>> yamlFields = new ArrayList<>(Arrays.asList(Map.of("$ref",
                Strings.format("#/definitions/%s",
                    objectType.getParent().getOrThrow().getEntityClass().getSimpleName())), new LinkedHashMap<>() {{
              PropertiesData propertiesData = getPropertiesFromFields(objectType, allExpectedJavaTypes);
              put("type", "object");
              if (!propertiesData.requiredFields().isEmpty()) {
                put("required", propertiesData.requiredFields());
              }
              if (!propertiesData.optionalFields().isEmpty()) {
                put("x-optional", propertiesData.optionalFields());
              }
              if (!propertiesData.propertiesSchema().isEmpty()) {
                put("properties", propertiesData.propertiesSchema());
              }
            }}));
            schema.put("allOf", yamlFields);

          } else {
            schema.put("type", "object");
            if (getSimpleName(objectType).equals("BooleanResponse")) {
              schema.put("x-concrete-baseclass", true);
            }
            if (getSimpleName(objectType).equals("DefaultMultipleErrorResponse")) {
              schema.put("x-parent-is-map", true);
              schema.put("additionalProperties", Map.of(
                  "type", "string"
              ));
            }
            PropertiesData propertiesData = getPropertiesFromFields(objectType, allExpectedJavaTypes);
            if (!propertiesData.requiredFields().isEmpty()) {
              schema.put("required", propertiesData.requiredFields());
            }
            if (!propertiesData.optionalFields().isEmpty()) {
              schema.put("x-optional", propertiesData.optionalFields());
            }
            if (!propertiesData.propertiesSchema().isEmpty()) {
              schema.put("properties", propertiesData.propertiesSchema());
            }
          }
        } else {
          if (!allExpectedJavaTypes.contains(objectType.getJavaType())) {
            throw new IllegalArgumentException(Strings.format("%s: is not exposed properly for yaml generation",
                objectType.getEntityClass().getSimpleName()));
          }
          schema.put("$ref", Strings.format("#/definitions/%s", objectType.getEntityClass().getSimpleName()));
        }
        return Unit.unit;
      }

      @Override
      public Unit caseOptionType(JavaApiType.JavaOptionType optionType) {
        throw new IllegalArgumentException("Option<> values are not allowed as direct parameters of json entities");
      }

      @Override
      public Unit caseCollectionType(JavaApiType.JavaCollectionType collectionType) {
        if (isOptional && maybeEntityPropertyDefaultValueKey.isDefinedAnd(ENTITY_PROPERTY_FOR_NULLABLE_ARRAY::contains)) {
          schema.put("x-extra-annotation", "@Nullable");
          schema.put("x-custom-default-value", "null");
        }
        schema.put("type", "array");
        schema.put("items", visitJavaApiType(collectionType.getApiValueType(), allExpectedJavaTypes, true, false,
            maybeEntityPropertyDefaultValueKey));
        return Unit.unit;
      }

      @Override
      public Unit caseEnumType(JavaApiType.JavaEnumType enumType) {
        if (!isField) {
          schema.put("type", "string");
          schema.put("enum", enumType.getEnumConstantNames());
        } else {
          if (!allExpectedJavaTypes.contains(enumType.getJavaType())) {
            throw new IllegalArgumentException(
                Strings.format("%s: is not exposed properly for yaml generation", getSimpleName(enumType)));
          }
          schema.put("$ref", Strings.format("#/definitions/%s", enumType.getEnumClass().getSimpleName()));
        }
        return Unit.unit;
      }
    });
    return schema;
  }

  private PropertiesData getPropertiesFromFields(
      JavaApiType.JavaObjectType objectType, Set<Type> allExpectedJavaTypes) {
    Map<String, Object> schema = new LinkedHashMap<>();
    List<String> optionalFields = new LinkedList<>();
    List<String> requiredFields = new LinkedList<>();

    objectType.getFields().forEach(field -> {
      if (isIdOfAbstractHibernateEntity(field.getJavaType())) {
        throw new IllegalArgumentException(Strings.format("Id<? extends AbstractHibernateEntity> cannot be used " +
                "in FRONTEND TwoLattes entities. Field: %s, Two Lattes Entity: %s", field.getApiName(),
            getSimpleName(objectType)));
      }
      if (field.isOptional()) {
        optionalFields.add(field.getApiName());
      } else {
        requiredFields.add(field.getApiName());
      }
      schema.put(field.getApiName(), visitJavaApiType(field.getApiType(), allExpectedJavaTypes, true,
          field.isOptional(), Option.some(Strings.format("%s,%s", getSimpleName(objectType), field.getApiName()))));
    });
    return new PropertiesData(schema, optionalFields, requiredFields);
  }

  private Map<String, Object> handleJavaSimpleType(
      JavaApiType.JavaSimpleType javaSimpleType,
      Option<String> maybeEntityPropertyDefaultValueKey) {
    switch (javaSimpleType.getSerializedType()) {
      case BOOLEAN:
        return new LinkedHashMap<>() {{
          put("type", "boolean");
          maybeEntityPropertyDefaultValueKey.ifDefined(entityPropertyDefaultValueKey -> {
            if (ENTITY_PROPERTY_TO_BOOLEAN_DEFAULT_VALUE.containsKey(entityPropertyDefaultValueKey)) {
              put("default",
                  ENTITY_PROPERTY_TO_BOOLEAN_DEFAULT_VALUE.get(entityPropertyDefaultValueKey).booleanValue());
            }
          });
        }};
      case INTEGER:
        return new LinkedHashMap<>() {{
          put("type", "integer");
          if (Set.of(long.class, Long.class).contains(javaSimpleType.getJavaType())) {
            put("format", "int64");
          } else {
            put("format", "int32");
          }
        }};
      case NUMBER:
        return new LinkedHashMap<>() {{
          put("type", "number");
          if (Set.of(double.class, Double.class).contains(javaSimpleType.getJavaType())) {
            put("format", "double");
          } else if (Set.of(float.class, Float.class).contains(javaSimpleType.getJavaType())) {
            put("format", "float");
          }
        }};
      case STRING:
        return new LinkedHashMap<>() {{
          put("type", "string");
          if (javaSimpleType.getJavaType().equals(DateTime.class)) {
            put("format", "date-time");
          }
          if (javaSimpleType.getJavaType().equals(LocalDate.class)) {
            put("format", "date");
          }
        }};
      case ANY_JSON_OBJECT:
        throw new IllegalArgumentException("ANY_JSON_OBJECT not supported");
      case ANY_JSON_VALUE:
        throw new IllegalArgumentException("ANY_JSON_VALUE not supported");
      default:
        throw new IllegalArgumentException();
    }
  }

  private static boolean isIdOfAbstractHibernateEntity(Type type) {
    Class<?> rawType = TypeUtils.getRawType(type, null);
    return rawType.equals(Id.class);
  }

  private record TypesData(Set<JavaApiType> javaApiTypes, Set<Type> javaTypes) {}

  private record PropertiesData(Map<String, Object> propertiesSchema, List<String> optionalFields,
                                List<String> requiredFields) {}

}
