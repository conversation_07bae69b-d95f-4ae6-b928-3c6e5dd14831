package com.kaching.platform.zk.greendotavailability;

import static com.kaching.platform.common.logging.Log.getLog;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static com.wealthfront.util.time.DateTimeZones.toLocalDate;
import static java.util.stream.Collectors.joining;

import java.io.IOException;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ContiguousSet;
import com.google.common.collect.Range;
import com.google.common.collect.RangeSet;
import com.google.common.collect.TreeRangeSet;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.kaching.Author;
import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.mattermost.HipChatMention;
import com.kaching.platform.mattermost.MattermostPublisher;
import com.kaching.platform.mattermost.OnCallHipChatMentioner;
import com.kaching.platform.tinykv.TinyKvReader;
import com.kaching.util.collections.DiscreteDomains;
import com.wealthfront.brokerage.funding.GreenDotScheduledDowntimeStore;
import com.wealthfront.brokerage.funding.GreenDotScheduledDowntimeStore.Key;
import com.wealthfront.brokerage.funding.GreenDotScheduledDowntimeStore.StoreValue;

@Singleton
public class ZkGreenDotAvailabilityProviderImpl implements GreenDotAvailabilityProvider {

  private static final Log log = getLog(ZkGreenDotAvailabilityProviderImpl.class);

  @Inject Provider<DateTime> clock;
  @Inject ZkGreenDotAvailabilityDataSync availabilityDataSync;
  @Inject MattermostPublisher mattermostPublisher;
  @Inject OnCallHipChatMentioner hipChatMentioner;
  @Inject TinyKvReader<GreenDotScheduledDowntimeStore, Key, StoreValue> greenDotAvailabilityStoreReader;

  @Override
  public boolean getAvailability(GreenDotAvailabilityGroup group, DataEnvironment dataEnvironment) {
    if (dataEnvironment.isFake()) {
      return true;
    }
    ZkGreenDotAvailabilityState state = availabilityDataSync.get();

    DateTime now = clock.get();
    boolean isDowntime = greenDotAvailabilityStoreReader.get(new Key(group, toLocalDate(now, ET)))
        .transform(value ->
            value.availiabilityRangeSetForDay().contains(now.toLocalTime())
        )
        .getOrElse(false);
    if (isDowntime) {
      return false;
    }

    return state == null || state.isAvailable(group);
  }

  @Override
  public void setAvailability(boolean availability, Set<Author> authors) {
    if (Arrays.stream(GreenDotAvailabilityGroup.values()).anyMatch(
        group -> availability != getAvailability(group, DataEnvironment.REAL))) {
      String description = Strings.format("Green Dot availability set to %s at %s", availability,
          clock.get().toString("YYYY-MM-dd HH:mm:ss"));
      log.info(description);
      mattermostPublisher.publish("ops", this.getClass().getSimpleName(), getMattermostMessage(description, authors));
    }

    Map<GreenDotAvailabilityGroup, Boolean> availabilityMap = Arrays.stream(GreenDotAvailabilityGroup.values())
        .collect(Collectors.toMap(group -> group, group -> availability));
    try {
      availabilityDataSync.set(new ZkGreenDotAvailabilityState(availability, availabilityMap));
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public void setAvailabilityForGroup(GreenDotAvailabilityGroup group, boolean availability, Set<Author> authors) {
    if (availability != getAvailability(group, DataEnvironment.REAL)) {
      String description = Strings.format("Green Dot availability for %s group set to %s at %s",
          group, availability, clock.get().toString("YYYY-MM-dd HH:mm:ss"));
      log.info(description);
      mattermostPublisher.publish("ops", this.getClass().getSimpleName(), getMattermostMessage(description, authors));
    }
    Map<GreenDotAvailabilityGroup, Boolean> availabilityMap = availabilityDataSync.get().getAvailability();
    availabilityMap.put(group, availability);
    try {
      availabilityDataSync.set(new ZkGreenDotAvailabilityState(availability, availabilityMap));
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public boolean overlapsWithDowntime(GreenDotAvailabilityGroup group, Range<DateTime> timeRange) {
    Range<LocalDate> dateRange = Range.closed(
        toLocalDate(timeRange.lowerEndpoint(), ET),
        toLocalDate(timeRange.upperEndpoint(), ET)
    );
    Set<LocalDate> dates = ContiguousSet.create(dateRange, DiscreteDomains.localDates());
    RangeSet<DateTime> outageRangeSet = collectEntries(greenDotAvailabilityStoreReader
        .getAll(dates.stream().map(date -> new Key(group, date)).toList())
        .entrySet()
        .stream());
    return outageRangeSet.intersects(timeRange);
  }

  @VisibleForTesting
  String getMattermostMessage(String description, Set<Author> authors) {
    return Strings.format("Green Dot Operational Status Changed: %s\n%s",
        description,
        authors.stream()
            .map(hipChatMentioner::mentionCurrentOnCallFor)
            .filter(com.kaching.platform.common.Option::isDefined)
            .map(com.kaching.platform.common.Option::getOrThrow)
            .map(HipChatMention::toMentionString)
            .collect(joining(" ")));
  }

  private RangeSet<DateTime> collectEntries(Stream<Map.Entry<Key, StoreValue>> entryStream) {
    return TreeRangeSet.create(entryStream
        .flatMap(entry -> entry.getValue()
            .availiabilityRangeSetForDay()
            .asDescendingSetOfRanges()
            .stream()
            .map(range -> Range.closed(
                entry.getKey().date().toLocalDateTime(range.lowerEndpoint()).toDateTime(ET),
                entry.getKey().date().toLocalDateTime(range.upperEndpoint()).toDateTime(ET)
            )))
        .toList());
  }

}
