package com.kaching.platform.zk.experiment;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.util.collections.InvariantMaps.newHashMap;
import static java.util.Map.Entry;
import static java.util.concurrent.locks.ReentrantReadWriteLock.ReadLock;
import static java.util.concurrent.locks.ReentrantReadWriteLock.WriteLock;

import java.security.SecureRandom;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.I0Itec.zkclient.ZkClient;
import org.I0Itec.zkclient.exception.ZkNodeExistsException;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.zk.ZkExperimentSync;
import com.kaching.util.collections.InvariantMap;
import com.kaching.util.io.UncheckedIo;
import com.twolattes.json.EntityMarshaller;

@Singleton
public class ZkExperimentManager {

  private static final EntityMarshaller<ExperimentStatus> MARSHALLER = createEntityMarshaller(ExperimentStatus.class);
  private static final String ROOT = "/experiment";
  private static final Pattern ROOT_PATTERN = Pattern.compile(String.format("%s/(.+)", ROOT));
  private static final Log log = getLog(ZkExperimentManager.class);

  ReentrantReadWriteLock syncsLock = new ReentrantReadWriteLock();
  InvariantMap<ExperimentName, ZkExperimentSync> syncs = newHashMap();
  ReentrantReadWriteLock experimentsLock = new ReentrantReadWriteLock();
  InvariantMap<ExperimentName, ExperimentStatus> experiments = newHashMap();

  @Inject ZkClient client;
  @Inject SecureRandom random;
  @Inject StackTraceMonitor monitor;

  public void set(ExperimentStatus status) {
    if (status == null) {
      return;
    }
    UncheckedIo.run(() -> getOrCreateSynchronizer(status.getExperiment()).set(status));
  }

  public ExperimentStatus get(ExperimentName experiment) {
    getOrCreateSynchronizer(experiment);
    ExperimentStatus status = null;
    ReadLock lock = experimentsLock.readLock();
    lock.lock();
    try {
      status = experiments.get(experiment);
    } finally {
      lock.unlock();
    }
    if (status == null) {
      status = new UnknownExperiment(experiment);
    }
    return status;
  }

  public void update(String path, ExperimentStatus status) {
    WriteLock lock = experimentsLock.writeLock();
    lock.lock();
    try {
      if (status != null) {
        log.info("Updated experiment %s: %s", status.getExperiment(), MARSHALLER.marshall(status).toString());
        experiments.put(status.getExperiment(), status);
      } else {
        ExperimentName expermentName = parseExperiment(path);
        log.info("experiment %s is null, so removing experiment.", expermentName);
        experiments.remove(expermentName);
      }
    } finally {
      lock.unlock();
    }
  }

  @VisibleForTesting
  ZkExperimentSync getOrCreateSynchronizer(ExperimentName experiment) {
    // Only lock on reading hoping that we've already loaded it.
    ReadLock readLock = syncsLock.readLock();
    readLock.lock();
    try {
      ZkExperimentSync sync = syncs.get(experiment);
      if (sync != null) {
        return sync;
      }
    } finally {
      readLock.unlock();
    }

    // Lock on writing if there's a good chance we haven't loaded it.
    WriteLock writeLock = syncsLock.writeLock();
    writeLock.lock();
    try {
      ZkExperimentSync sync = syncs.get(experiment);
      // This is fast if it already exists, and much slower if it does not.
      if (sync == null) {
        ensurePersistentNode(experiment);
        sync = createZkExperienceSync(experimentPath(experiment));
        syncs.put(experiment, sync);
        sync.start();
      }
      return sync;
    } finally {
      writeLock.unlock();
    }
  }

  @VisibleForTesting
  ZkExperimentSync createZkExperienceSync(String path) {
    return new ZkExperimentSync(path, client, MARSHALLER, this);
  }

  @VisibleForTesting
  boolean ensurePersistentNode(ExperimentName experiment) {
    try {
      if (!client.exists(ROOT)) {
        client.createPersistent(ROOT);
      }
    } catch (ZkNodeExistsException e) {
      log.info(e.getMessage());
    }
    try {
      if (!client.exists(experimentPath(experiment))) {
        client.createPersistent(experimentPath(experiment));
        return true;
      }
    } catch (ZkNodeExistsException e) {
      log.info(e.getMessage());
    }
    return false;
  }

  static ExperimentName parseExperiment(String path) {
    Matcher match = ROOT_PATTERN.matcher(path);
    if (match.matches()) {
      return new ExperimentName(match.group(1));
    }
    throw new IllegalArgumentException(Strings.format("Path '%s' could not be parsed.", path));
  }

  static String experimentPath(ExperimentName experiment) {
    return ROOT + "/" + experiment.getId().toLowerCase();
  }

  public String sample(ExperimentName experimentName) {
    ExperimentStatus experiment = get(experimentName);
    if (experiment.isUnknown()) {
      monitor
          .add(new IllegalStateException(String.format("Experiment '%s' is unknown, using control.", experimentName)));
    }
    double accumulated = 0;
    double choice = getRandom();
    String variantName = ExperimentStatus.CONTROL;
    for (Entry<String, Double> variant : experiment.getOrderedCachedWeights()) {
      accumulated += variant.getValue();
      variantName = variant.getKey();
      if (choice < accumulated) {
        return variantName;
      }
    }
    return variantName;
  }

  public boolean launched(ExperimentName experimentName) {
    ExperimentStatus experiment = get(experimentName);
    if (experiment.isUnknown()) {
      monitor
          .add(new IllegalStateException(String.format("Experiment '%s' is unknown, using control.", experimentName)));
    }
    return experiment.isLaunched();
  }

  @VisibleForTesting
  double getRandom() {
    return random.nextDouble();
  }

}
