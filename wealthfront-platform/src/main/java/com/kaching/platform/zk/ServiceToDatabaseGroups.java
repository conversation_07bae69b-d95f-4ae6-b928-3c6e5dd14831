package com.kaching.platform.zk;

import static com.kaching.platform.discovery.ServiceAllocation.DatabaseGroup.ESP_MASTER;
import static com.kaching.platform.discovery.ServiceAllocation.DatabaseGroup.TRADING_MASTER;
import static com.kaching.platform.discovery.ServiceAllocation.DatabaseGroup.TRADING_READONLY;
import static com.kaching.platform.discovery.ServiceAllocation.DatabaseGroup.USER_MASTER;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.kaching.platform.discovery.ServiceAllocation;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;

public class ServiceToDatabaseGroups {

  private static final Map<Class<? extends ServiceKind>, List<ServiceAllocation.DatabaseGroup>> MAP =
      ImmutableMap.<Class<? extends ServiceKind>, List<ServiceAllocation.DatabaseGroup>>builder()
          .put(KachingServices.CONTACTS.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.FEED.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.HTF.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.MD.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.LINK.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.BLINK.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.QT.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.SE.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.UM.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.BUM.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.NLSNY.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.LEND.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.LENDW.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.BLEND.class, ImmutableList.of(USER_MASTER))
          .put(KachingServices.FLEND.class, ImmutableList.of(USER_MASTER))

          .put(KachingServices.ASC.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.BANK.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.BANKW.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.BBANK.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.BI.class, ImmutableList.of(TRADING_MASTER, TRADING_READONLY))
          .put(KachingServices.BIW.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.ROBI.class, ImmutableList.of(TRADING_READONLY))
          .put(KachingServices.BLT.class, ImmutableList.of(TRADING_MASTER, TRADING_READONLY))
          .put(KachingServices.CASH.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.CBUS.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.CDEL.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.CLEAR.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.FAKE.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.FAKETWEB.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.FAKEX.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.FOPS.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.GTS.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.ICG.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.ICGTEST.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.KCG.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.OR.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.TRACE.class, ImmutableList.of(TRADING_MASTER))
          .put(KachingServices.TWEB.class, ImmutableList.of(TRADING_MASTER))

          .put(KachingServices.BACKM.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.DM.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.DMW.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.DOPE.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.DWI.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.ESP.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.ETL.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.FBANK.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.IKQ.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.IM.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.IRIS.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.MMR.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.RISK.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.RS.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.SAND.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.SRS.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.TAOS.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.TAX.class, ImmutableList.of(ESP_MASTER))
          .put(KachingServices.WORM.class, ImmutableList.of(ESP_MASTER))
          .build();

  public static List<ServiceAllocation.DatabaseGroup> getDatabaseGroups(Class<? extends ServiceKind> service) {
    return MAP.getOrDefault(service, Collections.emptyList());
  }

}
