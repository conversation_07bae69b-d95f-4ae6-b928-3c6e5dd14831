package com.kaching.platform.zk;

import static com.kaching.platform.discovery.Status.FAILED;
import static com.kaching.platform.discovery.Status.INIT;
import static com.kaching.platform.zk.ZkAnnounceModule.newZkAnnounceModule;
import static com.kaching.platform.zk.ZkModule.newZkModule;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.kaching.KachingSharedOptions;
import com.kaching.platform.common.Option;
import com.kaching.platform.components.Component;
import com.kaching.platform.components.DefaultStartupResultVisitor;
import com.kaching.platform.components.EmptyStartupListener;
import com.kaching.platform.components.StartupResult;
import com.kaching.platform.discovery.LocalAnnouncement;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.zk.ZkComponent.Listener;
import com.kaching.platform.zk.ZkComponent.Module;

@Component(
    cmdOptions = ZkOptions.class,
    modules = {
        Module.class
    },
    startupListener = Listener.class,
    dependsOn = {
        KachingSharedOptions.class
    }
)
public class ZkComponent {

  /**
   * Module.
   */
  static class Module extends AbstractModule {

    private final Class<ServiceKind> kind;
    private final ZkOptions options;
    private final KachingSharedOptions sharedOptions;

    @Inject
    Module(Class<ServiceKind> kind, ZkOptions options, KachingSharedOptions sharedOptions) {
      this.kind = kind;
      this.options = options;
      this.sharedOptions = sharedOptions;
    }

    @Override
    protected void configure() {
      install(newZkModule(options));
      install(newZkAnnounceModule(kind, options, sharedOptions));
    }

  }

  /**
   * Listener in charge of interacting with ZK as server is starting up.
   */
  static class Listener extends EmptyStartupListener {

    @Inject ZkLifecycle zkLifecycle;
    @Inject LocalAnnouncement localAnnouncement;

    @Override
    public void onPreQueryEngineStart() throws Exception {
      zkLifecycle.start();
      localAnnouncement.setStatus(INIT);
      localAnnouncement.announce();
    }

    @Override
    public void onSelfTestsCompletion(StartupResult result) {
      Option<String> maybeFailureReason = result.visit(new DefaultStartupResultVisitor<>(Option.none()) {
        @Override
        public Option<String> caseFailed(String reason) {
          return Option.some(reason);
        }

        @Override
        public Option<String> caseDegraded(String reason) {
          return Option.some(reason);
        }
      });
      localAnnouncement.setStatus(result.getStatus(), maybeFailureReason);
    }

    @Override
    public void onAbort(Exception e, Option<String> maybeFailureReason) {
      localAnnouncement.setStatus(FAILED, maybeFailureReason);
    }

  }

}
