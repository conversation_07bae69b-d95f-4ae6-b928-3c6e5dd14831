package com.kaching.platform.queryengine;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.kaching.platform.common.logging.Log.getLog;
import static java.util.concurrent.TimeUnit.MINUTES;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.SortedSet;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Stream;

import org.joda.time.DateTime;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.MapMaker;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.google.inject.util.Providers;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.monitoring.RollingStatistics;
import com.kaching.platform.queryengine.ExceptionClassifier.QueryResultClassification.QueryResultClassificationVisitor;
import com.kaching.platform.queryengine.exceptions.ConcurrentQueryExecutionException;
import com.kaching.util.functional.Either;

@Singleton
@SuppressWarnings("rawtypes")
public class QueryRuntimeMonitor {

  private static final Log log = getLog(QueryRuntimeMonitor.class);

  private static final long SLA_CHECK_DELAY = 200;
  private static final int LOGGING_THRESHOLD = 10;

  private final ConcurrentMap<String, RollingStatistics> statistics;
  private final ConcurrentMap<ServiceRevision, ConcurrentMap<String, RollingStatistics>> downstreamStatistics;
  @VisibleForTesting final Map<QuerySession, QuerySession> runningQueries;
  final Map<QuerySession, QuerySession> streamingQueries;
  final Map<Trace, Integer> queriesPerTrace;
  private final Provider<DateTime> clock;
  private final long startupTime;
  private final QueryEngineStatus status;
  private final ExceptionClassifier classifier;

  @VisibleForTesting
  public QueryRuntimeMonitor() {
    runningQueries = new HashMap<>();
    streamingQueries = new HashMap<>();
    queriesPerTrace = new HashMap<>();
    status = null;
    clock = Providers.of(new DateTime());
    startupTime = 0;
    statistics = new MapMaker().makeMap();
    downstreamStatistics = new MapMaker().makeMap();
    classifier = new DefaultExceptionClassifier();
  }

  @Inject
  public QueryRuntimeMonitor(
      Provider<DateTime> clock,
      @Named("daemon") Timer timer,
      QueryEngineStatus status,
      ExceptionClassifier classifier) {
    this.statistics = new MapMaker().makeMap();
    this.downstreamStatistics = new MapMaker().makeMap();
    this.runningQueries = CacheBuilder.newBuilder().weakKeys().softValues().<QuerySession, QuerySession>build().asMap();
    this.streamingQueries =
        CacheBuilder.newBuilder().weakKeys().softValues().<QuerySession, QuerySession>build().asMap();
    this.queriesPerTrace = new ConcurrentHashMap<>();
    this.clock = clock;
    this.status = status;
    this.classifier = classifier;
    startupTime = clock.get().getMillis();
    timer.schedule(new SlaGuardian(this), SLA_CHECK_DELAY);
  }

  private static class SlaGuardian extends TimerTask {

    private final QueryRuntimeMonitor queryRuntimeMonitor;

    SlaGuardian(QueryRuntimeMonitor queryRuntimeMonitor) {
      this.queryRuntimeMonitor = queryRuntimeMonitor;
    }

    @Override
    public void run() {
      queryRuntimeMonitor.checkForTimeouts(queryRuntimeMonitor);
    }

  }

  @VisibleForTesting
  void checkForTimeouts(QueryRuntimeMonitor queryRuntimeMonitor) {
    Set<QuerySession> queries = queryRuntimeMonitor.runningQueries();
    if (null == queries || queries.isEmpty()) {
      return;
    }
    for (QuerySession entry : queries) {
      if (entry.running() && !entry.isSlaCompliant()) {
        killSession(entry);
      }
    }
  }

  @VisibleForTesting
  void killSession(QuerySession session) {
    log.error("trying to kill query %s after %s nano",
        session.getQuery(), session.getStartupTimeNano());
    session.attemptKill();
  }

  public void record(Query<?> query, long executionTime, Throwable t) {
    RollingStatistics stats = getQueryStats(query);
    status.processedQueries.incrementAndGet();

    classifier.classifyResult(query, t).visit(new QueryResultClassificationVisitor<Unit>() {
      @Override
      public Unit caseSuccess() {
        stats.recordSuccessfulQuery((int) executionTime);
        return Unit.unit;
      }

      @Override
      public Unit caseFailure() {
        if (shouldRecordExceptionStats(t)) {
          stats.recordFailedQuery();
          status.failedQueries.incrementAndGet();
        }
        return Unit.unit;
      }

      @Override
      public Unit caseInvalid() {
        stats.recordInvalidQuery();
        status.invalidQueries.incrementAndGet();
        return Unit.unit;
      }
    });
  }

  public void record(String normalizedPath, long executionTime, int statusCode) {
    RollingStatistics stats = getEndpointStats(normalizedPath);
    if (statusCode >= 200 && statusCode < 300) {
      stats.recordSuccessfulQuery((int) executionTime);
    } else if (statusCode >= 400 && statusCode < 500) {
      stats.recordInvalidQuery();
    } else if (statusCode >= 500) {
      stats.recordFailedQuery();
    }
  }

  private RollingStatistics getEndpointStats(String normalizedPath) {
    return statistics.computeIfAbsent(normalizedPath, path -> new RollingStatistics(5, MINUTES, clock));
  }

  public void recordDownstream(ServiceRevision serviceRevision, Query<?> query, long executionTime, Throwable t) {
    RollingStatistics stats = getQueryStatsFromDownstream(serviceRevision, query);
    classifier.classifyResult(query, t).visit(new QueryResultClassificationVisitor<Unit>() {
      @Override
      public Unit caseSuccess() {
        stats.recordSuccessfulQuery((int) executionTime);
        return Unit.unit;
      }

      @Override
      public Unit caseFailure() {
        if (shouldRecordExceptionStats(t)) {
          stats.recordFailedQuery();
        }
        return Unit.unit;
      }

      @Override
      public Unit caseInvalid() {
        stats.recordInvalidQuery();
        return Unit.unit;
      }
    });
  }

  @VisibleForTesting
  boolean shouldRecordExceptionStats(Throwable throwable) {
    return !(throwable instanceof ConcurrentQueryExecutionException);
  }

  public SortedSet<Entry<String, Double>> getExecutionTimes() {
    SortedSet<Entry<String, Double>> set = Sets.newTreeSet((o1, o2) -> o2.getValue().compareTo(o1.getValue()));
    for (Entry<String, RollingStatistics> e : statistics()) {
      RollingStatistics stats = e.getValue();
      set.add(Maps.immutableEntry(e.getKey(), stats.rollingAverage()));
    }
    return set;
  }

  public Collection<Entry<String, RollingStatistics>> statistics() {
    return statistics.entrySet();
  }

  public Collection<Entry<ServiceRevision, ConcurrentMap<String, RollingStatistics>>> downstreamStatistics() {
    return downstreamStatistics.entrySet();
  }

  public QuerySession queryStart(Query<?> query, Sla sla, Thread queryMainThread, Trace trace) {
    QuerySession session;
    synchronized (runningQueries) {
      session = new QuerySession(query, sla, queryMainThread, trace);
      runningQueries.put(session, session);
    }
    recordNumberOfRunningQueries();
    queriesPerTrace.compute(session.getTrace(), (Trace t, Integer oldValue) -> {
      if (oldValue == null) {
        return 1;
      } else {
        int newValue = oldValue + 1;
        if (newValue > LOGGING_THRESHOLD) {
          log.warn("%s queries in progress for trace %s", newValue, session.getTrace().getToken());
        }
        return oldValue + 1;
      }
    });
    return session;
  }

  void recordNumberOfRunningQueries() {
    if (status != null) {
      status.recordNumberOfRunningQueries(runningQueries.size());
    }
  }

  public Pair<Boolean, QuerySession> streamingStart(Query<?> query, Sla sla, Thread queryMainThread, Trace trace) {
    QuerySession session;
    synchronized (streamingQueries) {
      session = new QuerySession(query, sla, queryMainThread, trace);
      if (streamingQueries.size() > 10) {
        return Pair.of(false, session);
      }
      streamingQueries.put(session, session);
    }
    return Pair.of(true, session);
  }

  public void queryStopSuccessfully(QuerySession session, Object result) {
    queryStop(session).ifDefined(querySession -> querySession.stoppedSuccessfully(result));
  }

  public void queryStopExceptionally(QuerySession session, Throwable e) {
    queryStop(session).ifDefined(querySession -> querySession.stoppedExceptionally(e));
  }

  private Option<QuerySession> queryStop(QuerySession session) {
    QuerySession removed = runningQueries.remove(session);
    recordNumberOfRunningQueries();
    queriesPerTrace.compute(session.getTrace(), (Trace t, Integer oldValue) -> {
      if (oldValue == null || oldValue == 1) {
        return null;
      } else {
        return oldValue - 1;
      }
    });
    return Option.of(removed);
  }

  public void streamingStopSuccessfully(QuerySession session, Object result) {
    streamingStop(session).ifDefined(querySession -> querySession.stoppedSuccessfully(result));
  }

  public void streamingStopExceptionally(QuerySession session, Throwable e) {
    streamingStop(session).ifDefined(querySession -> querySession.stoppedExceptionally(e));
  }

  public Option<QuerySession> streamingStop(QuerySession session) {
    synchronized (streamingQueries) {
      return Option.of(streamingQueries.remove(session));
    }
  }

  /**
   * @return a clone of the running query map
   */
  public Set<QuerySession> runningQueries() {
    return new HashSet<>(runningQueries.values());
  }

  public Set<QuerySession> streamingQueries() {
    return new HashSet<>(streamingQueries.values());
  }

  public Set<Class<? extends Query>> allRunningQueryClasses() {
    return Stream.of(runningQueries.values(), streamingQueries.values())
        .flatMap(Collection::stream)
        .filter(QuerySession::running)
        .map(session -> session.getQuery().getClass())
        .collect(toSet());
  }

  public List<QuerySession> getQuerySessions(String queryNameOrSessionId) {
    Either<String, QuerySessionId> eitherQueryNameOrSessionId;
    try {
      eitherQueryNameOrSessionId = Either.right(new QuerySessionId(Long.parseLong(queryNameOrSessionId)));
    } catch (NumberFormatException e) {
      eitherQueryNameOrSessionId = Either.left(queryNameOrSessionId);
    }
    Either<String, QuerySessionId> identifier = eitherQueryNameOrSessionId;

    Collection<QuerySession> querySessions = runningQueries.values();
    querySessions.addAll(streamingQueries.values());

    return querySessions.stream()
        .filter(querySession -> identifier.visit(
            (String name) ->
                querySession.getQuery().getClass().getSimpleName().toLowerCase().contains(name.toLowerCase()),
            (QuerySessionId sessionId) -> querySession.getQuerySessionId().equals(sessionId)))
        .collect(toList());
  }

  public Option<QuerySession> getQuerySessionBySessionId(QuerySessionId querySessionId) {
    Collection<QuerySession> querySessions = runningQueries.values();
    querySessions.addAll(streamingQueries.values());

    List<QuerySession> querySession = querySessions.stream()
        .filter(qs -> qs.getQuerySessionId().equals(querySessionId))
        .collect(toList());
    return querySession.size() == 1 ? Option.some(getOnlyElement(querySession)) : Option.none();
  }

  private RollingStatistics getQueryStats(Query query) {
    RollingStatistics newStats = new RollingStatistics(5, MINUTES, clock);
    RollingStatistics stats = statistics.putIfAbsent(query.getClass().getSimpleName(), newStats);
    return stats == null ? newStats : stats;
  }

  private RollingStatistics getQueryStatsFromDownstream(ServiceRevision serviceRevision, Query query) {
    ConcurrentMap<String, RollingStatistics> newMap = new MapMaker().makeMap();
    ConcurrentMap<String, RollingStatistics> downstreamStats =
        downstreamStatistics.putIfAbsent(serviceRevision, newMap);
    if (downstreamStats == null) {
      downstreamStats = newMap;
    }
    RollingStatistics newStats = new RollingStatistics(5, MINUTES, clock);
    RollingStatistics stats = downstreamStats.putIfAbsent(query.getClass().getSimpleName(), newStats);
    return stats == null ? newStats : stats;
  }

  public long getUptime() {
    return clock.get().getMillis() - startupTime;
  }

  public long getStartupTime() {
    return startupTime;
  }

  public void clearStatistics() {
    statistics.clear();
    downstreamStatistics.clear();
  }

  @VisibleForTesting
  Map<String, RollingStatistics> getStatistics() {
    return statistics;
  }

  @VisibleForTesting
  ConcurrentMap<ServiceRevision, ConcurrentMap<String, RollingStatistics>> getDownstreamStatistics() {
    return downstreamStatistics;
  }

  @VisibleForTesting
  Map<Trace, Integer> getQueriesPerTrace() {
    return queriesPerTrace;
  }

}
