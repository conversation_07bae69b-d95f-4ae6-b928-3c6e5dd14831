package com.kaching.platform.queryengine.client;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.OptionVisitor;
import com.kaching.platform.common.Pair;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.client.SmartClient.SmartQueryResult;
import com.kaching.platform.queryengine.progress.ProgressMonitor;
import com.kaching.util.concurrent.ConcurrentExecutor;
import com.kaching.util.functional.Result;

@Singleton
public class ConcurrentQueryExecutorImpl implements ConcurrentQueryExecutor {

  @Inject ConcurrentExecutor concurrentExecutor;

  @Deprecated
  @Override
  public <Q extends Query<T>, T> CompletableFuture<List<SmartQueryResult<Q, T>>> invokeQueriesAsync(
      Function<Q, CompletableFuture<T>> invokeQuery, Collection<? extends Q> queries, int maxConcurrentQueries,
      Option<Pair<ProgressMonitor, String>> maybeProgressMonitorAndStageName) {
    return maybeProgressMonitorAndStageName.visit(
        new OptionVisitor<Pair<ProgressMonitor, String>, CompletableFuture<List<Pair<Q, Result<T>>>>>() {
          @Override
          public CompletableFuture<List<Pair<Q, Result<T>>>> caseNone() {
            return concurrentExecutor.executeConcurrently(invokeQuery, queries, maxConcurrentQueries);
          }

          @Override
          public CompletableFuture<List<Pair<Q, Result<T>>>> caseSome(
              Pair<ProgressMonitor, String> progressMonitorStringPair) {
            return concurrentExecutor.executeConcurrently(invokeQuery, queries, maxConcurrentQueries,
                progressMonitorStringPair.getRight());
          }
        })
        .thenApply(results -> results.stream()
            .map(result -> result.getRight().isSuccess() ?
                SmartQueryResult.success(result.getLeft(), result.getRight().getOrThrow()) :
                SmartQueryResult.error(result.getLeft(), result.getRight().getException().getOrThrow()))
            .collect(Collectors.toList()));
  }

  @Override
  public <Q extends Query<T>, T> CompletableFuture<List<SmartQueryResult<Q, T>>> invokeQueriesAsync(
      Function<Q, CompletableFuture<T>> invokeQuery, Collection<? extends Q> queries, int maxConcurrentQueries) {
    return concurrentExecutor.executeConcurrently(invokeQuery, queries, maxConcurrentQueries)
        .thenApply(results -> results.stream()
            .map(result -> result.getRight().isSuccess() ?
                SmartQueryResult.success(result.getLeft(), result.getRight().getOrThrow()) :
                SmartQueryResult.error(result.getLeft(), result.getRight().getException().getOrThrow()))
            .collect(Collectors.toList()));
  }

  @Override
  public <Q extends Query<T>, T> CompletableFuture<List<SmartQueryResult<Q, T>>> invokeQueriesAsync(
      Function<Q, CompletableFuture<T>> invokeQuery, Collection<? extends Q> queries, int maxConcurrentQueries,
      String progressMonitorStageName) {
    return concurrentExecutor.executeConcurrently(invokeQuery, queries, maxConcurrentQueries,
                    progressMonitorStageName)
        .thenApply(results -> results.stream()
            .map(result -> result.getRight().isSuccess() ?
                SmartQueryResult.success(result.getLeft(), result.getRight().getOrThrow()) :
                SmartQueryResult.error(result.getLeft(), result.getRight().getException().getOrThrow()))
            .collect(Collectors.toList()));
  }

}
