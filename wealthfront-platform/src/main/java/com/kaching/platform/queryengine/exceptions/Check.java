package com.kaching.platform.queryengine.exceptions;

import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;

public class Check {

  public static <T> T shouldExist(T object, String message) {
    if (object == null) {
      throw new NotFoundException(message);
    }
    return object;
  }

  public static <T extends HibernateEntity> T shouldExist(T object, Class<? extends T> clazz, Id<T> id) {
    if (object == null) {
      throw new NotFoundException(clazz, id);
    }
    return object;
  }

  public static <T extends HibernateEntity> T shouldExist(
      T object, Class<? extends T> clazz, String name, Object value) {
    if (object == null) {
      throw new NotFoundException(clazz, name, value);
    }
    return object;
  }

}
