package com.kaching.platform.queryengine.postprocessors;

import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

class MarshallingPostProcessor<T>
    extends AbstractMarshallingPostProcessor<T, T> {

  MarshallingPostProcessor(<PERSON>er<T> marshaller) {
    super(marshaller);
  }

  @Override
  protected Json.Value marshall(Marshaller<T> marshaller, T queryResult) {
    return marshaller.marshall(queryResult);
  }

}
