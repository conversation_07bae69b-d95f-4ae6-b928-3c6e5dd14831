package com.kaching.platform.queryengine;

import static com.kaching.platform.queryengine.ExceptionClassifier.QueryResultClassification.FAILURE;
import static com.kaching.platform.queryengine.ExceptionClassifier.QueryResultClassification.INVALID;
import static com.kaching.platform.queryengine.ExceptionClassifier.QueryResultClassification.SUCCESS;

import org.hibernate.ObjectNotFoundException;

import com.kaching.platform.queryengine.exceptions.IkqQueryInvocationException;
import com.kaching.platform.queryengine.exceptions.QueryClientException;

public class DefaultExceptionClassifier implements ExceptionClassifier {

  @Override
  public boolean isFailure(Query<?> query, Throwable t) {
    return isFailure(classifyResult(query, t));
  }

  @Override
  public boolean isFailure(QueryResultClassification classification) {
    return classification == FAILURE;
  }

  @Override
  public QueryResultClassification classifyResult(Query<?> query, Throwable t) {
    if (t == null) {
      return SUCCESS;
    } else if (t instanceof QueryClientException) {
      return INVALID;
    } else if (t instanceof ObjectNotFoundException) {
      return INVALID;
    } else if (t instanceof IkqQueryInvocationException) {
      return SUCCESS;
    }
    return FAILURE;
  }

}
