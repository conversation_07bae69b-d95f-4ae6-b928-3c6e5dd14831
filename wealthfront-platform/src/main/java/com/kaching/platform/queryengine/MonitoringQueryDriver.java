package com.kaching.platform.queryengine;

import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.common.logging.Log.logContextPut;
import static com.kaching.platform.common.logging.Log.logContextRemove;
import static com.kaching.platform.queryengine.QueryEngineModule.REQUEST_HEADERS_KEY;
import static java.util.Objects.nonNull;
import static javax.servlet.http.HttpServletResponse.SC_OK;

import java.util.Map;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;

/**
 * A {@link QueryDriver} that interprets the runtime execution of a query.
 */
public class MonitoringQueryDriver extends DelegatingQueryDriver {

  private static final Log log = getLog(MonitoringQueryDriver.class);
  private static final Log accessLog = getLog(MonitoringQueryDriver.class.getName() + ".access");
  private static final String IKQ_USER_AGENT = "ikq";

  private final QueryRuntimeMonitor runtimeMonitor;
  private final QueryProxies proxies;
  private final ThreadLocal<Trace> traces;

  public MonitoringQueryDriver(
      QueryDriver delegate, QueryRuntimeMonitor runtimeMonitor,
      QueryProxies proxies, ThreadLocal<Trace> traces) {
    super(delegate);
    this.runtimeMonitor = runtimeMonitor;
    this.proxies = proxies;
    this.traces = traces;
  }

  @Override
  public <T> Object execute(
      Query<T> query,
      Map<Key<?>, Object> relaxedCache,
      Provider<? extends PostProcessor> postProcessor) {
    long startTimeMillis = System.currentTimeMillis();
    Sla sla = proxies.getProxy(query.getClass()).getSla();
    QuerySession session = runtimeMonitor.queryStart(query, sla, Thread.currentThread(), traces.get());
    Throwable throwable = null;
    try {
      // delegating
      ImmutableMap<Key<?>, Object> updatedRelaxedCache = ImmutableMap.<Key<?>, Object>builder()
          .putAll(relaxedCache)
          .put(QueryEngineModule.QUERY_SESSION_ID_KEY, session.getQuerySessionId())
          .build();
      Object result = delegate.execute(query, updatedRelaxedCache, postProcessor);
      try {
        runtimeMonitor.queryStopSuccessfully(session, result);
      } catch (Exception ex) {
        log.error(ex, "could not de-register query %s", query);
      }
      return result;
    } catch (Throwable e) {
      try {
        runtimeMonitor.queryStopExceptionally(session, e);
      } catch (Exception ex) {
        log.error(ex, "could not de-register query %s", query);
      }
      throwable = e;

      if (e instanceof Error) {
        throw (Error) e;
      } else if (e instanceof RuntimeException) {
        throw (RuntimeException) e;
      } else {
        // Should never get executed!
        throw new RuntimeException(e);
      }
    } finally {
      String tag = query.getClass().getName();
      long executionTime = System.currentTimeMillis() - startTimeMillis;
      if (!isInitiatedByIkq(relaxedCache)) {
        runtimeMonitor.record(query, executionTime, throwable);
      }
      int httpStatusCode = (throwable != null && throwable instanceof Exception) ?
          DefaultExceptionHandler.statusFor((Exception) throwable) : SC_OK;
      Option<Long> maybeInjectionTime = session.getInjectionTime();

      logContextPutWrapper("start", startTimeMillis);
      logContextPutWrapper("ms", executionTime);
      logContextPutWrapper("error", throwable != null);
      logContextPutWrapper("httpStatusCode", httpStatusCode);
      maybeInjectionTime.ifDefined(injectionTime -> logContextPutWrapper("injectionTime", injectionTime));
      accessLog.info(tag);
      logContextRemoveWrapper("start");
      logContextRemoveWrapper("ms");
      logContextRemoveWrapper("error");
      logContextRemoveWrapper("httpStatusCode");
      maybeInjectionTime.ifDefined(injectionTime -> logContextRemoveWrapper("injectionTime"));
    }
  }

  @VisibleForTesting
  void logContextPutWrapper(String key, Object value) {
    logContextPut(key, value);
  }

  @VisibleForTesting
  void logContextRemoveWrapper(String key) {
    logContextRemove(key);
  }

  boolean isInitiatedByIkq(Map<Key<?>, Object> relaxedCache) {
    Map<String, String> httpRequestHeaders = (Map<String, String>) relaxedCache.get(REQUEST_HEADERS_KEY);
    return nonNull(httpRequestHeaders) && IKQ_USER_AGENT.equals(httpRequestHeaders.get(HttpHeaders.USER_AGENT));
  }

}
