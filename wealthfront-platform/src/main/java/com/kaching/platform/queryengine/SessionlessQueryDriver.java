package com.kaching.platform.queryengine;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Singleton;
import com.kaching.platform.timing.Chronograph;

@Singleton
public class SessionlessQueryDriver extends DelegatingQueryDriver {

  @Inject
  public SessionlessQueryDriver(
      Injector injector,
      QueryScope queryScope,
      StackTraceMonitor monitor,
      QueryRuntimeMonitor runtimeMonitor,
      QueryProxies proxies,
      ThreadLocal<Trace> traces,
      Chronograph chronograph,
      ExceptionClassifier classifier,
      QueryRuntimeMonitor queryRuntimeMonitor) {
    super(new CommonQueryDriver(
        injector, monitor, runtimeMonitor, proxies, traces, chronograph, classifier,
        new CommonScopingQueryDriver(injector, queryScope,
            new InjectingQueryDriver(injector)
        )
    ));
  }

}
