package com.kaching.platform.queryengine.querydiffmetatest;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.kaching.entities.BackwardsIncompatibleChangeType.CONSTRUCTOR_ARGUMENT_ADDED_LAST_ARGUMENTS_NOT_OPTIONAL;
import static com.kaching.entities.BackwardsIncompatibleChangeType.CONSTRUCTOR_ARGUMENT_ANNOTATIONS_CHANGED;
import static com.kaching.entities.BackwardsIncompatibleChangeType.CONSTRUCTOR_ARGUMENT_REMOVED;
import static com.kaching.entities.BackwardsIncompatibleChangeType.CONSTRUCTOR_ARGUMENT_TYPE_CHANGED;
import static com.kaching.entities.BackwardsIncompatibleChangeType.NO_LONGER_CLEAR_INSTANTIATING_CONSTRUCTOR;
import static com.kaching.entities.BackwardsIncompatibleChangeType.QUERY_ANNOTATIONS_CHANGED;
import static com.kaching.entities.BackwardsIncompatibleChangeType.RETURN_TYPE_CHANGED;
import static com.kaching.entities.ServiceQueryInfo.QueryInfo.buildQueryInfo;
import static java.util.Collections.emptySet;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Modifier;
import java.nio.file.FileSystem;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.google.common.collect.Streams;
import com.kaching.entities.BackwardsIncompatibleChangeType;
import com.kaching.entities.QueryDiffMetatestResult;
import com.kaching.entities.ServiceQueryInfo;
import com.kaching.entities.ServiceQueryInfo.ConstructorArgumentInfo;
import com.kaching.entities.ServiceQueryInfo.ConstructorInfo;
import com.kaching.entities.ServiceQueryInfo.QueryInfo;
import com.kaching.mq.events.PublishHeartbeat;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.QueryProxies;
import com.kaching.platform.queryengine.exceptions.QueryNotFoundException;
import com.kaching.platform.zk.ZkAnnounceModule;
import com.kaching.platform.zk.ZkModule;

public class QueryDiffMetatest {

  @VisibleForTesting
  static final String TARGET_DIR = "target/";

  @VisibleForTesting
  static final String FILE_NAME = "%s-query-diff-metatest-result.json";

  private static final String OPTIONAL_ANNOTATION = "com.kaching.platform.converters.Optional";
  private static final String INSTANTIATE_ANNOTATION = "com.kaching.platform.converters.Instantiate";
  private static final String OPTION_TYPE = "com.kaching.platform.common.Option";

  private static final Set<String> ANNOTATION_CHANGE_BADLIST = ImmutableSet.of(
      "com.kaching.platform.queryengine.admin.Administrative",
      "com.kaching.platform.queryengine.NoManualInvocation"
  );

  private static final Set<String> CONSTRUCTOR_ARGUMENT_ANNOTATION_CHANGE_BADLIST = ImmutableSet.of(
      "com.kaching.platform.queryengine.Owned"
  );

  private static final Set<String> EXCLUDE_FROM_RENAMED_OR_DELETED = ImmutableSet.<Class>builder()
      .addAll(ZkModule.QUERIES)
      .addAll(ZkAnnounceModule.QUERIES)
      .add(PublishHeartbeat.class)
      .build().stream()
      .map(Class::getSimpleName)
      .collect(Collectors.toSet());

  private static final Log log = Log.getLog(QueryDiffMetatest.class);

  public static void writeQueryBackwardsIncompatibilityFile(
      QueryProxies queryProxies,
      ServiceQueryInfoS3Client queryInfoClient,
      FileSystem fs,
      AmazonS3 s3Client) throws IOException {
    ServiceQueryInfo s3Queries = queryInfoClient.fetchQueryInfoForService(s3Client);
    saveFile(backwardsCompatibilityCheck(queryProxies, s3Queries), fs, s3Queries.getServiceName(), Option.none());
  }

  public static void writeQueryBackwardsIncompatibilityFile(
      QueryProxies queryProxies,
      ServiceQueryInfoS3Client queryInfoClient,
      FileSystem fs,
      AmazonS3 s3Client,
      String specificPath) throws IOException {
    ServiceQueryInfo s3Queries = queryInfoClient.fetchQueryInfoForService(s3Client);
    saveFile(
        backwardsCompatibilityCheck(queryProxies, s3Queries),
        fs,
        s3Queries.getServiceName(),
        Option.some(specificPath));
  }

  private static QueryDiffMetatestResult backwardsCompatibilityCheck(
      QueryProxies localQueries,
      ServiceQueryInfo s3Queries) {
    Map<String, Set<BackwardsIncompatibleChangeType>> backwardsIncompatibleChangedQueries = new HashMap<>();
    Set<String> deletedOrRenamedQueries = new HashSet<>();

    Map<String, QueryInfo> s3QueryNameToInfo = s3Queries.getQueryNameToInfo();

    localQueries.forEach(queryProxy -> {
      String queryName = queryProxy.getQueryClass().getSimpleName();
      if (!s3QueryNameToInfo.containsKey(queryName)) {
        return;
      }

      Set<BackwardsIncompatibleChangeType> backwardsIncompatibleChangeTypes =
          hasBackwardsIncompatibleChange(buildQueryInfo(queryProxy.getQueryClass()), s3QueryNameToInfo.get(queryName));

      if (!backwardsIncompatibleChangeTypes.isEmpty()) {
        backwardsIncompatibleChangedQueries.put(queryName, backwardsIncompatibleChangeTypes);
      }
    });

    s3QueryNameToInfo.keySet().forEach(queryName -> {
      try {
        localQueries.getProxy(queryName);
      } catch (QueryNotFoundException e) {
        if (!EXCLUDE_FROM_RENAMED_OR_DELETED.contains(queryName)) {
          deletedOrRenamedQueries.add(queryName);
        }
      }
    });

    return new QueryDiffMetatestResult(backwardsIncompatibleChangedQueries, deletedOrRenamedQueries);
  }

  private static void saveFile(
      QueryDiffMetatestResult fileToSave, FileSystem fs, String serviceName, Option<String> maybeSpecificPath) {
    try {
      Files.createDirectories(fs.getPath(maybeSpecificPath.getOrElse(TARGET_DIR)));
      OutputStream os = Files.newOutputStream(
          fs.getPath(maybeSpecificPath.getOrElse(TARGET_DIR) + Strings.format(FILE_NAME, serviceName)));
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.writeValue(os, fileToSave);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  @VisibleForTesting
  static Set<BackwardsIncompatibleChangeType> hasBackwardsIncompatibleChange(
      QueryInfo localQuery,
      QueryInfo s3Query) {
    Set<BackwardsIncompatibleChangeType> backwardsIncompatibleChangeTypes = new HashSet<>();

    backwardsIncompatibleChangeTypes.addAll(compareQueryConstructors(localQuery, s3Query));

    Set<String> queryAnnotationsDiff = Sets.difference(localQuery.getAnnotations(), s3Query.getAnnotations());
    if (ANNOTATION_CHANGE_BADLIST.stream().anyMatch(queryAnnotationsDiff::contains)) {
      backwardsIncompatibleChangeTypes.add(QUERY_ANNOTATIONS_CHANGED);
    }

    if (!localQuery.getReturnType().equals(s3Query.getReturnType())) {
      backwardsIncompatibleChangeTypes.add(RETURN_TYPE_CHANGED);
    }

    return backwardsIncompatibleChangeTypes;
  }

  @VisibleForTesting
  static Set<BackwardsIncompatibleChangeType> compareQueryConstructors(QueryInfo localQuery, QueryInfo s3Query) {
    if (localQuery.getConstructorInfos().isEmpty() && s3Query.getConstructorInfos().isEmpty()) {
      log.info("Query %s has no accessible constructors. No constructor comparison was done.",
          localQuery.getFullQueryName());
      return emptySet();
    }

    Set<BackwardsIncompatibleChangeType> backwardsIncompatibleChangeTypes = new HashSet<>();

    Option<ConstructorInfo> maybeLocalQueryInstantiatingConstructor = getInstantiatingConstructor(localQuery);
    Option<ConstructorInfo> maybeS3QueryInsantiatingConstructor = getInstantiatingConstructor(s3Query);

    if (maybeLocalQueryInstantiatingConstructor.isDefined() && maybeS3QueryInsantiatingConstructor.isDefined()) {
      backwardsIncompatibleChangeTypes.addAll(compareConstructorInfos(
          maybeLocalQueryInstantiatingConstructor.getOrThrow(), maybeS3QueryInsantiatingConstructor.getOrThrow()));
    } else if (maybeLocalQueryInstantiatingConstructor.isDefined() &&
        hasNoExternallyAccessibleConstructors(s3Query)) {
      if (constructorHasAnyNonOptionalArguments(maybeLocalQueryInstantiatingConstructor.getOrThrow())) {
        backwardsIncompatibleChangeTypes.add(CONSTRUCTOR_ARGUMENT_ADDED_LAST_ARGUMENTS_NOT_OPTIONAL);
      } else {
        return backwardsIncompatibleChangeTypes;
      }
    } else if (maybeLocalQueryInstantiatingConstructor.isEmpty() && maybeS3QueryInsantiatingConstructor.isDefined()) {
      backwardsIncompatibleChangeTypes.add(NO_LONGER_CLEAR_INSTANTIATING_CONSTRUCTOR);
    }

    return backwardsIncompatibleChangeTypes;
  }

  @VisibleForTesting
  static Set<BackwardsIncompatibleChangeType> compareConstructorInfos(
      ConstructorInfo localConstructor,
      ConstructorInfo s3Constructor) {
    Set<BackwardsIncompatibleChangeType> backwardsIncompatibleChangeTypes = new HashSet<>();

    int localConstructorNumArguments = localConstructor.getArgumentInfos().size();
    int s3ConstructorNumArguments = s3Constructor.getArgumentInfos().size();

    if (localConstructorNumArguments == s3ConstructorNumArguments) {
      backwardsIncompatibleChangeTypes.addAll(compareEqualCountConstructorArgumentInfos(
          localConstructor.getArgumentInfos(), s3Constructor.getArgumentInfos()));
    } else if (localConstructorNumArguments > s3ConstructorNumArguments) {
      int diff = localConstructorNumArguments - s3ConstructorNumArguments;
      for (int i = 1; i <= diff; i++) {
        ConstructorArgumentInfo argumentInfo = localConstructor.getArgumentInfos().get(localConstructorNumArguments - i);
        boolean argumentHasOptionalAnnotation = argumentInfo.getArgumentAnnotations().contains(OPTIONAL_ANNOTATION);
        boolean argumentIsOptionType = argumentInfo.getArgumentType().startsWith(OPTION_TYPE);
        if (!argumentHasOptionalAnnotation && !argumentIsOptionType) {
          backwardsIncompatibleChangeTypes.add(CONSTRUCTOR_ARGUMENT_ADDED_LAST_ARGUMENTS_NOT_OPTIONAL);
          break;
        }
      }

      backwardsIncompatibleChangeTypes.addAll(compareEqualCountConstructorArgumentInfos(
          localConstructor.getArgumentInfos().subList(0, localConstructorNumArguments - diff),
          s3Constructor.getArgumentInfos()));
    } else {
      backwardsIncompatibleChangeTypes.add(CONSTRUCTOR_ARGUMENT_REMOVED);
    }

    return backwardsIncompatibleChangeTypes;
  }

  @VisibleForTesting
  static Set<BackwardsIncompatibleChangeType> compareEqualCountConstructorArgumentInfos(
      List<ConstructorArgumentInfo> localConstructorArgumentInfos,
      List<ConstructorArgumentInfo> s3ConstructorArgumentInfos) {
    Set<BackwardsIncompatibleChangeType> backwardsIncompatibleChangeTypes = new HashSet<>();

    Streams.zip(localConstructorArgumentInfos.stream(), s3ConstructorArgumentInfos.stream(),
        (localArgument, s3Argument) -> Pair.of(localArgument, s3Argument))
        .forEach(argumentPair -> {
          ConstructorArgumentInfo localArgumentInfo = argumentPair.getLeft();
          ConstructorArgumentInfo s3ArgumentInfo = argumentPair.getRight();
          if (!localArgumentInfo.getArgumentType().equals(s3ArgumentInfo.getArgumentType())) {
            backwardsIncompatibleChangeTypes.add(CONSTRUCTOR_ARGUMENT_TYPE_CHANGED);
          }

          Set<String> addedConstructorArgumentAnnotations = Sets.difference(
              localArgumentInfo.getArgumentAnnotations(),
              s3ArgumentInfo.getArgumentAnnotations());

          if (CONSTRUCTOR_ARGUMENT_ANNOTATION_CHANGE_BADLIST.stream()
              .anyMatch(addedConstructorArgumentAnnotations::contains)) {
            backwardsIncompatibleChangeTypes.add(CONSTRUCTOR_ARGUMENT_ANNOTATIONS_CHANGED);
          }
        });

    return backwardsIncompatibleChangeTypes;
  }

  private static Option<ConstructorInfo> getInstantiatingConstructor(QueryInfo query) {
    List<ConstructorInfo> instantiatingConstructors = new ArrayList<>();
    List<ConstructorInfo> publicConstructors = new ArrayList<>();
    List<ConstructorInfo> remainingAccessibleConstructors = new ArrayList<>();

    query.getConstructorInfos().forEach(constructorInfo -> {
      if (constructorInfo.getConstructorAnnotations().contains(INSTANTIATE_ANNOTATION)) {
        instantiatingConstructors.add(constructorInfo);
      } else if (Modifier.isPublic(constructorInfo.getModifiers())) {
        publicConstructors.add(constructorInfo);
      } else {
        remainingAccessibleConstructors.add(constructorInfo);
      }
    });

    if (instantiatingConstructors.size() > 1) {
      return Option.none();
    }
    if (instantiatingConstructors.size() == 1) {
      return Option.some(getOnlyElement(instantiatingConstructors));
    }
    if (publicConstructors.size() > 1) {
      return Option.none();
    }
    if (publicConstructors.size() == 1) {
      return Option.some(getOnlyElement(publicConstructors));
    }
    if (remainingAccessibleConstructors.size() == 1) {
      return Option.some(getOnlyElement(remainingAccessibleConstructors));
    }

    return Option.none();
  }

  private static boolean constructorHasAnyNonOptionalArguments(ConstructorInfo constructor) {
    return constructor.getArgumentInfos().stream()
        .map(ConstructorArgumentInfo::getArgumentAnnotations)
        .anyMatch(annotations -> !annotations.contains(OPTIONAL_ANNOTATION));
  }

  private static boolean hasNoExternallyAccessibleConstructors(QueryInfo query) {
    return query.getConstructorInfos().isEmpty();
  }

}
