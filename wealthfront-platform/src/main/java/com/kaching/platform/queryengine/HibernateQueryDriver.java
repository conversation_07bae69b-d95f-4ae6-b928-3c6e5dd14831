package com.kaching.platform.queryengine;

import org.hibernate.SessionFactory;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.kaching.platform.multicolo.MultiColoStatusProvider;
import com.kaching.platform.queryengine.authorization.RequestAuthorizationTokenHasAdminUserPredicate;
import com.kaching.platform.timing.Chronograph;

/**
 * Default Hibernate-aware query driver.
 */
@Singleton
public class HibernateQueryDriver extends DelegatingQueryDriver {

  @Inject
  public HibernateQueryDriver(
      Injector injector,
      Provider<SessionFactory> sessionFactoryProvider,
      QueryScope queryScope,
      StackTraceMonitor monitor,
      QueryRuntimeMonitor runtimeMonitor,
      MultiColoStatusProvider multiColoStatusProvider,
      RequestAuthorizationTokenHasAdminUserPredicate hasAdminUser,
      QueryProxies proxies,
      ThreadLocal<Trace> traces,
      Chronograph chronograph,
      ExceptionClassifier classifier) {
    super(new CommonQueryDriver(
        injector, monitor, runtimeMonitor, proxies, traces, chronograph, classifier,
        new CommonScopingQueryDriver(injector, queryScope,
            new AuthorizingQueryDriver(
                new TransactingQueryDriver(
                    new InjectingQueryDriver(injector),
                    sessionFactoryProvider,
                    queryScope,
                    multiColoStatusProvider,
                    hasAdminUser,
                    injector
                ),
                injector
            )
        )
    ));
  }

}
