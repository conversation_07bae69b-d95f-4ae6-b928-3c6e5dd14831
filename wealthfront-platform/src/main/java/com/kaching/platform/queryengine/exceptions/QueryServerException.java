package com.kaching.platform.queryengine.exceptions;

import static com.google.common.base.Preconditions.checkArgument;

public class QueryServerException extends QueryException {

  private static final long serialVersionUID = -5889581435685453821L;

  public QueryServerException(int httpErrorCode, String httpReasonPhrase) {
    super(httpErrorCode, httpReasonPhrase);
    checkArgument(httpErrorCode >= 500 && httpErrorCode < 600,
        "Http status code must be between 500 and 599 for server errors", httpErrorCode);
  }

  public QueryServerException(
      int httpErrorCode, String httpReasonPhrase, String message) {
    super(httpErrorCode, httpReasonPhrase, message);
    checkArgument(httpErrorCode >= 500 && httpErrorCode < 600,
        "Http status code must be between 500 and 599 for server errors", httpErrorCode);
  }

}
