package com.kaching.platform.queryengine.progress;

import java.util.List;

import com.google.inject.ImplementedBy;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.queryengine.progress.ProgressMonitor.QueryProgress;

@ImplementedBy(QueryProgressFormatterImpl.class)
public interface QueryProgressFormatter {

  String formatAllQueryProgress(List<QuerySession> querySessions);

  List<QueryProgress> getAllQueryProgress(List<QuerySession> querySessions);

}
