package com.kaching.platform.queryengine;

import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.queryengine.QueryEngineModule.CALL_DEPTH_COUNT_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.TRACE_KEY;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Provider;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;

public class CallDepthCountAlertingDriver extends DelegatingScopedQueryDriver {

  public static final String CALL_DEPTH_HEADER_TOKEN = "X-WF-CallDepth";
  public static final int CALL_DEPTH_COUNT_ALERTING_THRESHOLD = 100;
  public static final int CALL_DEPTH_COUNT_TERMINATION_THRESHOLD = 200;

  private final Injector injector;

  public CallDepthCountAlertingDriver(ScopedQueryDriver delegate, Injector injector) {
    super(delegate);
    this.injector = injector;
  }

  @Override
  public <T> Object execute(Query<T> query, Provider<? extends PostProcessor> postProcessor) {
    Alerting alerting = injector.getInstance(Alerting.class);
    alerting.alertAndTerminateIfCallDepthCountExceedsThreshold(query);

    return delegate.execute(query, postProcessor);
  }

  @QueryScoped
  static class Alerting {

    private static final Log log = getLog(CallDepthCountAlertingDriver.Alerting.class);

    @Inject QueryScope queryScope;

    @VisibleForTesting
    void alertAndTerminateIfCallDepthCountExceedsThreshold(Query<?> query) {
      int callDepthCount = Option.of(getCallDepthCount())
          .transform(Identifier::getId)
          .getOrElse(0L)
          .intValue();
      if (callDepthCount >= CALL_DEPTH_COUNT_ALERTING_THRESHOLD && (callDepthCount % 100 == 0)) {
        alertCallDepthCountExceededThreshold(query);
        terminateIfCallDepthCountExceedsThreshold(callDepthCount, query);
      }
    }

    private void alertCallDepthCountExceededThreshold(Query<?> query) {
      Trace trace = getTrace();
      if (trace != null) {
        log.error(
            "Call Depth count exceeds threshold",
            Strings
                .format("Query: %s. Check for trace token: %s", query.getClass().getSimpleName(), trace.getToken()));
      } else {
        log.error(
            "Call Depth count exceeds threshold",
            Strings.format("Query: %s.", query.getClass().getSimpleName()));
      }
    }

    private void terminateIfCallDepthCountExceedsThreshold(int callDepthCount, Query<?> query) {
      if (callDepthCount >= CALL_DEPTH_COUNT_TERMINATION_THRESHOLD) {
        Trace trace = getTrace();
        if (trace != null) {
          throw new RuntimeException(
              Strings.format("Call Depth count exceeded for Query: %s. " +
                      "Terminating the presumably-infinite loop of Query calls. " +
                      "For further investigation, check for trace token: %s. " +
                      "Fix the code by removing the infinite loop.",
                  query.getClass().getSimpleName(),
                  trace.getToken()));
        } else {
          throw new RuntimeException(
              Strings.format("Call Depth count exceeded for Query: %s. " +
                      "Terminating the presumably-infinite loop of Query calls. " +
                      "Fix the code by removing the infinite loop.",
                  query.getClass().getSimpleName()));
        }
      }
    }

    @VisibleForTesting
    CallDepthCount getCallDepthCount() {
      return queryScope.get(CALL_DEPTH_COUNT_KEY);
    }

    @VisibleForTesting
    Trace getTrace() {
      return queryScope.get(TRACE_KEY);
    }

  }

}
