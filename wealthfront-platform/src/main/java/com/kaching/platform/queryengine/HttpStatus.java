package com.kaching.platform.queryengine;

import static com.kaching.platform.monitoring.VarzValue.VarzType.COUNTER;

import java.util.concurrent.atomic.AtomicLong;

import com.google.inject.Singleton;
import com.kaching.platform.monitoring.VarzValue;

@Singleton
public class HttpStatus {

  private final AtomicLong sc200 = new AtomicLong();
  private final AtomicLong sc400 = new AtomicLong();
  private final AtomicLong sc500 = new AtomicLong();

  @VarzValue(COUNTER)
  public long get200() {
    return sc200.get();
  }

  @VarzValue(COUNTER)
  public long get400() {
    return sc400.get();
  }

  @VarzValue(COUNTER)
  public long get500() {
    return sc500.get();
  }

  public void record(int code) {
    if (code >= 500) {
      sc500.incrementAndGet();
    } else if (code >= 400) {
      sc400.incrementAndGet();
    } else {
      sc200.incrementAndGet();
    }
  }

}
