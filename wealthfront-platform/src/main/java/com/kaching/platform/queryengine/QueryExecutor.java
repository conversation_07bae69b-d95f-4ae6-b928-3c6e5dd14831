package com.kaching.platform.queryengine;

import java.util.Collection;

/**
 * An object that executes submitted {@link Query} tasks. This interface
 * provides a way of decoupling task submission from the mechanics of how each
 * task will be run, including details of thread use, scheduling, etc.
 * An Executor is normally used instead of explicitly using a
 * {@link QueryDriver}.
 *
 * @see QueryExecutorService to access the {@link Future}
 */
public interface QueryExecutor {

  /**
   * Executes the given {@code query} at some time  in the future.
   */
  void execute(Query<?> query);

  /**
   * Executes the {@code queries} at some time  in the future, without
   * consuming more than {@code maxConcurrentQueries}.
   *
   * @throws IllegalArgumentException if {@code maxConcurrentQueries} is 0 or
   *                                  negative
   */
  void execute(Collection<? extends Query<?>> queries, int maxConcurrentQueries);

}
