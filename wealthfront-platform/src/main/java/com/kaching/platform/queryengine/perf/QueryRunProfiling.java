package com.kaching.platform.queryengine.perf;

import java.util.Collections;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.NoConcurrentExecution;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.queryengine.QuerySessionId;
import com.wealthfront.util.perf.ProfileCollector;
import com.wealthfront.util.perf.StackTraceRecording;
import com.wealthfront.util.perf.StackTraceRecordingImpl;

public class QueryRunProfiling extends AbstractQuery<String> {

  private static final ScheduledExecutorService EVICTION_SERVICE = Executors.newSingleThreadScheduledExecutor();

  private static final int EVICTION_HOURS = 1;

  private final QuerySessionId querySessionId;
  private final double samplesPerSecond;
  private final int stopNumSeconds;

  public QueryRunProfiling(
      @NoConcurrentExecution QuerySessionId querySessionId,
      @Optional("100.0") double samplesPerSecond,
      @Optional("3600") int stopNumSeconds
  ) {
    this.querySessionId = querySessionId;
    this.samplesPerSecond = samplesPerSecond;
    this.stopNumSeconds = stopNumSeconds;
  }

  @Inject ProfileCollector profileCollector;
  @Inject @QueryRecording ConcurrentHashMap<QuerySessionId, StackTraceRecording> querySessionIdToRecording;
  @Inject QueryRuntimeMonitor runtimeMonitor;

  @Override
  public String process() {
    var recording = new StackTraceRecordingImpl();
    querySessionIdToRecording.put(querySessionId, recording);
    try {
      return profileCollector.collectProfile(
          recording,
          samplesPerSecond,
          stopNumSeconds,
          Option.some(0),
          () -> runtimeMonitor.getQuerySessionBySessionId(querySessionId)
              .transform(QuerySession::getQueryMainThread)
              .transform(Set::of)
              .getOrElse(Collections.emptySet())
      );
    } finally {
      EVICTION_SERVICE.schedule(
          new EvictQuery(querySessionIdToRecording, querySessionId),
          EVICTION_HOURS,
          TimeUnit.HOURS
      );
    }
  }

  private record EvictQuery(
      ConcurrentHashMap<QuerySessionId, StackTraceRecording> querySessionIdToRecording,
      QuerySessionId querySessionId
  ) implements Runnable {

    @Override
    public void run() {
      querySessionIdToRecording.remove(querySessionId);
    }

  }

}
