package com.kaching.platform.queryengine;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.google.common.collect.Maps.newHashMap;
import static com.google.common.collect.Maps.newTreeMap;
import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.common.logging.Log.logContextPut;
import static com.kaching.platform.common.logging.Log.logContextRemove;
import static com.kaching.platform.functional.Unit.unit;
import static com.kaching.platform.queryengine.QueryEngineModule.API_REQUEST_METADATA_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.CALL_DEPTH_COUNT_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.ORIGIN_INFO_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_CLASS_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_CLEARING_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_OUTPUT_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_STRING;
import static com.kaching.platform.queryengine.QueryEngineModule.REQUEST_AUTHORIZATION_TOKEN_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.REQUEST_HEADERS_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.RESPONSE_HEADERS_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.RETURN_STUB_FAKE_RESULT;
import static com.kaching.platform.queryengine.QueryEngineModule.SFE_END2END_TEST_USER;
import static com.kaching.platform.queryengine.QueryEngineModule.TRACER_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.TRACE_KEY;
import static com.kaching.platform.queryengine.authorization.RequestAuthorizationToken.X_WF_AUTHORIZATION;
import static java.lang.Boolean.parseBoolean;
import static java.lang.String.format;
import static java.util.Collections.unmodifiableMap;
import static javax.servlet.http.HttpServletResponse.SC_OK;
import static javax.servlet.http.HttpServletResponse.SC_SERVICE_UNAVAILABLE;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.ThreadContext;
import org.eclipse.jetty.io.EofException;
import org.eclipse.jetty.server.Request;
import org.eclipse.jetty.server.handler.AbstractHandler;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.kaching.entities.ApiRequestMetadata;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.Thunk;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.admin.QueryEngineStaticResourceHandler;
import com.kaching.platform.queryengine.authorization.EncryptedRequestAuthorizationToken;
import com.kaching.util.http.QueryStringFormatter;
import com.twolattes.json.Json;

@Singleton
public class JettyRequestAdapter extends AbstractHandler {

  public static final String IS_SFE_E2E_TEST_USER_HEADER = "X-WF-IsSfeE2ETestUser";
  public static final String SHOULD_RETURN_STUB_FAKE_RESULT = "X-WF-ShouldReturnStubFakeResult";
  public static final String ORIGIN_INFO_HEADER = "X-WF-OriginInfo";
  public static final String API_REQUEST_METADATA_HEADER = "X-WF-ApiRequestMetadata";

  private static final Log log = getLog(JettyRequestAdapter.class);

  private final RequestInterpreter interpreter;
  private final Provider<QueryDriver> queryDriverProvider;
  private final HttpStatus status;
  private final ThreadLocal<Trace> traces;
  private final Map<String, Provider<QueryEngineServletHandler>> pathHandlers;
  private final QueryStringFormatter queryStringFormatter;
  private final Headers headers;
  private final StackTraceMonitor monitor;
  private final QueryRuntimeMonitor runtimeMonitor;

  @Inject
  public JettyRequestAdapter(
      RequestInterpreter interpreter,
      Provider<QueryDriver> queryDriverProvider,
      HttpStatus status,
      Map<String, Provider<QueryEngineServletHandler>> pathHandlers,
      ThreadLocal<Trace> traces,
      QueryStringFormatter queryStringFormatter,
      Headers headers,
      StackTraceMonitor monitor,
      QueryRuntimeMonitor runtimeMonitor) {
    this.interpreter = interpreter;
    this.queryDriverProvider = queryDriverProvider;
    this.status = status;
    this.pathHandlers = pathHandlers;
    this.traces = traces;
    this.queryStringFormatter = queryStringFormatter;
    this.headers = headers;
    this.monitor = monitor;
    this.runtimeMonitor = runtimeMonitor;
  }

  @Inject public QueryEngineStaticResourceHandler resourceHandler;

  @SuppressWarnings("unchecked")
  @Override
  public void handle(
      String target,
      Request req,
      HttpServletRequest request,
      HttpServletResponse response) throws IOException {

    log(target, request);
    if (!target.equals("/")) {
      if (pathHandlers.containsKey(target)) {
        pathHandlers.get(target).get().handle(request, response);
        return;
      } else if (target.startsWith("/static")) {
        try {
          resourceHandler.handle(target, req, request, response);
          return;
        } catch (ServletException e) {
          throw new RuntimeException(e);
        }
      }
    }

    final HttpMethod method = getMethod(request.getMethod());
    final Map<String, String[]> httpParams = request.getParameterMap();
    final Map<String, String> reqHeaders = headers(request);
    final Map<String, String> respHeaders = newHashMap();
    final Trace trace = Trace.of(reqHeaders);
    logStartOfRequest(trace);
    Query<?> query = null;

    // Ensure trace always is returned.
    for (Entry<String, String> t : trace.toResponseHeaders().entrySet()) {
      response.setHeader(t.getKey(), t.getValue());
    }
    for (Entry<String, String> t : headers.get().entrySet()) {
      response.setHeader(t.getKey(), t.getValue());
    }

    boolean thrownBeforeExecution = true;
    try {
      traces.set(trace);

      final ByteArrayOutputStream outStream;
      final Class<? extends Query<?>> queryClass;
      final Provider<? extends PostProcessor> postProcessorProvider;
      try {
        outStream = new ByteArrayOutputStream();
        query = interpreter.getQuery(method, target, httpParams, reqHeaders);
        queryClass = (Class<? extends Query<?>>) query.getClass();
        postProcessorProvider = interpreter.getPostProcessorProvider(
            method, target, httpParams, reqHeaders, queryClass);
      } catch (Exception e) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "From, X-WF-Authorization");
        response.setHeader("Access-Control-Max-Age", "5184000");
        throw e;
      }

      if (log.isDebugEnabled()) {
        log.debug("trace token %s for %s", trace.getToken(), queryClass);
      }

      // driving the query.
      Tracer queryTracer = reqHeaders.get(Tracer.TRACER_HEADER) == null ?
          Tracers.empty() : new Tracer.RecordingTracer();

      String tokenHeaderValue = reqHeaders.get(X_WF_AUTHORIZATION);
      Option<EncryptedRequestAuthorizationToken> token =
          isNullOrEmpty(tokenHeaderValue) ?
              Option.none() :
              Option.of(new EncryptedRequestAuthorizationToken(tokenHeaderValue));

      String callDepthCountString = reqHeaders.get(CallDepthCountAlertingDriver.CALL_DEPTH_HEADER_TOKEN);
      Long callDepthCount = Option.of(callDepthCountString)
          .transform(Long::parseLong)
          .getOrElse(0L);

      ImmutableMap<Key<?>, Object> queryScopeCache = ImmutableMap.<Key<?>, Object>builder()
          .put(QUERY_STRING, new QueryString(
              interpreter, queryStringFormatter, httpParams, queryClass))
          .put(QUERY_CLASS_KEY, queryClass)
          .put(QUERY_OUTPUT_KEY, outStream)
          .put(QUERY_CLEARING_KEY, new ClearingStreamHolder())
          .put(RESPONSE_HEADERS_KEY, respHeaders)
          .put(REQUEST_HEADERS_KEY, reqHeaders)
          .put(TRACER_KEY, queryTracer)
          .put(REQUEST_AUTHORIZATION_TOKEN_KEY, token)
          .put(TRACE_KEY, trace)
          .put(CALL_DEPTH_COUNT_KEY, new CallDepthCount(callDepthCount + 1))
          .put(SFE_END2END_TEST_USER, getIsSfeE2ETestUserHeader(reqHeaders))
          .put(RETURN_STUB_FAKE_RESULT, getShouldReturnStubFakeResult(reqHeaders))
          .put(ORIGIN_INFO_KEY, getOriginInfo(reqHeaders))
          .put(API_REQUEST_METADATA_KEY, getApiRequestMetadata(reqHeaders))
          .build();
      thrownBeforeExecution = false;
      Object result = queryDriverProvider.get().execute(
          query,
          queryScopeCache,
          postProcessorProvider);

      response.setStatus(SC_OK);
      setContentType(response, respHeaders, queryClass);
      for (Entry<String, String> e : respHeaders.entrySet()) {
        response.setHeader(e.getKey(), e.getValue());
      }
      queryTracer.record(response);

      if (result != null && StreamingOutput.class.isAssignableFrom(result.getClass())) {
        @SuppressWarnings("rawtypes")
        StreamingOutput so = (StreamingOutput) result;
        Pair<Boolean, QuerySession> successAndQuerySession = runtimeMonitor.streamingStart(
            query, Sla.of(queryClass) /* SHOULD GO THROUGH QueryProxy */, Thread.currentThread(), trace);
        QuerySession session = successAndQuerySession.right;
        try {
          ServletOutputStream os = response.getOutputStream();
          if (successAndQuerySession.left) {
            so.write(os);
          } else {
            response.setStatus(SC_SERVICE_UNAVAILABLE);
            log.info("unable to start streaming, too many existing streaming sessions");
          }
          runtimeMonitor.streamingStopSuccessfully(session, unit);
        } catch (EofException eof) {
          runtimeMonitor.streamingStopExceptionally(session, eof);
          log.info(
              "client has closed connection before content of query %s could be written",
              queryInfo(query, request));
        }
      } else {
        byte[] content = outStream.toByteArray();
        response.setContentLength(content.length);
        try {
          response.getOutputStream().write(content);
          logEndOfRequest(trace);
        } catch (EofException endOfFile) {
          log.info(
              "client has closed connection before content of query %s could be written",
              queryInfo(query, request));
        }
      }
      status.record(200);
    } catch (Exception e) {
      int code = interpreter
          .getExceptionHandler(method, target, httpParams, reqHeaders)
          .handle(e, response);
      status.record(code);
      if (code >= 500) {
        if (thrownBeforeExecution) {
          // Only record the exception if it was thrown before the execution of
          // the query, otherwise it would get logged twice.
          monitor.add(e);
        }
        log.error(e, formatLogLine(e, code, query, request, trace, target));
      } else if (code >= 400) {
        if (!isSiriusNoSuchElementException(e.getClass().getCanonicalName(), code)) {
          log.error(e, "Message: %s. Log line: %s", e.getMessage(), formatLogLine(e, code, query, request, trace, target));
        }
      } else {
        log.error(e, formatLogLine(e, code, query, request, trace, target));
      }
    } finally {
      traces.remove();
    }

    if (request instanceof Request) {
      ((Request) request).setHandled(true);
    }
  }

  @VisibleForTesting
  static boolean isSiriusNoSuchElementException(String canonicalName, int code) {
    return code == 460 && canonicalName.equals("com.wealthfront.sirius.client.NoSuchElementException");
  }

  @VisibleForTesting
  static boolean getIsSfeE2ETestUserHeader(Map<String, String> requestHeaders) {
    return parseBoolean(requestHeaders.get(IS_SFE_E2E_TEST_USER_HEADER));
  }

  @VisibleForTesting
  static boolean getShouldReturnStubFakeResult(Map<String, String> requestHeaders) {
    return parseBoolean(requestHeaders.get(SHOULD_RETURN_STUB_FAKE_RESULT));
  }

  @VisibleForTesting
  static Option<OriginInfo> getOriginInfo(Map<String, String> requestHeaders) {
    return Option.of(requestHeaders.get(ORIGIN_INFO_HEADER))
        .transform(info -> OriginInfo.MARSHALLER.unmarshall(Json.fromString(info)));
  }

  @VisibleForTesting
  static Option<ApiRequestMetadata> getApiRequestMetadata(Map<String, String> requestHeaders) {
    return Option.of(requestHeaders.get(API_REQUEST_METADATA_HEADER))
        .transform(apiRequestMetadata -> ApiRequestMetadata.MARSHALLER.unmarshall(Json.fromString(apiRequestMetadata)));
  }

  private void logStartOfRequest(Trace trace) {
    if (log.isDebugEnabled()) {
      boolean hadTrace = ThreadContext.containsKey("trace");
      if (!hadTrace) {
        logContextPut("trace", trace.getToken());
      }

      log.debug("Starting to process request with trace %s", trace);

      if (!hadTrace) {
        logContextRemove("trace");
      }
    }
  }

  private void logEndOfRequest(Trace trace) {
    if (log.isDebugEnabled()) {
      boolean hadTrace = ThreadContext.containsKey("trace");
      if (!hadTrace) {
        logContextPut("trace", trace.getToken());
      }

      log.debug("Processed request with trace %s", trace);

      if (!hadTrace) {
        logContextRemove("trace");
      }
    }
  }

  private static void log(String target, HttpServletRequest request) {
    if (log.isDebugEnabled()) {
      log.debug("Target %s, Request %s", target, request.toString());
    }
  }

  static ThunkBackedMap<String, String> headers(final HttpServletRequest request) {
    return new ThunkBackedMap<>(new Thunk<Map<String, String>>() {
      @Override
      protected Map<String, String> compute() {
        Map<String, String> map = newTreeMap(String.CASE_INSENSITIVE_ORDER);
        Enumeration<String> headers = request.getHeaderNames();
        while (headers.hasMoreElements()) {
          String name = headers.nextElement();
          map.put(name, request.getHeader(name));
        }
        return unmodifiableMap(map);
      }
    });
  }

  private static void setContentType(
      HttpServletResponse response,
      Map<String, String> respHeaders,
      Class<? extends Query<?>> queryClass) {
    Iterator<Entry<String, String>> iter = respHeaders.entrySet().iterator();
    while (iter.hasNext()) {
      Entry<String, String> e = iter.next();
      if (e.getKey().equalsIgnoreCase("Content-Type")) {
        response.setContentType(e.getValue());
        iter.remove();
      }
    }
    if (response.getContentType() == null) {
      ContentType annotation = queryClass.getAnnotation(ContentType.class);
      if (annotation != null) {
        response.setContentType(annotation.value().text());
      }
    }
  }

  private static HttpMethod getMethod(String methodStr) {
    try {
      return HttpMethod.valueOf(methodStr);
    } catch (IllegalArgumentException e) {
      return null;
    }
  }

  private static String formatLogLine(
      Exception e, int code, Query<?> query, HttpServletRequest req,
      Trace trace, String target) {
    // TODO: Give RequestInterpreter the responsibility to provide any
    //  information possible about the intended query, for debugging purposes,
    //  when it cannot instantiate one due to missing or illegal argument values.
    String queryInfo = queryInfo(query, req);
    return queryInfo != null ?
        format("%s (%s): handled %s as %d, trace token %s",
            target,
            queryInfo,
            e.getClass().getSimpleName(),
            code,
            trace.getToken()) :
        format("%s: handled %s as %d, trace token %s",
            target,
            e.getClass().getSimpleName(),
            code,
            trace.getToken());
  }

  private static String queryInfo(Query<?> query, HttpServletRequest request) {
    return query != null ? query.toString() : request.getParameter("q");
  }

  static class ThunkBackedMap<K, V> implements Map<K, V> {

    private final Thunk<Map<K, V>> delegate;

    ThunkBackedMap(Thunk<Map<K, V>> delegate) {
      this.delegate = delegate;
    }

    @Override
    public void clear() {
      delegate.get().clear();
    }

    @Override
    public boolean containsKey(Object arg0) {
      return delegate.get().containsKey(arg0);
    }

    @Override
    public boolean containsValue(Object arg0) {
      return delegate.get().containsValue(arg0);
    }

    @Override
    public Set<Entry<K, V>> entrySet() {
      return delegate.get().entrySet();
    }

    @Override
    public boolean equals(Object arg0) {
      return delegate.get().equals(arg0);
    }

    @Override
    public V get(Object arg0) {
      return delegate.get().get(arg0);
    }

    @Override
    public int hashCode() {
      return delegate.get().hashCode();
    }

    @Override
    public boolean isEmpty() {
      return delegate.get().isEmpty();
    }

    @Override
    public Set<K> keySet() {
      return delegate.get().keySet();
    }

    @Override
    public V put(K arg0, V arg1) {
      return delegate.get().put(arg0, arg1);
    }

    @Override
    public void putAll(Map<? extends K, ? extends V> arg0) {
      delegate.get().putAll(arg0);
    }

    @Override
    public V remove(Object arg0) {
      return delegate.get().remove(arg0);
    }

    @Override
    public int size() {
      return delegate.get().size();
    }

    @Override
    public Collection<V> values() {
      return delegate.get().values();
    }

    @Override
    public String toString() {
      return delegate.get().toString();
    }

  }

}
