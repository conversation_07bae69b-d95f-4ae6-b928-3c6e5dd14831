package com.kaching.platform.queryengine.authorization;

import static org.joda.time.Minutes.minutes;

import org.joda.time.DateTime;
import org.joda.time.ReadablePeriod;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.name.Named;
import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.common.Option;
import com.kaching.security.Cipher;
import com.kaching.user.UserId;

public class CipherRequestAuthorizationTokenGenerator implements RequestAuthorizationTokenGenerator {

  @VisibleForTesting
  static final ReadablePeriod AUTHORIZATION_SESSION_LIFESPAN = minutes(15);

  private final Provider<DateTime> clock;
  private final Cipher cipher;

  @Inject
  public CipherRequestAuthorizationTokenGenerator(
      Provider<DateTime> clock, @Named("RequestAuthorizationToken") Cipher cipher) {
    this.clock = clock;
    this.cipher = cipher;
  }

  @Override
  public String generate(UserId authorizedUserId, Option<DataEnvironment> dataEnvironment) {
    return generate(authorizedUserId, authorizedUserId, dataEnvironment);
  }

  @Override
  public String generate(UserId authorizedUserId, UserId actorUserId, Option<DataEnvironment> dataEnvironment) {
    return generate(authorizedUserId, actorUserId.toString(), dataEnvironment, Option.none());
  }

  @Override
  public String generate(UserId authorizedUserId, String actor, Option<DataEnvironment> dataEnvironment, Option<UserId> adminUserId) {
    return new RequestAuthorizationToken(
        new UserId(authorizedUserId.getId()),
        actor,
        getExpirationTime(clock.get()),
        dataEnvironment,
        adminUserId)
        .encrypt(cipher)
        .toString();
  }

  private static DateTime getExpirationTime(DateTime now) {
    return now.plus(AUTHORIZATION_SESSION_LIFESPAN);
  }

}  
