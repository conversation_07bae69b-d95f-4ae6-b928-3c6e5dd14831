package com.kaching.platform.queryengine.xray;

import com.amazonaws.xray.AWSXRay;
import com.amazonaws.xray.AWSXRayRecorder;
import com.amazonaws.xray.AWSXRayRecorderBuilder;
import com.amazonaws.xray.log4j.Log4JSegmentListener;

// this exists to make BestEffortAwsXRayRecorderModule.class testable
public class AwsXrayRecorderProvider {

  public AWSXRayRecorder provideAwsXrayRecorder() {
    AWSXRayRecorder recorder = AWSXRayRecorderBuilder.standard()
        .withSegmentListener(new Log4JSegmentListener())
        .build();
    AWSXRay.setGlobalRecorder(recorder);
    return recorder;
  }

}
