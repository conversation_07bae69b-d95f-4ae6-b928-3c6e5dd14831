package com.kaching.platform.queryengine.authorization;

import static com.google.common.base.Charsets.UTF_8;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.util.Base64.DONT_BREAK_LINES;

import java.io.IOException;
import java.io.StringReader;

import org.joda.time.DateTime;

import com.kaching.platform.common.DataEnvironment;
import com.kaching.platform.common.Option;
import com.kaching.security.Cipher;
import com.kaching.user.UserId;
import com.kaching.util.Base64;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

public class EncryptedRequestAuthorizationToken {

  private static final Marshaller<Long> LONG_MARSHALLER = createMarshaller(Long.class);
  private static final Marshaller<String> STRING_MARSHALLER = createMarshaller(String.class);
  private static final Marshaller<DateTime> DATETIME_MARSHALLER = createMarshaller(DateTime.class);
  private static final Marshaller<DataEnvironment> DATA_ENVIRONMENT_MARSHALLER =
      createMarshaller(DataEnvironment.class);

  private final String ciphertext;

  public EncryptedRequestAuthorizationToken(String ciphertext) {
    this.ciphertext = ciphertext;
  }

  public RequestAuthorizationToken decrypt(Cipher cipher) {
    String plaintext = new String(cipher.decrypt(
        Base64.decode(ciphertext, DONT_BREAK_LINES)), UTF_8);
    try {
      Json.Array array = (Json.Array) Json.read(new StringReader(plaintext));
      DataEnvironment dataEnvironment = null;
      if (array.size() >= 4) {
        dataEnvironment = DATA_ENVIRONMENT_MARSHALLER.unmarshall(array.get(3));
      }
      UserId adminUserId = null;
      if (array.size() >= 5) {
        adminUserId = Json.NULL.equals(array.get(4)) ? null : new UserId(LONG_MARSHALLER.unmarshall(array.get(4)));
      }
      return new RequestAuthorizationToken(
          new UserId(LONG_MARSHALLER.unmarshall(array.get(0))),
          STRING_MARSHALLER.unmarshall(array.get(1)),
          DATETIME_MARSHALLER.unmarshall(array.get(2)),
          Option.of(dataEnvironment),
          Option.of(adminUserId));
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public String toString() {
    return ciphertext;
  }

}
