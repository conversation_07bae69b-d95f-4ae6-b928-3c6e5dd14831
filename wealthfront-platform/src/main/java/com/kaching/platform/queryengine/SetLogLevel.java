package com.kaching.platform.queryengine;

import static com.kaching.platform.common.logging.Log.getLog;
import static org.apache.logging.log4j.LogManager.getLogger;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.LoggerConfig;

import com.kaching.platform.common.logging.Log;
import com.kaching.platform.converters.Instantiate;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.queryengine.admin.Param;

public class SetLogLevel extends AbstractQuery<String> {

  private static final Log log = getLog(SetLogLevel.class);

  private final String name;
  private final String level;
  private final boolean force;

  public SetLogLevel(@Param("log name") String name, @Param("level") String level) {
    this(name, level, false);
  }

  @Instantiate
  public SetLogLevel(@Param("log name") String name, @Param("level") String level, @Optional("false") boolean force) {
    this.name = name;
    this.level = level;
    this.force = force;
  }

  @Override
  public String process() {
    LoggerContext ctx = (LoggerContext) LogManager.getContext(false);

    if (!ctx.hasLogger(name) && !force) {
      return "No logger named " + name + " exists (name misspelled?)." +
          " If this is a real log that just hasn't been loaded yet and you need to set it before" +
          " using it for the first time, use force=true.";
    }

    Configuration config = ctx.getConfiguration();

    LoggerConfig loggerConfig = config.getLoggerConfig(name);
    LoggerConfig specificConfig = loggerConfig;

    if (!loggerConfig.getName().equals(name)) {
      specificConfig = new LoggerConfig(name, Level.getLevel(level), true);
      specificConfig.setParent(loggerConfig);
      config.addLogger(name, specificConfig);
    }

    specificConfig.setLevel(Level.getLevel(level));
    ctx.updateLoggers();

    log.info(
        "log level of %s has been set to %s",
        name, getLogger(name).getLevel());

    return getLogger(name).getLevel().toString();
  }

}
