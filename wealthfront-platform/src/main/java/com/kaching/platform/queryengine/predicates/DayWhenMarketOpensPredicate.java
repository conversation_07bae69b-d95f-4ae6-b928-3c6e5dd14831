package com.kaching.platform.queryengine.predicates;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.platform.queryengine.Predicate;
import com.wealthfront.entities.Market;

public class DayWhenMarketOpensPredicate implements Predicate {

  @Inject Provider<DateTime> clock;
  @Inject Market market;

  public boolean satisfied() {
    return market.isOpenOnDay(clock.get());
  }

}
