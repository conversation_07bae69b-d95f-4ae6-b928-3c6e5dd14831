package com.kaching.platform.queryengine.exceptions;

import static java.lang.String.format;

import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;

public class NotFoundException extends QueryClientException {

  private static final long serialVersionUID = -5636915241881620405L;

  public NotFoundException(String message) {
    super(404, message);
  }

  public <T extends HibernateEntity> NotFoundException(Class<? extends T> clazz, Id<T> id) {
    super(404, format("%s#%s not found", clazz.getSimpleName(), id));
  }

  public <T extends HibernateEntity> NotFoundException(Class<? extends T> clazz, String name, Object value) {
    super(404, format("%s with %s=%s not found", clazz.getSimpleName(), name, value));
  }

  public <T extends HibernateEntity> NotFoundException(
      Class<? extends T> clazz,
      String name1, Object value1,
      String name2, Object value2) {
    super(404, format("%s with %s=%s and %s=%s not found", clazz.getSimpleName(), name1, value1, name2, value2));
  }

}
