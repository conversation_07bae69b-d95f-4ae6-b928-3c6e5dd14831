package com.kaching.platform.queryengine;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

@Retention(RUNTIME)
@Target(TYPE)
public @interface ContentType {

  enum MimeType {
    JAVASCRIPT("text/javascript; charset=utf-8"),
    JSON("text/javascript; charset=utf-8"),  // FF and Sf have trouble displaying json
    HTML("text/html; charset=utf-8"),
    GIF("image/gif"),
    PNG("image/png"),
    PLAIN("text/plain; charset=utf-8"),
    XML("text/xml; charset=utf-8"),
    BINARY("application/octet-stream"),
    JPG("image/jpeg"),
    PDF("application/pdf");

    private final String text;

    MimeType(String text) {
      this.text = text;
    }

    public String text() {
      return text;
    }
  }

  MimeType value();

}
