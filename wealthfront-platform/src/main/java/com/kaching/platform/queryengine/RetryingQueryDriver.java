package com.kaching.platform.queryengine;

import static com.kaching.platform.common.MySqlErrorNumbers.ER_LOCK_WAIT_TIMEOUT;

import java.util.Map;

import org.hibernate.StaleObjectStateException;
import org.hibernate.exception.GenericJDBCException;
import org.hibernate.exception.LockAcquisitionException;

import com.google.inject.Key;
import com.google.inject.Provider;
import com.kaching.platform.common.logging.Log;

/**
 * This query driver is in charge of retrying the execution of a query according
 * to the {@link Transactional#retry()} option.
 */
public class RetryingQueryDriver extends DelegatingQueryDriver {

  Log log = Log.getLog(RetryingQueryDriver.class);

  public RetryingQueryDriver(QueryDriver delegate) {
    super(delegate);
  }

  @Override
  public <T> Object execute(
      Query<T> query,
      Map<Key<?>, Object> relaxedCache,
      Provider<? extends PostProcessor> postProcessor) {
    // retries & sleep
    Transactional annotation =
        query.getClass().getAnnotation(Transactional.class);
    int retries;
    long sleep;
    if (annotation != null) {
      retries = annotation.retry();
      sleep = annotation.sleepBeforeRetry();
    } else {
      retries = 0;
      sleep = 0L;
    }
    // retries
    int count = 0;
    while (count < retries) {
      long retryTime = System.currentTimeMillis();
      try {
        Object result = delegate.execute(query, relaxedCache, postProcessor);
        if (count > 0) {
          log.warn("SUCCESS: retry [%s / %s] of %s took %sms",
              count, (retries + 1), query, System.currentTimeMillis() - retryTime);
        }
        return result;
      } catch (LockAcquisitionException e) {
        query = query.clone();
      } catch (StaleObjectStateException e) {
        query = query.clone();
      } catch (GenericJDBCException e) {
        // retry on lock wait timeout (see CORE-2879)
        if (e.getErrorCode() == ER_LOCK_WAIT_TIMEOUT.getCode()) {
          query = query.clone();
        } else {
          throw e;
        }
      }
      if (count > 0) {
        log.warn("FAIL: retry [%s / %s] of %s took %sms",
            count, (retries + 1), query, System.currentTimeMillis() - retryTime);
      }
      ClearingStreamHolder clearingStreamHolder =
          (ClearingStreamHolder) relaxedCache.get(QueryEngineModule.QUERY_CLEARING_KEY);
      if (clearingStreamHolder != null) {
        clearingStreamHolder.clearStream();
      }
      count++;
      log.warn("retrying [%s / %s] %s", count, (retries + 1), query);
      if (sleep > 0) {
        log.warn("sleeping for %s milli before retry %s ", sleep, query);
        try {
          Thread.sleep(sleep);
        } catch (InterruptedException e) {
          throw new RuntimeException(e);
        }
      }
    }
    // final execution propagates the exception (if any)
    return delegate.execute(query, relaxedCache, postProcessor);
  }

}
