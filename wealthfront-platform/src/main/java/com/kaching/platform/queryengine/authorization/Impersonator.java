package com.kaching.platform.queryengine.authorization;

import java.util.concurrent.Callable;

import com.google.inject.ImplementedBy;
import com.kaching.user.UserId;

@ImplementedBy(ImpersonatorImpl.class)
public interface Impersonator {

  void doAs(UserId userId, Object obj, Runnable runnable);

  void doAs(UserId userId, Class<?> clazz, Runnable runnable);

  void doAs(UserId userId, Class<?> clazz, Runnable runnable, String actorUserName);

  <T> T doAs(UserId userId, Object obj, Callable<T> callable);

  <T> T doAs(UserId userId, Class<?> clazz, Callable<T> callable);

  <T> T doAs(UserId userId, Class<?> clazz, Callable<T> callable, String actorUserName);
}
