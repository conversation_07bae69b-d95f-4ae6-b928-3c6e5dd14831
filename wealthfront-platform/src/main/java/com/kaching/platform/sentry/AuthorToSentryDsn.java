package com.kaching.platform.sentry;

import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.kaching.Author;
import com.kaching.platform.common.Strings;

public class AuthorToSentryDsn {

  private static final String PARAMS =
      "beacon=false&stacktrace.app.packages=com.kaching,com.wealthfront&uncaught.handler.enabled=false";
  private static final Map<Author, SentryDsn> MAP = ImmutableMap.<Author, SentryDsn>builder()
      .put(Author.ACATS_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/5?%s", PARAMS)))
      .put(Author.BANKING_PLATFORM_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/5?%s", PARAMS)))
      .put(Author.ADVICE_AUTOMATION_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/19?%s", PARAMS)))
      .put(Author.API_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/19?%s", PARAMS)))
      .put(Author.LINK_SERVICE_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/19?%s", PARAMS)))
      .put(Author.BROKERAGE_PLATFORM_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/15?%s", PARAMS)))
      .put(Author.ONLINE_SERVICES_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/19?%s", PARAMS)))
      .put(Author.INVESTMENT_SERVICES_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/11?%s", PARAMS)))
      .put(Author.TRADING_PRODUCTS_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/46?%s", PARAMS)))
      .put(Author.ANALYTICS_ENGINEERING_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/32?%s", PARAMS)))
      .put(Author.DATA_OPS_LEAD, SentryDsn.of(
          Strings.format("https://<EMAIL>/13?%s", PARAMS)))
      .put(Author.DATA_PLATFORM_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/10?%s", PARAMS)))
      .put(Author.DATA_SCIENCE_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/14?%s", PARAMS)))
      .put(Author.DATA_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/10?%s", PARAMS)))
      .put(Author.WEB_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/17?%s", PARAMS)))
      .put(Author.IOS_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/22?%s", PARAMS)))
      .put(Author.ANDROID_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/8?%s", PARAMS)))
      .put(Author.YODLEE_INTEGRATION_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/18?%s", PARAMS)))
      .put(Author.SITE_HEALTH_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/23?%s", PARAMS)))
      .put(Author.TAOS_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/3?%s", PARAMS)))
      .put(Author.TRADE_VALIDATION_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/37?%s", PARAMS)))
      .put(Author.SYSTEMS_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/26?%s", PARAMS)))
      .put(Author.FRAUD_RISK_ENG_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/30?%s", PARAMS)))
      .put(Author.BACKEND_INFRA_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/38?%s", PARAMS)))
      .put(Author.LENDING_PLATFORM_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/45?%s", PARAMS)))
      .put(Author.INVESTMENT_PRODUCTS_TEAM, SentryDsn.of(
          Strings.format("https://<EMAIL>/47?%s", PARAMS)))
      .build();

  private static final Map<List<Author>, SentryDsn> MULTI_AUTHOR_MAP = ImmutableMap.of(
      ImmutableList.of(Author.BROKERAGE_PLATFORM_TEAM, Author.INVESTMENT_SERVICES_TEAM),
      SentryDsn.of(
          Strings.format("https://<EMAIL>/24?%s", PARAMS)));

  public static Map<Author, SentryDsn> getMap() {
    return MAP;
  }

  public static Map<List<Author>, SentryDsn> getMultiAuthorMap() {
    return MULTI_AUTHOR_MAP;
  }

}
