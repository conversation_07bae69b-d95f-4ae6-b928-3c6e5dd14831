package com.kaching.platform.ipaddress;

public interface IpAddressVendorVisitor<T> {

  T visitDebug(IpAddressVendor vendor);

  T visitEMoney(IpAddressVendor vendor);

  T visitFiserv(IpAddressVendor vendor);

  T visitIntuit(IpAddressVendor vendor);

  T visitPlaid(IpAddressVendor vendor);

  T visitQuovo(IpAddressVendor vendor);

  T visitMint(IpAddressVendor vendor);

  T visitMorningStar(IpAddressVendor vendor);

  T visitMX(IpAddressVendor vendor);

  T visitPingdom(IpAddressVendor vendor);

  T visitTurboTax(IpAddressVendor vendor);

  T visitYodlee(IpAddressVendor vendor);

  T visitFinicity(IpAddressVendor vendor);

  T visitDataTheorem(IpAddressVendor vendor);

}
