package com.kaching.platform.memcached;

import java.net.SocketAddress;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.inject.Inject;

public class MemcachedStatsProviderImpl implements MemcachedStatsProvider {

  @Inject MemcachedClient client;
  @Inject MemcachedUtilizationStats utilizationStats;

  @Override
  public Map<SocketAddress, MemcachedStats> getStats() {
    Map<SocketAddress, MemcachedStats> newStats = client
        .getStats()
        .entrySet()
        .stream()
        .collect(Collectors.toMap(Map.Entry::getKey, entry -> new MemcachedStats(entry.getValue())));
    utilizationStats.update(newStats);
    return newStats;
  }

}
