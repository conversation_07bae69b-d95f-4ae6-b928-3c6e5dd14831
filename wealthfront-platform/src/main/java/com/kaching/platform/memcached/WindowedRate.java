package com.kaching.platform.memcached;

import org.joda.time.DateTime;

public class WindowedRate {

  private final DateTime at;
  private final DateTime currentWindowStart;
  private final long previousWindowCount;
  private final long currentWindowCount;
  private final double rate;

  public WindowedRate(
      DateTime at,
      DateTime currentWindowStart,
      long previousWindowCount,
      long currentWindowCount,
      double rate) {
    this.at = at;
    this.currentWindowStart = currentWindowStart;
    this.previousWindowCount = previousWindowCount;
    this.currentWindowCount = currentWindowCount;
    this.rate = rate;
  }

  public DateTime getAt() {
    return at;
  }

  public DateTime getCurrentWindowStart() {
    return currentWindowStart;
  }

  public long getPreviousWindowCount() {
    return previousWindowCount;
  }

  public long getCurrentWindowCount() {
    return currentWindowCount;
  }

  public double getRate() {
    return rate;
  }

  @Override
  public String toString() {
    return "WindowedRate{" +
        "at=" + at +
        ", currentWindowStart=" + currentWindowStart +
        ", previousWindowCount=" + previousWindowCount +
        ", currentWindowCount=" + currentWindowCount +
        ", rate=" + rate +
        '}';
  }

}

