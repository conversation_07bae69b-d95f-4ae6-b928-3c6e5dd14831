package com.kaching.platform.toggle;

import static org.hibernate.criterion.Restrictions.eq;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.kaching.entities.PersistentRepository;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.OptionVisitor;
import com.kaching.platform.hibernate.DbSession;

public class PersistentToggleRepository extends PersistentRepository<Toggle> implements ToggleRepository {

  @Inject
  public PersistentToggleRepository(DbSession session) {
    super(Toggle.class, session);
  }

  @Override
  public Option<Toggle> getByName(String name) {
    return Option.of(createCriteria(Toggle.class)
        .add(eq("name", name))
        .uniqueResult());
  }

  @Override
  public Toggle getOrCreate(String name, boolean defaultValue, DateTime now) {
    return getByName(name).visit(new OptionVisitor<Toggle, Toggle>() {
      @Override
      public Toggle caseNone() {
        Toggle toggle = new Toggle(name, defaultValue, now);
        persist(toggle);
        return toggle;
      }

      @Override
      public Toggle caseSome(Toggle toggle) {
        return toggle;
      }
    });
  }

  @Override
  public Toggle setOrCreate(String name, boolean value, DateTime now) {
    return getByName(name).visit(new OptionVisitor<Toggle, Toggle>() {
      @Override
      public Toggle caseNone() {
        Toggle toggle = new Toggle(name, value, now);
        persist(toggle);
        return toggle;
      }

      @Override
      public Toggle caseSome(Toggle toggle) {
        toggle.setState(value, now);
        return toggle;
      }
    });
  }

}
