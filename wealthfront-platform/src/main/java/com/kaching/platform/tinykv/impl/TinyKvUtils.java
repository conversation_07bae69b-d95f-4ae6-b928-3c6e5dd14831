package com.kaching.platform.tinykv.impl;

import static com.google.common.base.Preconditions.checkArgument;

import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.google.common.base.Strings;
import com.google.common.escape.CharEscaperBuilder;
import com.google.common.escape.Escaper;
import com.google.common.hash.Hashing;
import com.google.common.primitives.UnsignedInteger;
import com.kaching.platform.tinykv.TinyKvStoreId;

public class TinyKvUtils {

  private static final Escaper LIKE_ESCAPER = new CharEscaperBuilder()
      .addEscape('%', "\\%")
      .addEscape('_', "\\_")
      .addEscape('\\', "\\\\")
      .toEscaper();

  static String placeholders(int numColumns, int numRows) {
    checkArgument(numColumns > 0, "numColumns must be positive");
    checkArgument(numRows > 0, "numRows must be positive");
    String placeholder = IntStream.range(0, numColumns).mapToObj(i -> "?").collect(Collectors.joining(",", "(", ")"));
    StringBuilder sb = new StringBuilder((placeholder.length() + 2) * numRows);
    sb.append(Strings.repeat(placeholder + ", ", numRows - 1));
    sb.append(placeholder);
    return sb.toString();
  }
  
  static UnsignedInteger calculateHash(TinyKvStoreId storeId, String key, byte[] value) {
    byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
    return UnsignedInteger.fromIntBits(Hashing.crc32().newHasher(Integer.SIZE + Integer.SIZE + keyBytes.length + value.length)
        .putInt(storeId.asInt())
        .putInt(keyBytes.length)
        .putBytes(keyBytes)
        .putBytes(value)
        .hash()
        .asInt());
  }
  
  static UnsignedInteger xorHashes(UnsignedInteger... hashes) {
    int result = 0;
    for (UnsignedInteger hash : hashes) {
      result = result ^ hash.intValue();
    }
    return UnsignedInteger.fromIntBits(result);
  }
  
  static String escapeLikeStatement(String like) {
    return LIKE_ESCAPER.escape(like);
  }
  
}
