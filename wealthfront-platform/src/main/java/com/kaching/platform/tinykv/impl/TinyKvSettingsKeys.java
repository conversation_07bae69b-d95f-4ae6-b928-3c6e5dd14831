package com.kaching.platform.tinykv.impl;

import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvStoreId;

class TinyKvSettingsKeys {

  static String generateStoreSizeKey(TinyKvStoreId storeId) {
    return String.format("StoreSize-%s", storeId);
  }

  static String generateCachedKeysKey(ServiceId serviceId, TinyKvStoreId storeId) {
    return String.format("CachedKeys-%s-%s-gzip", storeId, serviceId);
  }
  
  static String generateReplicationStateKey(TinyKvGroupId groupId) {
    return "ReplicationState-Group-" + groupId.asInt();
  }
  
}
