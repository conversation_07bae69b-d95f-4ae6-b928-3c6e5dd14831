package com.kaching.platform.tinykv.impl.fake;

import static com.wealthfront.util.stream.WFCollectors.entriesToMap;

import java.util.Map;

import com.kaching.platform.tinykv.TinyKvBulkWriter;
import com.kaching.platform.tinykv.TinyKvStore;
import com.kaching.platform.util.WrappedBytes;

public class FakeTinyKvBulkWriter<C extends TinyKvStore<K, V>, K, V> implements TinyKvBulkWriter<C, K, V> {
  
  private final C config;
  private final FakeTinyKvDatabase database;

  public FakeTinyKvBulkWriter(C config, FakeTinyKvDatabase database) {
    this.config = config;
    this.database = database;
  }

  @Override
  public void replaceAll(String progressStageName, Map<K, V> values) {
    Map<String, WrappedBytes> serialized = values.entrySet()
        .stream()
        .map(e -> Map.entry(config.serializeKey(e.getKey()), config.serializeValue(e.getValue())))
        .collect(entriesToMap());
    database.replaceAll(config.getName().getStoreId(), serialized);
    database.commit();
  }
  
}
