package com.kaching.platform.tinykv.impl;

import java.util.List;

import com.google.common.collect.Range;
import com.google.inject.ImplementedBy;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvLogId;

@ImplementedBy(TinyKvGroupCacheInvalidatorImpl.class)
public interface TinyKvGroupCacheInvalidator {

  void invalidateCaches(TinyKvGroupId groupId, Range<TinyKvLogId> range, List<StoreAndKey> keys);
  
}
