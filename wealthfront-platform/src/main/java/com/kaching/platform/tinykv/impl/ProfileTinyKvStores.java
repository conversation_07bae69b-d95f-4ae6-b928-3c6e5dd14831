package com.kaching.platform.tinykv.impl;

import java.util.Map;

import com.google.inject.Inject;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.tinykv.TinyKvStoreId;

public class ProfileTinyKvStores extends AbstractQuery<Unit> {
  
  @Inject RetryingTransacter transacter;
  @Inject IsTinyKvAdminTaskRunner isAdminTaskRunner;

  @Override
  public Unit process() {
    Map<TinyKvStoreId, Integer> storeSizes = transacter.execute(new WithReadOnlySessionExpression<>() {

      @Inject TinyKvRawReader rawReader;

      @Override
      public Map<TinyKvStoreId, Integer> run(DbSession session) {
        return rawReader.getStoreSizes(isAdminTaskRunner.getStoresThisServiceIsAdminFor());
      }

    });
    transacter.execute(new WithSession() {
      
      @Inject TinyKvStoreSizeFetcher storeSizeFetcher;
      
      @Override
      public void run(DbSession session) {
        for (var entry : storeSizes.entrySet()) {
          storeSizeFetcher.setSize(entry.getKey(), entry.getValue());
        }
      }
      
    });
    return Unit.unit;
  }
  
}
