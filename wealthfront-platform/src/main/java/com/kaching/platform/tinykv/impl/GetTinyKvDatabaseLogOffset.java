package com.kaching.platform.tinykv.impl;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvLogId;

public class GetTinyKvDatabaseLogOffset extends AbstractQuery<Option<TinyKvLogId>> {
  
  private final TinyKvGroupId groupId;

  public GetTinyKvDatabaseLogOffset(TinyKvGroupId groupId) {
    this.groupId = groupId;
  }

  @Inject Transacter transacter;

  @Override
  public Option<TinyKvLogId> process() {
    return transacter.execute(new WithReadOnlySessionExpression<>() {

      @Inject TinyKvRawReader reader;

      @Override
      public Option<TinyKvLogId> run(DbSession session) {
        return reader.getOffset(groupId);
      }

    });
  }
  
}
