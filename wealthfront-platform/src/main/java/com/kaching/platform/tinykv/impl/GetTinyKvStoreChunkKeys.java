package com.kaching.platform.tinykv.impl;

import java.util.Map;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.tinykv.TinyKvStoreId;
import com.kaching.platform.util.Ranges;

public class GetTinyKvStoreChunkKeys extends AbstractQuery<Map<String, Integer>> {

  private final TinyKvStoreId storeId;
  private final Option<String> keyFromIncl;
  private final Option<String> keyToExcl;

  public GetTinyKvStoreChunkKeys(TinyKvStoreId storeId, Option<String> keyFromIncl, Option<String> keyToExcl) {
    this.storeId = storeId;
    this.keyFromIncl = keyFromIncl;
    this.keyToExcl = keyToExcl;
  }

  @Inject Transacter transacter;

  @Override
  public Map<String, Integer> process() {
    return transacter.execute(new WithReadOnlySessionExpression<Map<String, Integer>>() {
      @Inject TinyKvRawReader reader;
      @Override
      public Map<String, Integer> run(DbSession session) {
        return reader.getHashes(storeId, Ranges.fromInclExclBounds(keyFromIncl, keyToExcl));
      }
    });
  }
  
}
