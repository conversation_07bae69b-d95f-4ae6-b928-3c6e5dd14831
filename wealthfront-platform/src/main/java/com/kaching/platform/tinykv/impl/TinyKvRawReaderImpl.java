package com.kaching.platform.tinykv.impl;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.wealthfront.util.time.DateTimeZones.ET;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.joda.time.DateTime;

import com.google.common.collect.BoundType;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Range;
import com.google.common.primitives.UnsignedInts;
import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.tinykv.TinyKvGroupId;
import com.kaching.platform.tinykv.TinyKvLogId;
import com.kaching.platform.tinykv.TinyKvLogOffset;
import com.kaching.platform.tinykv.TinyKvStoreId;
import com.kaching.platform.util.WrappedBytes;
import com.kaching.util.functional.Pointer;
import com.kaching.util.functional.Tuple3;

public class TinyKvRawReaderImpl implements TinyKvRawReader {
  
  @Inject DbSession session;

  @Override
  public Map<String, byte[]> get(TinyKvStoreId storeId, Collection<String> keys) {
    if (keys.isEmpty()) {
      return ImmutableMap.of();
    }
    ImmutableMap.Builder<String, byte[]> builder = ImmutableMap.builder();
    String sql = "SELECT store_key, value FROM tinykv_entries WHERE store_id = ? AND store_key IN (" + TinyKvUtils.placeholders(1, keys.size()) + ")";
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        int i = 1;
        statement.setLong(i++, storeId.getId());
        for (String key : keys) {
          statement.setString(i++, key);
        }
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            builder.put(resultSet.getString(1), resultSet.getBytes(2));
          }
        }
      }
    });
    return builder.build();
  }

  @Override
  public List<StoreAndKey> getKeysFromLogs(TinyKvGroupId groupId, Range<TinyKvLogId> logIdRange) {
    ImmutableList.Builder<StoreAndKey> builder = ImmutableList.builder();
    Range<TinyKvLogId> canonical = logIdRange.canonical(TinyKvLogId.DISCRETE_DOMAIN);
    String sql = "SELECT store_id, store_key FROM tinykv_logs WHERE group_id = ? AND log_id >= ? AND log_id < ?";
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        statement.setLong(1, groupId.getId());
        statement.setLong(2, canonical.lowerEndpoint().getId());
        statement.setLong(3, canonical.upperEndpoint().getId());
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            builder.add(new StoreAndKey(
                new TinyKvStoreId(resultSet.getInt(1)),
                resultSet.getString(2)
            ));
          }
        }
      }
    });
    return builder.build();
  }

  @Override
  public Option<TinyKvLogList> getLogs(TinyKvGroupId groupId, TinyKvLogId fromIncl, TinyKvLogId toExcl) {
    checkArgument(fromIncl.compareTo(toExcl) < 0, "fromId must be less than toId: %s, %s", fromIncl, toExcl);
    ImmutableList.Builder<TinyKvLog> builder = ImmutableList.builderWithExpectedSize((int) (toExcl.getId() - fromIncl.getId()));
    String sql = "SELECT log_id, store_id, store_key, is_delete, entered_at, value FROM tinykv_logs WHERE group_id = ? AND log_id >= ? AND log_id < ?";
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        statement.setLong(1, groupId.getId());
        statement.setLong(2, fromIncl.getId());
        statement.setLong(3, toExcl.getId());
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            builder.add(new TinyKvLog(
                new TinyKvLogId(resultSet.getLong(1)),
                groupId,
                new TinyKvStoreId(resultSet.getInt(2)),
                resultSet.getString(3),
                resultSet.getBoolean(4), 
                new DateTime(resultSet.getTimestamp(5).toInstant().toEpochMilli(), ET),
                Option.of(resultSet.getBytes(6)).transform(WrappedBytes::wrap)
            ));
          }
        }
      }
    });
    List<TinyKvLog> list = builder.build();
    if (list.isEmpty()) {
      return Option.none();
    }
    return Option.some(TinyKvLogList.fromLogs(list));
  }
  
  @Override
  public List<TinyKvLogOffset> getAllOffsets() {
    ImmutableList.Builder<TinyKvLogOffset> builder = ImmutableList.builder();
    String sql = "SELECT group_id, last_log_id, updated_at FROM tinykv_log_offsets";
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            builder.add(new TinyKvLogOffset(
                new TinyKvGroupId(resultSet.getInt(1)),
                new TinyKvLogId(resultSet.getLong(2)),
                new DateTime(resultSet.getTimestamp(3).toInstant().toEpochMilli(), ET)
            ));
          }
        }
      }
    });
    return builder.build();
  }

  @Override
  public Option<TinyKvLogId> getOffset(TinyKvGroupId groupId) {
    String sql = "SELECT last_log_id FROM tinykv_log_offsets WHERE group_id = ?";
    Pointer<TinyKvLogId> result = new Pointer<>();
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        statement.setLong(1, groupId.getId());
        try (ResultSet resultSet = statement.executeQuery()) {
          if (resultSet.next()) {
            result.set(new TinyKvLogId(resultSet.getLong(1)));
            checkArgument(!resultSet.next(), "Expected at most one row, got more");
          }
        }
      }
    });
    return result.getOption();
  }

  @Override
  public List<String> getKeys(TinyKvStoreId storeId, Range<String> keyRange, Option<Integer> maybeLimit) {
    return getKeysWithOrder(storeId, keyRange, true, maybeLimit);
  }

  @Override
  public List<String> getKeysWithPrefix(TinyKvStoreId storeId, String prefix) {
    checkArgument(!prefix.isEmpty(), "prefix must not be empty");
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    String sql = "SELECT store_key FROM tinykv_entries WHERE store_id = ? AND store_key LIKE ?";
    String like = TinyKvUtils.escapeLikeStatement(prefix) + "%";
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        statement.setLong(1, storeId.getId());
        statement.setString(2, like);
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            builder.add(resultSet.getString(1));
          }
        }
      }
    });
    return builder.build();
  }

  @Override
  public Map<String, Integer> getHashes(TinyKvStoreId storeId, Range<String> keyRange) {
    ImmutableMap.Builder<String, Integer> builder = ImmutableMap.builder();
    String sql = "SELECT store_key, hash FROM tinykv_entries WHERE store_id = ? " + andWhereKeyInRange(keyRange);
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        int i = 1;
        statement.setInt(i++, storeId.getId());
        if (keyRange.hasLowerBound()) {
          statement.setString(i++, keyRange.lowerEndpoint());
        }
        if (keyRange.hasUpperBound()) {
          statement.setString(i++, keyRange.upperEndpoint());
        }
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            builder.put(resultSet.getString(1), UnsignedInts.checkedCast(resultSet.getLong(2)));
          }
        }
      }
    });
    return builder.build();
  }
  
  @Override
  public TinyKvReconChunk getReconChunk(TinyKvStoreId storeId, Range<String> keyRange) {
    String sql = "SELECT bit_xor(hash), count(*) FROM tinykv_entries WHERE store_id = ? " + andWhereKeyInRange(keyRange);
    Pointer<TinyKvReconChunk> result = new Pointer<>();
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        int i = 1;
        statement.setInt(i++, storeId.getId());
        if (keyRange.hasLowerBound()) {
          statement.setString(i++, keyRange.lowerEndpoint());
        }
        if (keyRange.hasUpperBound()) {
          statement.setString(i++, keyRange.upperEndpoint());
        }
        try (ResultSet resultSet = statement.executeQuery()) {
          if (resultSet.next()) {
            result.set(new TinyKvReconChunk(keyRange, UnsignedInts.checkedCast(resultSet.getLong(1)), Option.some(resultSet.getInt(2))));
            checkArgument(!resultSet.next(), "Expected at most one row, got more");
          }
        }
      }
    });
    return checkNotNull(result.get());
  }

  @Override
  public List<TinyKvReconChunk> getReconChunks(TinyKvStoreId storeId, Range<String> keyRange, int chunkSize) {
    checkArgument(chunkSize > 0, "chunkSize must be positive: %s", chunkSize);
    String sql = 
        " WITH all_rows AS (" +
        "   SELECT store_key, hash, @rownum := @rownum + 1 AS rank" +
        "   FROM tinykv_entries, (SELECT @rownum := -1) r WHERE store_id = ? " +
            andWhereKeyInRange(keyRange) +
        "   ORDER BY store_id, store_key " +
        " ) select min(store_key), bit_xor(hash), count(*) " +
        " FROM all_rows " +
        " GROUP BY (rank DIV ?) " +
        " ORDER BY 1";
    List<Tuple3<String, Integer, Integer>> rows = new ArrayList<>();
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        int i = 1;
        statement.setInt(i++, storeId.getId());
        if (keyRange.hasLowerBound()) {
          statement.setString(i++, keyRange.lowerEndpoint());
        }
        if (keyRange.hasUpperBound()) {
          statement.setString(i++, keyRange.upperEndpoint());
        }
        statement.setInt(i++, chunkSize);
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            rows.add(Tuple3.of(resultSet.getString(1), UnsignedInts.checkedCast(resultSet.getLong(2)), resultSet.getInt(3)));
          }
        }
      }
    });
    if (rows.isEmpty()) {
      return ImmutableList.of(new TinyKvReconChunk(keyRange, 0, Option.some(0)));
    }
    ImmutableList.Builder<TinyKvReconChunk> builder = ImmutableList.builder();
    if (!keyRange.hasLowerBound()) {
      builder.add(new TinyKvReconChunk(Range.lessThan(rows.get(0)._1), 0, Option.some(0))); 
    }
    for (int i = 0; i < rows.size(); i++) {
      Tuple3<String, Integer, Integer> row = rows.get(i);
      if (i == rows.size() - 1) {
        builder.add(new TinyKvReconChunk(keyRange.intersection(Range.atLeast(row._1)), row._2, Option.some(row._3)));
      } else {
        Tuple3<String, Integer, Integer> nextRow = rows.get(i + 1);
        builder.add(new TinyKvReconChunk(Range.closedOpen(row._1, nextRow._1), row._2, Option.some(row._3)));
      }
    }
    return builder.build();
  }

  @Override
  public List<String> getFirstKeys(TinyKvStoreId storeId, int count) {
    return getKeysWithOrder(storeId, Range.all(), true, Option.some(count));
  }

  @Override
  public List<String> getLastKeys(TinyKvStoreId storeId, int count) {
    return getKeysWithOrder(storeId, Range.all(), false, Option.some(count));
  }

  @Override
  public Option<byte[]> getSettings(String key) {
    String sql = "SELECT value FROM tinykv_settings WHERE settings_key = ?";
    Pointer<byte[]> result = new Pointer<>();
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        statement.setString(1, key);
        try (ResultSet resultSet = statement.executeQuery()) {
          if (resultSet.next()) {
            result.set(resultSet.getBytes(1));
            checkArgument(!resultSet.next(), "Expected at most one row, got more");
          }
        }
      }
    });
    return result.getOption();
  }
  
  @Override
  public Map<TinyKvStoreId, Integer> getStoreSizes(Set<TinyKvStoreId> storeIds) {
    if (storeIds.isEmpty()) {
      return ImmutableMap.of();
    }
    String sql = """
    SELECT store_id, count(*)
      FROM tinykv_entries
      WHERE store_id IN (%s)
      GROUP BY store_id
      ORDER BY store_id
    """.formatted(TinyKvUtils.placeholders(1, storeIds.size()));
    ImmutableMap.Builder<TinyKvStoreId, Integer> result = ImmutableMap.builder();
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        int i = 1;
        for (TinyKvStoreId storeId : storeIds) {
          statement.setInt(i++, storeId.getId());
        }
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            result.put(new TinyKvStoreId(resultSet.getInt(1)), resultSet.getInt(2));
          }
        }
      }
    });
    return result.build();
  }

  private List<String> getKeysWithOrder(TinyKvStoreId storeId, Range<String> range, boolean ascending, Option<Integer> maybeCount) {
    String sql = String.format(
    """
    SELECT store_key
    FROM tinykv_entries
    WHERE store_id = ?
    %s
    ORDER BY store_key %s
    %s
    """, 
        andWhereKeyInRange(range),
        ascending ? "ASC" : "DESC",
        maybeCount.transform(unused -> "LIMIT ?").getOrElse("")
    );
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    session.getSession().doWork(connection -> {
      try (PreparedStatement statement = connection.prepareStatement(sql)) {
        int i = 1;
        statement.setInt(i++, storeId.getId());
        if (range.hasLowerBound()) {
          statement.setString(i++, range.lowerEndpoint());
        }
        if (range.hasUpperBound()) {
          statement.setString(i++, range.upperEndpoint());
        }
        for (int count : maybeCount) {
          statement.setInt(i++, count); 
        }
        try (ResultSet resultSet = statement.executeQuery()) {
          while (resultSet.next()) {
            builder.add(resultSet.getString(1));
          }
        }
      }
    });
    return builder.build();
  }

  private static String andWhereKeyInRange(Range<String> keyRange) {
    String rangeStatement = "";
    if (keyRange.hasLowerBound()) {
      if (keyRange.lowerBoundType() == BoundType.CLOSED) {
        rangeStatement += "AND store_key >= ? ";
      } else {
        rangeStatement += "AND store_key > ? ";
      }
    }
    if (keyRange.hasUpperBound()) {
      if (keyRange.upperBoundType() == BoundType.CLOSED) {
        rangeStatement += "AND store_key <= ? ";
      } else {
        rangeStatement += "AND store_key < ? ";
      }
    }
    return rangeStatement;
  }

}
