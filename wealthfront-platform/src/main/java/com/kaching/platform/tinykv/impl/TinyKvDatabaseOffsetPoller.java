package com.kaching.platform.tinykv.impl;

import java.util.List;

import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.LocalAnnouncement;
import com.kaching.platform.guice.MutableSingleton;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.tinykv.TinyKvLogOffset;
import com.kaching.util.BackoffSleeperCreator;
import com.kaching.util.BackoffSleeperCreator.BackoffSleeper;
import com.kaching.util.RandomSleeper;
import com.kaching.util.UncheckedInterruptedException;

@MutableSingleton
public class TinyKvDatabaseOffsetPoller implements Runnable {
  
  private static final Log log = Log.getLog(TinyKvDatabaseOffsetPoller.class);

  private static final Duration UNANNOUNCED_POLL_DB_SLEEP = Duration.standardSeconds(2);
  private static final Duration ANNOUNCED_POLL_DB_SLEEP = Duration.standardSeconds(15);
  
  @Inject RetryingTransacter transacter;
  @Inject RandomSleeper sleeper;
  @Inject StackTraceMonitor stm;
  @Inject AllTinyKvLogWorkers allWorkers;
  @Inject BackoffSleeperCreator sleeperCreator;
  @Inject TinyKvReadCache readCache;
  @Inject LocalAnnouncement localAnnouncement;
  
  @Override
  public void run() {
    BackoffSleeper exceptionSleeper = sleeperCreator.createWithMillisSequence(5_000, 30_000, 120_000);
    while (true) {
      try {
        runDbPollerOnce();
        if (localAnnouncement.isAnnounced()) {
          sleeper.sleep(ANNOUNCED_POLL_DB_SLEEP);
        } else {
          sleeper.sleep(UNANNOUNCED_POLL_DB_SLEEP);
        }
        exceptionSleeper.reset();
      } catch (UncheckedInterruptedException ex) {
        throw ex;
      } catch (Exception ex) {
        stm.add(ex);
        log.error(ex, "Unexpected exception in runDbPollerOnce. Will continue.");
        exceptionSleeper.sleepRandom();
      }
    }
  }

  @VisibleForTesting
  void runDbPollerOnce() {
    List<TinyKvLogOffset> allOffsets = transacter.execute(new WithReadOnlySessionExpression<>() {
      @Inject TinyKvRawReader rawReader;
      @Override
      public List<TinyKvLogOffset> run(DbSession session) {
        return rawReader.getAllOffsets();
      }
    });
    for (TinyKvLogOffset offset : allOffsets) {
      allWorkers.notifyWorkersOfNewOffset(offset.groupId(), offset.lastLogId());
    }
    readCache.notifyNewOffsetsInDb(allOffsets);
  }
  
}
