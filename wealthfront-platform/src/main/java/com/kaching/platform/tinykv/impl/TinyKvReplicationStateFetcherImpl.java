package com.kaching.platform.tinykv.impl;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;
import com.kaching.platform.guice.MutableSingleton;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.tinykv.TinyKvGroupId;

@MutableSingleton
public class TinyKvReplicationStateFetcherImpl implements TinyKvReplicationStateFetcher {
  
  private static final Duration CACHE_EXPIRATION = Duration.ofSeconds(30);
  
  @Inject RetryingTransacter transacter;
  @Inject StackTraceMonitor stm;
  
  private final LoadingCache<TinyKvGroupId, State> cache = CacheBuilder.newBuilder()
      .expireAfterWrite(CACHE_EXPIRATION)
      .build(new CacheLoader<>() {
        @Override
        public State load(TinyKvGroupId groupId) {
          return getNewState(groupId);
        }
      });

  @Override
  public State getCachedState(TinyKvGroupId groupId) {
    return cache.getUnchecked(groupId);
  }

  @Override
  public void setState(TinyKvGroupId groupId, State state) {
    transacter.execute(new WithSession() {
      @Inject TinyKvRawWriter writer;
      @Override
      public void run(DbSession session) {
        writer.upsertSettingsEntry(0, TinyKvSettingsKeys.generateReplicationStateKey(groupId), state.name().getBytes(UTF_8));
      }
    });
    cache.invalidate(groupId);
  }

  @VisibleForTesting
  State getNewState(TinyKvGroupId groupId) {
    try {
      return transacter.execute(new WithReadOnlySessionExpression<>() {
        @Inject TinyKvRawReader reader;
        
        @Override
        public State run(DbSession session) {
          return reader.getSettings(TinyKvSettingsKeys.generateReplicationStateKey(groupId))
              .transform(bytes -> State.valueOf(new String(bytes, UTF_8)))
              .getOrElse(State.REPLICATION_ON);
        }
      });
    } catch (Exception e) {
      stm.add(e);
      return State.REPLICATION_OFF;
    }
  }
  
}
