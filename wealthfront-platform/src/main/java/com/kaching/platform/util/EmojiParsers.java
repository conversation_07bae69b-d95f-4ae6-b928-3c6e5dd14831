package com.kaching.platform.util;

import static com.vdurmont.emoji.EmojiParser.parseToAliases;
import static com.vdurmont.emoji.EmojiParser.parseToUnicode;

public class EmojiParsers {

  public static String parseToAliasesOrNull(String input) {
    return input == null ? null : parseToAliases(input);
  }

  public static String parseToUnicodeOrNull(String input) {
    return input == null ? null : parseToUnicode(input);
  }

}
