package com.kaching.supermap.client;

import static com.google.common.base.Charsets.UTF_8;

import org.apache.commons.lang.StringUtils;

import com.kaching.DefaultKachingMarshallers;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

import voldemort.serialization.Serializer;

class JsonMarshallerSerializer implements Serializer<Object> {

  private static final String ONLY_JAVA_CLIENTS_SUPPORTED =
      "Only Java clients are supported currently, so the format of the " +
          "schema-info should be: <schema-info>java=foo.Bar</schema-info> " +
          "where foo.Bar is the fully qualified name of the JSON entity.";

  private final Marshaller<Object> marshaller;

  @SuppressWarnings("unchecked")
  JsonMarshallerSerializer(String currentSchemaInfo) {
    try {
      Class<?> clazz = Class.forName(
          getJavaFqnForMessage(currentSchemaInfo));
      this.marshaller = (Marshaller<Object>) DefaultKachingMarshallers.createMarshaller(clazz);
    } catch (ClassNotFoundException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public byte[] toBytes(Object object) {
    return marshaller.marshall(object).toString().getBytes(UTF_8);
  }

  @Override
  public Object toObject(byte[] bytes) {
    return marshaller.unmarshall(Json.fromString(new String(bytes, UTF_8)));
  }

  // copied from voldemort.serialization.protobuf.ProtoBufSerializer
  private String getJavaFqnForMessage(String currentSchemaInfo) {
    if (StringUtils.isEmpty(currentSchemaInfo)) {
      throw new IllegalArgumentException("The type protobuf requires a non-empty schema-info.");
    }

    String[] languagePairs = StringUtils.split(currentSchemaInfo, ',');
    if (languagePairs.length > 1) {
      throw new IllegalArgumentException(ONLY_JAVA_CLIENTS_SUPPORTED);
    }

    String[] javaPair = StringUtils.split(languagePairs[0], '=');
    if (javaPair.length != 2 || !javaPair[0].trim().equals("java")) {
      throw new IllegalArgumentException(ONLY_JAVA_CLIENTS_SUPPORTED);
    }

    return javaPair[1].trim();
  }

}
