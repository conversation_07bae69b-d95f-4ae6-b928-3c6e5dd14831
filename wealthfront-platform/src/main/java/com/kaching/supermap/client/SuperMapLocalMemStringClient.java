package com.kaching.supermap.client;

import java.util.HashMap;
import java.util.Map;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.kaching.platform.common.Option;

import voldemort.versioning.Versioned;

public class SuperMapLocalMemStringClient<V> implements SuperMapClient<String, V> {

  private final Cache<String, V> cache = CacheBuilder.newBuilder().maximumSize(100).build();

  @Override
  public void put(String key, Versioned<V> value) {
    cache.put(key, value.getValue());
  }

  @Override
  public void put(String key, V value) {
    cache.put(key, value);
  }

  @Override
  public Versioned<V> get(String key) {
    return Versioned.value(cache.getIfPresent(key));
  }

  @Override
  public Option<Versioned<V>> getIfExists(String key) {
    V d = cache.getIfPresent(key);
    if (d == null) {
      return Option.none();
    }
    return Option.some(Versioned.value(d));
  }

  @Override
  public Map<String, Versioned<V>> getAll(Iterable<String> keys) {
    Map<String, Versioned<V>> map = new HashMap<>();
    for (String id : keys) {
      map.put(id, get(id));
    }
    return map;
  }

  @Override
  public boolean delete(String key) {
    cache.invalidate(key);
    return true;
  }
}
