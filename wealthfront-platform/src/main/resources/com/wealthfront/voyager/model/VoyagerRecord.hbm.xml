<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping default-access="field" package="com.wealthfront.lending.voyager.model">
  <typedef class="com.kaching.util.types.UserTypeAdapter" name="UserId">
    <param name="type">com.kaching.user.UserId</param>
    <param name="converter">com.kaching.user.UserId$Converter</param>
  </typedef>
  <typedef class="com.kaching.util.types.UserTypeAdapter" name="VoyagerStepId">
    <param name="type">com.wealthfront.voyager.navigation.VoyagerStepId</param>
    <param name="converter">com.wealthfront.voyager.navigation.VoyagerStepId$Converter</param>
  </typedef>
  <typedef class="com.kaching.util.types.EnumTypeAdapter" name="VoyagerType">
    <param name="type">com.wealthfront.voyager.model.VoyagerType</param>
  </typedef>
  <typedef class="com.kaching.util.types.EnumTypeAdapter" name="State">
    <param name="type">com.wealthfront.voyager.model.VoyagerRecord$State</param>
  </typedef>
  <class name="com.wealthfront.voyager.model.VoyagerRecord" table="voyager_records">
    <id name="id" column="id" type="Id">
      <generator class="identity"/>
    </id>
    <property name="userId" column="user_id" type="UserId" not-null="true"/>
    <property name="createdAt" column="created_at" type="DateTime_full" not-null="true"/>
    <property name="voyagerType" column="voyager_type" type="VoyagerType" not-null="true"/>

    <kawala:archived
        name="currentStep"
        entity-name="current_step"
        denormalized="true"
        current-column="current_step"
        parent-column="voyager_record_id"
        version-table="version_voyager_record_current_step">
      <property name="value" type="VoyagerStepId" not-null="true"/>
    </kawala:archived>

    <kawala:archived
        name="state"
        denormalized="true"
        entity-name="voyager_record_state"
        current-column="state"
        parent-column="voyager_record_id"
        version-table="version_voyager_record_state">
      <property name="value" type="State" not-null="true"/>
    </kawala:archived>

  </class>
</hibernate-mapping>