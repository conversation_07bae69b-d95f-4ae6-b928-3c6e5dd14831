package com.demo;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity(
    discriminator = "child1"
)
public class Child1 extends Parent1 {
  @Value(
      nullable = true
  )
  private String child1Value;

  public Child1() {
    // JSON
  }

  public Child1(String grandparentValue, String parent1Value, String child1Value) {
    super(grandparentValue, parent1Value);
    this.child1Value = child1Value;
  }

  @Override
  public <T> T visit(Grandparent.Visitor<T> visitor) {
    return visitor.caseChild1(this);
  }

  public Option<String> getChild1Value() {
    return Option.of(child1Value);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.child1Value);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Child1 that = (Child1) o;
    return super.equals(that) &&
        Objects.equals(child1Value, that.child1Value);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(Child1.class);
    if (isExactClass) {
      sb.append("Child1 {\n");
    }
    sb.append(super.toString());
    sb.append("  child1Value: ").append(child1Value).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withGrandparentValue(getGrandparentValue().getOrNull())
      .withParent1Value(getParent1Value().getOrNull())
      .withChild1Value(getChild1Value().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder extends Grandparent.Builder {
    @Nullable
    private String grandparentValue = null;

    @Nullable
    private String parent1Value = null;

    @Nullable
    private String child1Value = null;

    public Builder withGrandparentValue(@Nullable String grandparentValue) {
      this.grandparentValue = grandparentValue;
      return this;
    }

    public Builder withParent1Value(@Nullable String parent1Value) {
      this.parent1Value = parent1Value;
      return this;
    }

    public Builder withChild1Value(@Nullable String child1Value) {
      this.child1Value = child1Value;
      return this;
    }

    public Child1 build() {
      Child1 obj1 = new Child1(grandparentValue, parent1Value, child1Value);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public Child1 buildForTesting() {
      Child1 obj1 = new Child1(grandparentValue, parent1Value, child1Value);
      return obj1;
    }
  }
}
