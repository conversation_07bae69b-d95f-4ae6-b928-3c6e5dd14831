package com.demo;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.kaching.user.UserId;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;

@ExposeType(ExposeTo.LOCAL)
@Entity
public class TestSecureEntity {
  @Value(
      nullable = true,
      name = "user_id",
      type = UserId.JsonType.class
  )
  private UserId userId;

  @Value(
      nullable = false
  )
  private int someNumber;

  @Value(
      nullable = false
  )
  private boolean someBoolean;

  @Value(
      nullable = false
  )
  private SimpleSecureObject simpleSecureObject;

  @Value(
      nullable = false
  )
  private List<SimpleSecureObject> secureObjectList;

  @Value(
      nullable = false
  )
  private Set<SimpleSecureObject> secureObjectSet;

  @Value(
      nullable = false
  )
  private Map<Integer, SimpleSecureObject> secureObjectMap;

  public TestSecureEntity() {
    // JSON
  }

  public TestSecureEntity(UserId userId, int someNumber, boolean someBoolean,
      SimpleSecureObject simpleSecureObject, List<SimpleSecureObject> secureObjectList,
      Set<SimpleSecureObject> secureObjectSet, Map<Integer, SimpleSecureObject> secureObjectMap) {
    this.userId = userId;
    this.someNumber = someNumber;
    this.someBoolean = someBoolean;
    this.simpleSecureObject = simpleSecureObject;
    this.secureObjectList = secureObjectList;
    this.secureObjectSet = secureObjectSet;
    this.secureObjectMap = secureObjectMap;
  }

  public Option<UserId> getUserId() {
    return Option.of(userId);
  }

  public int getSomeNumber() {
    return someNumber;
  }

  public boolean getSomeBoolean() {
    return someBoolean;
  }

  public SimpleSecureObject getSimpleSecureObject() {
    return simpleSecureObject;
  }

  public List<SimpleSecureObject> getSecureObjectList() {
    return secureObjectList;
  }

  public Set<SimpleSecureObject> getSecureObjectSet() {
    return secureObjectSet;
  }

  public Map<Integer, SimpleSecureObject> getSecureObjectMap() {
    return secureObjectMap;
  }

  public void validate() {
    Preconditions.checkNotNull(simpleSecureObject, "field 'simpleSecureObject' should not be null");
    Preconditions.checkNotNull(secureObjectList, "field 'secureObjectList' should not be null");
    Preconditions.checkNotNull(secureObjectSet, "field 'secureObjectSet' should not be null");
    Preconditions.checkNotNull(secureObjectMap, "field 'secureObjectMap' should not be null");
  }

  @Override
  public int hashCode() {
    return Objects.hash(this.userId, this.someNumber, this.someBoolean, this.simpleSecureObject, this.secureObjectList, this.secureObjectSet, this.secureObjectMap);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestSecureEntity that = (TestSecureEntity) o;
    return Objects.equals(userId, that.userId) &&
        Objects.equals(someNumber, that.someNumber) &&
        Objects.equals(someBoolean, that.someBoolean) &&
        Objects.equals(simpleSecureObject, that.simpleSecureObject) &&
        Objects.equals(secureObjectList, that.secureObjectList) &&
        Objects.equals(secureObjectSet, that.secureObjectSet) &&
        Objects.equals(secureObjectMap, that.secureObjectMap);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestSecureEntity.class);
    if (isExactClass) {
      sb.append("TestSecureEntity {\n");
    }
    sb.append("  userId: ").append(userId).append("\n");
    sb.append("  someNumber: ").append(someNumber).append("\n");
    sb.append("  someBoolean: ").append(someBoolean).append("\n");
    sb.append("  simpleSecureObject: ").append(simpleSecureObject == null ? "null" : simpleSecureObject.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  secureObjectList: ").append(secureObjectList == null ? "null" : secureObjectList.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  secureObjectSet: ").append(secureObjectSet == null ? "null" : secureObjectSet.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  secureObjectMap: ").append(secureObjectMap == null ? "null" : secureObjectMap.toString().replaceAll("\n", "\n  ")).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withUserId(getUserId().getOrNull())
      .withSomeNumber(getSomeNumber())
      .withSomeBoolean(getSomeBoolean())
      .withSimpleSecureObject(getSimpleSecureObject())
      .withSecureObjectList(getSecureObjectList())
      .withSecureObjectSet(getSecureObjectSet())
      .withSecureObjectMap(getSecureObjectMap());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private UserId userId = null;

    @Nonnull
    private Integer someNumber = null;

    @Nonnull
    private Boolean someBoolean = null;

    @Nonnull
    private SimpleSecureObject simpleSecureObject = null;

    @Nonnull
    private List<SimpleSecureObject> secureObjectList = new ArrayList<>();

    @Nonnull
    private Set<SimpleSecureObject> secureObjectSet = new HashSet<>();

    @Nonnull
    private Map<Integer, SimpleSecureObject> secureObjectMap = new HashMap<>();

    public Builder withUserId(@Nullable UserId userId) {
      this.userId = userId;
      return this;
    }

    public Builder withSomeNumber(int someNumber) {
      this.someNumber = someNumber;
      return this;
    }

    public Builder withSomeBoolean(boolean someBoolean) {
      this.someBoolean = someBoolean;
      return this;
    }

    public Builder withSimpleSecureObject(@Nonnull SimpleSecureObject simpleSecureObject) {
      this.simpleSecureObject = simpleSecureObject;
      return this;
    }

    public Builder withSecureObjectList(@Nonnull List<SimpleSecureObject> secureObjectList) {
      this.secureObjectList = secureObjectList;
      return this;
    }

    public Builder withSecureObjectSet(@Nonnull Set<SimpleSecureObject> secureObjectSet) {
      this.secureObjectSet = secureObjectSet;
      return this;
    }

    public Builder withSecureObjectMap(@Nonnull Map<Integer, SimpleSecureObject> secureObjectMap) {
      this.secureObjectMap = secureObjectMap;
      return this;
    }

    public TestSecureEntity build() {
      TestSecureEntity obj1 = new TestSecureEntity(userId, someNumber, someBoolean, simpleSecureObject, secureObjectList, secureObjectSet, secureObjectMap);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestSecureEntity buildForTesting() {
      TestSecureEntity obj1 = new TestSecureEntity(userId, someNumber, someBoolean, simpleSecureObject, secureObjectList, secureObjectSet, secureObjectMap);
      return obj1;
    }
  }
}
