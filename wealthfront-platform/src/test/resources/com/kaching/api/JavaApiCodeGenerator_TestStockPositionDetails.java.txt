package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Nullable;

@Entity(
    discriminator = "stock"
)
public class TestStockPositionDetails extends AbstractPositionDetails {
  @Value(
      optional = true,
      nullable = true
  )
  private List<Map<String, String>> someListMap;

  public TestStockPositionDetails() {
    // JSON
  }

  public TestStockPositionDetails(List<Map<String, String>> someListMap) {
    this.someListMap = someListMap;
  }

  @Override
  public <T> T visit(AbstractPositionDetails.Visitor<T> visitor) {
    return visitor.caseTestStockPositionDetails(this);
  }

  public Option<List<Map<String, String>>> getSomeListMap() {
    return Option.of(someListMap);
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.someListMap);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestStockPositionDetails that = (TestStockPositionDetails) o;
    return super.equals(that) &&
        Objects.equals(someListMap, that.someListMap);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestStockPositionDetails.class);
    if (isExactClass) {
      sb.append("TestStockPositionDetails {\n");
    }
    sb.append(super.toString());
    sb.append("  someListMap: ").append(someListMap == null ? "null" : someListMap.toString().replaceAll("\n", "\n  ")).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withSomeListMap(getSomeListMap().getOrNull());
  }

  public static TestStockPositionDetails toGlobal(
      com.wealthfront.model.TestStockPositionDetails model) {
    return builder()
      .withSomeListMap(model.getSomeListMap())
      .build();
  }

  @Override
  public com.wealthfront.model.TestStockPositionDetails toWFModel() {
    return com.wealthfront.model.TestStockPositionDetails.with()
      .someListMap(this.getSomeListMap().getOrNull())
      .build();
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private List<Map<String, String>> someListMap = new ArrayList<>();

    public Builder withSomeListMap(@Nullable List<Map<String, String>> someListMap) {
      this.someListMap = someListMap;
      return this;
    }

    public TestStockPositionDetails build() {
      TestStockPositionDetails obj1 = new TestStockPositionDetails(someListMap);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestStockPositionDetails buildForTesting() {
      TestStockPositionDetails obj1 = new TestStockPositionDetails(someListMap);
      return obj1;
    }
  }
}
