package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.FRONTEND
)
public enum SomeGrandchildFieldEnum {
  COOL,

  NEAT,

  ALRIGHT,

  GREAT;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case COOL:
        return visitor.caseCool();
      case NEAT:
        return visitor.caseNeat();
      case ALRIGHT:
        return visitor.caseAlright();
      case GREAT:
        return visitor.caseGreat();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseCool();

    T caseNeat();

    T caseAlright();

    T caseGreat();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseCool() {
      return defaultValue.get();
    }

    @Override
    public T caseNeat() {
      return defaultValue.get();
    }

    @Override
    public T caseAlright() {
      return defaultValue.get();
    }

    @Override
    public T caseGreat() {
      return defaultValue.get();
    }
  }
}
