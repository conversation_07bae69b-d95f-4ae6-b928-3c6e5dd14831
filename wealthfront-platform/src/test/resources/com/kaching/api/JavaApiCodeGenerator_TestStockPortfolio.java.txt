package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.kaching.platform.common.Option;
import com.kaching.user.UserId;
import com.kaching.util.Enums;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.voyager.navigation.VoyagerStepId;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;

@Entity(
    discriminator = "stock-portfolio"
)
public class TestStockPortfolio extends TestPortfolio {
  @Value(
      nullable = false
  )
  private UserId userId;

  @Value(
      nullable = false
  )
  private String stockPortfolioExternalId;

  @Value(
      optional = true,
      nullable = true
  )
  private String accountExternalId;

  @Value(
      optional = true,
      nullable = true
  )
  private String accountRequestExternalId;

  @Value(
      nullable = false
  )
  private String displayName;

  @Value(
      optional = true,
      nullable = true
  )
  private List<Long> instrumentIds;

  @Value(
      nullable = false
  )
  private List<List<Long>> nonNullNestedInstrumentIds;

  @Value(
      nullable = false
  )
  private TestStockPortfolioState state;

  @Value(
      optional = true,
      nullable = true
  )
  private BigDecimal marketValue;

  @Value(
      optional = true,
      nullable = true
  )
  private BigDecimal accruedValue;

  @Value(
      optional = true,
      nullable = true
  )
  private BigDecimal residualCash;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean isAccountBlocked;

  @Value(
      optional = true,
      nullable = true
  )
  private List<TestStockPortfolioPosition> positions;

  @Value(
      optional = true,
      nullable = true
  )
  private TestStockPortfolioPosition bestPosition;

  @Value(
      nullable = false
  )
  private TestStockPortfolioPosition nonNullPosition;

  @Value(
      nullable = false
  )
  private Map<String, TestStockPortfolioPosition> preferredPositions;

  @Value(
      optional = true,
      nullable = true
  )
  private Map<String, List<TestStockPortfolioPosition>> mapListPostions;

  @Value(
      optional = true,
      nullable = true
  )
  private Map<String, Map<String, List<TestStockPortfolioPosition>>> mapNestListPostions;

  @Value(
      nullable = false
  )
  private List<List<TestStockPortfolioPosition>> nestedListPostions;

  @Value(
      optional = true,
      nullable = true
  )
  private List<List<List<TestStockPortfolioPosition>>> superNestedPositions;

  @Value(
      optional = true,
      nullable = true
  )
  private TestStockPortfolioPosition[] positionArray;

  @Value(
      nullable = false
  )
  private TestStockPortfolioPosition[] nonNullPositionArray;

  @Value(
      optional = true,
      nullable = true
  )
  private TestStockPortfolioState[] states;

  @Value(
      nullable = false
  )
  private TestStockPortfolioState[] nonNullStates;

  @Value(
      optional = true,
      nullable = true
  )
  private List<TestStockPortfolioState> statesList;

  @Value(
      nullable = false
  )
  private List<TestStockPortfolioState> nonNullStatesList;

  @Value(
      optional = true,
      nullable = true
  )
  private byte[] bytes;

  @Value(
      nullable = false
  )
  private byte[] nonNullBytes;

  @Value(
      optional = true,
      nullable = true
  )
  private List<List<Map<String, TestStockPortfolioPosition>>> crazyNestedListMap;

  @Value(
      optional = true,
      nullable = true
  )
  private List<List<Map<String, TestStockPortfolioPosition>>> nonNullListedMap;

  @Value(
      nullable = false
  )
  private Map<String, Map<String, Long>> nestedLongMap;

  @Value(
      nullable = false
  )
  private Map<String, Map<String, List<TestStockPortfolioState>>> complexMapList;

  @Value(
      optional = true,
      nullable = true
  )
  private Map<String, Map<String, Map<String, TestStockPortfolioPosition>>> threeMaps;

  @Value(
      optional = true,
      nullable = true
  )
  private VoyagerStepId voyagerStepId;

  @Value(
      nullable = false
  )
  private VoyagerStepId nonNullStepId;

  public TestStockPortfolio() {
    // JSON
  }

  public TestStockPortfolio(
      List<List<List<TestStockPortfolioPosition>>> grandFatherNestedListPositions,
      byte[] grandBytes,
      Map<String, Map<String, List<TestStockPortfolioPosition>>> parentalMapListPositions,
      UserId userId, String stockPortfolioExternalId, String accountExternalId,
      String accountRequestExternalId, String displayName, List<Long> instrumentIds,
      List<List<Long>> nonNullNestedInstrumentIds, TestStockPortfolioState state,
      BigDecimal marketValue, BigDecimal accruedValue, BigDecimal residualCash,
      Boolean isAccountBlocked, List<TestStockPortfolioPosition> positions,
      TestStockPortfolioPosition bestPosition, TestStockPortfolioPosition nonNullPosition,
      Map<String, TestStockPortfolioPosition> preferredPositions,
      Map<String, List<TestStockPortfolioPosition>> mapListPostions,
      Map<String, Map<String, List<TestStockPortfolioPosition>>> mapNestListPostions,
      List<List<TestStockPortfolioPosition>> nestedListPostions,
      List<List<List<TestStockPortfolioPosition>>> superNestedPositions,
      TestStockPortfolioPosition[] positionArray, TestStockPortfolioPosition[] nonNullPositionArray,
      TestStockPortfolioState[] states, TestStockPortfolioState[] nonNullStates,
      List<TestStockPortfolioState> statesList, List<TestStockPortfolioState> nonNullStatesList,
      byte[] bytes, byte[] nonNullBytes,
      List<List<Map<String, TestStockPortfolioPosition>>> crazyNestedListMap,
      List<List<Map<String, TestStockPortfolioPosition>>> nonNullListedMap,
      Map<String, Map<String, Long>> nestedLongMap,
      Map<String, Map<String, List<TestStockPortfolioState>>> complexMapList,
      Map<String, Map<String, Map<String, TestStockPortfolioPosition>>> threeMaps,
      VoyagerStepId voyagerStepId, VoyagerStepId nonNullStepId) {
    super(grandFatherNestedListPositions, grandBytes, parentalMapListPositions);
    this.userId = userId;
    this.stockPortfolioExternalId = stockPortfolioExternalId;
    this.accountExternalId = accountExternalId;
    this.accountRequestExternalId = accountRequestExternalId;
    this.displayName = displayName;
    this.instrumentIds = instrumentIds;
    this.nonNullNestedInstrumentIds = nonNullNestedInstrumentIds;
    this.state = state;
    this.marketValue = marketValue;
    this.accruedValue = accruedValue;
    this.residualCash = residualCash;
    this.isAccountBlocked = isAccountBlocked;
    this.positions = positions;
    this.bestPosition = bestPosition;
    this.nonNullPosition = nonNullPosition;
    this.preferredPositions = preferredPositions;
    this.mapListPostions = mapListPostions;
    this.mapNestListPostions = mapNestListPostions;
    this.nestedListPostions = nestedListPostions;
    this.superNestedPositions = superNestedPositions;
    this.positionArray = positionArray;
    this.nonNullPositionArray = nonNullPositionArray;
    this.states = states;
    this.nonNullStates = nonNullStates;
    this.statesList = statesList;
    this.nonNullStatesList = nonNullStatesList;
    this.bytes = bytes;
    this.nonNullBytes = nonNullBytes;
    this.crazyNestedListMap = crazyNestedListMap;
    this.nonNullListedMap = nonNullListedMap;
    this.nestedLongMap = nestedLongMap;
    this.complexMapList = complexMapList;
    this.threeMaps = threeMaps;
    this.voyagerStepId = voyagerStepId;
    this.nonNullStepId = nonNullStepId;
  }

  @Override
  public <T> T visit(TestPortfolio.Visitor<T> visitor) {
    return visitor.caseTestStockPortfolio(this);
  }

  public UserId getUserId() {
    return userId;
  }

  public String getStockPortfolioExternalId() {
    return stockPortfolioExternalId;
  }

  public Option<String> getAccountExternalId() {
    return Option.of(accountExternalId);
  }

  public Option<String> getAccountRequestExternalId() {
    return Option.of(accountRequestExternalId);
  }

  public String getDisplayName() {
    return displayName;
  }

  public Option<List<Long>> getInstrumentIds() {
    return Option.of(instrumentIds);
  }

  public List<List<Long>> getNonNullNestedInstrumentIds() {
    return nonNullNestedInstrumentIds;
  }

  public TestStockPortfolioState getState() {
    return state;
  }

  public Option<BigDecimal> getMarketValue() {
    return Option.of(marketValue);
  }

  public Option<BigDecimal> getAccruedValue() {
    return Option.of(accruedValue);
  }

  public Option<BigDecimal> getResidualCash() {
    return Option.of(residualCash);
  }

  public Option<Boolean> getIsAccountBlocked() {
    return Option.of(isAccountBlocked);
  }

  public Option<List<TestStockPortfolioPosition>> getPositions() {
    return Option.of(positions);
  }

  public Option<TestStockPortfolioPosition> getBestPosition() {
    return Option.of(bestPosition);
  }

  public TestStockPortfolioPosition getNonNullPosition() {
    return nonNullPosition;
  }

  public Map<String, TestStockPortfolioPosition> getPreferredPositions() {
    return preferredPositions;
  }

  public Option<Map<String, List<TestStockPortfolioPosition>>> getMapListPostions() {
    return Option.of(mapListPostions);
  }

  public Option<Map<String, Map<String, List<TestStockPortfolioPosition>>>> getMapNestListPostions(
      ) {
    return Option.of(mapNestListPostions);
  }

  public List<List<TestStockPortfolioPosition>> getNestedListPostions() {
    return nestedListPostions;
  }

  public Option<List<List<List<TestStockPortfolioPosition>>>> getSuperNestedPositions() {
    return Option.of(superNestedPositions);
  }

  public Option<TestStockPortfolioPosition[]> getPositionArray() {
    return Option.of(positionArray);
  }

  public TestStockPortfolioPosition[] getNonNullPositionArray() {
    return nonNullPositionArray;
  }

  public Option<TestStockPortfolioState[]> getStates() {
    return Option.of(states);
  }

  public TestStockPortfolioState[] getNonNullStates() {
    return nonNullStates;
  }

  public Option<List<TestStockPortfolioState>> getStatesList() {
    return Option.of(statesList);
  }

  public List<TestStockPortfolioState> getNonNullStatesList() {
    return nonNullStatesList;
  }

  public Option<byte[]> getBytes() {
    return Option.of(bytes);
  }

  public byte[] getNonNullBytes() {
    return nonNullBytes;
  }

  public Option<List<List<Map<String, TestStockPortfolioPosition>>>> getCrazyNestedListMap() {
    return Option.of(crazyNestedListMap);
  }

  public Option<List<List<Map<String, TestStockPortfolioPosition>>>> getNonNullListedMap() {
    return Option.of(nonNullListedMap);
  }

  public Map<String, Map<String, Long>> getNestedLongMap() {
    return nestedLongMap;
  }

  public Map<String, Map<String, List<TestStockPortfolioState>>> getComplexMapList() {
    return complexMapList;
  }

  public Option<Map<String, Map<String, Map<String, TestStockPortfolioPosition>>>> getThreeMaps() {
    return Option.of(threeMaps);
  }

  public Option<VoyagerStepId> getVoyagerStepId() {
    return Option.of(voyagerStepId);
  }

  public VoyagerStepId getNonNullStepId() {
    return nonNullStepId;
  }

  @Override
  public void validate() {
    super.validate();
    Preconditions.checkNotNull(userId, "field 'userId' should not be null");
    Preconditions.checkNotNull(stockPortfolioExternalId, "field 'stockPortfolioExternalId' should not be null");
    Preconditions.checkNotNull(displayName, "field 'displayName' should not be null");
    Preconditions.checkNotNull(nonNullNestedInstrumentIds, "field 'nonNullNestedInstrumentIds' should not be null");
    Preconditions.checkNotNull(state, "field 'state' should not be null");
    Preconditions.checkNotNull(nonNullPosition, "field 'nonNullPosition' should not be null");
    Preconditions.checkNotNull(preferredPositions, "field 'preferredPositions' should not be null");
    Preconditions.checkNotNull(nestedListPostions, "field 'nestedListPostions' should not be null");
    Preconditions.checkNotNull(nonNullPositionArray, "field 'nonNullPositionArray' should not be null");
    Preconditions.checkNotNull(nonNullStates, "field 'nonNullStates' should not be null");
    Preconditions.checkNotNull(nonNullStatesList, "field 'nonNullStatesList' should not be null");
    Preconditions.checkNotNull(nonNullBytes, "field 'nonNullBytes' should not be null");
    Preconditions.checkNotNull(nestedLongMap, "field 'nestedLongMap' should not be null");
    Preconditions.checkNotNull(complexMapList, "field 'complexMapList' should not be null");
    Preconditions.checkNotNull(nonNullStepId, "field 'nonNullStepId' should not be null");
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.userId, this.stockPortfolioExternalId, this.accountExternalId, this.accountRequestExternalId, this.displayName, this.instrumentIds, this.nonNullNestedInstrumentIds, this.state, this.marketValue, this.accruedValue, this.residualCash, this.isAccountBlocked, this.positions, this.bestPosition, this.nonNullPosition, this.preferredPositions, this.mapListPostions, this.mapNestListPostions, this.nestedListPostions, this.superNestedPositions, Arrays.hashCode(positionArray), Arrays.hashCode(nonNullPositionArray), Arrays.hashCode(states), Arrays.hashCode(nonNullStates), this.statesList, this.nonNullStatesList, Arrays.hashCode(bytes), Arrays.hashCode(nonNullBytes), this.crazyNestedListMap, this.nonNullListedMap, this.nestedLongMap, this.complexMapList, this.threeMaps, this.voyagerStepId, this.nonNullStepId);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestStockPortfolio that = (TestStockPortfolio) o;
    return super.equals(that) &&
        Objects.equals(userId, that.userId) &&
        Objects.equals(stockPortfolioExternalId, that.stockPortfolioExternalId) &&
        Objects.equals(accountExternalId, that.accountExternalId) &&
        Objects.equals(accountRequestExternalId, that.accountRequestExternalId) &&
        Objects.equals(displayName, that.displayName) &&
        Objects.equals(instrumentIds, that.instrumentIds) &&
        Objects.equals(nonNullNestedInstrumentIds, that.nonNullNestedInstrumentIds) &&
        Objects.equals(state, that.state) &&
        Objects.equals(marketValue, that.marketValue) &&
        Objects.equals(accruedValue, that.accruedValue) &&
        Objects.equals(residualCash, that.residualCash) &&
        Objects.equals(isAccountBlocked, that.isAccountBlocked) &&
        Objects.equals(positions, that.positions) &&
        Objects.equals(bestPosition, that.bestPosition) &&
        Objects.equals(nonNullPosition, that.nonNullPosition) &&
        Objects.equals(preferredPositions, that.preferredPositions) &&
        Objects.equals(mapListPostions, that.mapListPostions) &&
        Objects.equals(mapNestListPostions, that.mapNestListPostions) &&
        Objects.equals(nestedListPostions, that.nestedListPostions) &&
        Objects.equals(superNestedPositions, that.superNestedPositions) &&
        Arrays.equals(positionArray, that.positionArray) &&
        Arrays.equals(nonNullPositionArray, that.nonNullPositionArray) &&
        Arrays.equals(states, that.states) &&
        Arrays.equals(nonNullStates, that.nonNullStates) &&
        Objects.equals(statesList, that.statesList) &&
        Objects.equals(nonNullStatesList, that.nonNullStatesList) &&
        Arrays.equals(bytes, that.bytes) &&
        Arrays.equals(nonNullBytes, that.nonNullBytes) &&
        Objects.equals(crazyNestedListMap, that.crazyNestedListMap) &&
        Objects.equals(nonNullListedMap, that.nonNullListedMap) &&
        Objects.equals(nestedLongMap, that.nestedLongMap) &&
        Objects.equals(complexMapList, that.complexMapList) &&
        Objects.equals(threeMaps, that.threeMaps) &&
        Objects.equals(voyagerStepId, that.voyagerStepId) &&
        Objects.equals(nonNullStepId, that.nonNullStepId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestStockPortfolio.class);
    if (isExactClass) {
      sb.append("TestStockPortfolio {\n");
    }
    sb.append(super.toString());
    sb.append("  userId: ").append(userId).append("\n");
    sb.append("  stockPortfolioExternalId: ").append(stockPortfolioExternalId).append("\n");
    sb.append("  accountExternalId: ").append(accountExternalId).append("\n");
    sb.append("  accountRequestExternalId: ").append(accountRequestExternalId).append("\n");
    sb.append("  displayName: ").append(displayName).append("\n");
    sb.append("  instrumentIds: ").append(instrumentIds == null ? "null" : instrumentIds.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nonNullNestedInstrumentIds: ").append(nonNullNestedInstrumentIds == null ? "null" : nonNullNestedInstrumentIds.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  state: ").append(state).append("\n");
    sb.append("  marketValue: ").append(marketValue).append("\n");
    sb.append("  accruedValue: ").append(accruedValue).append("\n");
    sb.append("  residualCash: ").append(residualCash).append("\n");
    sb.append("  isAccountBlocked: ").append(isAccountBlocked).append("\n");
    sb.append("  positions: ").append(positions == null ? "null" : positions.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  bestPosition: ").append(bestPosition == null ? "null" : bestPosition.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nonNullPosition: ").append(nonNullPosition == null ? "null" : nonNullPosition.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  preferredPositions: ").append(preferredPositions == null ? "null" : preferredPositions.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  mapListPostions: ").append(mapListPostions == null ? "null" : mapListPostions.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  mapNestListPostions: ").append(mapNestListPostions == null ? "null" : mapNestListPostions.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nestedListPostions: ").append(nestedListPostions == null ? "null" : nestedListPostions.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  superNestedPositions: ").append(superNestedPositions == null ? "null" : superNestedPositions.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  positionArray: ").append(positionArray == null ? "null" : positionArray.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nonNullPositionArray: ").append(nonNullPositionArray == null ? "null" : nonNullPositionArray.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  states: ").append(states == null ? "null" : states.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nonNullStates: ").append(nonNullStates == null ? "null" : nonNullStates.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  statesList: ").append(statesList == null ? "null" : statesList.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nonNullStatesList: ").append(nonNullStatesList == null ? "null" : nonNullStatesList.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  bytes: ").append(bytes == null ? "null" : bytes.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nonNullBytes: ").append(nonNullBytes == null ? "null" : nonNullBytes.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  crazyNestedListMap: ").append(crazyNestedListMap == null ? "null" : crazyNestedListMap.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nonNullListedMap: ").append(nonNullListedMap == null ? "null" : nonNullListedMap.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  nestedLongMap: ").append(nestedLongMap == null ? "null" : nestedLongMap.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  complexMapList: ").append(complexMapList == null ? "null" : complexMapList.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  threeMaps: ").append(threeMaps == null ? "null" : threeMaps.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  voyagerStepId: ").append(voyagerStepId).append("\n");
    sb.append("  nonNullStepId: ").append(nonNullStepId).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withGrandFatherNestedListPositions(getGrandFatherNestedListPositions().getOrNull())
      .withGrandBytes(getGrandBytes().getOrNull())
      .withParentalMapListPositions(getParentalMapListPositions().getOrNull())
      .withUserId(getUserId())
      .withStockPortfolioExternalId(getStockPortfolioExternalId())
      .withAccountExternalId(getAccountExternalId().getOrNull())
      .withAccountRequestExternalId(getAccountRequestExternalId().getOrNull())
      .withDisplayName(getDisplayName())
      .withInstrumentIds(getInstrumentIds().getOrNull())
      .withNonNullNestedInstrumentIds(getNonNullNestedInstrumentIds())
      .withState(getState())
      .withMarketValue(getMarketValue().getOrNull())
      .withAccruedValue(getAccruedValue().getOrNull())
      .withResidualCash(getResidualCash().getOrNull())
      .withIsAccountBlocked(getIsAccountBlocked().getOrNull())
      .withPositions(getPositions().getOrNull())
      .withBestPosition(getBestPosition().getOrNull())
      .withNonNullPosition(getNonNullPosition())
      .withPreferredPositions(getPreferredPositions())
      .withMapListPostions(getMapListPostions().getOrNull())
      .withMapNestListPostions(getMapNestListPostions().getOrNull())
      .withNestedListPostions(getNestedListPostions())
      .withSuperNestedPositions(getSuperNestedPositions().getOrNull())
      .withPositionArray(getPositionArray().getOrNull())
      .withNonNullPositionArray(getNonNullPositionArray())
      .withStates(getStates().getOrNull())
      .withNonNullStates(getNonNullStates())
      .withStatesList(getStatesList().getOrNull())
      .withNonNullStatesList(getNonNullStatesList())
      .withBytes(getBytes().getOrNull())
      .withNonNullBytes(getNonNullBytes())
      .withCrazyNestedListMap(getCrazyNestedListMap().getOrNull())
      .withNonNullListedMap(getNonNullListedMap().getOrNull())
      .withNestedLongMap(getNestedLongMap())
      .withComplexMapList(getComplexMapList())
      .withThreeMaps(getThreeMaps().getOrNull())
      .withVoyagerStepId(getVoyagerStepId().getOrNull())
      .withNonNullStepId(getNonNullStepId());
  }

  public static TestStockPortfolio toGlobal(com.wealthfront.model.TestStockPortfolio model) {
    return builder()
      .withGrandFatherNestedListPositions(Option.of(model.getGrandFatherNestedListPositions()).transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.stream().map(i2 -> TestStockPortfolioPosition.toGlobal(i2)).collect(Collectors.toList())).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .withGrandBytes(model.getGrandBytes())
      .withParentalMapListPositions(Option.of(model.getParentalMapListPositions()).transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().stream().map(i2 -> TestStockPortfolioPosition.toGlobal(i2)).collect(Collectors.toList())))))).getOrNull())
      .withUserId(new UserId(model.getUserId()))
      .withStockPortfolioExternalId(model.getStockPortfolioExternalId())
      .withAccountExternalId(model.getAccountExternalId())
      .withAccountRequestExternalId(model.getAccountRequestExternalId())
      .withDisplayName(model.getDisplayName())
      .withInstrumentIds(model.getInstrumentIds())
      .withNonNullNestedInstrumentIds(model.getNonNullNestedInstrumentIds())
      .withState(Enums.convert(model.getState(), TestStockPortfolioState.class))
      .withMarketValue(model.getMarketValue())
      .withAccruedValue(model.getAccruedValue())
      .withResidualCash(model.getResidualCash())
      .withIsAccountBlocked(model.getIsAccountBlocked())
      .withPositions(Option.of(model.getPositions()).transform(j -> j.stream().map(i0 -> TestStockPortfolioPosition.toGlobal(i0)).collect(Collectors.toList())).getOrNull())
      .withBestPosition(Option.of(model.getBestPosition()).transform(j -> TestStockPortfolioPosition.toGlobal(j)).getOrNull())
      .withNonNullPosition(TestStockPortfolioPosition.toGlobal(model.getNonNullPosition()))
      .withPreferredPositions(model.getPreferredPositions().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> TestStockPortfolioPosition.toGlobal(i0.getValue()))))
      .withMapListPostions(Option.of(model.getMapListPostions()).transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().stream().map(i1 -> TestStockPortfolioPosition.toGlobal(i1)).collect(Collectors.toList())))).getOrNull())
      .withMapNestListPostions(Option.of(model.getMapNestListPostions()).transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().stream().map(i2 -> TestStockPortfolioPosition.toGlobal(i2)).collect(Collectors.toList())))))).getOrNull())
      .withNestedListPostions(model.getNestedListPostions().stream().map(i0 -> i0.stream().map(i1 -> TestStockPortfolioPosition.toGlobal(i1)).collect(Collectors.toList())).collect(Collectors.toList()))
      .withSuperNestedPositions(Option.of(model.getSuperNestedPositions()).transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.stream().map(i2 -> TestStockPortfolioPosition.toGlobal(i2)).collect(Collectors.toList())).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .withPositionArray(Option.of(model.getPositionArray()).transform(j -> j.stream().map(i0 -> TestStockPortfolioPosition.toGlobal(i0)).toArray(TestStockPortfolioPosition[]::new)).getOrNull())
      .withNonNullPositionArray(model.getNonNullPositionArray().stream().map(i0 -> TestStockPortfolioPosition.toGlobal(i0)).toArray(TestStockPortfolioPosition[]::new))
      .withStates(Option.of(model.getStates()).transform(j -> j.stream().map(i0 -> Enums.convert(i0, TestStockPortfolioState.class)).toArray(TestStockPortfolioState[]::new)).getOrNull())
      .withNonNullStates(model.getNonNullStates().stream().map(i0 -> Enums.convert(i0, TestStockPortfolioState.class)).toArray(TestStockPortfolioState[]::new))
      .withStatesList(Option.of(model.getStatesList()).transform(j -> j.stream().map(i0 -> Enums.convert(i0, TestStockPortfolioState.class)).collect(Collectors.toList())).getOrNull())
      .withNonNullStatesList(model.getNonNullStatesList().stream().map(i0 -> Enums.convert(i0, TestStockPortfolioState.class)).collect(Collectors.toList()))
      .withBytes(model.getBytes())
      .withNonNullBytes(model.getNonNullBytes())
      .withCrazyNestedListMap(Option.of(model.getCrazyNestedListMap()).transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i2 -> TestStockPortfolioPosition.toGlobal(i2.getValue())))).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .withNonNullListedMap(Option.of(model.getNonNullListedMap()).transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i2 -> TestStockPortfolioPosition.toGlobal(i2.getValue())))).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .withNestedLongMap(model.getNestedLongMap())
      .withComplexMapList(model.getComplexMapList().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().stream().map(i2 -> Enums.convert(i2, TestStockPortfolioState.class)).collect(Collectors.toList()))))))
      .withThreeMaps(Option.of(model.getThreeMaps()).transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i2 -> TestStockPortfolioPosition.toGlobal(i2.getValue())))))))).getOrNull())
      .withVoyagerStepId(Option.of(model.getVoyagerStepId()).transform(j -> new VoyagerStepId(j)).getOrNull())
      .withNonNullStepId(new VoyagerStepId(model.getNonNullStepId()))
      .build();
  }

  @Override
  public com.wealthfront.model.TestStockPortfolio toWFModel() {
    return com.wealthfront.model.TestStockPortfolio.with()
      .grandFatherNestedListPositions(this.getGrandFatherNestedListPositions().transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.stream().map(i2 -> i2.toWFModel()).collect(Collectors.toList())).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .grandBytes(this.getGrandBytes().getOrNull())
      .parentalMapListPositions(this.getParentalMapListPositions().transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().stream().map(i2 -> i2.toWFModel()).collect(Collectors.toList())))))).getOrNull())
      .userId(this.getUserId().getId().intValue())
      .stockPortfolioExternalId(this.getStockPortfolioExternalId())
      .accountExternalId(this.getAccountExternalId().getOrNull())
      .accountRequestExternalId(this.getAccountRequestExternalId().getOrNull())
      .displayName(this.getDisplayName())
      .instrumentIds(this.getInstrumentIds().getOrNull())
      .nonNullNestedInstrumentIds(this.getNonNullNestedInstrumentIds())
      .state(Enums.convert(this.getState(), com.wealthfront.model.TestStockPortfolioState.class))
      .marketValue(this.getMarketValue().getOrNull())
      .accruedValue(this.getAccruedValue().getOrNull())
      .residualCash(this.getResidualCash().getOrNull())
      .isAccountBlocked(this.getIsAccountBlocked().getOrNull())
      .positions(this.getPositions().transform(j -> j.stream().map(i0 -> i0.toWFModel()).collect(Collectors.toList())).getOrNull())
      .bestPosition(this.getBestPosition().transform(j -> j.toWFModel()).getOrNull())
      .nonNullPosition(this.getNonNullPosition().toWFModel())
      .preferredPositions(this.getPreferredPositions().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().toWFModel())))
      .mapListPostions(this.getMapListPostions().transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().stream().map(i1 -> i1.toWFModel()).collect(Collectors.toList())))).getOrNull())
      .mapNestListPostions(this.getMapNestListPostions().transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().stream().map(i2 -> i2.toWFModel()).collect(Collectors.toList())))))).getOrNull())
      .nestedListPostions(this.getNestedListPostions().stream().map(i0 -> i0.stream().map(i1 -> i1.toWFModel()).collect(Collectors.toList())).collect(Collectors.toList()))
      .superNestedPositions(this.getSuperNestedPositions().transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.stream().map(i2 -> i2.toWFModel()).collect(Collectors.toList())).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .positionArray(this.getPositionArray().transform(j -> Arrays.stream(j).map(i0 -> i0.toWFModel()).collect(Collectors.toList())).getOrNull())
      .nonNullPositionArray(Arrays.stream(this.getNonNullPositionArray()).map(i0 -> i0.toWFModel()).collect(Collectors.toList()))
      .states(this.getStates().transform(j -> Arrays.stream(j).map(i0 -> Enums.convert(i0, com.wealthfront.model.TestStockPortfolioState.class)).collect(Collectors.toList())).getOrNull())
      .nonNullStates(Arrays.stream(this.getNonNullStates()).map(i0 -> Enums.convert(i0, com.wealthfront.model.TestStockPortfolioState.class)).collect(Collectors.toList()))
      .statesList(this.getStatesList().transform(j -> j.stream().map(i0 -> Enums.convert(i0, com.wealthfront.model.TestStockPortfolioState.class)).collect(Collectors.toList())).getOrNull())
      .nonNullStatesList(this.getNonNullStatesList().stream().map(i0 -> Enums.convert(i0, com.wealthfront.model.TestStockPortfolioState.class)).collect(Collectors.toList()))
      .bytes(this.getBytes().getOrNull())
      .nonNullBytes(this.getNonNullBytes())
      .crazyNestedListMap(this.getCrazyNestedListMap().transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i2 -> i2.getValue().toWFModel()))).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .nonNullListedMap(this.getNonNullListedMap().transform(j -> j.stream().map(i0 -> i0.stream().map(i1 -> i1.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i2 -> i2.getValue().toWFModel()))).collect(Collectors.toList())).collect(Collectors.toList())).getOrNull())
      .nestedLongMap(this.getNestedLongMap())
      .complexMapList(this.getComplexMapList().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().stream().map(i2 -> Enums.convert(i2, com.wealthfront.model.TestStockPortfolioState.class)).collect(Collectors.toList()))))))
      .threeMaps(this.getThreeMaps().transform(j -> j.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i0 -> i0.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i1 -> i1.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i2 -> i2.getValue().toWFModel()))))))).getOrNull())
      .voyagerStepId(this.getVoyagerStepId().transform(j -> j.getId()).getOrNull())
      .nonNullStepId(this.getNonNullStepId().getId())
      .build();
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private List<List<List<TestStockPortfolioPosition>>> grandFatherNestedListPositions = new ArrayList<>();

    @Nullable
    private byte[] grandBytes = null;

    @Nullable
    private Map<String, Map<String, List<TestStockPortfolioPosition>>> parentalMapListPositions = new HashMap<>();

    @Nonnull
    private UserId userId = null;

    @Nonnull
    private String stockPortfolioExternalId = null;

    @Nullable
    private String accountExternalId = null;

    @Nullable
    private String accountRequestExternalId = null;

    @Nonnull
    private String displayName = null;

    @Nullable
    private List<Long> instrumentIds = new ArrayList<>();

    @Nonnull
    private List<List<Long>> nonNullNestedInstrumentIds = new ArrayList<>();

    @Nonnull
    private TestStockPortfolioState state = null;

    @Nullable
    private BigDecimal marketValue = null;

    @Nullable
    private BigDecimal accruedValue = null;

    @Nullable
    private BigDecimal residualCash = null;

    @Nullable
    private Boolean isAccountBlocked = null;

    @Nullable
    private List<TestStockPortfolioPosition> positions = new ArrayList<>();

    @Nullable
    private TestStockPortfolioPosition bestPosition = null;

    @Nonnull
    private TestStockPortfolioPosition nonNullPosition = null;

    @Nonnull
    private Map<String, TestStockPortfolioPosition> preferredPositions = new HashMap<>();

    @Nullable
    private Map<String, List<TestStockPortfolioPosition>> mapListPostions = new HashMap<>();

    @Nullable
    private Map<String, Map<String, List<TestStockPortfolioPosition>>> mapNestListPostions = new HashMap<>();

    @Nonnull
    private List<List<TestStockPortfolioPosition>> nestedListPostions = new ArrayList<>();

    @Nullable
    private List<List<List<TestStockPortfolioPosition>>> superNestedPositions = new ArrayList<>();

    @Nullable
    private TestStockPortfolioPosition[] positionArray = null;

    @Nonnull
    private TestStockPortfolioPosition[] nonNullPositionArray = null;

    @Nullable
    private TestStockPortfolioState[] states = null;

    @Nonnull
    private TestStockPortfolioState[] nonNullStates = null;

    @Nullable
    private List<TestStockPortfolioState> statesList = new ArrayList<>();

    @Nonnull
    private List<TestStockPortfolioState> nonNullStatesList = new ArrayList<>();

    @Nullable
    private byte[] bytes = null;

    @Nonnull
    private byte[] nonNullBytes = null;

    @Nullable
    private List<List<Map<String, TestStockPortfolioPosition>>> crazyNestedListMap = new ArrayList<>();

    @Nullable
    private List<List<Map<String, TestStockPortfolioPosition>>> nonNullListedMap = new ArrayList<>();

    @Nonnull
    private Map<String, Map<String, Long>> nestedLongMap = new HashMap<>();

    @Nonnull
    private Map<String, Map<String, List<TestStockPortfolioState>>> complexMapList = new HashMap<>();

    @Nullable
    private Map<String, Map<String, Map<String, TestStockPortfolioPosition>>> threeMaps = new HashMap<>();

    @Nullable
    private VoyagerStepId voyagerStepId = null;

    @Nonnull
    private VoyagerStepId nonNullStepId = null;

    public Builder withGrandFatherNestedListPositions(
        @Nullable List<List<List<TestStockPortfolioPosition>>> grandFatherNestedListPositions) {
      this.grandFatherNestedListPositions = grandFatherNestedListPositions;
      return this;
    }

    public Builder withGrandBytes(@Nullable byte[] grandBytes) {
      this.grandBytes = grandBytes;
      return this;
    }

    public Builder withParentalMapListPositions(
        @Nullable Map<String, Map<String, List<TestStockPortfolioPosition>>> parentalMapListPositions) {
      this.parentalMapListPositions = parentalMapListPositions;
      return this;
    }

    public Builder withUserId(@Nonnull UserId userId) {
      this.userId = userId;
      return this;
    }

    public Builder withStockPortfolioExternalId(@Nonnull String stockPortfolioExternalId) {
      this.stockPortfolioExternalId = stockPortfolioExternalId;
      return this;
    }

    public Builder withAccountExternalId(@Nullable String accountExternalId) {
      this.accountExternalId = accountExternalId;
      return this;
    }

    public Builder withAccountRequestExternalId(@Nullable String accountRequestExternalId) {
      this.accountRequestExternalId = accountRequestExternalId;
      return this;
    }

    public Builder withDisplayName(@Nonnull String displayName) {
      this.displayName = displayName;
      return this;
    }

    public Builder withInstrumentIds(@Nullable List<Long> instrumentIds) {
      this.instrumentIds = instrumentIds;
      return this;
    }

    public Builder withNonNullNestedInstrumentIds(
        @Nonnull List<List<Long>> nonNullNestedInstrumentIds) {
      this.nonNullNestedInstrumentIds = nonNullNestedInstrumentIds;
      return this;
    }

    public Builder withState(@Nonnull TestStockPortfolioState state) {
      this.state = state;
      return this;
    }

    public Builder withMarketValue(@Nullable BigDecimal marketValue) {
      this.marketValue = marketValue;
      return this;
    }

    public Builder withAccruedValue(@Nullable BigDecimal accruedValue) {
      this.accruedValue = accruedValue;
      return this;
    }

    public Builder withResidualCash(@Nullable BigDecimal residualCash) {
      this.residualCash = residualCash;
      return this;
    }

    public Builder withIsAccountBlocked(@Nullable Boolean isAccountBlocked) {
      this.isAccountBlocked = isAccountBlocked;
      return this;
    }

    public Builder withPositions(@Nullable List<TestStockPortfolioPosition> positions) {
      this.positions = positions;
      return this;
    }

    public Builder withBestPosition(@Nullable TestStockPortfolioPosition bestPosition) {
      this.bestPosition = bestPosition;
      return this;
    }

    public Builder withNonNullPosition(@Nonnull TestStockPortfolioPosition nonNullPosition) {
      this.nonNullPosition = nonNullPosition;
      return this;
    }

    public Builder withPreferredPositions(
        @Nonnull Map<String, TestStockPortfolioPosition> preferredPositions) {
      this.preferredPositions = preferredPositions;
      return this;
    }

    public Builder withMapListPostions(
        @Nullable Map<String, List<TestStockPortfolioPosition>> mapListPostions) {
      this.mapListPostions = mapListPostions;
      return this;
    }

    public Builder withMapNestListPostions(
        @Nullable Map<String, Map<String, List<TestStockPortfolioPosition>>> mapNestListPostions) {
      this.mapNestListPostions = mapNestListPostions;
      return this;
    }

    public Builder withNestedListPostions(
        @Nonnull List<List<TestStockPortfolioPosition>> nestedListPostions) {
      this.nestedListPostions = nestedListPostions;
      return this;
    }

    public Builder withSuperNestedPositions(
        @Nullable List<List<List<TestStockPortfolioPosition>>> superNestedPositions) {
      this.superNestedPositions = superNestedPositions;
      return this;
    }

    public Builder withPositionArray(@Nullable TestStockPortfolioPosition[] positionArray) {
      this.positionArray = positionArray;
      return this;
    }

    public Builder withNonNullPositionArray(
        @Nonnull TestStockPortfolioPosition[] nonNullPositionArray) {
      this.nonNullPositionArray = nonNullPositionArray;
      return this;
    }

    public Builder withStates(@Nullable TestStockPortfolioState[] states) {
      this.states = states;
      return this;
    }

    public Builder withNonNullStates(@Nonnull TestStockPortfolioState[] nonNullStates) {
      this.nonNullStates = nonNullStates;
      return this;
    }

    public Builder withStatesList(@Nullable List<TestStockPortfolioState> statesList) {
      this.statesList = statesList;
      return this;
    }

    public Builder withNonNullStatesList(@Nonnull List<TestStockPortfolioState> nonNullStatesList) {
      this.nonNullStatesList = nonNullStatesList;
      return this;
    }

    public Builder withBytes(@Nullable byte[] bytes) {
      this.bytes = bytes;
      return this;
    }

    public Builder withNonNullBytes(@Nonnull byte[] nonNullBytes) {
      this.nonNullBytes = nonNullBytes;
      return this;
    }

    public Builder withCrazyNestedListMap(
        @Nullable List<List<Map<String, TestStockPortfolioPosition>>> crazyNestedListMap) {
      this.crazyNestedListMap = crazyNestedListMap;
      return this;
    }

    public Builder withNonNullListedMap(
        @Nullable List<List<Map<String, TestStockPortfolioPosition>>> nonNullListedMap) {
      this.nonNullListedMap = nonNullListedMap;
      return this;
    }

    public Builder withNestedLongMap(@Nonnull Map<String, Map<String, Long>> nestedLongMap) {
      this.nestedLongMap = nestedLongMap;
      return this;
    }

    public Builder withComplexMapList(
        @Nonnull Map<String, Map<String, List<TestStockPortfolioState>>> complexMapList) {
      this.complexMapList = complexMapList;
      return this;
    }

    public Builder withThreeMaps(
        @Nullable Map<String, Map<String, Map<String, TestStockPortfolioPosition>>> threeMaps) {
      this.threeMaps = threeMaps;
      return this;
    }

    public Builder withVoyagerStepId(@Nullable VoyagerStepId voyagerStepId) {
      this.voyagerStepId = voyagerStepId;
      return this;
    }

    public Builder withNonNullStepId(@Nonnull VoyagerStepId nonNullStepId) {
      this.nonNullStepId = nonNullStepId;
      return this;
    }

    public TestStockPortfolio build() {
      TestStockPortfolio obj1 = new TestStockPortfolio(grandFatherNestedListPositions, grandBytes, parentalMapListPositions, userId, stockPortfolioExternalId, accountExternalId, accountRequestExternalId, displayName, instrumentIds, nonNullNestedInstrumentIds, state, marketValue, accruedValue, residualCash, isAccountBlocked, positions, bestPosition, nonNullPosition, preferredPositions, mapListPostions, mapNestListPostions, nestedListPostions, superNestedPositions, positionArray, nonNullPositionArray, states, nonNullStates, statesList, nonNullStatesList, bytes, nonNullBytes, crazyNestedListMap, nonNullListedMap, nestedLongMap, complexMapList, threeMaps, voyagerStepId, nonNullStepId);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestStockPortfolio buildForTesting() {
      TestStockPortfolio obj1 = new TestStockPortfolio(grandFatherNestedListPositions, grandBytes, parentalMapListPositions, userId, stockPortfolioExternalId, accountExternalId, accountRequestExternalId, displayName, instrumentIds, nonNullNestedInstrumentIds, state, marketValue, accruedValue, residualCash, isAccountBlocked, positions, bestPosition, nonNullPosition, preferredPositions, mapListPostions, mapNestListPostions, nestedListPostions, superNestedPositions, positionArray, nonNullPositionArray, states, nonNullStates, statesList, nonNullStatesList, bytes, nonNullBytes, crazyNestedListMap, nonNullListedMap, nestedLongMap, complexMapList, threeMaps, voyagerStepId, nonNullStepId);
      return obj1;
    }
  }
}
