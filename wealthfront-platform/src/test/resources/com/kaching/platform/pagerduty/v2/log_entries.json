{"log_entries": [{"id": "RNMQ3B9SAR5SDE0G3WE360ES3C", "type": "notify_log_entry", "summary": "Notified <PERSON> by email", "self": "https://api.pagerduty.com/log_entries/RNMQ3B9SAR5SDE0G3WE360ES3C", "html_url": null, "created_at": "2018-08-19T21:30:49-05:00", "channel": {"type": "auto", "notification": {"type": "email", "address": "<EMAIL>", "status": "success"}}, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "incident": {"incident_number": 67987, "title": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "description": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "created_at": "2018-08-19T21:30:43-05:00", "status": "resolved", "pending_actions": [], "incident_key": null, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "assignments": [], "acknowledgements": [], "last_status_change_at": "2018-08-19T22:30:43-05:00", "last_status_change_by": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "first_trigger_log_entry": {"id": "R17AV0ZMSRIO585QJDN1ASPINO", "type": "trigger_log_entry_reference", "summary": "Triggered through the API", "self": "https://api.pagerduty.com/log_entries/R17AV0ZMSRIO585QJDN1ASPINO", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB/log_entries/R17AV0ZMSRIO585QJDN1ASPINO"}, "escalation_policy": {"id": "PGOEKZS", "type": "escalation_policy_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/escalation_policies/PGOEKZS", "html_url": "https://wealthfront.pagerduty.com/escalation_policies/PGOEKZS"}, "privilege": null, "teams": [], "alert_counts": {"all": 1, "triggered": 0, "resolved": 1}, "impacted_services": [{"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}], "is_mergeable": true, "basic_alert_grouping": null, "alert_grouping": null, "resolve_reason": null, "urgency": "low", "id": "PWQ6YXB", "type": "incident", "summary": "[#67987] CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "self": "https://api.pagerduty.com/incidents/PWQ6YXB", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB"}, "teams": [], "contexts": [], "user": {"id": "PO07Q6A", "type": "user_reference", "summary": "<PERSON>", "self": "https://api.pagerduty.com/users/PO07Q6A", "html_url": "https://wealthfront.pagerduty.com/users/PO07Q6A"}}, {"id": "R159XIJ3RNHCPFAKIAFBEP7O3J", "type": "event_rule_action_log_entry", "summary": "Event rule set urgency to low through the API", "self": "https://api.pagerduty.com/log_entries/R159XIJ3RNHCPFAKIAFBEP7O3J", "html_url": null, "created_at": "2018-08-19T21:30:43-05:00", "agent": {"id": "PDP7CJ6", "type": "nagios_inbound_integration_reference", "summary": "data-nonprod-icinga", "self": "https://api.pagerduty.com/services/P68J6O0/integrations/PDP7CJ6", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0/integrations/PDP7CJ6"}, "channel": {"type": "", "urgency": "low"}, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "incident": {"incident_number": 67987, "title": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "description": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "created_at": "2018-08-19T21:30:43-05:00", "status": "resolved", "pending_actions": [], "incident_key": null, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "assignments": [], "acknowledgements": [], "last_status_change_at": "2018-08-19T22:30:43-05:00", "last_status_change_by": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "first_trigger_log_entry": {"id": "R17AV0ZMSRIO585QJDN1ASPINO", "type": "trigger_log_entry_reference", "summary": "Triggered through the API", "self": "https://api.pagerduty.com/log_entries/R17AV0ZMSRIO585QJDN1ASPINO", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB/log_entries/R17AV0ZMSRIO585QJDN1ASPINO"}, "escalation_policy": {"id": "PGOEKZS", "type": "escalation_policy_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/escalation_policies/PGOEKZS", "html_url": "https://wealthfront.pagerduty.com/escalation_policies/PGOEKZS"}, "privilege": null, "teams": [], "alert_counts": {"all": 1, "triggered": 0, "resolved": 1}, "impacted_services": [{"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}], "is_mergeable": true, "basic_alert_grouping": null, "alert_grouping": null, "resolve_reason": null, "urgency": "low", "id": "PWQ6YXB", "type": "incident", "summary": "[#67987] CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "self": "https://api.pagerduty.com/incidents/PWQ6YXB", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB"}, "teams": [], "contexts": []}, {"id": "R3XO3NY7L7SLXQIQNDXSV96RD8", "type": "link_log_entry", "summary": "Alert \"CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'\" added through the API", "self": "https://api.pagerduty.com/log_entries/R3XO3NY7L7SLXQIQNDXSV96RD8", "html_url": null, "created_at": "2018-08-19T21:30:43-05:00", "agent": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "channel": {"type": "website"}, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "incident": {"incident_number": 67987, "title": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "description": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "created_at": "2018-08-19T21:30:43-05:00", "status": "resolved", "pending_actions": [], "incident_key": null, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "assignments": [], "acknowledgements": [], "last_status_change_at": "2018-08-19T22:30:43-05:00", "last_status_change_by": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "first_trigger_log_entry": {"id": "R17AV0ZMSRIO585QJDN1ASPINO", "type": "trigger_log_entry_reference", "summary": "Triggered through the API", "self": "https://api.pagerduty.com/log_entries/R17AV0ZMSRIO585QJDN1ASPINO", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB/log_entries/R17AV0ZMSRIO585QJDN1ASPINO"}, "escalation_policy": {"id": "PGOEKZS", "type": "escalation_policy_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/escalation_policies/PGOEKZS", "html_url": "https://wealthfront.pagerduty.com/escalation_policies/PGOEKZS"}, "privilege": null, "teams": [], "alert_counts": {"all": 1, "triggered": 0, "resolved": 1}, "impacted_services": [{"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}], "is_mergeable": true, "basic_alert_grouping": null, "alert_grouping": null, "resolve_reason": null, "urgency": "low", "id": "PWQ6YXB", "type": "incident", "summary": "[#67987] CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "self": "https://api.pagerduty.com/incidents/PWQ6YXB", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB"}, "teams": [], "contexts": [], "linked_incident": {"id": "PEWBB7W", "type": "alert_reference", "summary": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "self": "https://api.pagerduty.com/alerts/PEWBB7W", "html_url": "https://wealthfront.pagerduty.com/alerts/PEWBB7W"}}, {"id": "R0L59MVHUE3C0WTD8Q6G5XW8OQ", "type": "assign_log_entry", "summary": "Assigned to <PERSON>", "self": "https://api.pagerduty.com/log_entries/R0L59MVHUE3C0WTD8Q6G5XW8OQ", "html_url": null, "created_at": "2018-08-19T21:30:43-05:00", "agent": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "channel": {"type": "auto"}, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "incident": {"incident_number": 67987, "title": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "description": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "created_at": "2018-08-19T21:30:43-05:00", "status": "resolved", "pending_actions": [], "incident_key": null, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "assignments": [], "acknowledgements": [], "last_status_change_at": "2018-08-19T22:30:43-05:00", "last_status_change_by": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "first_trigger_log_entry": {"id": "R17AV0ZMSRIO585QJDN1ASPINO", "type": "trigger_log_entry_reference", "summary": "Triggered through the API", "self": "https://api.pagerduty.com/log_entries/R17AV0ZMSRIO585QJDN1ASPINO", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB/log_entries/R17AV0ZMSRIO585QJDN1ASPINO"}, "escalation_policy": {"id": "PGOEKZS", "type": "escalation_policy_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/escalation_policies/PGOEKZS", "html_url": "https://wealthfront.pagerduty.com/escalation_policies/PGOEKZS"}, "privilege": null, "teams": [], "alert_counts": {"all": 1, "triggered": 0, "resolved": 1}, "impacted_services": [{"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}], "is_mergeable": true, "basic_alert_grouping": null, "alert_grouping": null, "resolve_reason": null, "urgency": "low", "id": "PWQ6YXB", "type": "incident", "summary": "[#67987] CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "self": "https://api.pagerduty.com/incidents/PWQ6YXB", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB"}, "teams": [], "contexts": [], "assignees": [{"id": "PO07Q6A", "type": "user_reference", "summary": "<PERSON>", "self": "https://api.pagerduty.com/users/PO07Q6A", "html_url": "https://wealthfront.pagerduty.com/users/PO07Q6A"}]}, {"id": "R17AV0ZMSRIO585QJDN1ASPINO", "type": "trigger_log_entry", "summary": "Triggered through the API", "self": "https://api.pagerduty.com/log_entries/R17AV0ZMSRIO585QJDN1ASPINO", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB/log_entries/R17AV0ZMSRIO585QJDN1ASPINO", "created_at": "2018-08-19T21:30:43-05:00", "agent": {"id": "PDP7CJ6", "type": "nagios_inbound_integration_reference", "summary": "data-nonprod-icinga", "self": "https://api.pagerduty.com/services/P68J6O0/integrations/PDP7CJ6", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0/integrations/PDP7CJ6"}, "channel": {"type": "nagios", "summary": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'"}, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "incident": {"incident_number": 67987, "title": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "description": "CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "created_at": "2018-08-19T21:30:43-05:00", "status": "resolved", "pending_actions": [], "incident_key": null, "service": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "assignments": [], "acknowledgements": [], "last_status_change_at": "2018-08-19T22:30:43-05:00", "last_status_change_by": {"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}, "first_trigger_log_entry": {"id": "R17AV0ZMSRIO585QJDN1ASPINO", "type": "trigger_log_entry_reference", "summary": "Triggered through the API", "self": "https://api.pagerduty.com/log_entries/R17AV0ZMSRIO585QJDN1ASPINO", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB/log_entries/R17AV0ZMSRIO585QJDN1ASPINO"}, "escalation_policy": {"id": "PGOEKZS", "type": "escalation_policy_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/escalation_policies/PGOEKZS", "html_url": "https://wealthfront.pagerduty.com/escalation_policies/PGOEKZS"}, "privilege": null, "teams": [], "alert_counts": {"all": 1, "triggered": 0, "resolved": 1}, "impacted_services": [{"id": "P68J6O0", "type": "service_reference", "summary": "Data Non-Prod", "self": "https://api.pagerduty.com/services/P68J6O0", "html_url": "https://wealthfront.pagerduty.com/services/P68J6O0"}], "is_mergeable": true, "basic_alert_grouping": null, "alert_grouping": null, "resolve_reason": null, "urgency": "low", "id": "PWQ6YXB", "type": "incident", "summary": "[#67987] CRITICAL: 'srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7' on 'sv212.wlth.fr'", "self": "https://api.pagerduty.com/incidents/PWQ6YXB", "html_url": "https://wealthfront.pagerduty.com/incidents/PWQ6YXB"}, "teams": [], "contexts": [], "event_details": {"type": "service", "host": "sv212.wlth.fr", "service": "srs_MonitorUnexpectedJobControllerState_data_nonprod_24x7", "service_state": "CRITICAL"}}], "limit": 100, "offset": 0, "more": false, "total": null}