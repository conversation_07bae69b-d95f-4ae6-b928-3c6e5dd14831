<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping default-access="field" package="com.kaching.platform.tinykv.impl">
  <typedef name="TinyKvStoreId" class="com.kaching.util.types.UserTypeAdapter">
    <param name="type">com.kaching.platform.tinykv.TinyKvStoreId</param>
    <param name="converter">com.kaching.platform.tinykv.TinyKvStoreId$Converter</param>
    <param name="sqlType">INTEGER</param>
  </typedef>
  <typedef name="TinyKvGroupId" class="com.kaching.util.types.UserTypeAdapter">
    <param name="type">com.kaching.platform.tinykv.TinyKvGroupId</param>
    <param name="converter">com.kaching.platform.tinykv.TinyKvGroupId$Converter</param>
    <param name="sqlType">INTEGER</param>
  </typedef>
  <typedef name="TinyKvLogId" class="com.kaching.util.types.UserTypeAdapter">
    <param name="type">com.kaching.platform.tinykv.TinyKvLogId</param>
    <param name="converter">com.kaching.platform.tinykv.TinyKvLogId$Converter</param>
    <param name="sqlType">INTEGER</param>
  </typedef>
  <class name="com.kaching.platform.tinykv.impl.TinyKvLogEntity" table="tinykv_logs">
    <composite-id name="key" class="com.kaching.platform.tinykv.impl.TinyKvLogEntity$Key">
      <key-property name="groupId" column="group_id" type="TinyKvGroupId"/>
      <key-property name="logId" column="log_id" type="TinyKvLogId"/>
    </composite-id>
    <property name="storeId" column="store_id" type="TinyKvStoreId" not-null="true" />
    <property name="storeKey" column="store_key" type="string" not-null="true" />
    <property name="isDelete" column="is_delete" type="boolean" not-null="true" />
    <property name="enteredAt" column="entered_at" type="DateTime_full" not-null="true" />
    <property name="value" column="value" type="Binary" not-null="false" />
  </class>
</hibernate-mapping>
