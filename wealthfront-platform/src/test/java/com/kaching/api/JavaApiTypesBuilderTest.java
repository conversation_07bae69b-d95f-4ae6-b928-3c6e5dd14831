package com.kaching.api;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.kaching.platform.queryengine.AbstractQuery;
import com.twolattes.json.Entity;

public class JavaApiTypesBuilderTest {
  
  private enum MyEnum {
    ONE
  }
  
  @Entity
  private static class MyObject {
    
  }
  
  private static class MyQuery extends AbstractQuery<MyObject> {
    
    private final MyEnum val;

    MyQuery(MyEnum val) {
      this.val = val;
    }

    @Override
    public MyObject process() {
      return null;
    }
    
  }
  
  @Test
  public void addQuery_addsReferencedTypes() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(MyQuery.class, builder);
    
    assertEquals(ImmutableSet.of(MyObject.class), builder.getQueryObjectReferences(MyQuery.class));
    assertEquals(ImmutableSet.of(MyEnum.class), builder.getQueryEnumReferences(MyQuery.class));
  }

}