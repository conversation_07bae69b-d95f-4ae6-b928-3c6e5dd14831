package com.kaching.api;

import static com.kaching.api.ExposeTo.FRONTEND;
import static com.kaching.api.ExposeTo.LOCAL;
import static com.kaching.api.GeneratedFileAssertions.assertGeneratedFilesEqual;
import static com.kaching.api.GeneratedFileAssertions.getAllNewFiles;
import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.util.Closer.closeQuietly;
import static com.wealthfront.test.Assert.assertThrows;
import static java.util.stream.Collectors.toSet;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import javax.tools.JavaCompiler;
import javax.tools.StandardJavaFileManager;
import javax.tools.StandardLocation;
import javax.tools.ToolProvider;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.jimfs.Jimfs;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.kaching.fs.ExposeEntitiesClassLoader;
import com.kaching.platform.common.logging.Log;
import com.squareup.javapoet.JavaFile;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

public class JavaJsonEntityGeneratorTest {

  private static final Log log = getLog(JavaJsonEntityGenerator.class);
  private static final JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
  private static final StandardJavaFileManager fileManager = compiler.getStandardFileManager(null, null, null);
  private static FileSystem fakeFs;

  @Before
  public void before() throws IOException {
    fileManager.setLocation(StandardLocation.CLASS_OUTPUT, Collections.singletonList(new File("target/test-classes")));
    log.debug("Do not delete this line, or else Jimfs will fail to initialize");

    fakeFs = Jimfs.newFileSystem();

    Files.createDirectories(fakeFs.getPath("example/target/classes"));
    Set<Path> exampleRepoClassPaths =
        Files.walk(FileSystems.getDefault().getPath("target/test-classes/com/wealthfront/model"))
            .filter(Files::isRegularFile)
            .filter(path -> !path.toString().contains("collisions"))
            .collect(toSet());
    for (Path classPath : exampleRepoClassPaths) {
      Files.copy(classPath, fakeFs.getPath("example/target/classes/" + classPath.getFileName()));
    }
  }

  @After
  public void after() {
    closeQuietly(fakeFs);
  }

  @Test
  public void main_invalidArguments() throws IOException {
    try {
      JavaJsonEntityGenerator.main("buildDirectory1", "buildDirectory2");
      fail();
    } catch (IllegalArgumentException e) {
      assertEquals("exactly one argument required (build directory)", e.getMessage());
    }
  }

  @Test
  public void generate() throws IOException {
    JavaJsonEntityGenerator generator = getGenerator();

    generator.generate();

    List<String> allFiles = getAllNewFiles(fakeFs);
    assertEquals(ImmutableList.of(
        "./src/main/java/com/wealthfront/auto/types/global/AbstractPositionDetails.java",
        "./src/main/java/com/wealthfront/auto/types/global/BookTypeEnum.java",
        "./src/main/java/com/wealthfront/auto/types/global/GenreEnum.java",
        "./src/main/java/com/wealthfront/auto/types/global/NonPolyChild1.java",
        "./src/main/java/com/wealthfront/auto/types/global/NonPolyChild2.java",
        "./src/main/java/com/wealthfront/auto/types/global/NonPolyGrandChild.java",
        "./src/main/java/com/wealthfront/auto/types/global/NonPolyParent.java",
        "./src/main/java/com/wealthfront/auto/types/global/PolymorphicBook.java",
        "./src/main/java/com/wealthfront/auto/types/global/PolymorphicEmptySubclassBook.java",
        "./src/main/java/com/wealthfront/auto/types/global/PolymorphicEncyclopedia.java",
        "./src/main/java/com/wealthfront/auto/types/global/PolymorphicFictionBook.java",
        "./src/main/java/com/wealthfront/auto/types/global/PolymorphicNonFictionBook.java",
        "./src/main/java/com/wealthfront/auto/types/global/SimpleJacksonEntity.java",
        "./src/main/java/com/wealthfront/auto/types/global/SomeGrandchildFieldEnum.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestCashPositionDetails.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestPortfolio.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestStockPortfolio.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestStockPortfolioPosition.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestStockPortfolioState.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestStockPositionDetails.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestWFModel.java"
    ), allFiles);
  }

  @Test
  public void testGenerateFromSingleEntityClass_compareWithExpected() throws IOException {
    List<JavaFile> generatedFiles = getGenerator().generateEntity(TestEntity.class);

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaJsonEntityGenerator_TestEntity.java.txt"
    ), generatedFiles);
  }

  @Test
  public void testGenerateFromMultipleEntityClasses() throws IOException {
    List<JavaFile> generatedFiles = getGenerator().generateEntities(
        Arrays.asList(TestEntity.class, AnotherTestEntity.class));

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaJsonEntityGenerator_AnotherTestEntity.java.txt",
        "JavaJsonEntityGenerator_TestEntity.java.txt"
    ), generatedFiles);
  }

  @Test
  public void testGenerateFromMixedEntityTypes() throws IOException {
    List<JavaFile> generatedFiles = getGenerator().generateEntities(
        Arrays.asList(TestEntity.class, JacksonTestEntity.class));

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaJsonEntityGenerator_JacksonTestEntity.java.txt",
        "JavaJsonEntityGenerator_TestEntity.java.txt"
    ), generatedFiles);
  }

  @Test
  public void testGenerateFromPolymorphicEntityHierarchy() throws IOException {
    List<JavaFile> generatedFiles = getGenerator().generateEntity(ParentEntity.class);

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaJsonEntityGenerator_ChildEntity.java.txt",
        "JavaJsonEntityGenerator_ParentEntity.java.txt"
    ), generatedFiles);
  }

  @Test
  public void testGenerateFromNonEntityClass() {
    assertThrows(RuntimeException.class,
        "1) Cannot determine how to marshall TypeKey<class com.kaching.api.JavaJsonEntityGeneratorTest$NotAnEntity, nullable=true>",
        () -> getGenerator().generateEntity(NotAnEntity.class));
  }

  @ExposeType(FRONTEND)
  @Entity
  public static class TestEntity {

    @Value(nullable = false, optional = false)
    private String testValue;

  }

  @ExposeType(FRONTEND)
  @Entity
  public static class AnotherTestEntity {

    @Value(nullable = false, optional = false)
    private int intValue;

  }

  @ExposeType(LOCAL)
  @Entity(discriminatorName = "type", subclasses = {ChildEntity.class})
  public static class ParentEntity {

    @Value(nullable = false, optional = false)
    private String parentValue;

  }

  @Entity(discriminator = "child")
  public static class ChildEntity extends ParentEntity {

    @Value(nullable = false)
    private String childValue;

  }

  public static class NotAnEntity {
  }

  @ExposeType(value = FRONTEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
  @JsonClassDescription("A Jackson entity for testing")
  public static class JacksonTestEntity {

    @JsonProperty
    private String value;

    @JsonProperty
    private int numericValue;

  }

  private JavaJsonEntityGenerator getGenerator() {
    JavaJsonEntityGenerator generator = new JavaJsonEntityGenerator("example");
    Injector injector = Guice.createInjector();

    generator.fs = fakeFs;
    generator.javaApiCodeGenerator = injector.getInstance(JavaApiCodeGenerator.class);
    generator.exposeEntitiesClassLoader = injector.getInstance(ExposeEntitiesClassLoader.class);
    generator.namespaceTransformer = injector.getInstance(GlobalEntityNamespaceTransformer.class);
    generator.javaApiValidator = injector.getInstance(JavaApiValidator.class);
    return generator;
  }

}
