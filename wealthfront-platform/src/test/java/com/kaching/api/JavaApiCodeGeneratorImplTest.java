package com.kaching.api;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.google.common.collect.MoreCollectors.onlyElement;
import static com.kaching.Author.JACOB_PRESTON;
import static com.kaching.Author.JEFF_HOLT;
import static com.kaching.api.ExposeTo.API_SERVER;
import static com.kaching.api.ExposeTo.BACKEND;
import static com.kaching.api.ExposeTo.FRONTEND;
import static com.kaching.api.ExposeTo.LOCAL;
import static com.kaching.api.ExposeTo.TAOS;
import static com.kaching.api.ExposeType.RewriteNamespace.DO_NOT_COPY;
import static com.kaching.api.ExposeType.RewriteNamespace.QUERY;
import static com.kaching.api.ExposeType.RewriteNamespace.SERVICE;
import static com.kaching.api.GeneratedFileAssertions.assertGeneratedFilesEqual;
import static com.kaching.api.JavaApiCodeGeneratorImpl.getAllTypeFields;
import static com.kaching.api.JavaApiCodeGeneratorImpl.rewriteJavaType;
import static com.kaching.api.JavaApiSchemaIntrospector.introspectTypeOrThrow;
import static com.kaching.api.JavaApiTypesBuilder.TypeKey;
import static com.kaching.entities.Money.money;
import static com.kaching.platform.testing.Mockeries.mockery;
import static com.wealthfront.test.Assert.assertContains;
import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;
import static java.util.Collections.singletonList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Array;
import java.lang.reflect.Constructor;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.annotation.Nullable;
import javax.lang.model.element.Modifier;
import javax.tools.DiagnosticCollector;
import javax.tools.JavaCompiler;
import javax.tools.SimpleJavaFileObject;
import javax.tools.StandardJavaFileManager;
import javax.tools.StandardLocation;
import javax.tools.ToolProvider;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.reflect.TypeLiteral;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.ImmutableSortedMap;
import com.kaching.Author;
import com.kaching.api.CodeGenEntities.NonInlinedNonPolymorphicParent;
import com.kaching.api.JavaApiCodeGeneratorImpl.TypeWithName;
import com.kaching.api.JavaApiTypeNamespaceTransformer.FullyQualifiedTypeName;
import com.kaching.entities.Money;
import com.kaching.entities.types.LocalDateJsonType;
import com.kaching.lending.codegen.CancelTaskInput;
import com.kaching.lending.codegen.EntityWithByteArray;
import com.kaching.lending.codegen.EntityWithJsonNode;
import com.kaching.lending.codegen.EntityWithLocalDate;
import com.kaching.lending.codegen.PolymorphicObject;
import com.kaching.monitor.esp.ResponsibleParty;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.common.types.ParameterizedTypeImpl;
import com.kaching.platform.converters.Instantiate;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.KachingServices.SAND;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.DeprecatedStubQueryConstructorGeneratorImpl;
import com.kaching.platform.queryengine.Owned;
import com.kaching.platform.queryengine.SecureField;
import com.kaching.platform.queryengine.StubQuery;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.user.AbstractBackendResult;
import com.kaching.user.UserId;
import com.kaching.util.id.TestEntity;
import com.squareup.javapoet.AnnotationSpec;
import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.CodeBlock;
import com.squareup.javapoet.FieldSpec;
import com.squareup.javapoet.JavaFile;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.ParameterSpec;
import com.squareup.javapoet.ParameterizedTypeName;
import com.squareup.javapoet.TypeName;
import com.squareup.javapoet.TypeSpec;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.model.JacksonEntity;
import com.wealthfront.model.NonPolyGrandChild;
import com.wealthfront.model.PolymorphicBook;
import com.wealthfront.model.SimpleJacksonEntity;
import com.wealthfront.model.collisions.SimpleJacksonEntityWithEnumCollision;
import com.wealthfront.model.collisions.SimpleJacksonEntityWithObjectCollision;
import com.wealthfront.types.BrokenDataClass;
import com.wealthfront.types.MissingDataFields;
import com.wealthfront.types.TestVoyagerUpdateRequest;
import com.wealthfront.types.TestVoyagerView;
import com.wealthfront.types.definitions.TestStockPortfolio;
import com.wealthfront.types.definitions.TestVoyagerResult;
import com.wealthfront.voyager.navigation.VoyagerRequest;
import com.wealthfront.voyager.navigation.VoyagerStepId;
import com.wealthfront.voyager.ui.View;

public class JavaApiCodeGeneratorImplTest {

  private static final Log log = Log.getLog(JavaApiCodeGeneratorImplTest.class);
  private static final JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
  private static final StandardJavaFileManager fileManager = compiler.getStandardFileManager(null, null, null);

  private final WFMockery mockery = mockery();

  @Before
  public void before() throws IOException {
    fileManager.setLocation(StandardLocation.CLASS_OUTPUT, singletonList(new File("target/classes")));
    log.debug("Do not delete this line, or else FileSystemProvider.installedProviders will fail");
  }

  @ExposeType(FRONTEND)
  private enum MyFrontendEnum {
    ONE,
    TWO_OR_THREE
  }

  @ExposeQuery(FRONTEND)
  private static class FrontendQueryWithEnum extends AbstractQuery<List<MyFrontendEnum>> {

    @SecureField private final List<MyFrontendEnum> value1;
    private final MyFrontendEnum value2;

    FrontendQueryWithEnum(List<MyFrontendEnum> value1, MyFrontendEnum value2) {
      this.value1 = value1;
      this.value2 = value2;
    }

    @Override
    public List<MyFrontendEnum> process() {
      return null;
    }

  }

  @Test
  public void generateFiles_frontendQuery() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(FrontendQueryWithEnum.class, builder);
    Errors errors = new Errors();
    DefaultJavaApiTypeNamespaceTransformer transformer = new DefaultJavaApiTypeNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> files = getGenerator().generateFiles(errors, SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_FrontendQueryWithEnum.java.txt"
    ), files);
    assertJavaFilesCompile(files);
  }

  @ExposeType(LOCAL)
  private enum MyEnum {
    ONE,
    TWO_OR_THREE
  }

  @ExposeQuery(LOCAL)
  private static class QueryWithEnum extends AbstractQuery<List<MyEnum>> {

    @SecureField private final List<MyEnum> value1;
    private final MyEnum value2;

    QueryWithEnum(List<MyEnum> value1, MyEnum value2) {
      this.value1 = value1;
      this.value2 = value2;
    }

    @Override
    public List<MyEnum> process() {
      return null;
    }

  }

  @Test
  public void generateFiles_queryReferencingCopiedEnum() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(QueryWithEnum.class, builder);
    Errors errors = new Errors();
    DefaultJavaApiTypeNamespaceTransformer transformer = new DefaultJavaApiTypeNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> files = getGenerator().generateFiles(errors, SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_QueryWithEnum_1.java.txt"
    ), files);
    assertJavaFilesCompile(files);
  }

  private static class ExampleQueryWithOptionConstructorPrimitiveArgs
      extends AbstractQuery<String> {

    Id<TestEntity> testEntityId;
    Integer integer;
    Option<DateTime> maybeDateTime;
    String anOptionalString;
    boolean aBoolean;
    int aInt;
    String aNullableString;

    @Instantiate
    ExampleQueryWithOptionConstructorPrimitiveArgs(
        Id<TestEntity> testEntityId,
        Integer integer,
        Option<DateTime> maybeDateTime,
        @Optional("hello") String anOptionalString,
        @Optional("false") boolean aBoolean,
        @Optional("35") int aInt,
        @Optional String aNullableString
    ) {
      this.testEntityId = testEntityId;
      this.integer = integer;
      this.maybeDateTime = maybeDateTime;
      this.anOptionalString = anOptionalString;
      this.aBoolean = aBoolean;
      this.aInt = aInt;
      this.aNullableString = aNullableString;
    }

    @Override
    public String process() {
      return "world";
    }

  }

  @Test
  public void generateFiles_queryWithOptionalParamsAndPrimitives() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(ExampleQueryWithOptionConstructorPrimitiveArgs.class, builder);
    Errors errors = new Errors();
    DefaultJavaApiTypeNamespaceTransformer transformer = new DefaultJavaApiTypeNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> files = getGenerator().generateFiles(errors, SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_ExampleQueryWithOptionConstructorPrimitiveArgs.java.txt"
    ), files);
    assertJavaFilesCompile(files);
  }

  private static class TestEntity2 extends TestEntity {}

  private static class ExampleQuery extends AbstractQuery<Map<Id<TestEntity>, List<String>>> {

    private final Id<TestEntity> testEntityId;
    private final Integer integer;
    private final Option<DateTime> maybeDateTime;
    private final Map<Id<TestEntity>, List<String>> map;

    ExampleQuery(
        Id<TestEntity> testEntityId,
        Integer integer,
        Option<DateTime> maybeDateTime,
        Map<Id<TestEntity>, List<String>> map) {
      this.testEntityId = testEntityId;
      this.integer = integer;
      this.maybeDateTime = maybeDateTime;
      this.map = map;
    }

    @Override
    public Map<Id<TestEntity>, List<String>> process() {
      return map;
    }

  }

  @Test
  public void generateFiles_withExampleQuery() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    builder.addTypeMappingOverride(new TypeLiteral<Id<TestEntity>>() {}.getType(), UserId.class);
    JavaApiSchemaIntrospector.introspectQueryOrThrow(ExampleQuery.class, builder);
    Errors errors = new Errors();
    JavaFile file =
        getOnlyElement(getGenerator().generateFiles(errors, SAND.class, builder, new FakeNamespaceTransformer()));
    errors.throwIfHasErrors();

    JavaFile expectedStubQueryJavaFile = JavaFile.builder("com.demo",
            TypeSpec.classBuilder("ExampleQuery")
                .addModifiers(Modifier.PUBLIC)
                .superclass(ParameterizedTypeName.get(
                    ClassName.get(StubQuery.class),
                    ParameterizedTypeName.get(
                        ClassName.get(Map.class),
                        ClassName.get(UserId.class),
                        ParameterizedTypeName.get(List.class, String.class)
                    ),
                    ClassName.get(SAND.class)
                ))
                .addFields(ImmutableList.of(
                    FieldSpec.builder(UserId.class, "testEntityId")
                        .addModifiers(Modifier.PRIVATE, Modifier.FINAL)
                        .build(),
                    FieldSpec.builder(Integer.class, "integer")
                        .addModifiers(Modifier.PRIVATE, Modifier.FINAL)
                        .build(),
                    FieldSpec.builder(new ParameterizedTypeImpl(Option.class, new Type[]{DateTime.class}), "maybeDateTime")
                        .addModifiers(Modifier.PRIVATE, Modifier.FINAL)
                        .build(),
                    FieldSpec.builder(
                            ParameterizedTypeName.get(
                                ClassName.get(Map.class),
                                ClassName.get(UserId.class),
                                ParameterizedTypeName.get(List.class, String.class)
                            ), "map")
                        .addModifiers(Modifier.PRIVATE, Modifier.FINAL)
                        .build()))
                .addMethod(MethodSpec.constructorBuilder()
                    .addModifiers(Modifier.PUBLIC)
                    .addParameters(ImmutableList.of(
                        ParameterSpec.builder(UserId.class, "testEntityId")
                            .build(),
                        ParameterSpec.builder(Integer.class, "integer")
                            .build(),
                        ParameterSpec
                            .builder(new ParameterizedTypeImpl(Option.class, new Type[]{DateTime.class}), "maybeDateTime")
                            .build(),
                        ParameterSpec
                            .builder(
                                ParameterizedTypeName.get(
                                    ClassName.get(Map.class),
                                    ClassName.get(UserId.class),
                                    ParameterizedTypeName.get(List.class, String.class)
                                ), "map")
                            .build()))
                    .addCode(CodeBlock.builder()
                        .addStatement("this.testEntityId = testEntityId")
                        .addStatement("this.integer = integer")
                        .addStatement("this.maybeDateTime = maybeDateTime")
                        .addStatement("this.map = map")
                        .build())
                    .build())
                .addMethods(ImmutableList.of(
                    MethodSpec.methodBuilder("getTestEntityId")
                        .addModifiers(Modifier.PUBLIC)
                        .returns(UserId.class)
                        .addStatement("return testEntityId")
                        .build(),
                    MethodSpec.methodBuilder("getInteger")
                        .addModifiers(Modifier.PUBLIC)
                        .returns(Integer.class)
                        .addStatement("return integer")
                        .build(),
                    MethodSpec.methodBuilder("getMaybeDateTime")
                        .addModifiers(Modifier.PUBLIC)
                        .returns(new ParameterizedTypeImpl(Option.class, new Type[]{DateTime.class}))
                        .addStatement("return maybeDateTime")
                        .build(),
                    MethodSpec.methodBuilder("getMap")
                        .addModifiers(Modifier.PUBLIC)
                        .returns(ParameterizedTypeName.get(
                            ClassName.get(Map.class),
                            ClassName.get(UserId.class),
                            ParameterizedTypeName.get(List.class, String.class)
                        ))
                        .addStatement("return map")
                        .build()
                ))
                .build())
        .skipJavaLangImports(true)
        .build();

    assertEquals(expectedStubQueryJavaFile, file);
  }

  @ResponsibleParty({Author.JACOB_PRESTON, JEFF_HOLT})
  private static class ExampleResponsiblePartyQuery extends AbstractQuery<Boolean> {

    ExampleResponsiblePartyQuery() {}

    @Override
    public Boolean process() {
      return true;
    }

  }

  @Test
  public void generateFiles_withExampleResponsiblePartyQuery() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    builder.addTypeMappingOverride(new TypeLiteral<Id<TestEntity>>() {}.getType(), UserId.class);
    JavaApiSchemaIntrospector.introspectQueryOrThrow(ExampleResponsiblePartyQuery.class, builder);
    Errors errors = new Errors();
    JavaFile file =
        getOnlyElement(getGenerator().generateFiles(errors, SAND.class, builder, new FakeNamespaceTransformer()));
    errors.throwIfHasErrors();

    JavaFile expectedStubQueryJavaFile = JavaFile.builder("com.demo",
            TypeSpec.classBuilder("ExampleResponsiblePartyQuery")
                .addAnnotation(AnnotationSpec.builder(ResponsibleParty.class)
                    .addMember("value", "$T.$L", Author.class, JACOB_PRESTON)
                    .addMember("value", "$T.$L", Author.class, JEFF_HOLT)
                    .build())
                .addModifiers(Modifier.PUBLIC)
                .superclass(ParameterizedTypeName.get(
                    ClassName.get(StubQuery.class),
                    ParameterizedTypeName.get(Boolean.class),
                    ClassName.get(SAND.class)
                ))
                .addMethod(MethodSpec.constructorBuilder()
                    .addModifiers(Modifier.PUBLIC)
                    .build())
                .build())
        .skipJavaLangImports(true)
        .build();

    assertEquals(expectedStubQueryJavaFile, file);
  }

  @Deprecated(since = "2024-10-01", forRemoval = true)
  private static class ExampleDeprecatedQuery extends AbstractQuery<Boolean> {

    ExampleDeprecatedQuery() {}

    @Override
    public Boolean process() {
      return true;
    }

  }

  @Test
  public void generateFiles_withExampleDeprecatedQuery() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    builder.addTypeMappingOverride(new TypeLiteral<Id<TestEntity>>() {}.getType(), UserId.class);
    JavaApiSchemaIntrospector.introspectQueryOrThrow(ExampleDeprecatedQuery.class, builder);
    Errors errors = new Errors();
    JavaFile file =
        getOnlyElement(getGenerator().generateFiles(errors, SAND.class, builder, new FakeNamespaceTransformer()));
    errors.throwIfHasErrors();

    JavaFile expectedStubQueryJavaFile = JavaFile.builder("com.demo",
            TypeSpec.classBuilder("ExampleDeprecatedQuery")
                .addAnnotation(AnnotationSpec.builder(Deprecated.class).build())
                .addModifiers(Modifier.PUBLIC)
                .superclass(ParameterizedTypeName.get(
                    ClassName.get(StubQuery.class),
                    ParameterizedTypeName.get(Boolean.class),
                    ClassName.get(SAND.class)
                ))
                .addMethod(MethodSpec.constructorBuilder()
                    .addModifiers(Modifier.PUBLIC)
                    .build())
                .build())
        .skipJavaLangImports(true)
        .build();

    assertEquals(expectedStubQueryJavaFile, file);
  }

  private enum Enum1 {
    ONE
  }

  private enum Enum2 {
    TWO
  }

  private enum Enum3 {
    THREE
  }

  private enum Enum4 {
    FOUR
  }

  private static class QueryWithEnums extends AbstractQuery<Integer> {

    private final Enum1 enum1;
    private final Enum2 enum2;
    private final Enum3 enum3;
    private final Enum4 enum4;

    QueryWithEnums(Enum1 enum1, Enum2 enum2, Enum3 enum3, Enum4 enum4) {
      this.enum1 = enum1;
      this.enum2 = enum2;
      this.enum3 = enum3;
      this.enum4 = enum4;
    }

    @Override
    public Integer process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withNamespaceCollision_returnsError() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiTypeNamespaceTransformer transformer = mockery.mock(JavaApiTypeNamespaceTransformer.class);
    JavaApiCodeGeneratorImpl generator = getGenerator();
    Errors errors = new Errors();
    JavaApiQuerySchema querySchema = JavaApiSchemaIntrospector.introspectQueryOrThrow(QueryWithEnums.class, builder);

    mockery.checking(new WExpectations() {{
      atLeast(1).of(transformer)
          .resolveEnum(with(SAND.class), with(querySchema), with(new JavaApiType.JavaEnumType(false, Enum1.class)));
      will(returnValue(new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("Enum1"))));

      atLeast(1).of(transformer)
          .resolveEnum(with(SAND.class), with(querySchema), with(new JavaApiType.JavaEnumType(false, Enum2.class)));
      will(returnValue(new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("Enum2"))));

      atLeast(1).of(transformer)
          .resolveEnum(with(SAND.class), with(querySchema), with(new JavaApiType.JavaEnumType(false, Enum3.class)));
      will(returnValue(new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("Enum2"))));

      atLeast(1).of(transformer)
          .resolveEnum(with(SAND.class), with(querySchema), with(new JavaApiType.JavaEnumType(false, Enum4.class)));
      will(returnValue(new FullyQualifiedTypeName("com.kaching", ImmutableList.of("Enum2"))));
    }});

    generator.generateFiles(errors, SAND.class, builder, transformer);
    String err = getOnlyElement(errors.getMessages());
    assertContains("should contain", "Multiple classes resolve to com.wealthfront.Enum2:", err);
    assertContains("should contain", "$Enum2", err);
    assertContains("should contain", "$Enum3", err);
  }

  @Test
  public void groupTypeDefinitionsIntoFiles_justTopLevelClass() {
    TypeWithName topLevelEnum1 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("MyEnum1")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "MyEnum1"))
            .addEnumConstant("ONE")
    );
    TypeWithName topLevelEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("MyEnum2")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "MyEnum2"))
            .addEnumConstant("TWO")
    );
    List<JavaFile> files = getGenerator().groupTypeDefinitionsIntoFiles(ImmutableList.of(topLevelEnum1, topLevelEnum2));
    assertEquals(2, files.size());
    JavaFile enum1File = files.stream()
        .filter(f -> f.typeSpec.name.equals("MyEnum1"))
        .collect(onlyElement());
    assertEmpty(enum1File.typeSpec.typeSpecs);
    JavaFile enum2File = files.stream()
        .filter(f -> f.typeSpec.name.equals("MyEnum2"))
        .collect(onlyElement());
    assertEmpty(enum2File.typeSpec.typeSpecs);
  }

  @Test
  public void groupTypeDefinitionsIntoFiles_combinesExactSameClassTypes() {
    TypeWithName topLevelEnum1 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("MyEnum1")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "MyEnum1"))
            .addEnumConstant("ONE")
    );
    TypeWithName topLevelEnum1point1 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("MyEnum1")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "MyEnum1"))
            .addEnumConstant("ONE")
    );
    List<JavaFile> files =
        getGenerator().groupTypeDefinitionsIntoFiles(ImmutableList.of(topLevelEnum1, topLevelEnum1point1));
    assertEquals(1, files.size());
    JavaFile enum1File = files.stream()
        .filter(f -> f.typeSpec.name.equals("MyEnum1"))
        .collect(onlyElement());
    assertEmpty(enum1File.typeSpec.typeSpecs);
  }

  @Test
  public void groupTypeDefinitionIntoFiles_linkInnerClassToOuterClass() {
    TypeWithName outerEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum"))
            .addEnumConstant("OUTER")
    );
    TypeWithName middleEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "MiddleEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "MiddleEnum"))
            .addEnumConstant("MIDDLE")
    );
    TypeWithName otherMiddleEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "OtherMiddleEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "OtherMiddleEnum"))
            .addEnumConstant("OTHER_MIDDLE")
    );
    TypeWithName innerEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "MiddleEnum", "InnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "MiddleEnum", "InnerEnum"))
            .addEnumConstant("INNER")
    );
    JavaFile file = getOnlyElement(getGenerator().groupTypeDefinitionsIntoFiles(
        ImmutableList.of(middleEnum, innerEnum, outerEnum, otherMiddleEnum)));

    assertEquals("OuterEnum", file.typeSpec.name);
    assertEquals(ImmutableSet.of("OUTER"), file.typeSpec.enumConstants.keySet());
    assertEquals(2, file.typeSpec.typeSpecs.size());

    TypeSpec middleSpec = file.typeSpec.typeSpecs.stream()
        .filter(n -> n.name.equals("MiddleEnum"))
        .collect(onlyElement());
    assertEquals(ImmutableSet.of("MIDDLE"), middleSpec.enumConstants.keySet());

    TypeSpec otherMiddleSpec = file.typeSpec.typeSpecs.stream()
        .filter(n -> n.name.equals("OtherMiddleEnum"))
        .collect(onlyElement());
    assertEquals(ImmutableSet.of("OTHER_MIDDLE"), otherMiddleSpec.enumConstants.keySet());

    TypeSpec innerSpec = getOnlyElement(middleSpec.typeSpecs);
    assertEquals("InnerEnum", innerSpec.name);
    assertEquals(ImmutableSet.of("INNER"), innerSpec.enumConstants.keySet());

    TypeWithName outerEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum"))
            .addEnumConstant("OUTER")
    );
    TypeWithName otherMiddleEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "OtherMiddleEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "OtherMiddleEnum"))
            .addEnumConstant("OTHER_MIDDLE")
    );
    TypeWithName middleEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "MiddleEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "MiddleEnum"))
            .addEnumConstant("MIDDLE")
    );
    TypeWithName innerEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "MiddleEnum", "InnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "MiddleEnum", "InnerEnum"))
            .addEnumConstant("INNER")
    );

    assertEquals(file, getOnlyElement(getGenerator().groupTypeDefinitionsIntoFiles(
        ImmutableList.of(outerEnum2, otherMiddleEnum2, innerEnum2, middleEnum2))));
  }

  @Test
  public void groupTypeDefinitionsIntoFiles_withSkipLevels() {
    TypeWithName outerEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum"))
            .addEnumConstant("OUTER")
    );
    TypeWithName innerEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "MiddleClass", "InnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "MiddleClass", "InnerEnum"))
            .addEnumConstant("INNER")
    );
    JavaFile file =
        getOnlyElement(getGenerator().groupTypeDefinitionsIntoFiles(ImmutableList.of(innerEnum, outerEnum)));

    assertEquals("OuterEnum", file.typeSpec.name);
    assertEquals(ImmutableSet.of("OUTER"), file.typeSpec.enumConstants.keySet());
    assertEquals(1, file.typeSpec.typeSpecs.size());

    TypeSpec autoGeneratedMiddleSpec = file.typeSpec.typeSpecs.stream()
        .filter(n -> n.name.equals("MiddleClass"))
        .collect(onlyElement());
    assertEmpty(autoGeneratedMiddleSpec.methodSpecs);
    assertEquals(ImmutableSet.of(Modifier.STATIC, Modifier.PUBLIC), autoGeneratedMiddleSpec.modifiers);

    TypeSpec innerSpec = getOnlyElement(autoGeneratedMiddleSpec.typeSpecs);
    assertEquals("InnerEnum", innerSpec.name);
    assertEquals(ImmutableSet.of("INNER"), innerSpec.enumConstants.keySet());

    TypeWithName outerEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum"))
            .addEnumConstant("OUTER")
    );
    TypeWithName innerEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterEnum", "MiddleClass", "InnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterEnum", "MiddleClass", "InnerEnum"))
            .addEnumConstant("INNER")
    );

    assertEquals(file,
        getOnlyElement(getGenerator().groupTypeDefinitionsIntoFiles(ImmutableList.of(outerEnum2, innerEnum2))));
  }

  @Test
  public void groupTypeDefinitionsIntoFiles_withNoTopLevelGiven_autoGeneratesEmpty() {
    TypeWithName innerEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterClass", "MiddleClass", "InnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterClass", "MiddleClass", "InnerEnum"))
            .addEnumConstant("INNER")
    );
    TypeWithName otherInnerEnum = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterClass", "MiddleClass", "OtherInnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterClass", "MiddleClass", "OtherInnerEnum"))
            .addEnumConstant("OTHER_INNER")
    );
    JavaFile file =
        getOnlyElement(getGenerator().groupTypeDefinitionsIntoFiles(ImmutableList.of(innerEnum, otherInnerEnum)));

    assertEquals("OuterClass", file.typeSpec.name);
    TypeSpec middleClass = getOnlyElement(file.typeSpec.typeSpecs);
    assertEquals("MiddleClass", middleClass.name);
    assertEquals(2, middleClass.typeSpecs.size());

    TypeSpec inner = middleClass.typeSpecs.stream()
        .filter(s -> s.name.equals("InnerEnum"))
        .collect(onlyElement());
    assertEquals(ImmutableSet.of("INNER"), inner.enumConstants.keySet());

    TypeSpec otherInner = middleClass.typeSpecs.stream()
        .filter(s -> s.name.equals("OtherInnerEnum"))
        .collect(onlyElement());
    assertEquals(ImmutableSet.of("OTHER_INNER"), otherInner.enumConstants.keySet());

    TypeWithName innerEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterClass", "MiddleClass", "InnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterClass", "MiddleClass", "InnerEnum"))
            .addEnumConstant("INNER")
    );
    TypeWithName otherInnerEnum2 = new TypeWithName(
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("OuterClass", "MiddleClass", "OtherInnerEnum")),
        TypeSpec.enumBuilder(ClassName.get("com.wealthfront", "OuterClass", "MiddleClass", "OtherInnerEnum"))
            .addEnumConstant("OTHER_INNER")
    );

    assertEquals(file,
        getOnlyElement(getGenerator().groupTypeDefinitionsIntoFiles(ImmutableList.of(otherInnerEnum2, innerEnum2))));
  }

  @Entity
  @ExposeType(LOCAL)
  private static class SimpleEntity {

    @Value(name = "user_id", type = UserId.JsonType.class)
    private UserId userId;

    @Value(nullable = false)
    private Money money;

    @Value(nullable = false)
    private int someNumber;

    @Value(nullable = false)
    private boolean someBoolean;

    @Value(optional = true, types = {LocalDateJsonType.class, UserId.JsonType.class})
    private Map<LocalDate, List<UserId>> map;

    @Value(optional = true)
    private SortedMap<Integer, String> sortedMap;

    @Value(nullable = true)
    private SimpleEntityMetadata simpleEntityMetaData;

  }

  @Entity
  @ExposeType(LOCAL)
  private static class SimpleEntityMetadata {

    @Value(nullable = false)
    private Money money;

    @Value(nullable = false)
    private SimpleEntitySecureMetadataNested nested;

  }

  @Entity
  @SecureField
  @ExposeType(LOCAL)
  private static class SimpleEntitySecureMetadataNested {

    @Value(nullable = false)
    private String secureFieldString;

  }

  @ExposeQuery(LOCAL)
  private static class SimpleQuery extends AbstractQuery<SimpleEntity> {

    private final Id<TestEntity> id;

    SimpleQuery(@Owned Id<TestEntity> id) {
      this.id = id;
    }

    @Override
    public SimpleEntity process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withSimpleJsonEntity() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    builder.addTypeMappingOverride(
        new TypeLiteral<Id<TestEntity>>() {}.getType(),
        UserId.class
    );
    JavaApiSchemaIntrospector.introspectQueryOrThrow(SimpleQuery.class, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFiles(errors, SAND.class, builder, new FakeNamespaceTransformer());
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_SimpleEntity_1.java.txt",
        "JavaApiCodeGenerator_SimpleEntityMetadata_1.java",
        "JavaApiCodeGenerator_SimpleEntitySecureMetadataNested_1.java.txt",
        "JavaApiCodeGenerator_SimpleQuery_1.java.txt"

    ), javaFiles);
    assertJavaFilesCompile(javaFiles);

    Object simpleEntityMetadata = newGeneratedObject(
        "com.demo.SimpleEntityMetadata",
        money(1000),
        newGeneratedObject("com.demo.SimpleEntitySecureMetadataNested", "Should be private"));

    assertCopyAndToStringEquals(
        ImmutableList.of(
            newGeneratedObject("com.demo.SimpleEntity",
                new UserId(1),
                money(1000),
                5,
                true,
                ImmutableMap.of(
                    new LocalDate(2022, 1, 1), new UserId(1),
                    new LocalDate(2023, 2, 5), new UserId(2)
                ),
                ImmutableSortedMap.of(
                    2, "World",
                    1, "Hello"),
                simpleEntityMetadata)
        ),
        ImmutableList.of(
            "JavaApiCodeGenerator_SimpleEntity_toString.java.txt"
        ));
  }

  @Entity
  @ExposeType(LOCAL)
  private static class TestSecureEntity {

    @Value(name = "user_id", type = UserId.JsonType.class)
    private UserId userId;

    @Value(nullable = false)
    private int someNumber;

    @Value(nullable = false)
    private boolean someBoolean;

    @Value(nullable = false)
    private SimpleSecureObject simpleSecureObject;

    @Value(nullable = false)
    private List<SimpleSecureObject> secureObjectList;

    @Value(nullable = false)
    private Set<SimpleSecureObject> secureObjectSet;

    @Value(nullable = false)
    private Map<Integer, SimpleSecureObject> secureObjectMap;

  }

  @Entity
  @SecureField
  @ExposeType(LOCAL)
  private static class SimpleSecureObject {

    @Value(nullable = false)
    private Money money;

    @Value(nullable = false)
    private SimpleEntityMetadata nestedObject;

  }

  @ExposeQuery(API_SERVER)
  private static class SecureQuery extends AbstractQuery<TestSecureEntity> {

    private final Id<TestEntity> id;

    SecureQuery(@Owned Id<TestEntity> id) {
      this.id = id;
    }

    @Override
    public TestSecureEntity process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withSecureTypes() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    builder.addTypeMappingOverride(
        new TypeLiteral<Id<TestEntity>>() {}.getType(),
        UserId.class
    );
    JavaApiSchemaIntrospector.introspectQueryOrThrow(SecureQuery.class, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFiles(errors, SAND.class, builder, new FakeNamespaceTransformer());
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_SecureQuery_1.java.txt",
        "JavaApiCodeGenerator_SimpleEntityMetadata_1.java",
        "JavaApiCodeGenerator_SimpleEntitySecureMetadataNested_1.java.txt",
        "JavaApiCodeGenerator_SimpleSecureObject_1.java.txt",
        "JavaApiCodeGenerator_TestSecureEntity_1.java.txt"

    ), javaFiles);
    assertJavaFilesCompile(javaFiles);

    Object simpleEntityMetadata = newGeneratedObject(
        "com.demo.SimpleEntityMetadata",
        money(1000),
        newGeneratedObject("com.demo.SimpleEntitySecureMetadataNested", "Should be private"));

    Object simpleSecureObject = newGeneratedObject(
        "com.demo.SimpleSecureObject",
        money(1000),
        simpleEntityMetadata);

    Object simpleSecureObject2 = newGeneratedObject(
        "com.demo.SimpleSecureObject",
        money(1000),
        simpleEntityMetadata);

    assertCopyAndToStringEquals(
        ImmutableList.of(
            newGeneratedObject("com.demo.TestSecureEntity",
                new UserId(1),
                5,
                true,
                simpleSecureObject,
                ImmutableList.of(simpleSecureObject, simpleSecureObject2),
                ImmutableSet.of(simpleSecureObject, simpleSecureObject2),
                ImmutableMap.of(1, simpleSecureObject, 2, simpleSecureObject2))
        ),
        ImmutableList.of(
            "JavaApiCodeGenerator_TestSecureEntity_toString.java.txt"
        ));
  }

  @ExposeType(value = ExposeTo.BACKEND, namespace = SERVICE)
  @Entity(discriminatorName = "type", discriminator = "grandparent", subclasses = {
      Parent2.class,
      Child1.class,
      ChildNoFields.class
  })
  private static class Grandparent {

    @Value String grandparentValue;

    Grandparent() {}

  }

  @Entity
  private abstract static class Parent1 extends Grandparent {

    @Value String parent1Value;

    Parent1() {}

  }

  @Entity(discriminator = "child1")
  private static class Child1 extends Parent1 {

    @Value String child1Value;

    Child1() {}

  }

  private static class PassThroughNonEntity extends Grandparent {

  }

  @Entity(discriminator = "parent2")
  private static class Parent2 extends PassThroughNonEntity {

    @Value String parent2Value;

    Parent2() {}

  }

  @Entity(discriminator = "child-no-fields")
  private static class ChildNoFields extends Parent2 {

  }

  private static class PolyQuery extends AbstractQuery<Boolean> {

    private final Parent2 val;

    PolyQuery(Parent2 val) {
      this.val = val;
    }

    @Override
    public Boolean process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withPolymorphicEntity() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(PolyQuery.class, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFiles(errors, SAND.class, builder, new FakeNamespaceTransformer());
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_Child1_1.java.txt",
        "JavaApiCodeGenerator_ChildNoFields_1.java.txt",
        "JavaApiCodeGenerator_Grandparent_1.java.txt",
        "JavaApiCodeGenerator_Parent1_1.java.txt",
        "JavaApiCodeGenerator_Parent2_1.java.txt",
        "JavaApiCodeGenerator_PolyQuery_1.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);

    assertCopyAndToStringEquals(
        ImmutableList.of(
            newGeneratedObject("com.demo.Child1", "grandparentName", "parentName", "childName"),
            newGeneratedObject("com.demo.Grandparent", "grandparentName"),
            newGeneratedObject("com.demo.Parent2", "grandparentName", "parent2Name")),
        ImmutableList.of(
            "JavaApiCodeGenerator_Child1_toString.java.txt",
            "JavaApiCodeGenerator_Grandparent_toString.java.txt",
            "JavaApiCodeGenerator_Parent2_toString.java.txt"
        ));
  }

  @Entity(discriminatorName = "type", subclasses = {
      ChildA.class,
      ChildB.class
  })
  @ExposeType(value = {BACKEND}, namespace = QUERY)
  private abstract static class AbstractTopLevelPoly {

    @Value(nullable = false) Money val1;

  }

  @Entity(discriminator = "a")
  private static class ChildA extends AbstractTopLevelPoly {

    @Value(nullable = false) BigDecimal val2;

  }

  @Entity(discriminator = "b")
  private static class ChildB extends AbstractTopLevelPoly {

    @Value(nullable = false) Money val3;

  }

  @Entity(discriminatorName = "type", subclasses = {
      Default.class
  })
  @ExposeType(value = {BACKEND}, namespace = QUERY)
  private abstract static class AbstractTopLevelPolyWithKeyword {

    @Value(nullable = false) Money val1;

  }

  @Entity(discriminator = "default")
  private static class Default extends AbstractTopLevelPolyWithKeyword {

    @Value(nullable = false) Money val4;

  }

  private static class GetAbstractTopLevelPoly extends AbstractQuery<AbstractTopLevelPoly> {

    @Override
    public AbstractTopLevelPoly process() {
      return null;
    }

  }

  private static class GetAbstractTopLevelPolyWithKeyword extends AbstractQuery<AbstractTopLevelPolyWithKeyword> {

    @Override
    public AbstractTopLevelPolyWithKeyword process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withAbstractTopLevelPolymorphicHierarchy() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(GetAbstractTopLevelPoly.class, builder);
    Errors errors = new Errors();

    DefaultJavaApiTypeNamespaceTransformer namespaceTransformer = new DefaultJavaApiTypeNamespaceTransformer();
    namespaceTransformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> javaFiles = getGenerator().generateFiles(errors, SAND.class, builder, namespaceTransformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_AbstractTopLevelPolyHierarchy.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);

    assertCopyAndToStringEquals(
        ImmutableList.of(
            newGeneratedObject("com.wealthfront.stubqueries.sand.GetAbstractTopLevelPoly$AbstractTopLevelPoly$ChildA",
                money(3000),
                BigDecimal.valueOf(2000)
            ),
            newGeneratedObject("com.wealthfront.stubqueries.sand.GetAbstractTopLevelPoly$AbstractTopLevelPoly$ChildB",
                money(2000),
                money(1000)
            )),
        ImmutableList.of(
            "JavaApiCodeGenerator_AbstractTopLevelPolyHeirarchy_ChildA_toString.java.txt",
            "JavaApiCodeGenerator_AbstractTopLevelPolyHeirarchy_ChildB_toString.java.txt"
        ));
  }

  @Test
  public void generateFiles_withAbstractTopLevelPolymorphicHierarchy_classNameKeyword() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(GetAbstractTopLevelPolyWithKeyword.class, builder);
    Errors errors = new Errors();

    DefaultJavaApiTypeNamespaceTransformer namespaceTransformer = new DefaultJavaApiTypeNamespaceTransformer();
    namespaceTransformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> javaFiles = getGenerator().generateFiles(errors, SAND.class, builder, namespaceTransformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_AbstractTopLevelPolyHierarchyWithKeyword.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);

    assertCopyAndToStringEquals(
        ImmutableList.of(
            newGeneratedObject(
                "com.wealthfront.stubqueries.sand.GetAbstractTopLevelPolyWithKeyword$AbstractTopLevelPolyWithKeyword$Default",
                money(1000),
                money(500)
            )),
        ImmutableList.of(
            "JavaApiCodeGenerator_AbstractTopLevelPolyHeirarchyWithKeyword_Default_toString.java.txt"
        ));
  }

  @Entity
  @ExposeType(value = {TAOS}, namespace = QUERY)
  private static class NonAbstractNonPolyEntity {

    @Value(nullable = false) Money val0;

  }

  @Entity(discriminatorName = "type", subclasses = {
      ChildA2.class,
      ChildB2.class
  })
  @ExposeType(value = {TAOS}, namespace = QUERY)
  private abstract static class AbstractPolyWithNonPolyParent extends NonInlinedNonPolymorphicParent {

    @Value(nullable = false) Money val1;

  }

  @Entity(discriminator = "a")
  private static class ChildA2 extends AbstractPolyWithNonPolyParent {

    @Value(nullable = false) BigDecimal val2;

  }

  @Entity(discriminator = "b")
  private static class ChildB2 extends AbstractPolyWithNonPolyParent {

    @Value(nullable = false) Money val3;

  }

  private static class GetAbstractPolyWithNonPolyParent extends AbstractQuery<AbstractPolyWithNonPolyParent> {

    @Override
    public AbstractPolyWithNonPolyParent process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withAbstractPolyWithNonPolyParent() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(GetAbstractPolyWithNonPolyParent.class, builder);
    Errors errors = new Errors();

    DefaultJavaApiTypeNamespaceTransformer namespaceTransformer = new DefaultJavaApiTypeNamespaceTransformer();
    namespaceTransformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> javaFiles = getGenerator().generateFiles(errors, SAND.class, builder, namespaceTransformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_NonInlinedNonPolymorphicParent_1.java.txt",
        "JavaApiCodeGenerator_AbstractPolyWithNonPolyParent.java.txt"

    ), javaFiles);
    assertJavaFilesCompile(javaFiles);

    assertCopyAndToStringEquals(
        ImmutableList.of(
            newGeneratedObject(
                "com.wealthfront.stubqueries.sand.GetAbstractPolyWithNonPolyParent$AbstractPolyWithNonPolyParent$ChildA2",
                money(4000),
                money(3000),
                BigDecimal.valueOf(2000)
            ),
            newGeneratedObject(
                "com.wealthfront.stubqueries.sand.GetAbstractPolyWithNonPolyParent$AbstractPolyWithNonPolyParent$ChildB2",
                money(4000),
                money(2000),
                money(1000)
            )),
        ImmutableList.of(
            "JavaApiCodeGenerator_AbstractPolyWithNonPolyParent_ChildA2_toString.java.txt",
            "JavaApiCodeGenerator_AbstractPolyWithNonPolyParent_ChildB2_toString.java.txt"
        ));
  }

  @Test
  public void generateEqualsSpec_childClass() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Parent1.class, true), builder);
    FullyQualifiedTypeName fullyQualifiedTypeName =
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("Parent2"));

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("equals")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(boolean.class)
        .addParameter(Object.class, "o")
        .addCode(CodeBlock.builder()
            .beginControlFlow("if (o == this)")
            .addStatement("return true")
            .endControlFlow()
            .build())
        .addCode(CodeBlock.builder()
            .beginControlFlow("if (o == null || getClass() != o.getClass())")
            .addStatement("return false")
            .endControlFlow()
            .build())
        .addStatement("com.wealthfront.Parent2 that = (com.wealthfront.Parent2) o")
        .addCode("$[return super.equals(that) &&\n")
        .addCode("java.util.Objects.equals(parent1Value, that.parent1Value);\n$]");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateEqualsSpec(fullyQualifiedTypeName, javaApiType));
  }

  @Test
  public void generateEqualsSpec_entityWithMultipleFields() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        SimpleEntity.class, true), builder);
    FullyQualifiedTypeName fullyQualifiedTypeName =
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("SimpleEntity"));

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("equals")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(boolean.class)
        .addParameter(Object.class, "o")
        .addCode(CodeBlock.builder()
            .beginControlFlow("if (o == this)")
            .addStatement("return true")
            .endControlFlow()
            .build())
        .addCode(CodeBlock.builder()
            .beginControlFlow("if (o == null || getClass() != o.getClass())")
            .addStatement("return false")
            .endControlFlow()
            .build())
        .addStatement("com.wealthfront.SimpleEntity that = (com.wealthfront.SimpleEntity) o")
        .addCode("$[return java.util.Objects.equals(userId, that.userId) &&\n")
        .addCode("java.util.Objects.equals(money, that.money) &&\n")
        .addCode("java.util.Objects.equals(someNumber, that.someNumber) &&\n")
        .addCode("java.util.Objects.equals(someBoolean, that.someBoolean) &&\n")
        .addCode("java.util.Objects.equals(map, that.map) &&\n")
        .addCode("java.util.Objects.equals(sortedMap, that.sortedMap) &&\n")
        .addCode("java.util.Objects.equals(simpleEntityMetaData, that.simpleEntityMetaData);\n$]");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateEqualsSpec(fullyQualifiedTypeName, javaApiType));
  }

  @Test
  public void generateEqualsSpec_entityWithNoFields() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Entity1.class, true), builder);
    FullyQualifiedTypeName fullyQualifiedTypeName =
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("Entity1"));

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("equals")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(boolean.class)
        .addParameter(Object.class, "o")
        .addStatement("return o instanceof com.wealthfront.Entity1");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateEqualsSpec(fullyQualifiedTypeName, javaApiType));
  }

  @Test
  public void generateCopySpec_entityWithMultipleFields() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        SimpleEntity.class, true), builder);

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("copy")
        .addModifiers(Modifier.PUBLIC)
        .addCode("return builder()\n")
        .addCode("  .withUserId(getUserId().getOrNull())\n")
        .addCode("  .withMoney(getMoney())\n")
        .addCode("  .withSomeNumber(getSomeNumber())\n")
        .addCode("  .withSomeBoolean(getSomeBoolean())\n")
        .addCode("  .withMap(getMap().getOrNull())\n")
        .addCode("  .withSortedMap(getSortedMap().getOrNull())\n")
        .addCode("  .withSimpleEntityMetaData(getSimpleEntityMetaData().getOrNull());\n")
        .returns(ClassName.get("", "Builder"));

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateCopySpec(javaApiType));
  }

  @Test
  public void generateCopySpecSpec_entityWithNoFields() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Entity1.class, true), builder);

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("copy")
        .addModifiers(Modifier.PUBLIC)
        .addCode("return builder();\n")
        .returns(ClassName.get("", "Builder"));

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateCopySpec(javaApiType));
  }

  @Test
  public void generateBuilderMethodSpec() {
    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("builder")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .returns(ClassName.get("", "Builder"))
        .addStatement("return new Builder()");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateBuilderMethodSpec());
  }

  @Test
  public void generateBuilderSpec_entityWithNoFieldsOrParent() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Entity1.class, true), builder);
    JavaApiQuerySchema querySchema = JavaApiSchemaIntrospector.introspectQueryOrThrow(Query1.class, builder);
    FullyQualifiedTypeName fullyQualifiedTypeName =
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("Entity1"));

    TypeSpec.Builder expectedSpecBuilder = TypeSpec.classBuilder("Builder")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .addMethod(MethodSpec.methodBuilder("build")
            .addModifiers(Modifier.PUBLIC)
            .returns(ClassName.get("", "com.wealthfront.Entity1"))
            .addCode("com.wealthfront.Entity1 obj1 = new com.wealthfront.Entity1();\n")
            .addCode("obj1.validate();\n")
            .addCode("return obj1;\n")
            .build());

    MethodSpec.Builder buildForTesting = MethodSpec.methodBuilder("buildForTesting")
        .addModifiers(Modifier.PUBLIC)
        .addAnnotation(ClassName.get("com.google.common.annotations", "VisibleForTesting"))
        .returns(ClassName.get("", "com.wealthfront.Entity1"))
        .addCode("com.wealthfront.Entity1 obj1 = new com.wealthfront.Entity1();\n")
        .addCode("return obj1;\n");
    expectedSpecBuilder.addMethod(buildForTesting.build());

    assertEquals(expectedSpecBuilder.build(),
        getGenerator().generateBuilderSpec(SAND.class, querySchema, fullyQualifiedTypeName, javaApiType,
            new FakeNamespaceTransformer()));
  }

  @Test
  public void generateBuilderSpec_entityWithSimpleFields() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        MyMoneyResult.class, true), builder);
    JavaApiQuerySchema querySchema = JavaApiSchemaIntrospector.introspectQueryOrThrow(GetMyMoneyResult.class, builder);
    FullyQualifiedTypeName fullyQualifiedTypeName =
        new FullyQualifiedTypeName("com.wealthfront", ImmutableList.of("MyMoneyResult"));

    TypeSpec.Builder expectedSpecBuilder = TypeSpec.classBuilder("Builder")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC);

    expectedSpecBuilder.addField(FieldSpec.builder(ParameterizedTypeName.get(
            Map.class, String.class, String.class), "errors")
        .addAnnotation(Nullable.class)
        .addModifiers(Modifier.PRIVATE)
        .initializer("new $T<>()", HashMap.class)
        .build());

    expectedSpecBuilder.addField(FieldSpec.builder(Money.class, "value")
        .addAnnotation(Nullable.class)
        .addModifiers(Modifier.PRIVATE)
        .initializer("null")
        .build());

    MethodSpec.Builder withErrors = MethodSpec.methodBuilder("withErrors")
        .addModifiers(Modifier.PUBLIC)
        .returns(ClassName.get("", "Builder"))
        .addParameter(ParameterSpec.builder(ParameterizedTypeName.get(
                Map.class, String.class, String.class), "errors")
            .addAnnotation(ClassName.get("javax.annotation", "Nullable")).build())
        .addStatement("this.errors = errors")
        .addStatement("return this");
    expectedSpecBuilder.addMethod(withErrors.build());

    MethodSpec.Builder withValue = MethodSpec.methodBuilder("withValue")
        .addModifiers(Modifier.PUBLIC)
        .returns(ClassName.get("", "Builder"))
        .addParameter(ParameterSpec.builder(Money.class, "value")
            .addAnnotation(Nullable.class).build())
        .addStatement("this.value = value")
        .addStatement("return this");
    expectedSpecBuilder.addMethod(withValue.build());

    MethodSpec.Builder build = MethodSpec.methodBuilder("build")
        .addModifiers(Modifier.PUBLIC)
        .returns(ClassName.get("com.wealthfront", "MyMoneyResult"))
        .addStatement("$T obj1 = new $T(errors, value)", ClassName.get("com.wealthfront", "MyMoneyResult"),
            ClassName.get("com.wealthfront", "MyMoneyResult"))
        .addStatement("obj1.validate()")
        .addStatement("return obj1");
    expectedSpecBuilder.addMethod(build.build());

    MethodSpec.Builder buildForTesting = MethodSpec.methodBuilder("buildForTesting")
        .addModifiers(Modifier.PUBLIC)
        .addAnnotation(ClassName.get("com.google.common.annotations", "VisibleForTesting"))
        .returns(ClassName.get("com.wealthfront", "MyMoneyResult"))
        .addStatement("$T obj1 = new $T(errors, value)", ClassName.get("com.wealthfront", "MyMoneyResult"),
            ClassName.get("com.wealthfront", "MyMoneyResult"))
        .addStatement("return obj1");
    expectedSpecBuilder.addMethod(buildForTesting.build());

    assertEquals(expectedSpecBuilder.build(),
        getGenerator().generateBuilderSpec(SAND.class, querySchema, fullyQualifiedTypeName, javaApiType,
            new FakeNamespaceTransformer()));
  }

  @Test
  public void generateBuilderSpec_jacksonEntitySkipsValidateMethod() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        JacksonEntity.class, true), builder);
    JavaApiQuerySchema querySchema = JavaApiSchemaIntrospector.introspectQueryOrThrow(Query1.class, builder);
    FullyQualifiedTypeName fullyQualifiedTypeName =
        new FullyQualifiedTypeName("com.wealthfront.model", ImmutableList.of("JacksonEntity"));

    TypeSpec.Builder expectedSpecBuilder = TypeSpec.classBuilder("Builder")
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .addMethod(MethodSpec.methodBuilder("build")
            .addModifiers(Modifier.PUBLIC)
            .returns(ClassName.get("", "com.wealthfront.model.JacksonEntity"))
            .addCode("com.wealthfront.model.JacksonEntity obj1 = new com.wealthfront.model.JacksonEntity();\n")
            .addCode("return obj1;\n")
            .build());

    MethodSpec.Builder buildForTesting = MethodSpec.methodBuilder("buildForTesting")
        .addModifiers(Modifier.PUBLIC)
        .addAnnotation(ClassName.get("com.google.common.annotations", "VisibleForTesting"))
        .returns(ClassName.get("", "com.wealthfront.model.JacksonEntity"))
        .addCode("com.wealthfront.model.JacksonEntity obj1 = new com.wealthfront.model.JacksonEntity();\n")
        .addCode("return obj1;\n");
    expectedSpecBuilder.addMethod(buildForTesting.build());

    assertEquals(expectedSpecBuilder.build(),
        getGenerator().generateBuilderSpec(SAND.class, querySchema, fullyQualifiedTypeName, javaApiType,
            new FakeNamespaceTransformer()));
  }

  @Test
  public void generateHashCodeSpec_childClass() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Parent1.class, true), builder);

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("hashCode")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(int.class)
        .addStatement("return java.util.Objects.hash(super.hashCode(), this.parent1Value)");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateHashCodeSpec(javaApiType));
  }

  @Test
  public void generateHashCodeSpec_entityWithMultipleFields() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        SimpleEntity.class, true), builder);

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("hashCode")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(int.class)
        .addStatement(
            "return java.util.Objects.hash(this.userId, this.money, this.someNumber, this.someBoolean, this.map, this.sortedMap, this.simpleEntityMetaData)");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateHashCodeSpec(javaApiType));
  }

  @Test
  public void generateHashCodeSpec_entityWithNoFieldsOrParent() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Entity1.class, true), builder);

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("hashCode")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(int.class)
        .addStatement("return 0");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateHashCodeSpec(javaApiType));
  }

  @Entity
  private static class Entity2 extends Parent1 {

  }

  @Test
  public void generateHashCodeSpec_entityWithNoFieldsAndHasParent() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaApiType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Entity2.class, true), builder);

    MethodSpec.Builder expectedSpecBuilder = MethodSpec.methodBuilder("hashCode")
        .addAnnotation(Override.class)
        .addModifiers(Modifier.PUBLIC)
        .returns(int.class)
        .addStatement("return super.hashCode()");

    assertEquals(expectedSpecBuilder.build(), getGenerator().generateHashCodeSpec(javaApiType));
  }

  @Test
  public void rewriteJavaType_map() {
    TypeName result = rewriteJavaType(new JavaApiType.JavaMapType(
        false,
        new TypeLiteral<Map<UserId, Enum1>>() {
        }.getType(),
        UserId.class,
        Enum1.class,
        new JavaApiType.JavaSimpleType(false, UserId.class, JavaApiType.JavaSimpleType.SerializedType.INTEGER),
        new JavaApiType.JavaEnumType(false, Enum1.class)
    ), SAND.class, null, new FakeNamespaceTransformer());
    assertEquals("java.util.Map<com.kaching.user.UserId, com.demo.Enum1>", result.toString());
  }

  @Test
  public void rewriteJavaType_primitiveArray() {
    TypeName result = rewriteJavaType(new JavaApiType.JavaArrayType(
        true,
        new TypeLiteral<int[]>() {}.getType(),
        int.class,
        new JavaApiType.JavaSimpleType(false, int.class, JavaApiType.JavaSimpleType.SerializedType.INTEGER)
    ), SAND.class, null, new FakeNamespaceTransformer());
    assertEquals("int[]", result.toString());
  }

  @Entity
  private static class Entity1 {

  }

  private static class Query1 extends AbstractQuery<List<Map<Money, Entity1>>> {

    @Override
    public List<Map<Money, Entity1>> process() {
      return null;
    }

  }

  @Test
  public void rewriteJavaType_collections() {
    JavaApiQuerySchema querySchema =
        JavaApiSchemaIntrospector.introspectQueryOrThrow(Query1.class, new JavaApiTypesBuilder());
    assertEquals("java.util.List<java.util.Map<com.kaching.entities.Money, com.demo.Entity1>>",
        rewriteJavaType(querySchema.getReturnType(), SAND.class, null,
            new FakeNamespaceTransformer()).toString());
  }

  private static class Query2 extends AbstractQuery<Option<UserId>> {

    @Override
    public Option<UserId> process() {
      return null;
    }

  }

  @Test
  public void rewriteJavaType_option() {
    JavaApiQuerySchema querySchema =
        JavaApiSchemaIntrospector.introspectQueryOrThrow(Query2.class, new JavaApiTypesBuilder());
    assertEquals("com.kaching.platform.common.Option<com.kaching.user.UserId>",
        rewriteJavaType(querySchema.getReturnType(), SAND.class, null,
            new FakeNamespaceTransformer()).toString());
  }

  @Entity
  @ExposeType(TAOS)
  private static class MyMoneyResult extends AbstractBackendResult<MyMoneyResult, Money> {

    @Value Money value;

    @Override
    public MyMoneyResult parent() {
      return this;
    }

    @Override
    public MyMoneyResult withResult(Money money) {
      this.value = money;
      return this;
    }

    @Override
    public Money getResult() {
      return value;
    }

  }

  @ExposeQuery(TAOS)
  private static class GetMyMoneyResult extends AbstractQuery<MyMoneyResult> {

    @Override
    public MyMoneyResult process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withInlinedNonPolymorphicEntity_andQueryNamespace() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(GetMyMoneyResult.class, builder);
    Errors errors = new Errors();

    DefaultJavaApiTypeNamespaceTransformer defaultTransformer = new DefaultJavaApiTypeNamespaceTransformer();
    defaultTransformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> javaFiles = getGenerator().generateFiles(errors, SAND.class, builder, defaultTransformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_InlinedNonPolymorphic_1.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
    errors.throwIfHasErrors();
  }

  @ExposeQuery(value = TAOS)
  private static class GetNonPolymorphicChild extends AbstractQuery<CodeGenEntities.NonPolymorphicChild> {

    @Override
    public CodeGenEntities.NonPolymorphicChild process() {
      return null;
    }

  }

  @Test
  public void generateFiles_withNotInlinedNonPolymorphicEntity() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(GetNonPolymorphicChild.class, builder);
    Errors errors = new Errors();

    DefaultJavaApiTypeNamespaceTransformer defaultTransformer = new DefaultJavaApiTypeNamespaceTransformer();
    defaultTransformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> javaFiles = getGenerator().generateFiles(errors, SAND.class, builder, defaultTransformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_NonInlinedNonPolymorphicParent_1.java.txt",
        "JavaApiCodeGenerator_NonInlinedNonPolymorphic_1.java.txt"
    ), javaFiles);
    errors.throwIfHasErrors();
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void getAllFieldTypes() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiType.JavaObjectType javaParent1Type = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Parent1.class, true), builder);
    JavaApiType.JavaObjectType grandParentType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Grandparent.class, true), builder);
    JavaApiType.JavaObjectType childType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        Child1.class, true), builder);
    JavaApiType.JavaObjectType simpleType = (JavaApiType.JavaObjectType) introspectTypeOrThrow(new TypeKey(
        SimpleEntity.class, true), builder);

    assertEquals(ImmutableList.of("grandparentValue", "parent1Value"),
        getAllTypeFields(javaParent1Type).stream().map(
                JavaApiType.JavaObjectField::getApiName)
            .collect(Collectors.toList()));

    assertEquals(ImmutableList.of("grandparentValue"),
        getAllTypeFields(grandParentType).stream().map(
                JavaApiType.JavaObjectField::getApiName)
            .collect(Collectors.toList()));

    assertEquals(ImmutableList.of("grandparentValue", "parent1Value", "child1Value"),
        getAllTypeFields(childType).stream().map(
                JavaApiType.JavaObjectField::getApiName)
            .collect(Collectors.toList()));

    assertEquals(
        ImmutableList.of("user_id", "money", "someNumber", "someBoolean", "map", "sortedMap", "simpleEntityMetaData"),
        getAllTypeFields(simpleType).stream().map(
                JavaApiType.JavaObjectField::getApiName)
            .collect(Collectors.toList()));
  }

  @Entity
  @ExposeType(value = {TAOS, LOCAL, API_SERVER}, namespace = DO_NOT_COPY)
  private static class NonCodeGeneratedParent {

    @Value(nullable = false) Money parentMoney;

  }

  @Entity
  @ExposeType(value = {TAOS, API_SERVER}, namespace = QUERY)
  private static class ChildOfNonCodeGenParent extends NonCodeGeneratedParent {

    @Value(nullable = false) Money childMoney;

  }

  @ExposeQuery(value = TAOS)
  private static class GetChildOfNonCodeGenParent extends AbstractQuery<ChildOfNonCodeGenParent> {

    @Override
    public ChildOfNonCodeGenParent process() {
      return null;
    }

  }

  @Test
  public void generateFiles_nonPolymorphicInheritance_withNonCodeGeneratedParent_skipsAllOptionalMethods() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    JavaApiSchemaIntrospector.introspectQueryOrThrow(GetChildOfNonCodeGenParent.class, builder);
    Errors errors = new Errors();

    DefaultJavaApiTypeNamespaceTransformer defaultTransformer = new DefaultJavaApiTypeNamespaceTransformer();
    defaultTransformer.validator = new JavaApiValidatorImpl();
    List<JavaFile> javaFiles = getGenerator().generateFiles(errors, SAND.class, builder, defaultTransformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_NonCodeGeneratedInheritance_1.java.txt"
    ), javaFiles);
    errors.throwIfHasErrors();
  }

  @Test
  public void generateFilesFromJacksonEntities_simpleEntity() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(SimpleJacksonEntity.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_SimpleJacksonEntity.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJacksonEntities_polymorphic() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(PolymorphicBook.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_BookTypeEnum.java.txt",
        "JavaApiCodeGenerator_jackson_GenreEnum.java.txt",
        "JavaApiCodeGenerator_jackson_PolymorphicBook.java.txt",
        "JavaApiCodeGenerator_jackson_PolymorphicEmptySubclassBook.java.txt",
        "JavaApiCodeGenerator_jackson_PolymorphicEncyclopedia.java.txt",
        "JavaApiCodeGenerator_jackson_PolymorphicFictionBook.java.txt",
        "JavaApiCodeGenerator_jackson_PolymorphicNonFictionBook.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJacksonEntities_nonPolymorphic() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(NonPolyGrandChild.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_NonPolyChild1.java.txt",
        "JavaApiCodeGenerator_jackson_NonPolyGrandChild.java.txt",
        "JavaApiCodeGenerator_jackson_NonPolyParent.java.txt",
        "JavaApiCodeGenerator_jackson_SomeGrandchildFieldEnum.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJacksonEntities_simpleEntityEnumCollision() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(PolymorphicBook.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);

    TypeKey typeKey2 = new TypeKey(SimpleJacksonEntityWithEnumCollision.class, true);
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey2, builder);
    Errors errors = new Errors();

    getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    String err = getOnlyElement(errors.getMessages());
    assertContains("should contain", "Multiple classes resolve to com.wealthfront.auto.types.global.BookTypeEnum:",
        err);
    assertContains("should contain", "PolymorphicBook$BookTypeEnum", err);
    assertContains("should contain", "SimpleJacksonEntityWithEnumCollision$BookTypeEnum", err);
  }

  @Test
  public void generateFilesFromJacksonEntities_simpleEntityObjectCollision() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(SimpleJacksonEntity.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);

    TypeKey typeKey2 = new TypeKey(SimpleJacksonEntityWithObjectCollision.class, true);
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey2, builder);
    Errors errors = new Errors();

    getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    String err = getOnlyElement(errors.getMessages());
    assertContains("should contain",
        "Multiple classes resolve to com.wealthfront.auto.types.global.SimpleJacksonEntity:", err);
    assertContains("should contain", "SimpleJacksonEntityWithObjectCollision$SimpleJacksonEntity", err);
    assertContains("should contain", "com.wealthfront.model.SimpleJacksonEntity", err);
  }

  @Ignore("un-ignore to test generating all of com.wealthfront.model")
  @Test
  public void generateFilesFromJacksonEntities_compilesAllWfModels() {
    TreeMap<String, String> errors = new TreeMap<>();
    Path targetDirectory = Paths.get(".").toAbsolutePath().normalize().resolve("src/test/java/dump");
    Reflections reflections = new Reflections(new ConfigurationBuilder() {}
        .setUrls(ClasspathHelper.forPackage("com.wealthfront.model"))
        .setScanners(new SubTypesScanner(false)));
    Set<Class<?>> classes = reflections.getSubTypesOf(Object.class).stream()
        .filter(clazz -> clazz.getPackageName().equals("com.wealthfront.model"))
        .filter(clazz -> clazz.isAnnotationPresent(JsonClassDescription.class))
        .collect(Collectors.toSet());

    for (Class<?> clazz : classes) {
      try {
        JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
        introspectTypeOrThrow(new TypeKey(clazz, true), builder);

        GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
        transformer.validator = new JavaApiValidatorImpl();
        List<JavaFile> files =
            getGenerator().generateFilesFromJsonEntities(new Errors(), SAND.class, builder, transformer);

        files.forEach(file -> {
          try {
            file.writeTo(targetDirectory);
          } catch (IOException e) {
            throw new RuntimeException(e);
          }
        });
      } catch (RuntimeException e) {
        errors.put(clazz.getSimpleName(), e.getMessage());
        System.err.println(clazz.getSimpleName() + ": " + e.getMessage());
      }
    }

    System.err.println("total entity validation failures: " + errors.size());
  }

  @Test
  public void generateFilesFromJacksonEntities_lending_onlyUsesDefaultConstructor() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(CancelTaskInput.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_CancelTaskInput.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJacksonEntities_lending_polymorphicObject_onlyUsesDefaultConstructor() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(PolymorphicObject.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_PolymorphicEmptySubclassObject.java.txt",
        "JavaApiCodeGenerator_jackson_PolymorphicObject.java.txt",
        "JavaApiCodeGenerator_jackson_PolymorphicRandomSubObject.java.txt",
        "JavaApiCodeGenerator_jackson_TypeEnum.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJacksonEntities_handlesGenericJavaObject() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(EntityWithJsonNode.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_EntityWithGenericJavaObject.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJacksonEntities_handlesByteArray() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(EntityWithByteArray.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_EntityWithByteArray.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Entity
  @ExposeType(value = {TAOS, LOCAL, API_SERVER}, namespace = DO_NOT_COPY)
  private enum EnumWithGetValue {

    SCI_FI("Sci Fi"),
    FICTION("Fiction");

    private final String value;

    EnumWithGetValue(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }

  }

  @Test
  public void generateFilesFromJacksonEntities_enumWithGetValue() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(EnumWithGetValue.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_EnumWithGetValue.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Entity
  @ExposeType(value = {TAOS, LOCAL, API_SERVER}, namespace = DO_NOT_COPY)
  private enum EnumWithGetValuePrivateMethod {

    SCI_FI("Sci Fi"),
    FICTION("Fiction");

    private final String value;

    EnumWithGetValuePrivateMethod(String value) {
      this.value = value;
    }

    private String getValue() {
      return value;
    }

  }

  @Test
  public void generateFilesFromJacksonEntities_enumWithGetValuePrivateMethod_throws() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(EnumWithGetValuePrivateMethod.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    assertThrows(RuntimeException.class,
        "Unable to invoke class com.kaching.api.JavaApiCodeGeneratorImplTest$EnumWithGetValuePrivateMethod.SCI_FI:getValue() method because it isn't public.",
        () -> getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer));
  }

  @Entity
  @ExposeType(value = {TAOS, LOCAL, API_SERVER}, namespace = DO_NOT_COPY)
  private enum EnumWithGetValueMethodThrows {

    SCI_FI("Sci Fi"),
    FICTION("Fiction");

    private final String value;

    EnumWithGetValueMethodThrows(String value) {
      this.value = value;
    }

    public String getValue() {
      throw new RuntimeException("uh oh");
    }

  }

  @Test
  public void generateFilesFromJacksonEntities_enumWithGetValueMethodThrows_throws() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(EnumWithGetValueMethodThrows.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    assertThrows(RuntimeException.class,
        "class com.kaching.api.JavaApiCodeGeneratorImplTest$EnumWithGetValueMethodThrows.SCI_FI:getValue() threw an exception uh oh",
        () -> getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer));
  }

  @Test
  public void generateFilesFromJacksonEntities_lending_annotatesDateTimeWithCustomConverter() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(EntityWithLocalDate.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);
    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_jackson_EntityWithDateTime.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJavaEntities_addsViewInterfaceForVoyagerView() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(TestVoyagerView.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    JavaApiCodeGeneratorImpl generator = getGenerator();

    List<JavaFile> javaFiles =
        generator.generateFilesFromJsonEntities(errors, SAND.class, builder, transformer);

    assertTrue("Generated class should implement View interface",
        javaFiles.get(0).typeSpec.superinterfaces.contains(ClassName.get(View.class)));

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_TestVoyagerCustomView.java.txt",
        "JavaApiCodeGenerator_TestVoyagerView.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJavaEntities_addsUpdateRequestInterfaceForVoyagerRequest() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(TestVoyagerUpdateRequest.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    JavaApiCodeGeneratorImpl generator = getGenerator();

    List<JavaFile> javaFiles =
        generator.generateFilesFromJsonEntities(errors, SAND.class, builder, transformer);

    assertTrue("Generated class should implement VoyagerRequest interface",
        javaFiles.get(0).typeSpec.superinterfaces.contains(ClassName.get(VoyagerRequest.class)));

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_TestVoyagerSingleKeyRequest.java.txt",
        "JavaApiCodeGenerator_TestVoyagerUpdateRequest.java.txt"
    ), javaFiles);
    assertJavaFilesCompile(javaFiles);
  }

  @Test
  public void generateFilesFromJavaEntities_globalAbstractVoyagerResult() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(TestVoyagerResult.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    JavaApiCodeGeneratorImpl generator = getGenerator();

    List<JavaFile> javaFiles =
        generator.generateFilesFromJsonEntities(errors, SAND.class, builder, transformer);

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_TestVoyagerCustomView.java.txt",
        "JavaApiCodeGenerator_TestVoyagerResult.java.txt",
        "JavaApiCodeGenerator_TestVoyagerView.java.txt"
    ), javaFiles);
  }

  @Test
  public void generateFilesFromJavaEntities_malformedValueAnnotation() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(BrokenDataClass.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    JavaApiCodeGeneratorImpl generator = getGenerator();

    String expectedMessage = String.join("\n\n",
        "1) Nullable mismatch for field 'variable1': method annotation has nullable=false but field type has nullable=true",
        "2) Type converter mismatch for field 'variable2': method annotation has type=com.kaching.util.id.LongIdentifiersTest$SomeLongId$JsonType but field has converter=com.kaching.util.id.IntIdentifiersTest$SomePrimitiveIntId$JsonType",
        "3) Multiple type converters mismatch for field 'variable3': method annotation types don't match field converters",
        "4) Type converter mismatch for field 'variable4': method annotation has type=com.twolattes.json.types.JsonType but field has converter=com.kaching.util.types.ListOfDateTimeJsonType");

    assertThrows(RuntimeException.class, expectedMessage, () ->
        generator.generateFilesFromJsonEntities(errors, SAND.class, builder, transformer));
  }

  @Test
  public void generateFilesFromJavaEntities_missingDataField_doesNotCompile() {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(MissingDataFields.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    JavaApiCodeGeneratorImpl generator = getGenerator();

    List<JavaFile> javaFiles =
        generator.generateFilesFromJsonEntities(errors, SAND.class, builder, transformer);

    assertTrue("Generated class should implement VoyagerRequest interface",
        javaFiles.get(0).typeSpec.superinterfaces.contains(ClassName.get(VoyagerRequest.class)));

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_MissingDataFields.java.txt"
    ), javaFiles);

    assertJavaFilesDoNotCompile(javaFiles);
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  @Test
  public void generateConversionMethods_forGlobalAndModels() throws ClassNotFoundException {
    JavaApiTypesBuilder builder = new JavaApiTypesBuilder();
    TypeKey typeKey = new TypeKey(TestStockPortfolio.class, true);
    GlobalEntityNamespaceTransformer transformer = new GlobalEntityNamespaceTransformer();
    transformer.validator = new JavaApiValidatorImpl();
    JavaApiSchemaIntrospector.introspectTypeOrThrow(typeKey, builder);
    Errors errors = new Errors();

    List<JavaFile> javaFiles =
        getGenerator().generateFilesFromJsonEntities(errors, KachingServices.SAND.class, builder, transformer);

    assertEquals(0, errors.size());

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaApiCodeGenerator_AbstractPositionDetails.java.txt",
        "JavaApiCodeGenerator_TestCashPositionDetails.java.txt",
        "JavaApiCodeGenerator_TestPortfolio.java.txt",
        "JavaApiCodeGenerator_TestStockPortfolio.java.txt",
        "JavaApiCodeGenerator_TestStockPortfolioPosition.java.txt",
        "JavaApiCodeGenerator_TestStockPortfolioState.java.txt",
        "JavaApiCodeGenerator_TestStockPositionDetails.java.txt"
    ), javaFiles);

    assertJavaFilesCompile(javaFiles);

    List<Map<String, String>> stockListMap = new ArrayList<>();
    Object TestStockPositionDetails = newGeneratedObject("com.wealthfront.auto.types.global.TestStockPositionDetails", stockListMap);

    Object TestCashPositionDetails = newGeneratedObject("com.wealthfront.auto.types.global.TestCashPositionDetails", BigDecimal.valueOf(2.0));

    Object samplePosition = newGeneratedObject(
        "com.wealthfront.auto.types.global.TestStockPortfolioPosition",
        1L,
        new BigDecimal("10"),
        new BigDecimal("500.00"),
        new BigDecimal("50.00"),
        TestStockPositionDetails,
        TestCashPositionDetails);

    Object nullablePosition = newGeneratedObject(
        "com.wealthfront.auto.types.global.TestStockPortfolioPosition",
        1L,
        new BigDecimal("10"),
        new BigDecimal("500.00"),
        new BigDecimal("50.00"),
        null,
        TestCashPositionDetails);

    List<List<Map<String, Object>>> crazyNestedList = new ArrayList<>();
    List<Map<String, Object>> innerList = new ArrayList<>();
    Map<String, Object> innerMap = new HashMap<>();
    innerMap.put("nested1", samplePosition);
    innerList.add(innerMap);
    crazyNestedList.add(innerList);

    Map<String, Object> preferredPositionsMap = Map.of("preferred1", samplePosition);

    Class<?> enumClass = Class.forName("com.wealthfront.auto.types.global.TestStockPortfolioState");
    Object enumValue = Enum.valueOf((Class<Enum>) enumClass, "ACTIVE");

    Object[] positionArray = createTypedArray("com.wealthfront.auto.types.global.TestStockPortfolioPosition", 1, samplePosition);
    Object[] statesArray = createTypedArray("com.wealthfront.auto.types.global.TestStockPortfolioState", 2, enumValue);
    Object[] nonNullStatesArray = createTypedArray("com.wealthfront.auto.types.global.TestStockPortfolioState", 1, enumValue);
    VoyagerStepId voyagerStepId = new VoyagerStepId("stepId");
    UserId userId = new UserId(1);

    Object globalPortfolio = newGeneratedObject(
        "com.wealthfront.auto.types.global.TestStockPortfolio",
        List.of(List.of(List.of(samplePosition))),
        new byte[]{4, 5, 6},
        Collections.singletonMap("parent", Collections.singletonMap("child", List.of(samplePosition))),
        userId,
        "SP123",
        "ACC456",
        "REQ789",
        "Test Portfolio",
        Arrays.asList(1L, 2L, 3L),
        Arrays.asList(Arrays.asList(1L, 2L), Arrays.asList(3L, 4L)),
        enumValue,
        new BigDecimal("1000.00"),
        new BigDecimal("900.00"),
        new BigDecimal("100.00"),
        false,
        singletonList(samplePosition),
        samplePosition,
        samplePosition,
        preferredPositionsMap,
        Collections.singletonMap("key1", singletonList(samplePosition)),
        Collections.singletonMap("outerKey", Collections.singletonMap("innerKey", singletonList(samplePosition))),
        List.of(List.of(samplePosition)),
        List.of(List.of(List.of(samplePosition))),
        positionArray,
        positionArray,
        statesArray,
        nonNullStatesArray,
        List.of(enumValue),
        List.of(enumValue),
        new byte[]{7, 8, 9},
        new byte[]{1, 2, 3},
        crazyNestedList,
        crazyNestedList,
        Collections.singletonMap("key1", Collections.singletonMap("key2", 42L)),
        Collections.singletonMap("key1", Collections.singletonMap("key2", List.of(enumValue))),
        Collections.singletonMap("key1", Collections.singletonMap("key2", Collections.singletonMap("key3", samplePosition))),
        voyagerStepId,
        voyagerStepId
    );

    assertModelConversionEquality(globalPortfolio, "com.wealthfront.auto.types.global.TestStockPortfolio");

    Object portfolioWithNulls = newGeneratedObject(
        "com.wealthfront.auto.types.global.TestStockPortfolio",
        null,
        null,
        null,
        userId,
        "SP123",
        null,
        null,
        "Test Portfolio",
        null,
        Arrays.asList(Arrays.asList(1L, 2L), Arrays.asList(3L, 4L)),
        enumValue,
        null,
        null,
        null,
        null,
        null,
        null,
        nullablePosition,
        preferredPositionsMap,
        null,
        null,
        List.of(List.of(nullablePosition)),
        null,
        null,
        positionArray,
        null,
        nonNullStatesArray,
        null,
        List.of(enumValue),
        null,
        new byte[]{1, 2, 3},
        crazyNestedList,
        crazyNestedList,
        Collections.singletonMap("key1", Collections.singletonMap("key2", 42L)),
        Collections.singletonMap("key1", Collections.singletonMap("key2", List.of(enumValue))),
        null,
        null,
        voyagerStepId
    );

    assertModelConversionEquality(portfolioWithNulls, "com.wealthfront.auto.types.global.TestStockPortfolio");
  }

  private JavaApiCodeGeneratorImpl getGenerator() {
    JavaApiCodeGeneratorImpl generator = new JavaApiCodeGeneratorImpl();
    generator.validator = new JavaApiValidatorImpl();
    generator.deprecatedStubQueryConstructorGenerator = new DeprecatedStubQueryConstructorGeneratorImpl();
    return generator;
  }

  private void assertCopyAndToStringEquals(List<Object> objects, List<String> expectedResourceFiles) {
    try {
      List<String> expected = expectedResourceFiles.stream()
          .map(resourceName -> Unchecked.get(
              () -> IOUtils.toString(getClass().getResourceAsStream(resourceName), StandardCharsets.UTF_8)))
          .sorted()
          .collect(Collectors.toList());

      List<String> actual = objects.stream()
          .map(this::getCopiedObject)
          .map(Object::toString)
          .sorted()
          .collect(Collectors.toList());

      assertEquals(expected.size(), actual.size());
      for (int i = 0; i < expected.size(); i++) {
        assertEquals(expected.get(i), actual.get(i));
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private Object newGeneratedObject(String className) {
    try {
      return Class.forName(className).getConstructors()[0].newInstance();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private Object newGeneratedObject(String className, Object... constructorArgs) {
    try {
      Class<?> clazz = Class.forName(className);
      Constructor<?> constructor = Arrays.stream(clazz.getConstructors())
          .filter(c -> c.getParameterCount() == constructorArgs.length)
          .findFirst()
          .orElseThrow(() -> new IllegalArgumentException(
              String.format("No constructor found with %d parameters in class %s", constructorArgs.length, className)));
      return constructor.newInstance(constructorArgs);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private Object getCopiedObject(Object originalObject) {
    try {
      Object copyBuilder = originalObject.getClass().getMethod("copy").invoke(originalObject);
      Object copiedObject = copyBuilder.getClass().getMethod("build").invoke(copyBuilder);
      assertEquals(originalObject, copiedObject);
      return copiedObject;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private void assertJavaFilesCompile(List<JavaFile> files) {
    List<JavaFileObject> javaFileObjects = files.stream().map(JavaFileObject::new)
        .collect(Collectors.toList());

    DiagnosticCollector<javax.tools.JavaFileObject> diagnostics = new DiagnosticCollector<>();

    List<String> options = new ArrayList<>();
    options.add("-source");
    options.add("1.8");
    options.add("-target");
    options.add("1.8");
    JavaCompiler.CompilationTask task = compiler.getTask(
        null,
        fileManager,
        diagnostics,
        options,
        null,
        javaFileObjects);
    boolean success = task.call();
    if (!success) {
      diagnostics.getDiagnostics().forEach(System.out::println);
    }
    assertTrue(success);
  }

  private void assertJavaFilesDoNotCompile(List<JavaFile> files) {
    List<JavaFileObject> javaFileObjects = files.stream().map(JavaFileObject::new)
        .collect(Collectors.toList());

    DiagnosticCollector<javax.tools.JavaFileObject> diagnostics = new DiagnosticCollector<>();

    List<String> options = new ArrayList<>();
    options.add("-source");
    options.add("1.8");
    options.add("-target");
    options.add("1.8");
    JavaCompiler.CompilationTask task = compiler.getTask(
        null,
        fileManager,
        diagnostics,
        options,
        null,
        javaFileObjects);
    boolean success = task.call();
    if (!success) {
      diagnostics.getDiagnostics().forEach(System.out::println);
    }
    assertFalse(success);
  }

  private static class JavaFileObject extends SimpleJavaFileObject {

    private final String source;

    protected JavaFileObject(JavaFile javaFile) {
      super(URI.create("string:///" + javaFile.typeSpec.name.replaceAll("\\.", "/") +
          Kind.SOURCE.extension), Kind.SOURCE);
      this.source = javaFile.toString();
    }

    @Override
    public CharSequence getCharContent(boolean ignoreEncodingErrors) {
      return source;
    }

  }

  private void assertModelConversionEquality(Object globalObject, String globalClassName) {
    try {
      Class<?> globalClass = Class.forName(globalClassName);

      Object wfModel = globalClass
          .cast(globalObject)
          .getClass()
          .getMethod("toWFModel")
          .invoke(globalObject);

      Object convertedBack = globalClass
          .getMethod("toGlobal", Class.forName(globalClassName.replace("auto.types.global", "model")))
          .invoke(null, wfModel);

      assertEquals(globalObject, convertedBack);
      assertEquals(globalObject.hashCode(), convertedBack.hashCode());

    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private static Object[] createTypedArray(String className, int size, Object value) throws ClassNotFoundException {
    Class<?> clazz = Class.forName(className);
    Object[] array = (Object[]) Array.newInstance(clazz, size);
    if (value != null) {
      Array.set(array, 0, value);
    }
    return array;
  }

}
