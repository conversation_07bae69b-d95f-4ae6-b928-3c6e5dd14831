package com.kaching.api;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertEquals;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystem;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import org.apache.commons.io.IOUtils;

import com.kaching.platform.functional.Unchecked;
import com.squareup.javapoet.JavaFile;

public class GeneratedFileAssertions {

  public static void assertGeneratedFilesEqual(Class<?> testClass, List<String> expectedResourceFiles, List<JavaFile> actualJavaFiles) {
    List<String> expected = expectedResourceFiles.stream()
        .map(resourceName -> Unchecked.get(
            () -> IOUtils.toString(Objects.requireNonNull(testClass.getResourceAsStream(resourceName)), StandardCharsets.UTF_8)))
        .toList();
    List<String> actual = actualJavaFiles.stream()
        .sorted(Comparator.comparing(javaFile -> javaFile.typeSpec.name))
        .map(JavaFile::toString)
        .toList();
    assertEquals(expected.size(), actual.size());
    for (int i = 0; i < expected.size(); i++) {
      assertEquals(expected.get(i), actual.get(i));
    }
  }

  public static List<String> getAllNewFiles(FileSystem fakeFs) throws IOException {
    return Files.walk(fakeFs.getPath("."))
        .filter(Files::isRegularFile)
        .map(Path::toString)
        .filter(string -> !string.contains("target"))
        .sorted()
        .collect(toList());
  }
}
