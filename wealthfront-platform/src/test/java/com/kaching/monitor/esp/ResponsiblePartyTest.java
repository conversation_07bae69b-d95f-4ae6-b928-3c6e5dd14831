package com.kaching.monitor.esp;

import static com.kaching.Author.DAVID_FORTUNATO;
import static com.kaching.Author.TRADING_TEAM;
import static com.wealthfront.test.Assert.assertContains;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.util.Collection;

import org.junit.Test;

import com.kaching.Author;

public class ResponsiblePartyTest {

  @Test
  public void testGetAlertedContacts() {
    Collection<Author> contacts1 = ResponsibleParty.Util.getAlertedContacts(
        TestClass.class);
    assertEquals(2, contacts1.size());
    assertContains(contacts1, TRADING_TEAM);
    assertContains(contacts1, DAVID_FORTUNATO);

    Collection<Author> contacts2 = ResponsibleParty.Util.getAlertedContacts(
        TestClass.class.getName());
    assertEquals(2, contacts2.size());
    assertContains(contacts2, TRADING_TEAM);
    assertContains(contacts2, DAVID_FORTUNATO);

    Collection<Author> contacts3 = ResponsibleParty.Util.getAlertedContacts(
        TestClass.class.getName() + "(some,parameters)");
    assertEquals(2, contacts1.size());
    assertContains(contacts3, TRADING_TEAM);
    assertContains(contacts3, DAVID_FORTUNATO);

    assertEquals(0, ResponsibleParty.Util.getAlertedContacts("bad class name").size());

    assertEquals(0, ResponsibleParty.Util.getAlertedContacts((String) null).size());
  }

  @Test
  public void testParseQuery() {
    assertEquals("com.kaching.monitor.esp.SendExceptionAlerts",
        ResponsibleParty.Util.parseQuery("com.kaching.monitor.esp.SendExceptionAlerts(some,parameters)"));
    assertEquals("com.kaching.monitor.esp.SendExceptionAlerts2",
        ResponsibleParty.Util.parseQuery("com.kaching.monitor.esp.SendExceptionAlerts2"));
    assertNull(ResponsibleParty.Util.parseQuery(null));
  }

  @ResponsibleParty(value = {TRADING_TEAM, DAVID_FORTUNATO})
  class TestClass {}

}
