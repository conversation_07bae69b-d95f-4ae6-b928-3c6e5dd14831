package com.kaching.entities.types;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.junit.Test;

import com.wealthfront.util.time.Inception;

public class InceptionDateTimeConverterTest {

  @Test
  public void conversion() {
    Inception.DateTime date = new Inception.DateTime(new DateTime(1984, 2, 13, 12, 34, 12, 890, ET));
    assertEquals("1984-02-13 12:34:12.890", new InceptionDateTimeConverter().toString(date));
    assertEquals(date, new InceptionDateTimeConverter().fromString("1984-02-13 12:34:12.890"));
  }

}
