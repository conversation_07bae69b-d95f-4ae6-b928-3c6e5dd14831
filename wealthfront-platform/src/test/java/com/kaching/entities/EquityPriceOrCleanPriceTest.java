package com.kaching.entities;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.kaching.entities.CleanPrice.cleanPrice;
import static com.kaching.entities.Price.price;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.kaching.platform.converters.Converter;
import com.twolattes.json.Entity;
import com.twolattes.json.EntityMarshaller;
import com.twolattes.json.Json;
import com.twolattes.json.Value;
import com.twolattes.json.types.JsonType;

public class EquityPriceOrCleanPriceTest {

  private final JsonType<EquityPriceOrCleanPrice, Json.Value> marshaller = new EquityPriceOrCleanPrice.JsonType();
  private final Converter<EquityPriceOrCleanPrice> converter = new EquityPriceOrCleanPrice.Converter();

  @Test
  public void json_marshallAsField() {
    EntityMarshaller<TestClass> marshaller = createEntityMarshaller(TestClass.class);
    TestClass instance1 = new TestClass(price(1.00));
    TestClass instance2 = new TestClass(cleanPrice(1.01));

    Json.Value expected1 = Json.object(
        Json.string("equityPriceOrCleanPrice"), Json.number(1.0)
    );
    assertMarshalling(marshaller, expected1, instance1);

    Json.Value expected2 = Json.object(
        Json.string("equityPriceOrCleanPrice"), Json.string("cp:1.01")
    );
    assertMarshalling(marshaller, expected2, instance2);
  }

  @Test
  public void json_upgradeAndDowngradePrice() {
    EntityMarshaller<TestClassOldVersion> oldMarshaller = createEntityMarshaller(TestClassOldVersion.class);
    EntityMarshaller<TestClass> marshaller = createEntityMarshaller(TestClass.class);

    TestClass instance = marshaller.unmarshall(oldMarshaller.marshall(new TestClassOldVersion(price(1.23))));

    Json.Value expected1 = Json.object(
        Json.string("equityPriceOrCleanPrice"), Json.number(1.23)
    );
    assertMarshalling(marshaller, expected1, instance);

    assertEquals(price(1.23), oldMarshaller.unmarshall(marshaller.marshall(instance)).equityPriceOrCleanPrice);
  }

  @Test
  public void json_unmarshall() {
    assertEquals(price(1.23), marshaller.unmarshall(Json.number(1.23)));
    assertEquals(price(1.23), marshaller.unmarshall(Json.string("1.23")));
    assertEquals(cleanPrice(1.23), marshaller.unmarshall(Json.string("cp:1.23")));
  }

  @Test
  public void converter() {
    assertEquals(price(1.23), converter.fromString("1.23"));
    assertEquals(cleanPrice(1.23), converter.fromString("cp:1.23"));
    assertEquals("1.23", converter.toString(price(1.23)));
    assertEquals("cp:1.23", converter.toString(cleanPrice(1.23)));
  }

  @Entity
  private static class TestClass {

    @Value
    EquityPriceOrCleanPrice equityPriceOrCleanPrice;

    TestClass() { }

    TestClass(EquityPriceOrCleanPrice price) {
      this.equityPriceOrCleanPrice = price;
    }

  }

  @Entity
  private static class TestClassOldVersion {

    @Value
    Price equityPriceOrCleanPrice;

    TestClassOldVersion() { }

    TestClassOldVersion(Price price) {
      this.equityPriceOrCleanPrice = price;
    }

  }

}
