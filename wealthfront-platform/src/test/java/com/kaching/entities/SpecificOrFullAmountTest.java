package com.kaching.entities;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.kaching.entities.Money.money;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import com.kaching.entities.SpecificOrFullAmount.FullAmount;
import com.kaching.entities.SpecificOrFullAmount.SpecificAmount;
import com.twolattes.json.Json;

public class SpecificOrFullAmountTest {

  @Test
  public void marshalling_specificAmount() {
    SpecificOrFullAmount specificAmount = new SpecificAmount(
        money(1_234.56)
    );

    Json.Object expected = Json.object(
        "amount", 1_234.56,
        "type", "specific-amount"
    );

    assertMarshalling(
        createEntityMarshaller(SpecificOrFullAmount.class),
        expected,
        specificAmount
    );
  }

  @Test
  public void marshalling_fullAmount() {
    SpecificOrFullAmount fullAmount = new FullAmount();

    Json.Object expected = Json.object(
        "type", "full-amount"
    );

    assertMarshalling(
        createEntityMarshaller(SpecificOrFullAmount.class),
        expected,
        fullAmount
    );
  }

  @Test
  public void visitor() {
    assertTrue(new SpecificAmount(money(10)).visit(new SpecificOrFullAmount.DefaultSpecificOrFullAmountVisitor<Boolean>(false) {

      @Override
      public Boolean caseSpecificAmount(SpecificAmount specificAmount) {
        assertEquals(money(10), specificAmount.getAmount());
        return true;
      }

    }));

    assertTrue(new FullAmount().visit(new SpecificOrFullAmount.DefaultSpecificOrFullAmountVisitor<Boolean>(false) {

      @Override
      public Boolean caseFullAmount(FullAmount fullAmount) {
        return true;
      }

    }));
  }

}