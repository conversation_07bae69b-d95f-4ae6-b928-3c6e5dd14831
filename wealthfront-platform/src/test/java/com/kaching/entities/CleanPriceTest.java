package com.kaching.entities;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.entities.AccruedInterest.accruedInterest;
import static com.kaching.entities.CleanPercentOfPar.cleanPercentOfPar;
import static com.kaching.entities.CleanPrice.cleanPrice;
import static com.kaching.entities.Money.money;
import static com.kaching.entities.Price.price;
import static com.kaching.entities.Quantity.quantity;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;

import org.junit.Test;

import com.twolattes.json.Json;

public class CleanPriceTest {

  @Test
  public void multiply() {
    assertEquals(money(4.99995), cleanPrice(0.99999).multiply(quantity(5)));
    assertEquals(money(-4.99995), cleanPrice(0.99999).multiply(quantity(-5)));
  }

  @Test
  public void isLessThan() {
    assertTrue(cleanPrice(19.0).isLessThan(cleanPrice(19.00001)));
    assertTrue(cleanPrice(19.0001).isLessThan(cleanPrice(19.0002)));
    assertFalse(cleanPrice(19.0100).isLessThan(cleanPrice(19.0100)));
    assertFalse(cleanPrice(19.0001).isLessThan(cleanPrice(19.0000)));
  }

  @Test
  public void testIsLessThanOrEqualTo() {
    assertTrue(cleanPrice(19.0001).isLessThanOrEqualTo(cleanPrice(19.0002)));
    assertTrue(cleanPrice(19.0100).isLessThanOrEqualTo(cleanPrice(19.0100)));
    assertFalse(cleanPrice(19.0001).isLessThanOrEqualTo(cleanPrice(19.0000)));
    assertTrue(cleanPrice(19.0).isLessThanOrEqualTo(cleanPrice(19.00001)));
  }

  @Test
  public void testIsGreaterThan() {
    assertFalse(cleanPrice(19.0001).isGreaterThan(cleanPrice(19.0002)));
    assertFalse(cleanPrice(19.0100).isGreaterThan(cleanPrice(19.0100)));
    assertTrue(cleanPrice(19.0001).isGreaterThan(cleanPrice(19.0000)));
    assertTrue(cleanPrice(19.00001).isGreaterThan(cleanPrice(19.0)));
  }

  @Test
  public void testIsGreaterThanOrEqualTo() {
    assertFalse(cleanPrice(19.0001).isGreaterThanOrEqualTo(cleanPrice(19.0002)));
    assertTrue(cleanPrice(19.0100).isGreaterThanOrEqualTo(cleanPrice(19.0100)));
    assertTrue(cleanPrice(19.0001).isGreaterThanOrEqualTo(cleanPrice(19.0000)));
    assertFalse(cleanPrice(19.0).isGreaterThanOrEqualTo(cleanPrice(19.00001)));
  }

  @Test
  public void toEquityPriceOrCleanPercentOfPar() {
    CleanPrice cleanPrice = cleanPrice(0.99999);
    EquityPriceOrCleanPercentOfPar cleanPercentOfPar = cleanPercentOfPar(99.999);
    assertEquals(cleanPercentOfPar, cleanPrice.toEquityPriceOrCleanPercentOfPar());
  }

  @Test
  public void marshall() {
    CleanPrice.JsonType jsonType = new CleanPrice.JsonType();
    CleanPrice cleanPrice = cleanPrice(BigDecimal.valueOf(0.99999));
    Json.String string = Json.string("cp:0.99999");

    assertEquals(cleanPrice, jsonType.nullSafeUnmarshall(string));
    assertEquals(string, jsonType.nullSafeMarshall(cleanPrice));
  }

  @Test
  public void marshalling() {
    assertMarshalling(createMarshaller(CleanPrice.class), Json.string("cp:0.99999"), cleanPrice("0.99999"));
  }

  @Test
  public void add_accruedInterest() {
    assertEquals(price("46.65"), cleanPrice("42.42").add(accruedInterest("4.23")));
  }

  @Test
  public void converter() {
    CleanPrice.Converter converter = new CleanPrice.Converter();
    assertEquals(cleanPrice(0.9999), converter.fromNonNullableString("cp:0.99990"));
    assertEquals("cp:0.9999", converter.nonNullableToString(cleanPrice(0.9999)));
  }

  @Test
  public void numberConverter() {
    CleanPrice.NumberConverter converter = new CleanPrice.NumberConverter();
    assertEquals(cleanPrice(0.9999), converter.fromNonNullableString("0.99990"));
    assertEquals("0.9999", converter.nonNullableToString(cleanPrice(0.9999)));
  }

}
