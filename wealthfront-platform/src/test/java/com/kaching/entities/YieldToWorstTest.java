package com.kaching.entities;

import static com.kaching.entities.YieldToWorst.yieldToWorst;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.kaching.DefaultKachingMarshallers;
import com.twolattes.json.Json;

public class YieldToWorstTest {

  @Test
  public void marshalling() {
    assertMarshalling(DefaultKachingMarshallers.createMarshaller(YieldToWorst.class),
        Json.number(0.1234),
        yieldToWorst("0.1234"));
  }

  @Test
  public void converting() {
    YieldToWorst.Converter converter = new YieldToWorst.Converter();
    YieldToWorst real = yieldToWorst("0.1234");
    String string = "0.1234";
    assertEquals(real, converter.fromNonNullableString(string));
    assertEquals(string, converter.nonNullableToString(real));
  }

}