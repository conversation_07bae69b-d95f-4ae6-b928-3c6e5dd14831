package com.kaching.entities.converters;

import java.math.BigDecimal;

import org.junit.Test;

import com.kaching.DefaultKachingMarshallers;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

public class EntityConverterTest {

  @Test(expected = IllegalStateException.class)
  public void castThrowsISE() {
    new EntityConverter<>(DefaultKachingMarshallers.createEntityMarshaller(Foo.class)).fromString("{\"bar\":\"123\"}");
  }

  @Entity
  static class Foo {

    @Value BigDecimal bar;
  }

}
