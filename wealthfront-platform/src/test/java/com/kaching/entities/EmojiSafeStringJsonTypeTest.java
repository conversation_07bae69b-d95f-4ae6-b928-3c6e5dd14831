package com.kaching.entities;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Test;

import com.twolattes.json.Json;

public class EmojiSafeStringJsonTypeTest {

  private static final EmojiSafeStringJsonType type = new EmojiSafeStringJsonType();

  @Test
  public void marshal() {
    assertEquals(Json.string("Joy \uD83D\uDE02"), type.marshall("Joy :joy:"));
    assertEquals(Json.string("Clap \uD83D\uDC4F"), type.marshall("Clap :clap:"));
    assertNull(type.marshall(null));
  }

  @Test
  public void unmarshal() {
    assertEquals("Joy :joy:", type.unmarshall(Json.string("Joy \uD83D\uDE02")));
    assertEquals("Clap :clap:", type.unmarshall(Json.string("Clap \uD83D\uDC4F")));
    assertNull(type.unmarshall(null));
  }

}