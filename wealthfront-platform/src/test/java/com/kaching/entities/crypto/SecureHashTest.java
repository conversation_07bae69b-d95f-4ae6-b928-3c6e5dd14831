package com.kaching.entities.crypto;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertMarshalling;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import com.kaching.security.ShaHasher;
import com.twolattes.json.Entity;
import com.twolattes.json.EntityMarshaller;
import com.twolattes.json.Json;
import com.twolattes.json.Value;

public class SecureHashTest {

  @Test
  public void marshall() {
    String stringHash = "315f5bdb76d078c43b8ac0064e4a0164612b1fce77c869345bfc94c75894edd3";
    EntityMarshaller<DummyJson> marshaller = createEntityMarshaller(DummyJson.class);
    Json.Object expected = object(
        "hash", stringHash
    );
    assertMarshalling(marshaller, expected, new DummyJson(new SecureHash(stringHash)));
  }

  @Test
  public void convertsToLowerCase() {
    assertEquals("315f5bdb76d078c43b8ac0064e4a0164612b1fce77c869345bfc94c75894edd3",
        new SecureHash("315F5BDB76D078C43B8AC0064E4A0164612B1FCE77C869345BFC94C75894EDD3").getId());

    assertEquals(
        new SecureHash("315f5bdb76d078c43b8ac0064e4a0164612b1fce77c869345bfc94c75894edd3"),
        new SecureHash("315F5BDB76D078C43B8AC0064E4A0164612B1FCE77C869345BFC94C75894EDD3")
    );
  }

  @Test
  public void toBytes_mostlyLeadingZeros() {
    byte[] bytes = new SecureHash("00000000000000000000000000000000000000000000000000000000000000ff").toBytes();

    assertEquals(32, bytes.length);
    for (int i = 0; i < 32; i++) {
      if (i == 31) {
        assertEquals((byte) -1, bytes[i]);
      } else {
        assertEquals((byte) 0, bytes[i]);
      }
    }
  }

  @Test
  public void toBytes_mostlyTrailingZeros() {
    byte[] bytes = new SecureHash("ff00000000000000000000000000000000000000000000000000000000000000").toBytes();

    assertEquals(32, bytes.length);
    for (int i = 0; i < 32; i++) {
      if (i == 0) {
        assertEquals((byte) -1, bytes[i]);
      } else {
        assertEquals((byte) 0, bytes[i]);
      }
    }
  }

  @Test
  public void invalidHex() {
    assertThrows(IllegalArgumentException.class, () ->
        new SecureHash("z0000000000000000000000000000000000000000000000000000000000000ff"));
  }

  @Test
  public void invalidLength() {
    assertThrows(IllegalArgumentException.class, () -> new SecureHash("deadbeef"));
  }

  @Test
  public void fromBytes() {
    byte[] bytes = new byte[32];
    bytes[1] = (byte) Integer.parseUnsignedInt("DE", 16);
    bytes[2] = (byte) Integer.parseUnsignedInt("AD", 16);
    bytes[3] = (byte) Integer.parseUnsignedInt("BE", 16);
    bytes[4] = (byte) Integer.parseUnsignedInt("EF", 16);
    assertEquals("00deadbeef000000000000000000000000000000000000000000000000000000",
        SecureHash.fromBytes(bytes).getId());
  }

  @Test
  public void fromHashCode() throws Exception {
    byte[] message = "lakata".getBytes("UTF-8");
    HashCode hashCode = Hashing.sha256().hashBytes(message);
    assertEquals(ShaHasher.hashSha256(message), SecureHash.fromHashCode(hashCode));
  }

  @Test
  public void fromHashCode_wrongSize() throws Exception {
    byte[] message = "lakata".getBytes("UTF-8");
    HashCode hashCode = Hashing.crc32().hashBytes(message);
    assertThrows(IllegalArgumentException.class, () -> SecureHash.fromHashCode(hashCode));
  }

  @Entity
  private static class DummyJson {

    @Value(type = SecureHash.JsonType.class) SecureHash hash;

    DummyJson() { /* JSON */ }

    DummyJson(SecureHash hash) {
      this.hash = hash;
    }

  }

}