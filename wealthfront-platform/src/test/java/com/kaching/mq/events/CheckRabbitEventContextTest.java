package com.kaching.mq.events;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.jmock.Mockery;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.monitoring.icinga.IcingaOutput;
import com.kaching.platform.testing.Mockeries;
import com.kaching.util.Resettable;
import com.rabbitmq.client.Connection;

public class CheckRabbitEventContextTest {

  private final Mockery mockery = Mockeries.mockery();

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void getIcingaOutput_ok() {
    Connection connection = mockery.mock(Connection.class);

    CheckRabbitEventContext check = new CheckRabbitEventContext();
    check.rabbitEventContextResettable = new Resettable<RabbitEventContext>() {
      @Override
      protected RabbitEventContext create() {
        return new RabbitEventContext(() -> connection);
      }

      @Override
      protected int numRetries() {
        return 0;
      }
    };

    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.OKAY, output.getExitCode());
  }

  @Test
  public void getIcingaOutput_critical() {
    CheckRabbitEventContext check = new CheckRabbitEventContext();
    check.rabbitEventContextResettable = new Resettable<RabbitEventContext>() {
      @Override
      protected RabbitEventContext create() {
        throw new RuntimeException();
      }

      @Override
      protected int numRetries() {
        return 0;
      }
    };

    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.CRITICAL, output.getExitCode());
    assertTrue(output.getMessage().contains("Error creating RabbitEventContext"));
  }

}