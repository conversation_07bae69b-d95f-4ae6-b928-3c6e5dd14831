package com.kaching.mq;

import static com.google.common.base.Charsets.UTF_8;
import static com.kaching.platform.testing.QueryMatcher.query;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.jmock.Expectations;
import org.junit.After;
import org.junit.Test;

import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryExecutorService;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;

public class QueryConsumerTest {

  static class TestQuery extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      return true;
    }

  }

  static class Kaboom extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      throw new RuntimeException("kaboom!");
    }

  }

  private final WFMockery mockery = Mockeries.mockery(true);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void call() throws Exception {
    final QueryConsumer consumer = new QueryConsumer();
    consumer.q = mockery.mock(MessageQueue.class);

    mockery.checking(new Expectations() {{
      oneOf(consumer.q).next(with(any(QueryConsumer.QueryMessageExecutor.class)));
    }});

    consumer.call();
  }

  @Test
  public void queryUnmarshalledAndExecuted() throws Exception {
    final QueryConsumer consumer = new QueryConsumer();
    consumer.jsonMarshallerFactory = new DefaultJsonMarshallerFactory();
    final CompletableFuture<Void> future = mockery.mockUnchecked(CompletableFuture.class);
    consumer.executor = mockery.mock(QueryExecutorService.class);

    mockery.checking(new Expectations() {{
      oneOf(consumer.executor).submit(with(query(new TestQuery())));
      will(returnValue(future));
      oneOf(future).get();
    }});

    QueryInvocation<TestQuery> invocation = new QueryInvocation<>(TestQuery.class);
    consumer.new QueryMessageExecutor().withMessage(
        QueryInvocation.MARSHALLER.marshall(invocation).toString().getBytes(UTF_8), false);
  }

  @Test(expected = ExecutionException.class)
  public void failingQueryExeceptionPropagatesUp() throws Exception {
    final QueryConsumer consumer = new QueryConsumer();
    consumer.jsonMarshallerFactory = new DefaultJsonMarshallerFactory();
    consumer.executor = mockery.mock(QueryExecutorService.class);

    final CompletableFuture<Void> future = mockery.mockUnchecked(CompletableFuture.class);

    mockery.checking(new Expectations() {{
      oneOf(consumer.executor).submit(with(query(new Kaboom())));
      will(returnValue(future));
      oneOf(future).get();
      will(throwException(new ExecutionException(new RuntimeException("kaboom!"))));
    }});

    QueryInvocation<Kaboom> invocation = new QueryInvocation<>(Kaboom.class);
    consumer.new QueryMessageExecutor().withMessage(
        QueryInvocation.MARSHALLER.marshall(invocation).toString().getBytes(UTF_8), false);
  }

  @Test
  public void redeliveredQueryHasExceptionEaten() throws Exception {
    final QueryConsumer consumer = new QueryConsumer();
    consumer.jsonMarshallerFactory = new DefaultJsonMarshallerFactory();
    consumer.executor = mockery.mock(QueryExecutorService.class);

    final CompletableFuture<Void> future = mockery.mockUnchecked(CompletableFuture.class);

    mockery.checking(new Expectations() {{
      oneOf(consumer.executor).submit(with(query(new Kaboom())));
      will(returnValue(future));
      oneOf(future).get();
      will(throwException(new ExecutionException(new RuntimeException("kaboom!"))));
    }});

    QueryInvocation<Kaboom> invocation = new QueryInvocation<>(Kaboom.class);
    consumer.new QueryMessageExecutor().withMessage(
        QueryInvocation.MARSHALLER.marshall(invocation).toString().getBytes(UTF_8), true);
    // Does NOT throw exception since isRedelivery == true and Future throws ExecutionException.
  }

}
