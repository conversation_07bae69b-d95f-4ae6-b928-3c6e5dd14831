package com.kaching.util.parsing;

import static com.kaching.util.parsing.FixedWidth.DeserializationBehavior.AUTO_FILL_WITH_PAD_CHAR;
import static com.kaching.util.parsing.FixedWidth.FillOption.LEFT_PAD;
import static com.kaching.util.parsing.FixedWidth.FillOption.RIGHT_PAD;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;

import org.joda.time.LocalDate;
import org.junit.Test;

import com.google.common.base.Objects;
import com.kaching.user.UserId;
import com.kaching.util.time.Year;
import com.kaching.util.types.IntegerConverter;
import com.kaching.util.types.LocalDateConverter;

public class FixedWidthLineConverterTest {

  @Test
  public void serializeAndDeserialize_successful() {
    FixedWidthLineConverter<ValidLineWithLeftPad> converter = new FixedWidthLineConverter<>(ValidLineWithLeftPad.class);
    assertEquals("199400012345", converter.serialize(new ValidLineWithLeftPad(Year.year(1994), new UserId(12345))));
    assertEquals("199400000123", converter.serialize(new ValidLineWithLeftPad(Year.year(1994), new UserId(123))));
    assertEquals(new ValidLineWithLeftPad(Year.year(1994), new UserId(12345)), converter.deserialize("199400012345"));
    assertEquals(new ValidLineWithLeftPad(Year.year(1994), new UserId(123)), converter.deserialize("199400000123"));
  }

  @Test
  public void serialize_throwsWhenFieldIsTooShort_andNoPaddingSpecified() {
    FixedWidthLineConverter<ValidLineWithLeftPad> converter = new FixedWidthLineConverter<>(ValidLineWithLeftPad.class);
    Year threeDigitYear = Year.year(999);
    assertThrows(IllegalArgumentException.class, "Serialized field year has illegal length of 3",
        () -> converter.serialize(new ValidLineWithLeftPad(threeDigitYear, new UserId(12345))));
  }

  @Test
  public void serializeAndDeserialize_withDefaultStringConverter() {
    FixedWidthLineConverter<ValidLineWithDefaultStringConverter> converter = new FixedWidthLineConverter<>(ValidLineWithDefaultStringConverter.class);
    assertEquals("helloAZ", converter.serialize(new ValidLineWithDefaultStringConverter("hello", "AZ")));
    assertEquals("xxxOOAZ", converter.serialize(new ValidLineWithDefaultStringConverter("OO", "AZ")));
    assertEquals("xxOOOAZ", converter.serialize(new ValidLineWithDefaultStringConverter("OOO", "AZ")));
    assertEquals("xxxxxAZ", converter.serialize(new ValidLineWithDefaultStringConverter("", "AZ")));

    assertEquals(new ValidLineWithDefaultStringConverter("xxOOO", "AZ"), converter.deserialize("xxOOOAZ"));
  }

  @Test
  public void serialize_withNullValue_throws() {
    FixedWidthLineConverter<ValidLineWithLeftPad> converter = new FixedWidthLineConverter<>(ValidLineWithLeftPad.class);
    assertThrows(NullPointerException.class, "Converter for field userId returned null",
        () -> converter.serialize(new ValidLineWithLeftPad(Year.year(2011), null)));
  }

  @Test
  public void serialize_withRightPad() {
    FixedWidthLineConverter<ValidLineWithRightPad> converter = new FixedWidthLineConverter<>(ValidLineWithRightPad.class);
    assertEquals("199412345xxx", converter.serialize(new ValidLineWithRightPad(Year.year(1994), new UserId(12345))));
    assertEquals("1994123456xx", converter.serialize(new ValidLineWithRightPad(Year.year(1994), new UserId(123456))));
  }

  @Test
  public void serialize_withSerializedFieldTooLong_throws() {
    FixedWidthLineConverter<ValidLineWithRightPad> converter = new FixedWidthLineConverter<>(ValidLineWithRightPad.class);
    assertEquals("199412345xxx", converter.serialize(new ValidLineWithRightPad(Year.year(1994), new UserId(12345))));
    assertEquals("199412345678", converter.serialize(new ValidLineWithRightPad(Year.year(1994), new UserId(12345678))));
    assertThrows(IllegalArgumentException.class, "Serialized field userId has illegal length of 9",
        () -> converter.serialize(new ValidLineWithRightPad(Year.year(1994), new UserId(123456789))));
  }

  @Test
  public void deserialize_withInputTooLong_throws() {
    FixedWidthLineConverter<ValidLineWithRightPad> converter =
        new FixedWidthLineConverter<>(ValidLineWithRightPad.class);
    assertThrows(IllegalArgumentException.class, "Converter expects line of length 12, minimum length 12. Given 25",
        () -> converter.deserialize("aasdfasdfasdfasdfasdfasdf"));
  }

  @Test
  public void invalidLineWithGapBetweenParameters_throwsUponInstantiation() {
    assertThrows(IllegalStateException.class,
        "Could not create converter. No field covers index 4 and no fill char was given",
        () -> new FixedWidthLineConverter<>(InvalidLineWithGapBetweenParameters.class));
  }

  @Test
  public void invalidLineWithOverlappingParameters_throwsUponInstantiation() {
    assertThrows(IllegalStateException.class, "Could not create converter. Fields overlap at index 3",
        () -> new FixedWidthLineConverter<>(InvalidLineWithOverlappingParameters.class));
  }

  @Test
  public void invalidLineWithIncompatibleConverterType_throwsDuringSerialization() {
    FixedWidthLineConverter<InvalidLineWithIncompatibleConverterType> converter = new FixedWidthLineConverter<>(InvalidLineWithIncompatibleConverterType.class);
    assertThrows(ClassCastException.class,
        "class com.kaching.user.UserId cannot be cast to class com.kaching.util.time.Year (com.kaching.user.UserId and com.kaching.util.time.Year are in unnamed module of loader 'app')",
        () -> converter.serialize(new InvalidLineWithIncompatibleConverterType(Year.year(2014), new UserId(1234))));
    assertThrows(IllegalArgumentException.class,
        "Can not set com.kaching.user.UserId field com.kaching.util.parsing.FixedWidthLineConverterTest$InvalidLineWithIncompatibleConverterType.userId to com.kaching.util.time.Year",
        () -> converter.deserialize("20141234"));
  }

  @Test
  public void deserialize_converterThrows_withUsefulMessage() {
    FixedWidthLineConverter<ValidLineWithRightPad> converter = new FixedWidthLineConverter<>(ValidLineWithRightPad.class);
    assertThrows(RuntimeException.class, "Failed deserializing field userId from \"abcdefxx\"",
        () -> converter.deserialize("1994abcdefxx"));
  }

  @Test
  public void invalidLineWithFillSpecifiedButNoPadChar_throwsDuringInstantiation() {
    assertThrows(IllegalStateException.class, "field year can be padded, but no padding char specified",
        () -> new FixedWidthLineConverter<>(InvalidLineWithFillSpecifiedButNoPadChar.class));
  }

  @Test
  public void invalidLineWithLengthSpecifiedButWrongLengthAndNoFillChar_throwsDuringInstantiation() {
    assertThrows(IllegalArgumentException.class,
        "A line length of 12 was explicitly specified, but the detected line length is shorter (9) and no filler char was given",
        () -> new FixedWidthLineConverter<>(InvalidLineWithLengthSpecifiedButWrongLengthAndNoFillChar.class));
  }

  @Test
  public void validLineWithExtraLengthSpecified() {
    FixedWidthLineConverter<ValidLineWithExtraLengthSpecifiedAndGaps> converter = new FixedWidthLineConverter<>(
        ValidLineWithExtraLengthSpecifiedAndGaps.class);
    assertEquals("aaaaa--bb---", converter.serialize(new ValidLineWithExtraLengthSpecifiedAndGaps("aaaaa", "bb")));
  }

  @Test
  public void invalidLineWithParametersLongerThanSpecifiedLength_throws() {
    assertThrows(IllegalArgumentException.class,
        "A line length of 10 was explicitly specified, but the parameters extend to a total length of 15",
        () -> new FixedWidthLineConverter<>(InvalidLineWithParametersLongerThanSpecifiedLength.class));
  }

  @Test
  public void deserialize_validLine_appendWhiteSpaceToTheEnd() {
    FixedWidthLineConverter<GreenDotNetAccountSettlementRecord> converter =
        new FixedWidthLineConverter<>(GreenDotNetAccountSettlementRecord.class);
    GreenDotNetAccountSettlementRecord actual = converter.deserialize(
            "01532020073120200731GDNW8925        9ba4d773-fbb3-4642-9bba-3409fa8f4103    0             ************.00WFNT    c9b2321c-6c90-4f7f-949b-de10999ae33d    ");
    assertEquals(153, actual.recordId);
    assertEquals(new LocalDate(2020, 7, 31), actual.effectiveDate);
  }

  @Test
  public void deserialize_invalidLine_invalidLineShorterThanMinimumRequiredLength() {
    FixedWidthLineConverter<ValidLineWithRightPad> converter_noWhiteSpacePadding = new FixedWidthLineConverter<>(ValidLineWithRightPad.class);
    assertThrows(IllegalArgumentException.class, "Converter expects line of length 12, minimum length 12. Given 5",
        () -> converter_noWhiteSpacePadding.deserialize("aasdf"));

    FixedWidthLineConverter<GreenDotNetAccountSettlementRecord> converter_allowWhiteSpacePadding = new FixedWidthLineConverter<>(GreenDotNetAccountSettlementRecord.class);
    assertThrows(IllegalArgumentException.class, "Converter expects line of length 244, minimum length 15. Given 4",
        () -> converter_allowWhiteSpacePadding.deserialize(
        "0900"));
  }

  @Test
  public void validateAndGetMinimumRequiredLength() {
    FixedWidthLineConverter<GreenDotNetAccountSettlementRecord> converter_allowWhiteSpacePadding = new FixedWidthLineConverter<>(GreenDotNetAccountSettlementRecord.class);
    assertEquals(15, converter_allowWhiteSpacePadding.validateAndGetMinimumRequiredLength());
    FixedWidthLineConverter<ValidLineWithLeftPad> converter_noWhiteSpacePadding = new FixedWidthLineConverter<>(ValidLineWithLeftPad.class);
    assertEquals(12, converter_noWhiteSpacePadding.validateAndGetMinimumRequiredLength());
    assertThrows(IllegalArgumentException.class, "AUTO_FILL_WITH_PAD_CHAR field not allowed before REQUIRE_FULL_LENGTH field",
        () -> new FixedWidthLineConverter<>(InvalidLineWithInvalidDeserializationBehavior.class));
  }

  @FixedWidthLine
  private static class ValidLineWithLeftPad {

    ValidLineWithLeftPad() {}

    @FixedWidth(startIncl = 0, endExcl = 4, converter = Year.YearConverter.class)
    Year year;

    @FixedWidth(startIncl = 4, endExcl = 12, converter = UserId.Converter.class, fill = LEFT_PAD, padChar = '0')
    UserId userId;

    ValidLineWithLeftPad(Year year, UserId userId) {
      this.year = year;
      this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
      ValidLineWithLeftPad that = (ValidLineWithLeftPad) o;
      return Objects.equal(year, that.year) && Objects.equal(userId, that.userId);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(year, userId);
    }

  }

  @FixedWidthLine
  private static class ValidLineWithRightPad {

    ValidLineWithRightPad() {}

    @FixedWidth(startIncl = 0, endExcl = 4, converter = Year.YearConverter.class)
    Year year;

    @FixedWidth(startIncl = 4, endExcl = 12, converter = UserId.Converter.class, fill = RIGHT_PAD, padChar = 'x')
    UserId userId;

    ValidLineWithRightPad(Year year, UserId userId) {
      this.year = year;
      this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
      ValidLineWithLeftPad that = (ValidLineWithLeftPad) o;
      return Objects.equal(year, that.year) && Objects.equal(userId, that.userId);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(year, userId);
    }

  }

  @FixedWidthLine
  private static class ValidLineWithDefaultStringConverter {

    ValidLineWithDefaultStringConverter() {}

    @FixedWidth(startIncl = 0, endExcl = 5, fill = LEFT_PAD, padChar = 'x')
    String value1;

    @FixedWidth(startIncl = 5, endExcl = 7)
    String value2;

    ValidLineWithDefaultStringConverter(String value1, String value2) {
      this.value1 = value1;
      this.value2 = value2;
    }

    @Override
    public boolean equals(Object o) {
      ValidLineWithDefaultStringConverter that = (ValidLineWithDefaultStringConverter) o;
      return Objects.equal(value1, that.value1) &&
          Objects.equal(value2, that.value2);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(value1, value2);
    }

  }

  @FixedWidthLine
  private static class InvalidLineWithGapBetweenParameters {

    InvalidLineWithGapBetweenParameters() {}

    @FixedWidth(startIncl = 0, endExcl = 4, converter = Year.YearConverter.class)
    Year year;

    @FixedWidth(startIncl = 5, endExcl = 12, converter = UserId.Converter.class, fill = RIGHT_PAD, padChar = 'x')
    UserId userId;

    InvalidLineWithGapBetweenParameters(Year year, UserId userId) {
      this.year = year;
      this.userId = userId;
    }

  }

  @FixedWidthLine
  private static class InvalidLineWithOverlappingParameters {

    InvalidLineWithOverlappingParameters() {}

    @FixedWidth(startIncl = 0, endExcl = 4, converter = Year.YearConverter.class)
    Year year;

    @FixedWidth(startIncl = 3, endExcl = 12, converter = UserId.Converter.class, fill = RIGHT_PAD, padChar = 'x')
    UserId userId;

    InvalidLineWithOverlappingParameters(Year year, UserId userId) {
      this.year = year;
      this.userId = userId;
    }

  }

  @FixedWidthLine
  private static class InvalidLineWithIncompatibleConverterType {

    InvalidLineWithIncompatibleConverterType() {}

    @FixedWidth(startIncl = 0, endExcl = 4, converter = Year.YearConverter.class)
    Year year;

    @FixedWidth(startIncl = 4, endExcl = 8, converter = Year.YearConverter.class)
    UserId userId;

    InvalidLineWithIncompatibleConverterType(Year year, UserId userId) {
      this.year = year;
      this.userId = userId;
    }

  }

  @FixedWidthLine
  private static class InvalidLineWithFillSpecifiedButNoPadChar {

    InvalidLineWithFillSpecifiedButNoPadChar() {}

    @FixedWidth(startIncl = 0, endExcl = 4, converter = Year.YearConverter.class, fill = RIGHT_PAD)
    Year year;

    InvalidLineWithFillSpecifiedButNoPadChar(Year year) {
      this.year = year;
    }

  }

  @FixedWidthLine(length = 12, fillChar = '-')
  private static class ValidLineWithExtraLengthSpecifiedAndGaps {

    @FixedWidth(startIncl = 0, endExcl = 5, fill = LEFT_PAD, padChar = 'x')
    String value1;

    @FixedWidth(startIncl = 7, endExcl = 9)
    String value2;

    ValidLineWithExtraLengthSpecifiedAndGaps() {}

    ValidLineWithExtraLengthSpecifiedAndGaps(String value1, String value2) {
      this.value1 = value1;
      this.value2 = value2;
    }

  }

  @FixedWidthLine(length = 12)
  private static class InvalidLineWithLengthSpecifiedButWrongLengthAndNoFillChar {

    @FixedWidth(startIncl = 0, endExcl = 5)
    String value1;

    @FixedWidth(startIncl = 7, endExcl = 9)
    String value2;

    InvalidLineWithLengthSpecifiedButWrongLengthAndNoFillChar() {}

    InvalidLineWithLengthSpecifiedButWrongLengthAndNoFillChar(String value1, String value2) {
      this.value1 = value1;
      this.value2 = value2;
    }

  }

  @FixedWidthLine(length = 10)
  private static class InvalidLineWithParametersLongerThanSpecifiedLength {

    @FixedWidth(startIncl = 0, endExcl = 15)
    String value;

    InvalidLineWithParametersLongerThanSpecifiedLength() {}

    InvalidLineWithParametersLongerThanSpecifiedLength(String value) {
      this.value = value;
    }

  }

  @FixedWidthLine(length = 244)
  private static class GreenDotNetAccountSettlementRecord {

    @FixedWidth(startIncl = 0, endExcl = 4, fill = LEFT_PAD, padChar = '0', converter = IntegerConverter.class)
    int recordId = 153;

    @FixedWidth(startIncl = 4, endExcl = 15, converter = LocalDateConverter.class)
    LocalDate effectiveDate;

    @FixedWidth(startIncl = 15, endExcl = 244, fill = RIGHT_PAD, padChar = ' ', deserializationBehavior = AUTO_FILL_WITH_PAD_CHAR)
    String filler = "";

    @SuppressWarnings("unused")
    GreenDotNetAccountSettlementRecord() { /* Fixed width serialization */ }

    GreenDotNetAccountSettlementRecord(int recordId, LocalDate effectiveDate) {
      this.recordId = recordId;
      this.effectiveDate = effectiveDate;
    }

  }

  @FixedWidthLine(length = 10)
  private static class InvalidLineWithInvalidDeserializationBehavior{
    @FixedWidth(startIncl = 0, endExcl = 4, fill = LEFT_PAD, padChar = '0')
    String value1;

    @FixedWidth(startIncl = 4, endExcl = 7, padChar = ' ', deserializationBehavior = AUTO_FILL_WITH_PAD_CHAR)
    String value2;

    @FixedWidth(startIncl = 7, endExcl = 10)
    String value3;

    @SuppressWarnings("unused")
    InvalidLineWithInvalidDeserializationBehavior() { /* Fixed width serialization */ }

    InvalidLineWithInvalidDeserializationBehavior(String value1, String value2, String value3) {
      this.value1 = value1;
      this.value2 = value2;
      this.value3 = value3;
    }

  }

}