package com.kaching.util;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.kaching.platform.testing.Mockeries;

import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;

public class ZipperTest {

  private static Mockery mockery;

  @Before
  public void setUp() throws Exception {
    mockery = Mockeries.mockery(true);
  }

  @After
  public void tearDown() throws Exception {
    mockery.assertIsSatisfied();
  }

  @Test
  public void unzip() throws IOException, URISyntaxException, ZipException {
    final ZipFile zipFile = mockery.mock(ZipFile.class);
    String path = "new/path";
    Zipper zipper = new Zipper() {
      @Override
      public ZipFile getZip4JZipFile(File file) throws ZipException {
        return zipFile;
      }
    };

    mockery.checking(new Expectations() {{
      oneOf(zipFile).extractAll(path);
    }});

    zipper.unzip(null, path);

  }

}