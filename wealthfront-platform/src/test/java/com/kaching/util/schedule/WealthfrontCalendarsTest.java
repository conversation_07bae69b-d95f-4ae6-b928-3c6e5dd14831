package com.kaching.util.schedule;

import static com.kaching.util.schedule.WealthfrontCalendars.ALL_CALENDARS;
import static com.kaching.util.schedule.WealthfrontCalendars.WEALTHFRONT_HOLIDAY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.joda.time.LocalDate;
import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Streams;
import com.wealthfront.util.time.calendar.Calendars;

public class WealthfrontCalendarsTest {

  @Test
  public void numberOfCalendars() {
    assertEquals(4, ALL_CALENDARS.size());
  }

  @Test
  public void allMembersFinal() {
    assertFalse(Arrays.stream(WealthfrontCalendars.class.getDeclaredFields())
        .anyMatch(field -> !java.lang.reflect.Modifier.isFinal(field.getModifiers())));
  }

  @Test
  public void noOverlappingCalendarDescriptions() {
    Set<String> calendarDescriptions = new HashSet<>();
    Streams.concat(ALL_CALENDARS.stream(), Calendars.ALL_CALENDARS.stream()).forEach(calendar -> {
      assertFalse(calendarDescriptions.contains(calendar.description()));
      calendarDescriptions.add(calendar.description());
    });
  }

  @Test
  public void holidays2022() {
    Set<LocalDate> calculatedHolidays = new HashSet<>();
    for (LocalDate date = new LocalDate(2022, 1, 1); date.getYear() < 2023; date = date.plusDays(1)) {
      if (WEALTHFRONT_HOLIDAY.includesDay(date).getOrThrow()) {
        calculatedHolidays.add(date);
      }
    }
    assertEquals(ImmutableSet.of(
        new LocalDate(2022, 1, 17),
        new LocalDate(2022, 2, 21),
        new LocalDate(2022, 4, 15),
        new LocalDate(2022, 5, 30),
        new LocalDate(2022, 6, 20),
        new LocalDate(2022, 7, 4),
        new LocalDate(2022, 9, 5),
        new LocalDate(2022, 11, 24),
        new LocalDate(2022, 11, 25),
        new LocalDate(2022, 12, 26),
        new LocalDate(2022, 12, 27),
        new LocalDate(2022, 12, 28),
        new LocalDate(2022, 12, 29),
        new LocalDate(2022, 12, 30)
    ), calculatedHolidays);
  }

  @Test
  public void holidays2021() {
    Set<LocalDate> calculatedHolidays = new HashSet<>();
    for (LocalDate date = new LocalDate(2021, 1, 1); date.getYear() < 2022; date = date.plusDays(1)) {
      if (WEALTHFRONT_HOLIDAY.includesDay(date).getOrThrow()) {
        calculatedHolidays.add(date);
      }
    }
    assertEquals(ImmutableSet.of(
        new LocalDate(2021, 1, 1),
        new LocalDate(2021, 1, 18),
        new LocalDate(2021, 2, 15),
        new LocalDate(2021, 4, 2),
        new LocalDate(2021, 5, 31),
        new LocalDate(2021, 6, 18),
        new LocalDate(2021, 7, 5),
        new LocalDate(2021, 9, 6),
        new LocalDate(2021, 11, 25),
        new LocalDate(2021, 11, 26),
        new LocalDate(2021, 12, 24),
        new LocalDate(2021, 12, 27),
        new LocalDate(2021, 12, 28),
        new LocalDate(2021, 12, 29),
        new LocalDate(2021, 12, 30),
        new LocalDate(2021, 12, 31)
    ), calculatedHolidays);
  }

}