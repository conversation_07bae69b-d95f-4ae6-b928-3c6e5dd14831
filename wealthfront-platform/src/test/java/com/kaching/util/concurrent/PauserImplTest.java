package com.kaching.util.concurrent;

import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static org.junit.Assert.*;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import org.junit.Test;

public class PauserImplTest {

  @Test
  public void pauser_unpause() {
    Pauser w = new PauserImpl();
    ScheduledExecutorService service = Executors.newScheduledThreadPool(2);
    service.submit(() -> w.pause(15_000));
    service.schedule(w::unpause, 250, MILLISECONDS);
    waitForShutdown(service);
  }

  @Test
  public void pauser_unpause_2x() {
    Pauser w = new PauserImpl();
    ScheduledExecutorService service = Executors.newScheduledThreadPool(2);
    service.submit(() -> w.pause(15_000));
    service.schedule(w::unpause, 250, MILLISECONDS);
    service.schedule(w::unpause, 250, MILLISECONDS);
    waitForShutdown(service);
  }

  @Test
  public void pauser_interrupt() {
    Pauser w = new PauserImpl();
    Thread t = new Thread(() -> w.pause(15_000));
    t.start();

    ScheduledExecutorService service = Executors.newScheduledThreadPool(2);
    service.schedule(t::interrupt, 250, MILLISECONDS);
    waitForShutdown(service);
  }

  public static void waitForShutdown(ExecutorService service) {
    service.shutdown();
    try {
      if (!service.awaitTermination(5_000, MILLISECONDS)) {
        fail("failed to terminate test.");
      }
    } catch (InterruptedException e) {
      fail("failed to terminate test.");
    } finally {
      service.shutdownNow();
    }
  }

}