package com.kaching.util.concurrent;

import java.util.Random;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

import org.joda.time.Duration;

public class ThreadSchedulerSpy implements ThreadScheduler {
  
  private final AtomicLong sleepMillis = new AtomicLong();

  @Override
  public void yield() {
    
  }

  @Override
  public Thread newThread(Runnable runnable, String name) {
    return new Thread(runnable, name);
  }

  @Override
  public ReentrantLock newReentrantLock() {
    return new ReentrantLock();
  }

  @Override
  public <E> BlockingQueue<E> wrapBlockingQueue(BlockingQueue<E> delegate) {
    return delegate;
  }

  @Override
  public boolean sleep(Duration duration) {
    sleepMillis.addAndGet(duration.getMillis());
    return true;
  }

  @Override
  public Random getRandom() {
    return ThreadLocalRandom.current();
  }
  
  public long getSleepMillis() {
    return sleepMillis.get();
  }
  
}
