package com.kaching.util.push;

import static com.google.common.collect.Iterators.forArray;
import static com.google.common.collect.Iterators.getOnlyElement;
import static com.kaching.util.http.ResultBasedHttpClient.Response;
import static com.kaching.util.push.FirebaseResponse.responseToFirebaseResponse;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import org.junit.Test;

import com.kaching.util.functional.Result;
import com.twolattes.json.Json;

public class FirebaseResponseTest {

  @Test
  public void testResponseToFirebaseResponse() {
    String jsonString =
        "{" +
            "  \"multicast_id\":7996367428320179777," +
            "  \"success\":0," +
            "  \"failure\":1," +
            "  \"canonical_ids\":0," +
            "  \"results\": [" +
            "    {" +
            "      \"error\":\"NotRegistered\"" +
            "    }" +
            "  ]" +
            "}\n";
    Result<Response> result = Result.success((new Response(200, "200:OK", jsonString)));
    FirebaseResponse response = result.transformOnSuccess(responseToFirebaseResponse()).getOrThrow();
    assertMarshalling(FirebaseResponse.MARSHALLER, Json.object(
        "multicast_id", 7996367428320179777L,
        "success", 0,
        "failure", 1,
        "canonical_ids", 0,
        "results", Json.array(
            Json.object("error", "NotRegistered")
        )
    ), response);
    assertFalse(response.isSuccess());
    assertEquals(7996367428320179777L, response.getMulticastId().longValue());
    assertEquals(0, response.getSuccess().longValue());
    assertEquals(1, response.getFailure().longValue());
    assertEquals(0, response.getCanonicalIds().longValue());
    assertEquals("NotRegistered", getOnlyElement(forArray(response.getResults().getOrThrow())).getError().getOrThrow());
  }

}