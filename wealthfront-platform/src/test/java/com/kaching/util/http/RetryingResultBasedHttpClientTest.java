package com.kaching.util.http;

import static com.kaching.util.http.ResultBasedHttpClient.Response;
import static com.kaching.util.http.RetryingResultBasedHttpClient.retrying;
import static java.util.Collections.emptyMap;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.nio.channels.InterruptedByTimeoutException;

import org.apache.http.NoHttpResponseException;
import org.apache.http.ProtocolVersion;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.message.BasicHttpResponse;
import org.apache.http.message.BasicStatusLine;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Test;

import com.kaching.util.functional.Result;

public class RetryingResultBasedHttpClientTest {

  private final Mockery mockery = new Mockery();
  private final org.apache.http.client.HttpClient httpClient = mockery.mock(org.apache.http.client.HttpClient.class);
  private final RetryingResultBasedHttpClient client = retrying(new ResultBasedHttpClient(httpClient));
  private final String url = "http://www.google.com/";

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void requestGet_noRetry_success() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(returnValue(new BasicHttpResponse(new BasicStatusLine(new ProtocolVersion("HTTP", 1, 1), 200, "OK"))));
    }});
    Result<Response> result = client.requestGet(0, url, emptyMap());
    assertTrue(result.isSuccess());
    assertResponse(result.getOrThrow(), 200, "200:OK", null);
  }

  @Test
  public void requestGet_noRetry_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new SocketTimeoutException()));
    }});
    Result<Response> result = client.requestGet(0, url, emptyMap());
    assertFalse(result.isSuccess());
  }

  @Test
  public void requestGet_retryConnectTimeoutExceptionByDefault_success() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new ConnectTimeoutException()));
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(returnValue(new BasicHttpResponse(new BasicStatusLine(new ProtocolVersion("HTTP", 1, 1), 200, "OK"))));
    }});
    Result<Response> result = client.requestGet(1, url, emptyMap());
    assertTrue(result.isSuccess());
    assertResponse(result.getOrThrow(), 200, "200:OK", null);
  }

  @Test
  public void requestGet_retryNoHttpResponseExceptionByDefault_success() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new NoHttpResponseException("events.pagerduty.com:443 failed to respond")));
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(returnValue(new BasicHttpResponse(new BasicStatusLine(new ProtocolVersion("HTTP", 1, 1), 200, "OK"))));
    }});
    Result<Response> result = client.requestGet(1, url, emptyMap());
    assertTrue(result.isSuccess());
    assertResponse(result.getOrThrow(), 200, "200:OK", null);
  }

  @Test
  public void requestGet_doNotRetrySocketTimeoutExceptionByDefault_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new SocketTimeoutException()));
    }});
    Result<Response> result = client.requestGet(1, url, emptyMap());
    assertFalse(result.isSuccess());
  }

  @Test
  public void requestGet_forcedRetrySocketTimeoutException_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new SocketTimeoutException()));
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(returnValue(new BasicHttpResponse(new BasicStatusLine(new ProtocolVersion("HTTP", 1, 1), 200, "OK"))));
    }});
    Result<Response> result = client.requestGet(1, e -> e instanceof SocketTimeoutException, url, emptyMap());
    assertTrue(result.isSuccess());
    assertResponse(result.getOrThrow(), 200, "200:OK", null);
  }

  @Test
  public void requestGet_exhaustRetries_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new ConnectTimeoutException()));
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new ConnectTimeoutException()));
    }});
    Result<Response> result = client.requestGet(1, url, emptyMap());
    assertFalse(result.isSuccess());
  }

  @Test
  public void builderExecuteGet_noRetry_success() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(returnValue(new BasicHttpResponse(new BasicStatusLine(new ProtocolVersion("HTTP", 1, 1), 200, "OK"))));
    }});
    Result<Response> result = client.request(0, url).executeGet();
    assertTrue(result.isSuccess());
    assertResponse(result.getOrThrow(), 200, "200:OK", null);
  }

  @Test
  public void builderExecuteGet_noRetry_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new SocketTimeoutException()));
    }});
    Result<Response> result = client.request(0, url).executeGet();
    assertFalse(result.isSuccess());
  }

  @Test
  public void builderExecuteGet_retryConnectTimeoutExceptionByDefault_success() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new ConnectTimeoutException()));
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(returnValue(new BasicHttpResponse(new BasicStatusLine(new ProtocolVersion("HTTP", 1, 1), 200, "OK"))));
    }});
    Result<Response> result = client.request(1, url).executeGet();
    assertTrue(result.isSuccess());
    assertResponse(result.getOrThrow(), 200, "200:OK", null);
  }

  @Test
  public void builderExecuteGet_doNotRetrySocketTimeoutExceptionByDefault_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new SocketTimeoutException()));
    }});
    Result<Response> result = client.request(1, url).executeGet();
    assertFalse(result.isSuccess());
  }

  @Test
  public void builderExecuteGet_forcedRetrySocketTimeoutException_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new SocketTimeoutException()));
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(returnValue(new BasicHttpResponse(new BasicStatusLine(new ProtocolVersion("HTTP", 1, 1), 200, "OK"))));
    }});
    Result<Response> result = client.request(1, e -> e instanceof SocketTimeoutException, url).executeGet();
    assertTrue(result.isSuccess());
    assertResponse(result.getOrThrow(), 200, "200:OK", null);
  }

  @Test
  public void builderExecuteGet_exhaustRetries_failure() throws IOException {
    mockery.checking(new Expectations() {{
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new ConnectTimeoutException()));
      oneOf(httpClient).execute(with(any(HttpUriRequest.class)));
      will(throwException(new ConnectTimeoutException()));
    }});
    Result<Response> result = client.request(1, url).executeGet();
    assertFalse(result.isSuccess());
  }

  @Test
  public void testDefaultRetryingExceptions() {
    assertTrue(RetryingResultBasedHttpClient.defaultRetryingExceptions().test(new ConnectTimeoutException()));
    assertTrue(RetryingResultBasedHttpClient.defaultRetryingExceptions().test(new UnknownHostException()));
    assertFalse(RetryingResultBasedHttpClient.defaultRetryingExceptions().test(new SocketTimeoutException()));
  }

  @Test
  public void retryOnSocketTimeout() {
    assertTrue(RetryingResultBasedHttpClient.retryOnSocketTimeout().test(new ConnectTimeoutException()));
    assertTrue(RetryingResultBasedHttpClient.retryOnSocketTimeout().test(new UnknownHostException()));
    assertTrue(RetryingResultBasedHttpClient.retryOnSocketTimeout().test(new SocketTimeoutException()));
    assertTrue(RetryingResultBasedHttpClient.retryOnSocketTimeout().test(new SocketException()));
    assertFalse(RetryingResultBasedHttpClient.retryOnSocketTimeout().test(new InterruptedByTimeoutException()));
  }

  private void assertResponse(Response response, int statusCode, String status, String body) {
    assertEquals(statusCode, response.getStatusCode());
    assertEquals(status, response.getStatus());
    assertEquals(body, response.getBody());
  }

}
