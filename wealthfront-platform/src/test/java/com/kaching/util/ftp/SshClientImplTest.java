package com.kaching.util.ftp;

import static com.kaching.util.ftp.SshClientImpl.EXEC_TYPE;
import static com.kaching.util.ftp.SshClientImpl.NO_KEY_CHECKING;
import static com.kaching.util.ftp.SshClientImpl.STRICT_HOST_KEY_CHECKING;
import static org.junit.Assert.assertEquals;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

import org.jmock.Mockery;
import org.jmock.Sequence;
import org.junit.After;
import org.junit.Test;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.kaching.platform.common.Pair;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.FakeSleeper;

public class SshClientImplTest {

  private static final int PORT = 22;
  private static final String COMMAND = "ls /tmp";
  private static final String HOST = "sv231.wlth.fr";
  private static final String USERNAME = "etl";

  private final Mockery mockery = Mockeries.mockery(true);
  private final ChannelExec channelExec = mockery.mock(ChannelExec.class);
  private final JSch jSch = mockery.mock(JSch.class);
  private final Session session = mockery.mock(Session.class);

  private final Sequence connectSequence = mockery.sequence("connect");

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void testExecutingCommand() throws JSchException, IOException {
    mockery.checking(new WExpectations() {{
      oneOf(jSch).getSession(USERNAME, HOST, PORT);
      will(returnValue(session));

      oneOf(session).setConfig(STRICT_HOST_KEY_CHECKING, NO_KEY_CHECKING);
      oneOf(session).connect();

      oneOf(session).openChannel(EXEC_TYPE);
      will(returnValue(channelExec));

      oneOf(channelExec).setCommand(COMMAND);

      String input = """
          line 0
          line 1
          line 2
          """;
      InputStream inputStream = new ByteArrayInputStream(input.getBytes());
      oneOf(channelExec).getInputStream();
      inSequence(connectSequence);
      will(returnValue(inputStream));

      oneOf(channelExec).connect();
      inSequence(connectSequence);

      oneOf(channelExec).isClosed();
      will(returnValue(true));

      oneOf(channelExec).getExitStatus();
      will(returnValue(0));

      oneOf(channelExec).disconnect();
      oneOf(session).disconnect();
    }});

    SshClientImpl client = new SshClientImpl(jSch, PORT, new FakeSleeper());
    Pair<Integer, List<String>> executed = client.executeAndGetExitStatus(USERNAME, HOST, COMMAND);
    assertEquals(0, executed.getLeft().intValue());
    assertEquals(Arrays.asList("line 0", "line 1", "line 2"), executed.getRight());
  }

}
