package com.kaching.util.addressstandardization;

import static com.kaching.util.RetryingCallable.SleepStrategy.CONSTANT;
import static com.wealthfront.test.Assert.assertThrows;
import static org.joda.time.Duration.ZERO;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.http.ConnectionClosedException;
import org.hamcrest.core.StringContains;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.jmock.Sequence;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.name.Names;
import com.kaching.platform.common.Option;
import com.kaching.util.http.HttpAPIConnection;

public class SmartyStreetsAddressStandardizerTest {

  private final Mockery mockery = new Mockery();
  private final HttpAPIConnection httpAPIConnection = mockery.mock(HttpAPIConnection.class);
  private final SmartyStreetsAddressStandardizer standardizer = Guice.createInjector(new AbstractModule() {
    @Override
    protected void configure() {
      bind(String.class).annotatedWith(Names.named("smartystreets.authid")).toInstance("someid");
      bind(String.class).annotatedWith(Names.named("smartystreets.authtoken")).toInstance("sometoken");
    }
  }).getInstance(SmartyStreetsAddressStandardizer.class);

  private static final Address validAddress = new Address("541 Cowper St", "", "Palo Alto", "CA", "US", "94301");
  private static final Address invalidUSAddress =
      new Address("Random Street", "Random something", "Not a city", "Not a state", "US", "12345");
  private static final Address nonUSAddress =
      new Address("Random Street", "Random something", "Not a city", "Not a state", "CA", "12345");
  private static final Address missingCountryAddress = new Address("541 Cowper St", "", "Palo Alto", "CA", "", "94301");
  private static final Address nullCountryAddress = new Address("541 Cowper St", "", "Palo Alto", "CA", null, "94301");
  private static final Address nullAddress = new Address(null, null, null, null, null, null);

  private static final String validAddressRequestJSON =
      "[{\"input_id\":\"0\",\"street\":\"541 Cowper St\",\"street2\":\"\",\"city\":\"Palo Alto\",\"state\":\"CA\"," +
          "\"zipcode\":\"94301\",\"candidates\":1}]";
  private static final String validAddressResponseJSON =
      "[{\"input_id\":\"0\",\"input_index\":0,\"candidate_index\":0,\"delivery_line_1\":\"541 Cowper St\"," +
          "\"last_line\":\"Palo Alto CA 94301-1835\",\"delivery_point_barcode\":\"943011835998\"," +
          "\"components\":{\"primary_number\":\"541\",\"street_name\":\"Cowper\",\"street_suffix\":\"St\"," +
          "\"city_name\":\"Palo Alto\",\"state_abbreviation\":\"CA\",\"zipcode\":\"94301\",\"plus4_code\":\"1835\"," +
          "\"delivery_point\":\"99\",\"delivery_point_check_digit\":\"8\"},\"metadata\":{\"record_type\":\"H\"," +
          "\"zip_type\":\"Standard\",\"county_fips\":\"06085\",\"county_name\":\"Santa Clara\"," +
          "\"carrier_route\":\"C010\",\"congressional_district\":\"18\",\"building_default_indicator\":\"Y\"," +
          "\"rdi\":\"Commercial\",\"elot_sequence\":\"0139\",\"elot_sort\":\"A\",\"latitude\":37.4479," +
          "\"longitude\":-122.15838,\"precision\":\"Zip9\",\"time_zone\":\"Pacific\",\"utc_offset\":-8,\"dst\":true}," +
          "\"analysis\":{\"dpv_match_code\":\"D\",\"dpv_footnotes\":\"AAN1\",\"dpv_cmra\":\"N\",\"dpv_vacant\":\"N\"," +
          "\"active\":\"Y\",\"footnotes\":\"H#\"}}]";
  private static final String validAddressesRequestJSON =
      "[{\"input_id\":\"1\",\"street\":\"541 Cowper St\",\"street2\":\"\",\"city\":\"Palo Alto\",\"state\":\"CA\"," +
          "\"zipcode\":\"94301\",\"candidates\":1},{\"input_id\":\"3\",\"street\":\"541 Cowper St\",\"street2\":\"\"," +
          "\"city\":\"Palo Alto\",\"state\":\"CA\",\"zipcode\":\"94301\",\"candidates\":1}]";
  private static final String validAddressesResponseJSON =
      "[{\"input_id\":\"1\",\"input_index\":1,\"candidate_index\":0,\"delivery_line_1\":\"541 Cowper St\"," +
          "\"last_line\":\"Palo Alto CA 94301-1835\",\"delivery_point_barcode\":\"943011835998\"," +
          "\"components\":{\"primary_number\":\"541\",\"street_name\":\"Cowper\",\"street_suffix\":\"St\"," +
          "\"city_name\":\"Palo Alto\",\"state_abbreviation\":\"CA\",\"zipcode\":\"94301\",\"plus4_code\":\"1835\"," +
          "\"delivery_point\":\"99\",\"delivery_point_check_digit\":\"8\"},\"metadata\":{\"record_type\":\"H\"," +
          "\"zip_type\":\"Standard\",\"county_fips\":\"06085\",\"county_name\":\"Santa Clara\"," +
          "\"carrier_route\":\"C010\",\"congressional_district\":\"18\",\"building_default_indicator\":\"Y\"," +
          "\"rdi\":\"Commercial\",\"elot_sequence\":\"0139\",\"elot_sort\":\"A\",\"latitude\":37.4479," +
          "\"longitude\":-122.15838,\"precision\":\"Zip9\",\"time_zone\":\"Pacific\",\"utc_offset\":-8,\"dst\":true}," +
          "\"analysis\":{\"dpv_match_code\":\"D\",\"dpv_footnotes\":\"AAN1\",\"dpv_cmra\":\"N\",\"dpv_vacant\":\"N\"," +
          "\"active\":\"Y\",\"footnotes\":\"H#\"}},{\"input_id\":\"3\",\"input_index\":2,\"candidate_index\":0," +
          "\"delivery_line_1\":\"541 Cowper St\",\"last_line\":\"Palo Alto CA 94301-1835\"," +
          "\"delivery_point_barcode\":\"943011835998\",\"components\":{\"primary_number\":\"541\"," +
          "\"street_name\":\"Cowper\",\"street_suffix\":\"St\",\"city_name\":\"Palo Alto\"," +
          "\"state_abbreviation\":\"CA\",\"zipcode\":\"94301\",\"plus4_code\":\"1835\",\"delivery_point\":\"99\"," +
          "\"delivery_point_check_digit\":\"8\"},\"metadata\":{\"record_type\":\"H\",\"zip_type\":\"Standard\"," +
          "\"county_fips\":\"06085\",\"county_name\":\"Santa Clara\",\"carrier_route\":\"C010\"," +
          "\"congressional_district\":\"18\",\"building_default_indicator\":\"Y\",\"rdi\":\"Commercial\"," +
          "\"elot_sequence\":\"0139\",\"elot_sort\":\"A\",\"latitude\":37.4479,\"longitude\":-122.15838," +
          "\"precision\":\"Zip9\",\"time_zone\":\"Pacific\",\"utc_offset\":-8,\"dst\":true}," +
          "\"analysis\":{\"dpv_match_code\":\"D\",\"dpv_footnotes\":\"AAN1\",\"dpv_cmra\":\"N\",\"dpv_vacant\":\"N\"," +
          "\"active\":\"Y\",\"footnotes\":\"H#\"}}]";

  @Before
  public void setup() {
    standardizer.httpAPIConnection = httpAPIConnection;
  }

  @After
  public void assertIsSatisfied() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void standardizeSingleValidAddress() {
    final List<Address> requests = ImmutableList.of(validAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection).sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(returnValue(validAddressResponseJSON));
      }
    });
    List<Option<StandardizedAddress>> responses = standardizer.standardizeAddresses(requests);
    assertEquals(1, responses.size());
    Address standardizedAddress = responses.get(0).getOrThrow();
    assertEquals("Palo Alto", standardizedAddress.getCity());
    assertEquals("541 Cowper St", standardizedAddress.getStreet1());
    assertNull(standardizedAddress.getStreet2());
    assertEquals("US", standardizedAddress.getCountry());
    assertEquals("CA", standardizedAddress.getState());
    assertEquals("94301", standardizedAddress.getZipcode());
  }

  @Test
  public void standardizeSingleInvalidAddress() {
    final List<Address> requests = ImmutableList.of(invalidUSAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection).sendJSONPost(with(any(String.class)), with(any(String.class)), with(200));
        will(returnValue("[]"));
      }
    });
    List<Option<StandardizedAddress>> responses = standardizer.standardizeAddresses(requests);
    assertEquals(1, responses.size());
    assertTrue(responses.get(0).isEmpty());
  }

  @Test
  public void standardizeSingleNonUSAddress() {
    final List<Address> requests = ImmutableList.of(nonUSAddress);
    List<Option<StandardizedAddress>> responses = standardizer.standardizeAddresses(requests);
    assertEquals(1, responses.size());
    assertTrue(responses.get(0).isEmpty());
  }

  @Test
  public void standardizeSingleNullAddress() {
    final List<Address> requests = ImmutableList.of(nullAddress);
    List<Option<StandardizedAddress>> responses = standardizer.standardizeAddresses(requests);
    assertEquals(1, responses.size());
    assertTrue(responses.get(0).isEmpty());
  }

  @Test
  public void standardizeSingleAddress_twoRetries() {
    final List<Address> requests = ImmutableList.of(validAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(throwException(new ConnectionClosedException("Lost connection")));
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(returnValue(null));
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(returnValue("[]"));
      }
    });
    List<Option<StandardizedAddress>> responses = standardizer.standardizeAddresses(requests, 3, ZERO, CONSTANT);
    assertEquals(1, responses.size());
    assertTrue(responses.get(0).isEmpty());
  }

  @Test(expected = NullPointerException.class)
  public void standardizeSingleNullResponse() {
    final List<Address> requests = ImmutableList.of(validAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(returnValue(null));
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(returnValue(null));
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(returnValue(null));
      }
    });
    standardizer.standardizeAddresses(requests, 3, ZERO, CONSTANT);
  }

  @Test
  public void standardizeSingleException() {
    final List<Address> requests = ImmutableList.of(validAddress);
    Sequence sequence = mockery.sequence("retrying");
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        inSequence(sequence);
        will(throwException(new RuntimeException("one")));
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        inSequence(sequence);
        will(throwException(new RuntimeException("two")));
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        inSequence(sequence);
        will(throwException(new RuntimeException("three")));
      }
    });

    assertThrows(RuntimeException.class, "three",
        () -> standardizer.standardizeAddresses(requests, 3, ZERO, CONSTANT));
  }

  @Test(expected = RuntimeException.class)
  public void standardizeSingleEmptyResponse() {
    final List<Address> requests = ImmutableList.of(validAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(validAddressRequestJSON), with(200));
        will(returnValue(""));
      }
    });
    standardizer.standardizeAddresses(requests);
  }

  @Test
  public void standardizeSingleNullCountryAddress() {
    final List<Address> requests = ImmutableList.of(nullCountryAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection)
            .sendJSONPost(with(any(String.class)), with(StringContains.containsString(nullCountryAddress.getStreet1())),
                with(200));
        will(returnValue("[]"));
      }
    });
    standardizer.standardizeAddresses(requests);
  }

  @Test
  public void standardizeSingleMissingCountryAddress() {
    final List<Address> requests = ImmutableList.of(missingCountryAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection).sendJSONPost(with(any(String.class)),
            with(StringContains.containsString(missingCountryAddress.getStreet1())), with(200));
        will(returnValue("[]"));
      }
    });
    standardizer.standardizeAddresses(requests);
  }

  @Test
  public void standardizeMultipleAddress() {
    final List<Address> requests =
        ImmutableList.of(nullAddress, nullCountryAddress, nonUSAddress, validAddress, nullAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection).sendJSONPost(with(any(String.class)),
            with(validAddressesRequestJSON), with(200));
        will(returnValue(validAddressesResponseJSON));
      }
    });
    List<Option<StandardizedAddress>> responses = standardizer.standardizeAddresses(requests);
    assertEquals(5, responses.size());
    assertTrue(responses.get(0).isEmpty());
    assertEquals("541 Cowper St", responses.get(1).getOrThrow().getStreet1());
    assertTrue(responses.get(2).isEmpty());
    assertEquals("541 Cowper St", responses.get(1).getOrThrow().getStreet1());
    assertTrue(responses.get(4).isEmpty());
  }

  @Test
  public void standardizeMultipleAddress_emptyResponse() {
    final List<Address> requests =
        ImmutableList.of(nullAddress, nullCountryAddress, nonUSAddress, validAddress, nullAddress);
    mockery.checking(new Expectations() {
      {
        oneOf(httpAPIConnection).sendJSONPost(with(any(String.class)),
            with(validAddressesRequestJSON), with(200));
        will(returnValue("[]"));
      }
    });
    List<Option<StandardizedAddress>> responses = standardizer.standardizeAddresses(requests);
    assertEquals(5, responses.size());
  }

  @Test(expected = IllegalArgumentException.class)
  public void standardizeTooManyAddresses() {
    List<Address> addresses = Lists.newArrayList();
    for (int i = 0; i < 101; i++) {
      addresses.add(validAddress);
    }
    standardizer.standardizeAddresses(addresses);
  }

  @Test
  public void testGetUrl() {
    String url = standardizer.getUrl();
    assertTrue(url.contains("someid"));
    assertTrue(url.contains("sometoken"));
    assertTrue(url.contains("us-core-enterprise-cloud"));
    assertTrue(url.contains("match=enhanced"));
  }

  @Test
  public void testIsValidSmartyStreetsAddress_checksFieldLengths() {
    String strLength16 = StringUtils.repeat("a", 16);
    String strLength17 = StringUtils.repeat("a", 17);
    String strLength32 = StringUtils.repeat("a", 32);
    String strLength33 = StringUtils.repeat("a", 33);
    String strLength64 = StringUtils.repeat("a", 64);
    String strLength65 = StringUtils.repeat("a", 65);

    Address goodAddress = new Address(strLength64, strLength64, strLength64, strLength32, "US", strLength16);
    Address badCountry = new Address(strLength64, strLength64, strLength64, strLength32, strLength65, strLength16);
    Address badState = new Address(strLength64, strLength64, strLength64, strLength33, strLength64, strLength16);
    Address badCity = new Address(strLength64, strLength64, strLength65, strLength32, strLength64, strLength16);
    Address badZipcode = new Address(strLength64, strLength64, strLength64, strLength32, strLength64, strLength17);
    Address badStreet1 = new Address(strLength65, strLength64, strLength64, strLength32, strLength64, strLength16);
    Address badStreet2 = new Address(strLength64, strLength65, strLength64, strLength32, strLength64, strLength16);

    assertTrue(SmartyStreetsAddressStandardizer.isValidSmartyStreetsAddress(goodAddress));
    assertFalse(SmartyStreetsAddressStandardizer.isValidSmartyStreetsAddress(badCountry));
    assertFalse(SmartyStreetsAddressStandardizer.isValidSmartyStreetsAddress(badState));
    assertFalse(SmartyStreetsAddressStandardizer.isValidSmartyStreetsAddress(badCity));
    assertFalse(SmartyStreetsAddressStandardizer.isValidSmartyStreetsAddress(badZipcode));
    assertFalse(SmartyStreetsAddressStandardizer.isValidSmartyStreetsAddress(badStreet1));
    assertFalse(SmartyStreetsAddressStandardizer.isValidSmartyStreetsAddress(badStreet2));
  }

}