package com.kaching.util.addressstandardization;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class AddressHasherTest {

  @Test
  public void mapAddressToString() {
    // VersionStandardizedCustomerAddressesEtl (see DATAOPS-5438) and WatchlistIndicators code relies on hashing an address in this way.

    // Note the "{"empty":true}" being captured from the Address.isEmpty() method.
    assertEquals(
        "{\"empty\":true}",
        AddressHasher.mapAddressToString(new StandardizedAddress(null, null, null, null, null, null)));

    assertEquals(
        "{\"street1\":\"123 Main St\",\"street2\":\"Apt A\",\"city\":\"Anytown\",\"state\":\"CA\",\"country\":\"USA\",\"zipcode\":\"98765\",\"empty\":false}",
        AddressHasher.mapAddressToString(new StandardizedAddress("123 Main St", "Apt A", "Anytown", "CA", "USA", "98765")));

    assertEquals(
        "{\"street1\":\"123 Main St\",\"city\":\"Anytown\",\"state\":\"CA\",\"country\":\"USA\",\"zipcode\":\"98765\",\"empty\":false}",
        AddressHasher.mapAddressToString(new StandardizedAddress("123 Main St", null, "Anytown", "CA", "USA", "98765")));

    assertEquals(
        "{\"street1\":\"123 Main St\",\"city\":\"Anytown\",\"state\":\"CA\",\"country\":\"USA\",\"empty\":false}",
        AddressHasher.mapAddressToString(new StandardizedAddress("123 Main St", null, "Anytown", "CA", "USA", null)));

    assertEquals(
        "{\"street1\":\"321 Elm St\",\"street2\":\"Apt 1\",\"city\":\"Springfield\",\"state\":\"AZ\",\"country\":\"USA\",\"zipcode\":\"88888\",\"empty\":false}",
        AddressHasher.mapAddressToString(
            new StandardizedAddress("321 Elm St", "Apt 1", "Springfield", "AZ", "USA", "88888")));
  }

  @Test
  public void hashAddress() {
    assertEquals("d5f1d14230b64188991d72965d11e92e", AddressHasher.hashAddress(
        new StandardizedAddress("street1", "street2", "city", "state", "country", "zipcode")));
  }

}