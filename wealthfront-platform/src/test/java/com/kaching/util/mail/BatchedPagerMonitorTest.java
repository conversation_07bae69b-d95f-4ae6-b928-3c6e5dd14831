package com.kaching.util.mail;

import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.util.time.DateTimeZones.ET;

import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.kaching.platform.common.Option;
import com.kaching.util.mail.BatchedPagerTest.SettableClock;

public class BatchedPagerMonitorTest {

  private final SettableClock clock = new SettableClock();

  @Test
  public void findNextPagerToPushAlerts_allLongerThanStandardDuration() {
    BatchedPagerMonitor monitor = getMonitor();
    final BatchedPager shortIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(10)), null, clock);
    addNullPendingAlertTo(shortIntervalledPager);

    final BatchedPager mediumIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(15)), null, clock);
    addNullPendingAlertTo(mediumIntervalledPager);

    final BatchedPager longIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(20)), null, clock);
    addNullPendingAlertTo(longIntervalledPager);

    assertOptionEmpty(monitor.findNextPagerToPushAlerts(
        ImmutableSet.of(shortIntervalledPager, mediumIntervalledPager, longIntervalledPager)));
  }

  @Test
  public void findNextPagerToPushAlerts_withLowerThanStandardDuration() {
    BatchedPagerMonitor monitor = getMonitor();

    final BatchedPager shortIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(5)), null, clock);
    addNullPendingAlertTo(shortIntervalledPager);

    final BatchedPager mediumIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(8)), null, clock);
    addNullPendingAlertTo(mediumIntervalledPager);

    final BatchedPager longIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(10)), null, clock);
    addNullPendingAlertTo(longIntervalledPager);

    assertOptionEquals(shortIntervalledPager, monitor.findNextPagerToPushAlerts(
        ImmutableSet.of(shortIntervalledPager, mediumIntervalledPager, longIntervalledPager)));
  }

  @Test
  public void findNextPagerToPushAlerts_longestDurationIsNext() {
    BatchedPagerMonitor monitor = getMonitor();
    DateTime lastThreadWakeUpTime = new DateTime(2011, 3, 25, 12, 30, 0, ET);
    clock.setTime(lastThreadWakeUpTime);

    DateTime oneMinuteBefore = new DateTime(2011, 3, 25, 12, 29, 0, ET);
    DateTime nineMinutesBefore = new DateTime(2011, 3, 25, 12, 21, 0, ET);

    final BatchedPager shortIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(5)), null, clock);
    addNullPendingAlertTo(shortIntervalledPager);
    shortIntervalledPager.setLastAlertTime(oneMinuteBefore);

    final BatchedPager mediumIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(8)), null, clock);
    addNullPendingAlertTo(mediumIntervalledPager);
    mediumIntervalledPager.setLastAlertTime(oneMinuteBefore);

    final BatchedPager longIntervalledPager =
        new BatchedPager(Option.none(), Option.some(Duration.standardMinutes(10)), null, clock);
    addNullPendingAlertTo(longIntervalledPager);
    longIntervalledPager.setLastAlertTime(nineMinutesBefore);

    assertOptionEquals(longIntervalledPager, monitor.findNextPagerToPushAlerts(
        ImmutableSet.of(shortIntervalledPager, mediumIntervalledPager, longIntervalledPager)));

  }

  private void addNullPendingAlertTo(BatchedPager pager) {
    pager.addPendingAlerts(new BatchedPager.PendingAlert(null, null, null, null));
    pager.setLastAlertTime(clock.get());
  }

  private BatchedPagerMonitor getMonitor() {
    BatchedPagerMonitor monitor = new BatchedPagerMonitor();
    monitor.clock = clock;
    clock.setTime(new DateTime(2015, 3, 25, 12, 0, 0, ET));
    return monitor;
  }

}