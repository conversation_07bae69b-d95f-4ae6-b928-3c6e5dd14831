package com.kaching.util.tests;

import static com.google.common.base.Preconditions.checkState;

import java.util.HashMap;
import java.util.Map;

import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.Scope;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;

public class UnitTestScope implements Scope {

  private static final Log log = Log.getLog(UnitTestScope.class);
  
  private volatile boolean autoEnter = true;
  private volatile Map<Key<?>, Object> cache = null;

  @Override
  public <T> Provider<T> scope(Key<T> key, Provider<T> unscoped) {
    return new Provider<T>() {
      @Override
      public T get() {
        if (cache == null) {
          if (autoEnter) {
            enter(); 
          } else {
            NullPointerException ex = new NullPointerException("Not currently in unit test scope. Trying to inject " +
                "a test-scoped item after .exit() has been called but before .enter() has been called again.");
            log.error(ex);
            throw ex;
          }
        }
        T value = (T) cache.get(key);
        if (value == null) {
          log.trace("Miss for %s, creating new", key);
          value = unscoped.get();
          cache.put(key, value);
        } else {
          log.trace("Hit for %s, reusing %s", key, value);
        }
        return value;
      }

      @Override
      public String toString() {
        return Strings.format("UnitTestScoped(%s)", unscoped.toString());
      }
    };
  }
  
  public boolean isActive() {
    return cache != null;
  }

  public void enter() {
    log.debug("Entering Scope.UNIT_TEST(%s)", Integer.toHexString(hashCode()));
    checkState(cache == null, "Already in unit test scope");
    cache = new HashMap<>();
  }

  public void exit() {
    log.debug("Exiting Scope.UNIT_TEST(%s)", Integer.toHexString(hashCode()));
    checkState(cache != null, "Not currently in unit test scope");
    cache = null;
    autoEnter = false;
  }

  public void enterIfNecessary() {
    if (cache == null) {
      cache = new HashMap<>();
    }
  }

  public void exitIfNecessary() {
    if (cache != null) {
      cache = null;
    }
  }

  @Override
  public String toString() {
    return "Scope.UNIT_TEST";
  }

}
