package com.kaching.util.pagerduty;

import org.apache.http.impl.client.HttpClients;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import com.kaching.util.http.ResultBasedHttpClient;
import com.kaching.util.http.RetryingResultBasedHttpClient;
import com.kaching.util.mail.Pager;

@Ignore("integration test")
public class PagerDutyPagerIntegrationTest {

  RetryingResultBasedHttpClient http;
  PagerDutyPager pagerDutyPager;

  @Before
  public void setUp() throws Exception {
    http = new RetryingResultBasedHttpClient(new ResultBasedHttpClient(HttpClients.createDefault()));
    pagerDutyPager = new PagerDutyPager();
    pagerDutyPager.http = http;
  }

  @Test
  public void canPage() {
    pagerDutyPager.alert("Page test subject", "Page test message", Pager.Device.PAGER_DATA);
  }

  @Test
  public void canResolve() {
    pagerDutyPager.resolve("Page test subject", "Page test message", Pager.Device.PAGER_DATA);
  }

  @Test
  public void canPageMultiplePagers() {
    pagerDutyPager.alert("Page test subject", "Page test message", Pager.Device.PAGER_SITE_HEALTH_AND_ONLINE_SERVICES);
  }

  @Test
  public void canResolveMultiplePages() {
    pagerDutyPager.resolve("Page test subject", "Page test message", Pager.Device.PAGER_SITE_HEALTH_AND_ONLINE_SERVICES);
  }

}
