package com.kaching.util.pagerduty;

import static com.wealthfront.test.Assert.assertNotEmpty;

import java.util.List;

import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;

public class ServiceKindToPagerDutyTest {

  private static final List<Class<? extends ServiceKind>> SERVICE_KIND_EXCEPTIONS = ImmutableList.of(
      KachingServices.KFE.class,   // Deprecated
      KachingServices.THEAP.class, // <PERSON>'s visualization service
      KachingServices.THEA.class,  // <PERSON>'s visualization service
      KachingServices.WLINK.class, // <PERSON>'s service
      KachingServices.MEM.class,   // Unclear
      KachingServices.MTA.class,   // Unclear
      KachingServices.FE.class,    // Unclear
      KachingServices.CROSS.class  // Unclear
  );

  @Test
  public void allServicesHavePagers() {
    for (Class<? extends ServiceKind> serviceKindClass : KachingServices.KINDS) {
      ServiceKind serviceKind = KachingServices.singleton(serviceKindClass);
      if (!SERVICE_KIND_EXCEPTIONS.contains(serviceKindClass)) {
        assertNotEmpty(ServiceKindToPagerDuty.getPagerDutyDevices(serviceKind));
      }
    }
  }

}