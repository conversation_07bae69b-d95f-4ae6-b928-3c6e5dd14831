package com.kaching.util;

import static com.google.common.collect.Iterators.size;
import static com.google.common.collect.Lists.newArrayList;
import static com.kaching.util.Batcher.batch;
import static com.kaching.util.Batcher.batchingCollector;
import static com.kaching.util.Batcher.exponentialBatch;
import static com.kaching.util.Batcher.lazyBatchStream;
import static com.kaching.util.GenericLists.list;
import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.stream.WFCollectors.pairsToMap;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import org.junit.Ignore;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.kaching.platform.common.Pair;
import com.kaching.platform.queryengine.progress.ProgressMonitor;

public class BatcherTest {

  @Test
  public void batchSmallList1() {
    assertBatchesEqual(
        list(list(1, 2, 3)),
        batch(list(1, 2, 3), 4));
  }

  @Test
  public void batchAndDoNotConsume() {
    int count = 0;
    for (Iterable<Integer> batch : batch(list(1, 2, 3), 1)) {
      assertNotNull(batch);
      count++;
    }
    assertEquals(3, count);
  }

  @Test
  public void batchAndOnlyPartiallyConsume() {
    int count = 0;
    for (Iterable<Integer> batch : batch(list(1, 2, 3), 2)) {
      assertNotNull(com.kaching.util.collections.Iterables.first(batch));
      count++;
    }

    assertEquals(2, count);
  }

  @Test
  public void batchAndOnlyPartiallyConsume2() {
    Iterator<Iterable<Integer>> batches = batch(list(1, 2, 3, 4, 5, 6), 2).iterator();
    // consume 1 of batch 0
    batches.next().iterator().next();
    // consume 0 of batch 1
    batches.next();
    // consume 2 of batch 2
    Iterator<Integer> lastBatch = batches.next().iterator();
    lastBatch.next();
    lastBatch.next();
    assertFalse(batches.hasNext());
  }

  @Test
  public void batchAndOnlyPartiallyConsume3() {
    Iterator<Iterable<Integer>> batches = batch(list(1, 2, 3, 4, 5, 6), 2).iterator();
    // consume 1 of batch 0
    batches.next().iterator().next();
    // consume 0 of batch 1
    batches.next();
    // consume 2 of batch 2
    Iterator<Integer> lastBatch = batches.next().iterator();
    assertFalse(batches.hasNext());
    lastBatch.next();
    lastBatch.next();
  }

  @Test
  public void batchSmallList2() {
    assertBatchesEqual(
        list(list(1, 2, 3, 4)),
        batch(list(1, 2, 3, 4), 4));
  }

  @Test
  public void batchLargeList1() {
    assertBatchesEqual(
        list(list(1, 2, 3, 4), list(5)),
        batch(list(1, 2, 3, 4, 5), 4));
  }

  @Test
  public void batchLargeList2() {
    assertBatchesEqual(
        list(list(1, 2, 3, 4), list(5, 6, 7, 8)),
        batch(list(1, 2, 3, 4, 5, 6, 7, 8), 4));
  }

  @Test
  public void batchLargeList3() {
    assertBatchesEqual(
        list(list(1, 2, 3, 4), list(5, 6, 7, 8), list(9)),
        batch(list(1, 2, 3, 4, 5, 6, 7, 8, 9), 4));
  }

  @Test
  @Ignore("broken")
  public void secondIteratorOnUnfilledBatch() {
    Iterable<Iterable<Integer>> batches = batch(list(1, 2, 3, 4, 5), 3);
    Iterable<Integer> batch = batches.iterator().next();
    Iterator<Integer> iterator1 = batch.iterator();
    iterator1.next();

    Iterator<Integer> iterator2 = batch.iterator();
    assertEquals(3, size(iterator2));
  }

  @Test
  public void hasNext1() {
    Iterable<Iterable<Object>> batch = batch(emptyList(), 5);
    assertFalse(batch.iterator().hasNext());
    assertFalse(batch.iterator().hasNext());
  }

  @Test
  public void hasNext2() {
    Iterable<Iterable<Integer>> batch = batch(list(3), 5);
    assertTrue(batch.iterator().hasNext());
    assertTrue(batch.iterator().hasNext());
  }

  @Test
  public void hasNext3() {
    Iterable<Iterable<Integer>> batch = batch(list(3, 4), 5);
    Iterator<Iterable<Integer>> batchIt = batch.iterator();
    assertTrue(batchIt.hasNext());
    Iterable<Integer> itit = batchIt.next();
    assertTrue(itit.iterator().hasNext());
    assertEquals(3, itit.iterator().next().intValue());
  }

  @Test
  public void batch_withProgress() {
    ProgressMonitor progressMonitor = new ProgressMonitor();
    Iterator<Iterable<Integer>> batches = batch(list(1, 2, 3, 4, 5, 6, 7), 3, progressMonitor, "some name").iterator();
    ProgressMonitor.StageProgress stageProgress = Iterables.getOnlyElement(progressMonitor.getStages());

    assertEquals("some name", stageProgress.getStageName());
    assertEquals(7, stageProgress.getTotalWorkToDo());

    Iterable<Integer> firstBatch = batches.next();
    assertEquals(0, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(1, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(2, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> secondBatch = batches.next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(4, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(5, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> thirdBatch = batches.next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    thirdBatch.iterator().next();
    assertEquals(7, stageProgress.getWorkCompletedSoFar());
  }

  @Test
  public void batchList_withProgress() {
    ProgressMonitor progressMonitor = new ProgressMonitor();
    Iterator<ImmutableList<Integer>> batches =
        Batcher.batchList(list(1, 2, 3, 4, 5, 6, 7), 3, progressMonitor, "some name").iterator();
    ProgressMonitor.StageProgress stageProgress = Iterables.getOnlyElement(progressMonitor.getStages());

    assertEquals("some name", stageProgress.getStageName());
    assertEquals(7, stageProgress.getTotalWorkToDo());

    Iterable<Integer> firstBatch = batches.next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> secondBatch = batches.next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> thirdBatch = batches.next();
    assertEquals(7, stageProgress.getWorkCompletedSoFar());
    thirdBatch.iterator().next();
    assertEquals(7, stageProgress.getWorkCompletedSoFar());
  }

  @Test
  public void batchStream_withProgress() {
    ProgressMonitor progressMonitor = new ProgressMonitor();
    Iterator<ImmutableList<Integer>> batches =
        Batcher.batchStream(list(1, 2, 3, 4, 5, 6, 7), 3, progressMonitor, "some name").iterator();
    ProgressMonitor.StageProgress stageProgress = Iterables.getOnlyElement(progressMonitor.getStages());

    assertEquals("some name", stageProgress.getStageName());
    assertEquals(7, stageProgress.getTotalWorkToDo());

    Iterable<Integer> firstBatch = batches.next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> secondBatch = batches.next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> thirdBatch = batches.next();
    assertEquals(7, stageProgress.getWorkCompletedSoFar());
    thirdBatch.iterator().next();
    assertEquals(7, stageProgress.getWorkCompletedSoFar());
  }

  @Test
  public void exponentialBatch1() {
    Iterable<Iterable<Integer>> batch = exponentialBatch(list(1, 2, 3, 4, 5, 6, 7, 8, 9, 10), 1, 2);
    Iterator<Iterable<Integer>> batchIt = batch.iterator();
    assertTrue(batchIt.hasNext());

    Iterator<Integer> itit = batchIt.next().iterator();
    assertEquals(1, itit.next().intValue());
    assertFalse(itit.hasNext());

    Iterator<Integer> itit2 = batchIt.next().iterator();
    assertEquals(2, itit2.next().intValue());
    assertEquals(3, itit2.next().intValue());
    assertFalse(itit2.hasNext());

    Iterator<Integer> itit3 = batchIt.next().iterator();
    assertEquals(4, itit3.next().intValue());
    assertEquals(5, itit3.next().intValue());
    assertEquals(6, itit3.next().intValue());
    assertEquals(7, itit3.next().intValue());
    assertFalse(itit3.hasNext());

    Iterator<Integer> itit4 = batchIt.next().iterator();
    assertEquals(8, itit4.next().intValue());
    assertEquals(9, itit4.next().intValue());
    assertEquals(10, itit4.next().intValue());
    assertFalse(itit4.hasNext());

    assertFalse(batchIt.hasNext());
  }

  @Test
  public void batchRegression1() throws Exception {
    Iterable<Iterable<Integer>> batches = batch(list(1, 2, 3), 2);
    assertBatchesEqual(list(list(1, 2), list(3)), batches);
    assertBatchesEqual(list(list(1, 2), list(3)), batches);
  }

  @Test
  public void batchRegression2() throws Exception {
    Iterator<Iterable<Integer>> batches = batch(list(1, 2, 3), 2).iterator();
    Iterable<Integer> firstBatch = batches.next();
    assertEquals(list(1, 2), newArrayList(firstBatch));
    assertEquals(list(1, 2), newArrayList(firstBatch));
    Iterable<Integer> secondBatch = batches.next();
    assertEquals(list(3), newArrayList(secondBatch));
    assertEquals(list(3), newArrayList(secondBatch));
  }

  @Test
  public void batchRegression3() throws Exception {
    Iterable<Iterable<Integer>> batches = batch(list(1, 2, 3), 2);
    Iterator<Iterable<Integer>> batchesIterator = batches.iterator();

    assertBatchesEqual(list(list(1, 2), list(3)), batches);
    Iterable<Integer> firstBatch = batchesIterator.next();
    assertEquals(list(1, 2), newArrayList(firstBatch));
    assertEquals(list(1, 2), newArrayList(firstBatch));

    assertBatchesEqual(list(list(1, 2), list(3)), batches);
    Iterable<Integer> secondBatch = batchesIterator.next();
    assertEquals(list(3), newArrayList(secondBatch));
    assertEquals(list(3), newArrayList(secondBatch));

    Iterator<Iterable<Integer>> newBatchesIterator = batches.iterator();

    assertBatchesEqual(list(list(1, 2), list(3)), batches);
    Iterable<Integer> newFirstBatch = newBatchesIterator.next();
    assertEquals(list(1, 2), newArrayList(newFirstBatch));
    assertEquals(list(1, 2), newArrayList(newFirstBatch));

    assertBatchesEqual(list(list(1, 2), list(3)), batches);
    Iterable<Integer> newSecondBatch = newBatchesIterator.next();
    assertEquals(list(3), newArrayList(newSecondBatch));
    assertEquals(list(3), newArrayList(newSecondBatch));
  }

  @Test
  public void exponentialBatchRegression1() throws Exception {
    Iterable<Iterable<Integer>> batches = exponentialBatch(list(1, 2, 3), 1, 2);
    assertBatchesEqual(list(list(1), list(2, 3)), batches);
    assertBatchesEqual(list(list(1), list(2, 3)), batches);
  }

  @Test
  public void exponentialBatchRegression2() throws Exception {
    Iterator<Iterable<Integer>> batches = exponentialBatch(list(1, 2, 3), 1, 2).iterator();
    Iterable<Integer> firstBatch = batches.next();
    assertEquals(list(1), newArrayList(firstBatch));
    assertEquals(list(1), newArrayList(firstBatch));
    Iterable<Integer> secondBatch = batches.next();
    assertEquals(list(2, 3), newArrayList(secondBatch));
    assertEquals(list(2, 3), newArrayList(secondBatch));
  }

  @Test
  public void exponentialBatchRegression3() throws Exception {
    Iterable<Iterable<Integer>> batches = exponentialBatch(list(1, 2, 3), 1, 2);
    Iterator<Iterable<Integer>> batchesIterator = batches.iterator();

    assertBatchesEqual(list(list(1), list(2, 3)), batches);
    Iterable<Integer> firstBatch = batchesIterator.next();
    assertEquals(list(1), newArrayList(firstBatch));
    assertEquals(list(1), newArrayList(firstBatch));

    assertBatchesEqual(list(list(1), list(2, 3)), batches);
    Iterable<Integer> secondBatch = batchesIterator.next();
    assertEquals(list(2, 3), newArrayList(secondBatch));
    assertEquals(list(2, 3), newArrayList(secondBatch));

    Iterator<Iterable<Integer>> newBatchesIterator = batches.iterator();

    assertBatchesEqual(list(list(1), list(2, 3)), batches);
    Iterable<Integer> newFirstBatch = newBatchesIterator.next();
    assertEquals(list(1), newArrayList(newFirstBatch));
    assertEquals(list(1), newArrayList(newFirstBatch));

    assertBatchesEqual(list(list(1), list(2, 3)), batches);
    Iterable<Integer> newSecondBatch = newBatchesIterator.next();
    assertEquals(list(2, 3), newArrayList(newSecondBatch));
    assertEquals(list(2, 3), newArrayList(newSecondBatch));
  }

  @Test
  public void batchStream() {
    List<Integer> inputs = ImmutableList.of(1, 2, 3, 4, 5, 6);
    List<List<Integer>> batches = Batcher.batchStream(inputs, 2).collect(toList());
    List<List<Integer>> expected = list(list(1, 2), list(3, 4), list(5, 6));
    assertEquals(expected, batches);
  }

  @Test
  public void batchingCollector_evenlyDivisible() {
    List<String> result = list(1, 2, 3, 4, 5, 6)
        .stream()
        .map(i -> Integer.toString(i))
        .collect(batchingCollector(2))
        .map(list -> String.join(",", list))
        .collect(Collectors.toList());

    assertEquals(ImmutableList.of("1,2", "3,4", "5,6"), result);
  }

  @Test
  public void batchingCollector_empty() {
    List<String> result = Stream.<Integer>empty()
        .map(i -> Integer.toString(i))
        .collect(batchingCollector(2))
        .map(list -> String.join(",", list))
        .collect(Collectors.toList());

    assertEquals(Collections.emptyList(), result);
  }

  @Test
  public void batchingCollector_withRemainder() {
    List<String> result = list(1, 2, 3, 4, 5, 6, 7, 8)
        .stream()
        .map(i -> Integer.toString(i))
        .collect(batchingCollector(3))
        .map(list -> String.join(",", list))
        .collect(Collectors.toList());

    assertEquals(ImmutableList.of("1,2,3", "4,5,6", "7,8"), result);
  }

  @Test
  public void batchingCollector_singleItem() {
    List<String> result = list(1)
        .stream()
        .map(i -> Integer.toString(i))
        .collect(batchingCollector(8))
        .map(list -> String.join(",", list))
        .collect(Collectors.toList());

    assertEquals(ImmutableList.of("1"), result);
  }

  @Test
  public void batchingCollector_withProgress() {
    ProgressMonitor progressMonitor = new ProgressMonitor();
    Iterator<ImmutableList<Integer>> batches = Stream.of(1, 2, 3, 4, 5, 6, 7)
        .collect(batchingCollector(3, progressMonitor, "some name"))
        .iterator();
    ProgressMonitor.StageProgress stageProgress = Iterables.getOnlyElement(progressMonitor.getStages());

    assertEquals("some name", stageProgress.getStageName());
    assertEquals(7, stageProgress.getTotalWorkToDo());

    Iterable<Integer> firstBatch = batches.next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());
    firstBatch.iterator().next();
    assertEquals(3, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> secondBatch = batches.next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());
    secondBatch.iterator().next();
    assertEquals(6, stageProgress.getWorkCompletedSoFar());

    Iterable<Integer> thirdBatch = batches.next();
    assertEquals(7, stageProgress.getWorkCompletedSoFar());
    thirdBatch.iterator().next();
    assertEquals(7, stageProgress.getWorkCompletedSoFar());
  }

  @Test
  public void lazyBatchStream_withInfiniteStream() {
    Stream<String> infiniteIntegerStream = IntStream.iterate(1, i -> i + 1)
        .boxed()
        .map(i -> Integer.toString(i));

    List<String> result = lazyBatchStream(infiniteIntegerStream, 3)
        .map(list -> String.join(",", list))
        .limit(3)
        .collect(toList());
    assertEquals(ImmutableList.of("1,2,3", "4,5,6", "7,8,9"), result);
  }

  @Test
  public void lazyBatchStream_withLimitedStream() {
    Stream<String> finiteIntegerStream = IntStream.iterate(1, i -> i + 1)
        .boxed()
        .map(i -> Integer.toString(i))
        .limit(8);

    List<String> result = lazyBatchStream(finiteIntegerStream, 3)
        .map(list -> String.join(",", list))
        .collect(toList());
    assertEquals(ImmutableList.of("1,2,3", "4,5,6", "7,8"), result);
  }

  @Test
  public void lazyBatchStream_withNoElements() {
    Stream<String> emptyStream = Stream.empty();

    List<String> result = lazyBatchStream(emptyStream, 3)
        .map(list -> String.join(",", list))
        .collect(toList());
    assertEmpty(result);
  }

  @Test
  public void interruptedThread_throwsExceptionOnMainIteratorAccess_andClearsFlag() {
    Iterator<Iterable<Integer>> iteratorOfBatches = batch(list(1, 2, 3, 4, 5, 6), 4).iterator();
    Thread.currentThread().interrupt();
    assertThrows(UncheckedInterruptedException.class, iteratorOfBatches::next);
    assertFalse(Thread.interrupted());
  }

  @Test
  public void interruptedThread_throwsExceptionOnSubIteratorAccess_andClearsFlag() {
    Iterator<Integer> firstBatchIterator = batch(list(1, 2, 3, 4, 5, 6), 4).iterator().next().iterator();
    Thread.currentThread().interrupt();
    assertThrows(UncheckedInterruptedException.class, firstBatchIterator::next);
    assertFalse(Thread.interrupted());
  }

  @Test
  public void batchMapAndCollect_toCollection() {
    List<Long> inputs = ImmutableList.of(-2L, 4L, 25L, 100L);
    assertEquals(ImmutableSet.of(16d, 625d, 10000d),
        Batcher.batchMapAndCollect(inputs, 2, toSet(),
            inputBatch -> inputBatch.stream()
                .filter(number -> number > 0)
                .map(number -> (double) number * number)
                .collect(Collectors.toList())));
  }

  @Test
  public void batchMapAndCollect_toMap() {
    List<Long> inputs = ImmutableList.of(-2L, 4L, 25L, 100L);
    Map<Long, Double> expected = ImmutableMap.of(
        4L, 16d,
        25L, 625d,
        100L, 10000d);
    assertEquals(expected,
        Batcher.batchMapAndCollect(inputs, 2, pairsToMap(),
            inputBatch -> inputBatch.stream()
                .filter(number -> number > 0)
                .map(number -> Pair.of(number, (double) number * number))
                .collect(Collectors.toList())));
  }

  @Test
  public void batchMapAndCollect_withProgressMonitorAndStageName() {
    List<Long> inputs = ImmutableList.of(-2L, 4L, 25L, 100L);
    Map<Long, Double> expected = ImmutableMap.of(
        4L, 16d,
        25L, 625d,
        100L, 10000d);
    assertEquals(expected,
        Batcher.batchMapAndCollect(inputs, 2, pairsToMap(), new ProgressMonitor(), "hello",
            inputBatch -> inputBatch.stream()
                .filter(number -> number > 0)
                .map(number -> Pair.of(number, (double) number * number))
                .collect(Collectors.toList())));
  }

  private void assertBatchesEqual(
      List<List<Integer>> expected,
      Iterable<Iterable<Integer>> actual) {
    List<List<Integer>> convertedActual = newArrayList();
    for (Iterable<Integer> batch : actual) {
      convertedActual.add(ImmutableList.copyOf(batch));
    }
    assertEquals(expected, convertedActual);
  }

}
