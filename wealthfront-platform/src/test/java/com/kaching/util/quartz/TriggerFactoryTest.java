package com.kaching.util.quartz;

import static org.junit.Assert.assertEquals;

import org.joda.time.DateTimeConstants;
import org.joda.time.LocalTime;
import org.junit.Test;

public class TriggerFactoryTest {

  @Test
  public void everyDay() {
    assertEquals("0 30 12 * * ?",
        TriggerFactory.CronBuilder.everyDay()
            .at(new LocalTime(12, 30))
            .build());
  }

  @Test
  public void weekdays() {
    assertEquals("0 30 12 ? * MON-FRI",
        TriggerFactory.CronBuilder.weekdays()
            .at(new LocalTime(12, 30))
            .build());
  }

  @Test
  public void specificDays() {
    assertEquals("0 30 12 ? * MON,WED,FRI",
        TriggerFactory.CronBuilder.onDays(
            DateTimeConstants.MONDAY,
            DateTimeConstants.WEDNESDAY,
            DateTimeConstants.FRIDAY)
            .at(new LocalTime(12, 30))
            .build());
  }

  @Test
  public void specificDaysOfMonth() {
    assertEquals("0 30 12 1,2 * ?",
        TriggerFactory.CronBuilder.onDaysOfMonth(
            1, 2)
            .at(new LocalTime(12, 30))
            .build());
  }

  @Test
  public void specificDaysOfMonth_alotOfDays() {
    assertEquals("0 30 12 5,6,7,8,9,10,11,12,13,14 * ?",
        TriggerFactory.CronBuilder.onDaysOfMonth(5, 6, 7, 8, 9, 10, 11, 12, 13, 14)
            .at(new LocalTime(12, 30))
            .build());
  }

  @Test
  public void specificDaysDups() {
    assertEquals("0 30 12 ? * MON,WED,FRI",
        TriggerFactory.CronBuilder.onDays(
            DateTimeConstants.MONDAY,
            DateTimeConstants.WEDNESDAY,
            DateTimeConstants.WEDNESDAY,
            DateTimeConstants.FRIDAY)
            .at(new LocalTime(12, 30))
            .build());
  }

  @Test
  public void eachHourAt() {
    assertEquals("0 15 * * * ?", TriggerFactory.CronBuilder.everyDay().eachHourAt(15).build());
    assertEquals("0 0,15,30,45 * * * ?", TriggerFactory.CronBuilder.everyDay().eachHourAt(0, 15, 30, 45).build());
  }

  @Test
  public void everyNMinutes() {
    assertEquals("0 */5 * ? * MON",
        TriggerFactory.CronBuilder.onDays(DateTimeConstants.MONDAY).everyNMinutes(5).build());
  }

  @Test(expected = IllegalArgumentException.class)
  public void illegalMinuteTooLow() {
    TriggerFactory.CronBuilder.everyDay().eachHourAt(-1).build();
  }

  @Test(expected = IllegalArgumentException.class)
  public void illegalMinuteTooHigh() {
    TriggerFactory.CronBuilder.everyDay().eachHourAt(60).build();
  }

  @Test(expected = IllegalArgumentException.class)
  public void illegalEveryNMinuteTooLow() {
    TriggerFactory.CronBuilder.everyDay().everyNMinutes(-1).build();
  }

  @Test(expected = IllegalArgumentException.class)
  public void illegalEveryNMinuteTooHigh() {
    TriggerFactory.CronBuilder.everyDay().everyNMinutes(60).build();
  }

  @Test(expected = IllegalArgumentException.class)
  public void enumeratedHours_noHours() {
    TriggerFactory.CronBuilder.everyDay().enumeratedHours(0);
  }

  @Test(expected = IllegalArgumentException.class)
  public void enumeratedHours_illegalHours() {
    TriggerFactory.CronBuilder.everyDay().enumeratedHours(0, 25);
  }

  @Test(expected = IllegalArgumentException.class)
  public void enumeratedHours_illegalMinute() {
    TriggerFactory.CronBuilder.everyDay().enumeratedHours(60, 25);
  }

  @Test
  public void enumeratedHours() {
    assertEquals("0 50 1,3,5,8 * * ?", TriggerFactory.CronBuilder.everyDay().enumeratedHours(50, 1, 3, 5, 8).build());
  }

}

