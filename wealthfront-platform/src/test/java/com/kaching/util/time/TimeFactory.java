package com.kaching.util.time;

import static com.wealthfront.util.time.DateTimeZones.ET;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import com.google.inject.Provider;

public class TimeFactory {

  public static DateTime now() {
    return new DateTime(2022, 2, 13, 1, 2, 3, ET);
  }

  public static LocalDate today() {
    return new LocalDate(2022, 2, 13);
  }

  public static Provider<DateTime> dateTimeProvider() {
    return new Provider<DateTime>() {
      @Override
      public DateTime get() {
        return now();
      }
    };
  }

  public static Provider<LocalDate> localDateProvider() {
    return new Provider<LocalDate>() {
      @Override
      public LocalDate get() {
        return today();
      }
    };
  }

}
