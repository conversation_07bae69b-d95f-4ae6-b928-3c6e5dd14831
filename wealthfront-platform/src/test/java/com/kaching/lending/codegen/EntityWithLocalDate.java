package com.kaching.lending.codegen;

import java.util.Objects;

import javax.annotation.Nonnull;

import org.joda.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.logging.Log;

import io.swagger.annotations.ApiModelProperty;

@JsonClassDescription("")
@ExposeType(
    value = {ExposeTo.LOCAL, ExposeTo.BACKEND, ExposeTo.API_SERVER},
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY
)
public class EntityWithLocalDate {
  private static final Log log = Log.getLog("com.wealthfront.model.nullabilityLogging");
  @Nonnull
  @JsonProperty("date")
  private LocalDate date = null;

  public EntityWithLocalDate() {
  }

  @Nonnull
  @ApiModelProperty(
      required = true,
      value = ""
  )
  public LocalDate getDate() {
    return this.date;
  }

  protected void setDate(LocalDate date) {
    this.date = date;
  }

  String nullabilityLogString() {
    StringBuilder sb = new StringBuilder();
    sb.append("nullability log for class EntityWithDateTime {");
    sb.append("date:").append(this.date == null ? "null" : "present").append(",");
    sb.setLength(sb.length() - 1);
    sb.append("}");
    return sb.toString();
  }

  void validate() {
    if (this.date == null) {
      throw new IllegalArgumentException("EntityWithDateTime: Required property date is null");
    }
  }

  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EntityWithDateTime {\n");
    sb.append("    date: ").append(this.date).append("\n");
    sb.append("}");
    return sb.toString();
  }

  public boolean equals(Object o) {
    if (this == o) {
      return true;
    } else if (o != null && this.getClass() == o.getClass()) {
      EntityWithLocalDate entityWithLocalDate = (EntityWithLocalDate) o;
      return Objects.equals(this.date, entityWithLocalDate.date);
    } else {
      return false;
    }
  }

  public int hashCode() {
    return Objects.hash(new Object[]{this.date});
  }

  public Builder copy() {
    return with(this);
  }

  public static Builder with() {
    return new Builder();
  }

  public static Builder with(EntityWithLocalDate that) {
    Builder builder = new Builder();
    builder.date(that.getDate());
    return builder;
  }

  public static class Builder {
    @Nonnull
    private LocalDate date = null;

    public Builder() {
    }

    public Builder date(@Nonnull LocalDate date) {
      this.date = date;
      return this;
    }

    public EntityWithLocalDate build() {
      EntityWithLocalDate val = new EntityWithLocalDate();
      val.setDate(this.date);
      EntityWithLocalDate.log.debug(val.nullabilityLogString());
      val.validate();
      return val;
    }

    public EntityWithLocalDate buildForTesting() {
      EntityWithLocalDate val = new EntityWithLocalDate();
      val.setDate(this.date);
      return val;
    }

    public boolean equals(Object o) {
      if (this == o) {
        return true;
      } else if (o != null && this.getClass() == o.getClass()) {
        Builder builder = (Builder) o;
        return Objects.equals(this.date, builder.date);
      } else {
        return false;
      }
    }

    public int hashCode() {
      return Objects.hash(new Object[]{this.date});
    }
  }
}
