package com.kaching.security.pgp;

import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.NoSuchElementException;

import javax.xml.bind.DatatypeConverter;

import org.bouncycastle.bcpg.ArmoredInputStream;
import org.bouncycastle.openpgp.PGPPublicKeyRingCollection;
import org.bouncycastle.openpgp.PGPSecretKeyRingCollection;
import org.bouncycastle.openpgp.PGPUtil;
import org.bouncycastle.openpgp.operator.KeyFingerPrintCalculator;
import org.bouncycastle.openpgp.operator.bc.BcKeyFingerprintCalculator;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import com.kaching.platform.common.logging.Log;
import com.kaching.security.pgp.keys.KeyringConfig;
import com.kaching.security.pgp.keys.ResourceBasedKeyringConfig;

public class PgpUtilitiesTest {

  private static final Log log = Log.getLog(PgpUtilitiesTest.class);
  private static final long KEY_ID_A_1 = 8698980184766607759L;

  private KeyringConfig keyringConfig;

  @Before
  public void before() {
    ResourceBasedKeyringConfig resourceKeyring = new ResourceBasedKeyringConfig(getClass().getClassLoader(),
        "com/kaching/security/a.pkr", "com/kaching/security/a.skr");
    resourceKeyring.addPassword(KEY_ID_A_1, "kaching");
    keyringConfig = resourceKeyring;
  }

  @Test
  public void findSecretKey() {
    assertNotNull(PgpUtilities.findSecretKey(keyringConfig.getSecretKeyRings(), KEY_ID_A_1, "kaching"));
    assertNull(PgpUtilities.findSecretKey(keyringConfig.getSecretKeyRings(), 123L, "kaching"));
  }

  @Test
  public void findSecretKey_wrongPassword_throws() {
    assertThrows(RuntimeException.class, () -> PgpUtilities.findSecretKey(keyringConfig.getSecretKeyRings(),
        KEY_ID_A_1, "wrong password"));
  }

  @Test
  public void findFirstPublicKey() {
    assertEquals(KEY_ID_A_1, PgpUtilities.findFirstPublicKey(keyringConfig.getPublicKeyRings(),
        key -> key.getKeyID() == KEY_ID_A_1).getKeyID());
  }

  @Test
  public void findFirstPublicKey_withNoResult_throws() {
    assertThrows(NoSuchElementException.class, () -> PgpUtilities.findFirstPublicKey(keyringConfig.getPublicKeyRings(),
        key -> key.getKeyID() == 1234L));
  }

  @Test
  @Ignore("Paste in an armored ASCII key block to extract key info")
  public void displayKeyMetadataFromArmoredAscii() throws Exception {
    String publicKeyring = "";  // Paste armored ASCII text here
    String secretKeyring = "";  // Paste armored ASCII text here

    InputStream publicStream = new ArmoredInputStream(new ByteArrayInputStream(publicKeyring.getBytes("UTF-8")));
    InputStream privateStream = new ArmoredInputStream(new ByteArrayInputStream(secretKeyring.getBytes("UTF-8")));

    KeyFingerPrintCalculator keyFingerPrintCalculator = new BcKeyFingerprintCalculator();
    PGPPublicKeyRingCollection publicKeyRings = new PGPPublicKeyRingCollection(PGPUtil.getDecoderStream(publicStream), keyFingerPrintCalculator);
    PGPSecretKeyRingCollection secretKeyRings = new PGPSecretKeyRingCollection(PGPUtil.getDecoderStream(privateStream), keyFingerPrintCalculator);

    publicKeyRings.forEach(keyRing -> {
      keyRing.forEach(publicKey -> {
        log.info("Public Key:");
        log.info("Key ID: " + Long.toString(publicKey.getKeyID()));
        log.info("Fingerprint: " + DatatypeConverter.printHexBinary(publicKey.getFingerprint()));
        log.info("Is Encryption Key: " + Boolean.toString(publicKey.isEncryptionKey()));
        log.info("Algorithm: " + publicKey.getAlgorithm());
        log.info("Bit strength: " + publicKey.getBitStrength());
        log.info("Generated on: " + publicKey.getCreationTime());
        publicKey.getUserIDs().forEachRemaining(userAttr -> {
          log.info("User: " + userAttr.toString());
        });
        log.info("");
      });
    });
    secretKeyRings.forEach(keyRing -> {
      keyRing.forEach(secretKey -> {
        log.info("Secret Key:");
        log.info("Key ID: " + Long.toString(secretKey.getKeyID()));
        log.info("Is Signing Key: " + Boolean.toString(secretKey.isSigningKey()));
        log.info("Algorithm: " + secretKey.getKeyEncryptionAlgorithm());
        secretKey.getUserIDs().forEachRemaining(userId -> log.info("User: " + userId));
        log.info("");
      });
    });
  }

}