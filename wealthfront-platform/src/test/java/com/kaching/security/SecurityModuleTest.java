package com.kaching.security;

import static com.google.common.io.BaseEncoding.base64;
import static com.google.inject.Guice.createInjector;
import static com.google.inject.name.Names.named;
import static com.kaching.security.SecurityModule.CipherInfo.Cipher.AES;
import static com.kaching.security.SecurityModule.CipherInfo.Cipher.BLOWFISH;
import static com.kaching.security.SecurityModule.createMac;
import static com.kaching.util.Base64.decode;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.List;
import java.util.Map;

import javax.crypto.Mac;

import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.inject.AbstractModule;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Module;
import com.google.inject.ProvisionException;
import com.google.inject.name.Named;
import com.google.inject.util.Modules;
import com.kaching.entities.crypto.SecureHash;
import com.kaching.platform.guice.KawalaTestModule;
import com.kaching.platform.guice.PropertiesModule;
import com.kaching.platform.guice.TestingApplicationOptions;
import com.kaching.security.SecurityModule.CipherInfo;
import com.kaching.util.functional.Pointer;

public class SecurityModuleTest {

  private static final String KEY1 = "9eqK2zJ/MFEix051k4i6jBP6AFb7gLcXQSMxND0P3wM=";
  private static final String KEY2 = "JGndw+D90fnPFQ/KX5FI6rm/yZ43ptB0AXw53ly4q3g=";

  private static final Cipher AES1 = Ciphers.aes(decode(KEY1), new SecureRandom());
  private static final Cipher AES2 = Ciphers.aes(decode(KEY2), new SecureRandom());
  private static final Cipher BLOWFISH1 = Ciphers.blowfish(decode(KEY1));

  @Test
  public void cipherInfos() {
    AbstractModule propertiesModule = new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class).annotatedWith(named("um.cipher_keys.customer")).toInstance("9eqK2zJ/MFEix051k4i6jBP6AFb7gLcXQSMxND0P3wM=");
        bind(String.class).annotatedWith(named("um.cipher_keys.mail")).toInstance("9eqK2zJ/MFEix051k4i6jBP6AFb7gLcXQSMxND0P3wM=");
      }
    };

    List<CipherInfo> cipherInfos = List.of(
        new CipherInfo("Customer", "um.cipher_keys.customer", CipherInfo.Cipher.AES),
        new CipherInfo("Mail", "um.cipher_keys.mail", CipherInfo.Cipher.BLOWFISH)
    );
    SecurityModule securityModule = new SecurityModule(cipherInfos);

    Injector injector = createInjector(propertiesModule, securityModule, new KawalaTestModule());
    assertNotNull(injector.getInstance(Key.get(Cipher.class, named("Customer"))));
    assertNotNull(injector.getInstance(Key.get(Cipher.class, named("Mail"))));
  }

  @Test
  public void cipherInfos_allCiphersAreSupported() {
    CipherInfo.Cipher[] ciphers = CipherInfo.Cipher.values();

    ImmutableList.Builder<CipherInfo> cipherInfosBuilder = ImmutableList.builder();
    for (CipherInfo.Cipher cipher : ciphers) {
      cipherInfosBuilder.add(new CipherInfo(cipher.name(), cipher.name(), cipher));
    }
    SecurityModule securityModule = new SecurityModule(cipherInfosBuilder.build());

    AbstractModule propertiesModule = new AbstractModule() {
      @Override
      protected void configure() {
        for (CipherInfo.Cipher cipher : ciphers) {
          String name = cipher.name();
          cipherInfosBuilder.add(new CipherInfo(name, name, cipher));
          bind(String.class).annotatedWith(named(name)).toInstance("9eqK2zJ/MFEix051k4i6jBP6AFb7gLcXQSMxND0P3wM=");
        }
      }
    };

    Injector injector = createInjector(propertiesModule, securityModule, new KawalaTestModule());
    for (CipherInfo.Cipher cipher : ciphers) {
      assertNotNull(injector.getInstance(Key.get(Cipher.class, named(cipher.name()))));
    }
  }

  @Test
  public void cannotAddHmacAfterConfiguration() {
    try {
      SecurityModule module = new SecurityModule();
      createInjector(module);
      module.addHmacSHA1(null, null);
      fail();
    } catch (Throwable t) {
      assertEquals(IllegalStateException.class, t.getClass());
      assertEquals("Cannot add a security operation after configuration.",
          t.getMessage());
    }
  }

  @Test
  public void testMd5() throws Exception {
    Injector injector = createInjector(new SecurityModule());
    assertEquals("MD5", injector.getInstance(
        Key.get(MessageDigest.class, MD5.class)).getAlgorithm());
  }

  @Test
  public void testSha1() throws Exception {
    Injector injector = createInjector(new SecurityModule());
    assertEquals("SHA1", injector.getInstance(
        Key.get(MessageDigest.class, SHA1.class)).getAlgorithm());
  }

  @Test
  public void testMessageDigest_threadLocalSingleton() throws Exception {
    Injector injector = createInjector(new SecurityModule());
    MessageDigest thread1Digest1 = injector.getInstance(Key.get(MessageDigest.class, SHA1.class));
    MessageDigest thread1Digest2 = injector.getInstance(Key.get(MessageDigest.class, SHA1.class));
    Pointer<MessageDigest> thread2Digest1 = Pointer.pointer();
    Pointer<MessageDigest> thread2Digest2 = Pointer.pointer();
    Pointer<MessageDigest> thread3Digest1 = Pointer.pointer();

    Thread thread2 = new Thread(() -> {
      thread2Digest1.set(injector.getInstance(Key.get(MessageDigest.class, SHA1.class)));
      thread2Digest2.set(injector.getInstance(Key.get(MessageDigest.class, SHA1.class)));
    });
    thread2.start();

    Thread thread3 = new Thread(() -> {
      thread3Digest1.set(injector.getInstance(Key.get(MessageDigest.class, SHA1.class)));
    });
    thread3.start();

    thread2.join();
    thread3.join();

    assertSame(thread1Digest1, thread1Digest2);
    assertSame(thread2Digest1.get(), thread2Digest2.get());

    assertNotSame(thread1Digest1, thread2Digest1.get());
    assertNotSame(thread1Digest1, thread3Digest1.get());
    assertNotSame(thread2Digest1.get(), thread3Digest1.get());
  }

  @Test
  public void testMac() throws Exception {
    Named annotation = named("cookie");
    Injector injector = createInjector(
        new SecurityModule().addHmacSHA1(annotation, "secret"));

    Mac mac1 = injector.getInstance(Key.get(Mac.class, annotation));
    Mac mac2 = injector.getInstance(Key.get(Mac.class, annotation));
    assertEquals("HmacSHA1", mac1.getAlgorithm());
    assertEquals("HmacSHA1", mac2.getAlgorithm());
    assertTrue("No scoping around Mac.", mac1 != mac2);
  }

  @Test
  public void testCreateMac() throws Exception {
    Mac mac = createMac("secret");
    assertEquals("HmacSHA1", mac.getAlgorithm());
    assertEquals("URIFXAX5RPhXVe/FzYlw4ZTp9Fs=", base64().encode(mac.doFinal("hello".getBytes())));
    assertEquals("URIFXAX5RPhXVe/FzYlw4ZTp9Fs=", base64().encode(mac.doFinal("hello".getBytes())));
  }

  @Test
  public void bindingTwoSecurityModulesIsAllowed() {
    Injector injector = createInjector(
        new SecurityModule(ImmutableList.of(new CipherInfo("SessionKey", "um.cipher_keys.session_key", BLOWFISH))),
        new SecurityModule(ImmutableList.of(new CipherInfo("Customer", "um.cipher_keys.customer", AES))),
        new PropertiesModule(new TestingApplicationOptions()),
        new KawalaTestModule()
    );

    assertNotNull(injector.getInstance(Key.get(MessageDigest.class, SHA1.class)));
    assertNotNull(injector.getInstance(Key.get(MessageDigest.class, MD5.class)));
    assertNotNull(injector.getInstance(Key.get(Cipher.class, named("SessionKey"))));
    assertNotNull(injector.getInstance(Key.get(Cipher.class, named("Customer"))));
  }

  @Test
  public void cipherInfosPrimarySecondaryDefinedGetCipherGroup() {
    Cipher cipher = getCipher(Map.of(
        "um.cipher_keys.customer.primary.key", KEY1,
        "um.cipher_keys.customer.secondary.key", KEY2,
        "um.cipher_keys.customer.primary.cipher_type", "AES",
        "um.cipher_keys.customer.secondary.cipher_type", "AES"));

    assertEquals(Aes.class, cipher.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), cipher.getFingerprint());
    assertEncryptsDecrypts(AES1, cipher);
    assertEncryptsDecrypts(cipher, AES1);
    assertEncryptsDecrypts(AES2, cipher);
    assertEncryptDecryptsFails(cipher, AES2);
  }

  @Test
  public void cipherInfosPrimaryButNoSecondaryGetsPrimaryCipher() {
    Cipher cipher = getCipher(Map.of(
        "um.cipher_keys.customer.primary.key", KEY1,
        "um.cipher_keys.customer.primary.cipher_type", "AES"));

    assertEquals(Aes.class, cipher.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), cipher.getFingerprint());
    assertEncryptsDecrypts(AES1, cipher);
    assertEncryptsDecrypts(cipher, AES1);
    assertEncryptDecryptsFails(AES2, cipher);
    assertEncryptDecryptsFails(cipher, AES2);
  }

  @Test
  public void cipherInfosPrimaryUsesDefaultCipherType() {
    Cipher cipher = getCipher(Map.of(
        "um.cipher_keys.customer.primary.key", KEY1));

    assertEquals(Aes.class, cipher.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), cipher.getFingerprint());
    assertEncryptsDecrypts(AES1, cipher);
    assertEncryptsDecrypts(cipher, AES1);
    assertEncryptDecryptsFails(AES2, cipher);
    assertEncryptDecryptsFails(cipher, AES2);
  }

  @Test
  public void cipherInfosPrimaryUsesConfiguredCipherType() {
    Cipher cipher = getCipher(Map.of(
        "um.cipher_keys.customer.primary.key", KEY1,
        "um.cipher_keys.customer.primary.cipher_type", "BLOWFISH"));

    assertEquals(Blowfish.class, cipher.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), cipher.getFingerprint());
    assertEncryptsDecrypts(BLOWFISH1, cipher);
    assertEncryptsDecrypts(cipher, BLOWFISH1);
  }

  @Test
  public void cipherInfosDefaultCipher() {
    Cipher cipher = getCipher(Map.of("um.cipher_keys.customer", KEY2));
    assertEquals(Aes.class, cipher.getCipherClass());
    assertEquals(new SecureHash("1b5fb51cd4364dc7646250a245ba4372b76f01b5fe1bd8f2931c5307a9ffd2fd"), cipher.getFingerprint());
    assertEncryptsDecrypts(AES2, cipher);
    assertEncryptsDecrypts(cipher, AES2);
  }

  @Test
  public void cipherInfosNoCiphers() {
    List<CipherInfo> cipherInfos = List.of(
        new CipherInfo("Customer", "um.cipher_keys.customer", AES)
    );
    SecurityModule securityModule = new SecurityModule(cipherInfos);

    Injector injector = createInjector(securityModule, new KawalaTestModule());
    assertThrows(ProvisionException.class, () -> injector.getInstance(Key.get(Cipher.class, named("Customer"))));
  }

  @Test
  public void cipherInfosOnlySecondaryProvidesDefaultCipher() {
    Cipher cipher = getCipher(Map.of(
        "um.cipher_keys.customer", KEY2,
        "um.cipher_keys.customer.secondary.key", KEY1));

    assertEquals(Aes.class, cipher.getCipherClass());
    assertEquals(new SecureHash("1b5fb51cd4364dc7646250a245ba4372b76f01b5fe1bd8f2931c5307a9ffd2fd"), cipher.getFingerprint());
    assertEncryptsDecrypts(AES2, cipher);
    assertEncryptsDecrypts(cipher, AES2);
    assertEncryptDecryptsFails(AES1, cipher);
    assertEncryptDecryptsFails(cipher, AES1);
  }

  @Test
  public void cipherInfosAesAndBlowfishMixedFail() {
    assertThrows(ProvisionException.class, () -> getCipher(Map.of(
        "um.cipher_keys.customer.primary.key", KEY1,
        "um.cipher_keys.customer.secondary.key", KEY2,
        "um.cipher_keys.customer.primary.cipher_type", "AES",
        "um.cipher_keys.customer.secondary.cipher_type", "BLOWFISH")));
  }

  @Test
  public void cipherInfosBlowfishAndBlowfishFails() {
    assertThrows(ProvisionException.class, () -> getCipher(Map.of(
        "um.cipher_keys.customer.primary.key", KEY1,
        "um.cipher_keys.customer.secondary.key", KEY2,
        "um.cipher_keys.customer.primary.cipher_type", "BLOWFISH",
        "um.cipher_keys.customer.secondary.cipher_type", "BLOWFISH")));
  }

  public static Cipher getCipher(Map<String, String> properties) {
    Module propertiesModule = propertiesModule(properties);

    List<CipherInfo> cipherInfos = List.of(
        new CipherInfo("Customer", "um.cipher_keys.customer", AES)
    );
    SecurityModule securityModule = new SecurityModule(cipherInfos);

    Injector injector = createInjector(propertiesModule, securityModule, new KawalaTestModule());
    return injector.getInstance(Key.get(Cipher.class, named("Customer")));
  }

  public static Module propertiesModule(Map<String, String> properties) {
    return Modules
        .override(new PropertiesModule(new TestingApplicationOptions()))
        .with(new AbstractModule() {
          @Override
          protected void configure() {
            properties.forEach((k, v) -> bind(String.class).annotatedWith(named(k)).toInstance(v));
          }
        });
  }

  private static void assertEncryptDecryptsFails(Cipher encrypting, Cipher decrypting) {
    try {
      assertNotEquals("testmesg", new String(decrypting.decrypt(encrypting.encrypt("testmesg".getBytes()))));
      fail("Expected cipher to throw exception.");
    } catch (Exception ignored) {}
  }

  private static void assertEncryptsDecrypts(Cipher encrypting, Cipher decrypting) {
    assertEquals("testmesg", new String(decrypting.decrypt(encrypting.encrypt("testmesg".getBytes()))));
  }

}
