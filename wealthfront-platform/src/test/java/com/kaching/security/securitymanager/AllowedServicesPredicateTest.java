package com.kaching.security.securitymanager;

import static java.util.Arrays.asList;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.net.InetAddress;
import java.net.SocketPermission;
import java.net.UnknownHostException;

import org.junit.Test;

import com.kaching.platform.discovery.Manifest;
import com.kaching.platform.discovery.ServiceAllocation;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.guice.KachingServices;

public class AllowedServicesPredicateTest {

  @Test
  public void test() throws UnknownHostException {
    Manifest manifest = new Manifest(asList(
        new ServiceAllocation(
            new ServiceId("fbank1"),
            KachingServices.FBANK.class,
            "something-9999-00ZXX",
            InetAddress.getByName("*******")),
        new ServiceAllocation(
            new ServiceId("um20"),
            KachingServices.UM.class,
            "something-9999-00ZXX",
            InetAddress.getByName("*******"))));
    AllowedServicesPredicate predicate = new AllowedServicesPredicate(manifest,
        asList(KachingServices.FBANK.class, KachingServices.UM.class));

    assertTrue(predicate.test(new SocketPermission("*******:9031", "connect")));
    assertTrue(predicate.test(new SocketPermission("*******:8085", "connect")));
    assertFalse(predicate.test(new SocketPermission("*******:80", "connect")));
  }

  @Test
  public void testNoIP() throws UnknownHostException {
    Manifest manifest = new Manifest(asList(
        new ServiceAllocation(
            new ServiceId("fbank1"),
            KachingServices.FBANK.class,
            "something-9999-00ZXX",
            null),
        new ServiceAllocation(
            new ServiceId("um20"),
            KachingServices.UM.class,
            "something-9999-00ZXX",
            InetAddress.getByName("*******"))));
    AllowedServicesPredicate predicate = new AllowedServicesPredicate(manifest,
        asList(KachingServices.FBANK.class, KachingServices.UM.class));

    assertFalse(predicate.test(new SocketPermission("*******:9031", "connect")));
    assertTrue(predicate.test(new SocketPermission("*******:8085", "connect")));
    assertFalse(predicate.test(new SocketPermission("*******:80", "connect")));
  }

}
