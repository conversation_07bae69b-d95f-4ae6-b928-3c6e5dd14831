package com.kaching.security;

import static com.google.common.base.Charsets.UTF_8;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.security.SecureRandom;
import java.util.Objects;

import org.junit.Before;
import org.junit.Test;

import com.kaching.util.Base64;

public class OpenSslAesCbcTest {

  private SecureRandom random;

  @Before
  public void before() throws Exception {
    random = new SecureRandom();
  }

  @Test
  public void encryptAndDecrypt() throws Exception {
    UnsafeCipher cipher = new OpenSslAesCbc("this-is-some-passphrase".getBytes(), 128, random);

    String encrypted = cipher.encrypt("foo".getBytes(UTF_8));
    assertEquals("foo", new String(cipher.decrypt(encrypted), UTF_8));

    encrypted = cipher.encrypt("foobar".getBytes(UTF_8));
    assertEquals("foobar", new String(cipher.decrypt(encrypted), UTF_8));
  }

  @Test
  public void ivIsDifferent() throws Exception {
    UnsafeCipher cipher = new OpenSslAesCbc("this-is-some-passphrase".getBytes(), 128, random);
    assertFalse(Objects.equals(
        cipher.encrypt("foo".getBytes(UTF_8)),
        cipher.encrypt("foo".getBytes(UTF_8))));
  }

  @Test
  public void integrityIsVerified() throws Exception {
    UnsafeCipher cipher = new OpenSslAesCbc("this-is-some-passphrase".getBytes(), 128, random);
    String encrypted = cipher.encrypt("foo".getBytes(UTF_8));
    byte[] bytes = Base64.decode(encrypted);
    bytes[11] = (byte) (bytes[11] + 10);
    try {
      cipher.decrypt(Base64.encodeBytes(bytes));
      fail();
    } catch (RuntimeException e) {
      assertTrue(e.getMessage().contains("pad block corrupted"));
    }
  }

}