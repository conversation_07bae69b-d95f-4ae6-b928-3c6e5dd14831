package com.kaching.platform.monitoring;

import static com.kaching.platform.functional.Unit.unit;
import static java.lang.String.format;
import static org.junit.Assert.assertEquals;

import org.jmock.Mockery;
import org.jmock.api.Invocation;
import org.jmock.lib.action.CustomAction;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.UncheckedInterruptedException;

public class KillTest {

  private final Mockery mockery = Mockeries.mockery(true);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_noMatch() {
    QueryRuntimeMonitor queryRuntimeMonitor = new QueryRuntimeMonitor();
    QuerySession session = queryRuntimeMonitor.queryStart(null, null, null, null);

    String sessionToKill = "" + (session.getQuerySessionId().getId() + 100);
    Kill query = new Kill(sessionToKill);

    query.runtimeMonitor = queryRuntimeMonitor;

    assertEquals("No running queries matched '" + sessionToKill + "'.", query.process());
  }

  @Test
  public void process_multipleMatches() {
    String queryToKill = "TopAll";
    Kill query = new Kill(queryToKill);
    query.runtimeMonitor = new QueryRuntimeMonitor();
    query.runtimeMonitor.queryStart(new TopAll(null), null, null, null);
    query.runtimeMonitor.queryStart(new TopAll(null), null, null, null);

    assertEquals("Multiple running queries matched 'TopAll'. Specify a session id instead.", query.process());
  }

  @Test
  public void process_singleMatch_success() {
    Thread thread = mockery.mock(Thread.class);
    QueryRuntimeMonitor queryRuntimeMonitor = new QueryRuntimeMonitor();
    QuerySession session = queryRuntimeMonitor.queryStart(null, null, thread, null);
    String sessionToKill = "" + session.getQuerySessionId();

    Kill query = new Kill(sessionToKill) {
      @Override
      protected void sleepOverrideMeForTesting() {}
    };

    query.runtimeMonitor = queryRuntimeMonitor;

    mockery.checking(new WExpectations() {{
      oneOf(thread).interrupt();
      will(new CustomAction("stops session") {
        @Override
        public Object invoke(Invocation invocation) {
          session.stoppedExceptionally(new UncheckedInterruptedException());
          return null;
        }
      });
    }});

    assertEquals(format("Session %s successfully interrupted.", sessionToKill), query.process());
  }

  @Test
  public void process_singleMatch_failure() {
    Thread thread = mockery.mock(Thread.class);
    QueryRuntimeMonitor queryRuntimeMonitor = new QueryRuntimeMonitor();
    QuerySession session = queryRuntimeMonitor.queryStart(null, null, thread, null);

    String sessionToKill = "" + session.getQuerySessionId();
    Kill query = new Kill(sessionToKill) {
      @Override
      protected void sleepOverrideMeForTesting() {}
    };

    query.runtimeMonitor = queryRuntimeMonitor;

    mockery.checking(new WExpectations() {{
      allowing(thread).interrupt();
    }});

    assertEquals("Tried to interrupt session " + sessionToKill + ", but it did not die before timeout.",
        query.process());
  }

  @Test
  public void process_singleMatch_alreadyDead() {
    Thread thread = mockery.mock(Thread.class);
    QueryRuntimeMonitor queryRuntimeMonitor = new QueryRuntimeMonitor();
    QuerySession session = queryRuntimeMonitor.queryStart(null, null, thread, null);
    session.stoppedSuccessfully(unit);
    String sessionToKill = "" + session.getQuerySessionId();
    Kill query = new Kill(sessionToKill) {
      @Override
      protected void sleepOverrideMeForTesting() {}
    };

    query.runtimeMonitor = queryRuntimeMonitor;

    mockery.checking(new WExpectations() {{
      allowing(thread).interrupt();
    }});

    assertEquals("Session " + sessionToKill + " says it already isn't running.", query.process());
  }

}
