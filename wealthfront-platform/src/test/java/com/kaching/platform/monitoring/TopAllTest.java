package com.kaching.platform.monitoring;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.hamcrest.CustomTypeSafeMatcher;
import org.hamcrest.Matcher;
import org.junit.After;
import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.TypeLiterals;
import com.kaching.platform.monitoring.RunningQueriesSummarizer.SortType;
import com.kaching.platform.queryengine.client.SmartClient;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.functional.Either;
import com.kaching.util.http.Pooling;

public class TopAllTest {

  private final WFMockery mockery = Mockeries.mockery();
  private final Injector injector = Guice.createInjector(new FakeResolverModule());
  private final SmartClient<KachingServices.UM> umSmartClientMock = mockery.mockUnchecked(SmartClient.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process() {
    TopAll query =
        getTopAllQuery(KachingServices.singleton(KachingServices.UM.class), SortType.name);
    Map<ServiceId, Either<? extends Exception, String>> queryResultMap = new HashMap<>();
    queryResultMap.put(ServiceId.of("um0"), Either.right("TestQueryResult", Exception.class));
    queryResultMap.put(ServiceId.of("um1"), Either.right("TestQueryResult2", Exception.class));

    mockery.checking(new WExpectations() {{
      oneOf(umSmartClientMock)
          .scatterGatherInvoke(with(topWithSort(SortType.name)));
      will(returnValue(queryResultMap));
    }});
    assertEquals("On um0:\nTestQueryResult\nOn um1:\nTestQueryResult2\n", query.process());
  }

  @Test
  public void process_partialException() {
    TopAll query =
        getTopAllQuery(KachingServices.singleton(KachingServices.UM.class), SortType.name);
    Map<ServiceId, Either<? extends Exception, String>> queryResultMap = new HashMap<>();
    queryResultMap.put(ServiceId.of("um0"), Either.right("TestQueryResult", Exception.class));
    queryResultMap.put(ServiceId.of("um1"), Either.left(new IOException("Fail!")));

    mockery.checking(new WExpectations() {{
      oneOf(umSmartClientMock)
          .scatterGatherInvoke(with(topWithSort(SortType.name)));
      will(returnValue(queryResultMap));
    }});
    assertTrue(
        query.process().startsWith(
            "On um0:\nTestQueryResult\nQuery on um1 failed with Exception:\njava.io.IOException: Fail!"));
  }

  private TopAll getTopAllQuery(ServiceKind serviceKind, SortType sortType) {
    TopAll query = new TopAll(sortType);
    query.serviceKind = serviceKind;
    query.injector = injector;
    return query;
  }

  private Matcher<Top> topWithSort(SortType sortType) {
    return new CustomTypeSafeMatcher<Top>("Top query with sort type " + sortType) {
      @Override
      protected boolean matchesSafely(Top query) {
        return query.sortBy == sortType;
      }
    };
  }

  private class FakeResolverModule extends AbstractModule {

    @Override
    protected void configure() {
      bind(TypeLiterals.get(SmartClient.class, KachingServices.UM.class))
          .annotatedWith(Pooling.class)
          .toProvider(() -> umSmartClientMock);
    }

  }

}