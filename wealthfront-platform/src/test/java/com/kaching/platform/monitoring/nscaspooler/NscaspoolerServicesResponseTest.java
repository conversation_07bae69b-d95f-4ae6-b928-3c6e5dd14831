package com.kaching.platform.monitoring.nscaspooler;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.twolattes.json.Json.array;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertMarshalling;

import java.util.Collections;

import org.junit.Test;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.twolattes.json.Json;

public class NscaspoolerServicesResponseTest {

  @Test
  public void marshal() throws JsonProcessingException {
    NscaspoolerServicesResponse actual = new NscaspoolerServicesResponse.Builder()
        .withError(false)
        .withMessage("")
        .withServicesMetadata(
            Collections.singletonList(new NscaspoolerServiceMetadata.Builder()
                .withFile("thefile")
                .withSha("thesha")
                .withGroup("thegroup")
                .withTimeperiod("thetimeperiod")
                .withInterval("theinterval")
                .withService("theservice")
                .build())
        )
        .build();
    Json.Value expected = object(
        "error", false,
        "message", "",
        "data", array(
            object(
                "file", "thefile",
                "sha", "thesha",
                "service", "theservice",
                "group", "thegroup",
                "timeperiod", "thetimeperiod",
                "interval", "theinterval"
            )
        )
    );
    assertMarshalling(createEntityMarshaller(NscaspoolerServicesResponse.class), expected, actual);
  }

}