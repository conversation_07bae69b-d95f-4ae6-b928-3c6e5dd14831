package com.kaching.platform.monitoring;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.joda.time.Duration;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.KachingServices.DMW;
import com.kaching.platform.guice.KachingServices.UM;
import com.kaching.platform.guice.Revision;
import com.kaching.platform.monitoring.HealthzReport.DownstreamHealthzReport;
import com.kaching.platform.monitoring.HealthzReport.Statistics;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.ServiceRevision;
import com.kaching.platform.queryengine.client.SmartClient;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.FakeSleeper;

public class ApplicationHealthMetricsPublisherTest {

  private final WFMockery mockery = Mockeries.mockery(true);
  private final ServiceDescriptor serviceDescriptor = new ServiceDescriptor(ServiceId.of("um5"), UM.class, null);
  private final QueryRuntimeMonitor queryRuntimeMonitor = mockery.mock(QueryRuntimeMonitor.class);
  private final SmartClient<DMW> dmwSmartClient = mockery.mockSmartClient(KachingServices.DMW.class);
  private final RollingStatistics rollingStatistics1 = mockery.mock(RollingStatistics.class, "1");
  private final RollingStatistics rollingStatistics2 = mockery.mock(RollingStatistics.class, "2");
  private final RollingStatistics rollingStatistics3 = mockery.mock(RollingStatistics.class, "3");
  private final RollingStatistics rollingStatistics4 = mockery.mock(RollingStatistics.class, "4");

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void publish() {
    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).statistics();
      will(returnList(
          Map.entry(QueryA.class.getName(), rollingStatistics1),
          Map.entry(QueryB.class.getName(), rollingStatistics2)));

      oneOf(rollingStatistics1).rollingAverage();
      will(returnValue(200.0));
      oneOf(rollingStatistics1).rollingMedian();
      will(returnValue(195));
      oneOf(rollingStatistics1).rollingMin();
      will(returnValue(50));
      oneOf(rollingStatistics1).rollingMax();
      will(returnValue(250));
      exactly(2).of(rollingStatistics1).rollingTotalCount();
      will(returnValue(20));
      oneOf(rollingStatistics1).rollingFailurePercentage();
      will(returnValue(0.0));
      oneOf(rollingStatistics1).rollingInvalidPercentage();
      will(returnValue(0.0));

      oneOf(rollingStatistics2).rollingTotalCount();
      will(returnValue(0));

      ConcurrentHashMap<String, RollingStatistics> queryStats = new ConcurrentHashMap<>();
      queryStats.put(QueryC.class.getName(), rollingStatistics3);
      queryStats.put(QueryC.class.getName(), rollingStatistics3);
      queryStats.put(QueryD.class.getName(), rollingStatistics4);
      oneOf(queryRuntimeMonitor).downstreamStatistics();
      will(returnList(Map.entry(
          new ServiceRevision(ServiceId.of("bank1"), KachingServices.BANK.class, new Revision("abc123")),
          queryStats)));

      oneOf(rollingStatistics3).rollingAverage();
      will(returnValue(100.0));
      oneOf(rollingStatistics3).rollingMedian();
      will(returnValue(95));
      oneOf(rollingStatistics3).rollingMin();
      will(returnValue(50));
      oneOf(rollingStatistics3).rollingMax();
      will(returnValue(150));
      exactly(2).of(rollingStatistics3).rollingTotalCount();
      will(returnValue(10));
      oneOf(rollingStatistics3).rollingFailurePercentage();
      will(returnValue(0.6));
      oneOf(rollingStatistics3).rollingInvalidPercentage();
      will(returnValue(0.2));

      oneOf(rollingStatistics4).rollingTotalCount();
      will(returnValue(0));

      HealthzReport healthzReport = new HealthzReport();
      healthzReport.getStatistics().put(QueryA.class.getName(), new Statistics(
          200.0, 195, 50, 250, 20, 0.0, 0.0));
      healthzReport.getDownstreamReports().add(new DownstreamHealthzReport(
          new ServiceRevision(ServiceId.of("bank1"), KachingServices.BANK.class, new Revision("abc123")),
          Map.of(QueryC.class.getName(), new Statistics(
              100.0, 95, 50, 150, 10, 0.6, 0.2))));

      oneOf(dmwSmartClient).invoke(with(query(
          new PublishApplicationHealthMetrics(ServiceRevision.fromDescriptor(serviceDescriptor), healthzReport))));
    }});

    getPublisher(Duration.standardMinutes(1)).publish();
  }

  @Test
  public void run_invalidInterval() {
    mockery.checking(new WExpectations() {{
      never(dmwSmartClient);
    }});
    getPublisher(Duration.ZERO).run();
    getPublisher(null).run();
  }

  @Test
  public void run_isIntegrationServer() throws Exception {
    mockery.checking(new WExpectations() {{
      never(queryRuntimeMonitor);
    }});

    CompletableFuture<?> future = CompletableFuture.runAsync(() -> {
      ApplicationHealthMetricsPublisher publisher = getPublisher(Duration.standardMinutes(1));
      publisher.isIntegrationServer = true;
      publisher.run();
    });

    future.get(1, TimeUnit.MINUTES);
  }

  @Test
  public void run_notIntegrationServer() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).statistics();
      will(throwException(new ExpectedError()));
    }});

    CompletableFuture<?> future = CompletableFuture.runAsync(() -> {
      try {
        getPublisher(Duration.standardMinutes(1)).run();
      } catch (ExpectedError e) {
        return;
      }

      throw new AssertionError();
    });

    future.get(1, TimeUnit.MINUTES);
  }

  private static class QueryA extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      return true;
    }

  }

  private static class QueryB extends AbstractQuery<String> {

    @Override
    public String process() {
      return "Tom Cat";
    }

  }

  private static class QueryC extends AbstractQuery<String> {

    @Override
    public String process() {
      return "Foo";
    }

  }

  private static class QueryD extends AbstractQuery<String> {

    @Override
    public String process() {
      return "Bar";
    }

  }

  private ApplicationHealthMetricsPublisher getPublisher(Duration interval) {
    ApplicationHealthMetricsPublisher publisher = new ApplicationHealthMetricsPublisher();
    publisher.monitor = queryRuntimeMonitor;
    publisher.serviceDescriptor = serviceDescriptor;
    publisher.dmw = dmwSmartClient;
    publisher.sleeper = new FakeSleeper();
    publisher.config = new EnabledApplicationHealthMetricsPublisherConfig() {
      @Override
      public Duration getExecutionInterval() {
        return interval;
      }
    };
    publisher.isIntegrationServer = false;

    return publisher;
  }

  private static class ExpectedError extends Error {
  }

}