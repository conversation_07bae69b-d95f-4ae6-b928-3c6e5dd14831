package com.kaching.platform.monitoring.trafficlevels;

import static com.kaching.platform.monitoring.trafficlevels.TrafficLevelMeasurement.TrafficLevels.HIGH_TRAFFIC;
import static com.kaching.platform.monitoring.trafficlevels.TrafficLevelMeasurement.TrafficLevels.MINIMAL_TRAFFIC;
import static com.kaching.platform.monitoring.trafficlevels.TrafficLevelMeasurement.TrafficLevels.MODERATE_TRAFFIC;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class ApiRequestsPerIPTest {

  @Test
  public void apiRequestsPerIPDefaults() {
    ApiRequestsPerIP apiRequestsPerIP = new ApiRequestsPerIP();

    assertEquals(10, apiRequestsPerIP.getDefaultTrafficCheckWindowSeconds());
    assertEquals(1500, apiRequestsPerIP.getDefaultHighTrafficThreshold());
    assertEquals(10, apiRequestsPerIP.getTrafficCheckWindowSeconds());
    assertEquals(1500, apiRequestsPerIP.getHighTrafficThreshold());
    assertEquals(1200, apiRequestsPerIP.getModerateTrafficThreshold());
    assertEquals(750, apiRequestsPerIP.getLowTrafficThreshold());
    assertEquals(MINIMAL_TRAFFIC, apiRequestsPerIP.getTrafficLevelFromCurrentValue(456));
    assertFalse(apiRequestsPerIP.isTrafficHigh(456));
    assertEquals(HIGH_TRAFFIC, apiRequestsPerIP.getTrafficLevelFromCurrentValue(5555));
    assertTrue(apiRequestsPerIP.isTrafficHigh(5555));
  }

  @Test
  public void apiRequestsPerIPOverrides() {
    ApiRequestsPerIP apiRequestsPerIP = new ApiRequestsPerIP();

    apiRequestsPerIP.setHighTrafficThreshold(6000);
    apiRequestsPerIP.setTrafficCheckWindowSeconds(60);
    assertEquals(10, apiRequestsPerIP.getDefaultTrafficCheckWindowSeconds());
    assertEquals(1500, apiRequestsPerIP.getDefaultHighTrafficThreshold());
    assertEquals(60, apiRequestsPerIP.getTrafficCheckWindowSeconds());
    assertEquals(6000, apiRequestsPerIP.getHighTrafficThreshold());
    assertEquals(4800, apiRequestsPerIP.getModerateTrafficThreshold());
    assertEquals(3000, apiRequestsPerIP.getLowTrafficThreshold());
    assertEquals(MODERATE_TRAFFIC, apiRequestsPerIP.getTrafficLevelFromCurrentValue(5555));
    assertFalse(apiRequestsPerIP.isTrafficHigh(5555));
  }

}
