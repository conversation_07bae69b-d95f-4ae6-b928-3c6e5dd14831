package com.kaching.platform.monitoring;

import static com.kaching.platform.testing.Mockeries.mockery;
import static org.junit.Assert.assertEquals;

import java.util.Set;

import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.kaching.platform.common.Errors;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;

public class ManuallyRunAllQueryHealthSelfTestsTest {
  
  private final WFMockery mockery = mockery();
  private final QueryHealthSelfTestRunner runner = mockery.mock(QueryHealthSelfTestRunner.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_success() {
    ManuallyRunAllQueryHealthSelfTests query = getQuery();
    FakeQueryHealthTest1 test1 = new FakeQueryHealthTest1();
    FakeQueryHealthTest2 test2 = new FakeQueryHealthTest2();

    query.allSelfTests = ImmutableSet.of(test1, test2);

    mockery.checking(new WExpectations() {{
      oneOf(runner).runSelfTestUnchecked(test1);
      will(returnValue(new Errors()));

      oneOf(runner).runSelfTestUnchecked(test2);
      will(returnValue(new Errors()));
    }});
    assertEquals("no errors", query.process());
  }

  @Test
  public void process_errors() {
    ManuallyRunAllQueryHealthSelfTests query = getQuery();
    FakeQueryHealthTest1 test1 = new FakeQueryHealthTest1();
    FakeQueryHealthTest2 test2 = new FakeQueryHealthTest2();

    query.allSelfTests = ImmutableSet.of(test1, test2);

    mockery.checking(new WExpectations() {{
      oneOf(runner).runSelfTestUnchecked(test1);
      will(returnValue(new Errors().addMessage("darp")));

      oneOf(runner).runSelfTestUnchecked(test2);
      will(returnValue(new Errors().addMessage("derp")));
    }});
    assertEquals("1) darp\n\n2) derp", query.process());
  }

  private static class FakeQuery1 extends AbstractQuery<Boolean> {

    FakeQuery1(long accountId) {
    }

    @Override
    public Boolean process() {
      return true;
    }

  }

  private static class FakeQuery2 extends AbstractQuery<Boolean> {

    FakeQuery2(long accountId) {
    }

    @Override
    public Boolean process() {
      return true;
    }

  }

  private static class FakeQueryHealthTest1 implements QueryHealthSelfTest<Query<Boolean>, Long> {

    @Override
    public Class<Query<Boolean>> getQueryClazz() {
      return null;
    }

    @Override
    public Set<Long> generateParameters() {
      return null;
    }

    @Override
    public FakeQuery1 instantiateQuery(Long parameters) {
      return null;
    }

  }

  private static class FakeQueryHealthTest2 implements QueryHealthSelfTest<Query<Boolean>, Long> {

    @Override
    public Class<Query<Boolean>> getQueryClazz() {
      return null;
    }

    @Override
    public Set<Long> generateParameters() {
      return null;
    }

    @Override
    public FakeQuery2 instantiateQuery(Long parameters) {
      return null;
    }

  }

  private ManuallyRunAllQueryHealthSelfTests getQuery() {
    ManuallyRunAllQueryHealthSelfTests query = new ManuallyRunAllQueryHealthSelfTests();
    query.selfTestRunner = runner;
    return query;
  }

}