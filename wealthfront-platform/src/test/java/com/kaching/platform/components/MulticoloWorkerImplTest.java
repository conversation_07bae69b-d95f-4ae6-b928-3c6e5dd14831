package com.kaching.platform.components;

import static com.kaching.platform.multicolo.MultiColoSchedule.SLAVE_ONLY;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

import org.jmock.Expectations;
import org.joda.time.Duration;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.functional.Unit;
import com.kaching.platform.multicolo.MultiColo;
import com.kaching.platform.multicolo.MultiColoStatusProvider;
import com.kaching.platform.queryengine.FakeStackTraceMonitor;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.util.DefaultSleeper;

public class MulticoloWorkerImplTest {

  private final WFMockery mockery = Mockeries.mockery();
  private final FakeStackTraceMonitor fakeStm = new FakeStackTraceMonitor();

  @After
  public void after() {
    mockery.assertIsSatisfied();
    fakeStm.assertNoUnexpectedExceptions();
  }

  @Test
  public void onMasterAndNoMulticoloAnnotation_startWorker() {
    MulticoloWorkerImpl worker = getWorker();
    mockery.checking(new Expectations() {{
      oneOf(worker.multiColoStatusProvider).getStatus();
      will(returnValue(MultiColoStatusProvider.ColoStatus.MASTER));
    }});

    Runnable runnable = new Runnable() {
      @Override
      public void run() {
      }
    };
    assertTrue(worker.startWorker("workerName", runnable));
  }

  @Test
  public void workerThrows_addToStm() throws Exception {
    MulticoloWorkerImpl worker = getWorker();
    mockery.checking(new Expectations() {{
      oneOf(worker.multiColoStatusProvider).getStatus();
      will(returnValue(MultiColoStatusProvider.ColoStatus.MASTER));
    }});

    AtomicReference<Thread> workerThread = new AtomicReference<>();
    Runnable runnable = () -> {
      workerThread.set(Thread.currentThread());
      throw new RuntimeException("network super unreachable");
    };
    DefaultSleeper sleeper = new DefaultSleeper();
    assertTrue(worker.startWorker("workerName", runnable));
    for (int i = 0; i < 1_000; i++) {
      if (workerThread.get() != null) {
        break;
      }
      sleeper.sleep(Duration.millis(10));
    }
    workerThread.get().join();

    fakeStm.assertOnlyStackTraceAndClear("network super unreachable");
  }

  @Test
  public void onSlaveAndNoMulticoloAnnotation_doesNotStartWorker() {
    MulticoloWorkerImpl worker = getWorker();
    mockery.checking(new Expectations() {{
      oneOf(worker.multiColoStatusProvider).getStatus();
      will(returnValue(MultiColoStatusProvider.ColoStatus.FOLLOWER));
    }});

    Runnable runnable = new Runnable() {
      @Override
      public void run() {
      }
    };
    assertFalse(worker.startWorker("workerName", runnable));
  }

  @Test
  public void onSlaveAndSlaveMulticoloAnnotation_startWorker() {
    MulticoloWorkerImpl worker = getWorker();
    mockery.checking(new Expectations() {{
      oneOf(worker.multiColoStatusProvider).getStatus();
      will(returnValue(MultiColoStatusProvider.ColoStatus.FOLLOWER));
    }});

    assertTrue(worker.startWorker("workerName", new TestRunnable()));
  }

  @Test
  public void onMasterAndNoMulticoloAnnotation_defaultPriorityLow() {
    MulticoloWorkerImpl worker = getWorker();
    mockery.checking(new Expectations() {{
      oneOf(worker.multiColoStatusProvider).getStatus();
      will(returnValue(MultiColoStatusProvider.ColoStatus.MASTER));
    }});

    CompletableFuture<Unit> result = new CompletableFuture<>();
    assertTrue(worker.startWorker("workerName", () -> {
      if (Thread.currentThread().getPriority() == Thread.NORM_PRIORITY - 1) {
        result.complete(Unit.unit);
      } else {
        result.completeExceptionally(new RuntimeException());
      }
    }));
    result.join();
  }

  @Test
  public void onMasterAndNoMulticoloAnnotation_highPriority() {
    MulticoloWorkerImpl worker = getWorker();
    mockery.checking(new Expectations() {{
      oneOf(worker.multiColoStatusProvider).getStatus();
      will(returnValue(MultiColoStatusProvider.ColoStatus.MASTER));
    }});

    CompletableFuture<Unit> result = new CompletableFuture<>();
    assertTrue(worker.startWorker("workerName", MulticoloWorker.Priority.HIGH, () -> {
      if (Thread.currentThread().getPriority() == Thread.NORM_PRIORITY + 1) {
        result.complete(Unit.unit);
      } else {
        result.completeExceptionally(new RuntimeException());
      }
    }));
    result.join();
  }

  @MultiColo(enabledOn = SLAVE_ONLY, reason = "slave only runnable")
  class TestRunnable implements Runnable {

    @Override
    public void run() {
    }

  }

  private MulticoloWorkerImpl getWorker() {
    MulticoloWorkerImpl worker = new MulticoloWorkerImpl();
    worker.multiColoStatusProvider = mockery.mock(MultiColoStatusProvider.class);
    worker.stm = fakeStm;
    return worker;
  }

}