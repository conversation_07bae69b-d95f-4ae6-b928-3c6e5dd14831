package com.kaching.platform.components;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Sets.newHashSet;
import static com.google.inject.Guice.createInjector;
import static java.lang.String.format;
import static org.hamcrest.Matchers.containsString;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.junit.Before;
import org.junit.Test;
import org.kohsuke.args4j.Option;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterators;
import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Module;
import com.google.inject.TypeLiteral;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.KachingInstantiators;
import com.kaching.platform.bus.impl.IncomingEvent;
import com.kaching.platform.common.Errors;
import com.kaching.platform.components.ModuleFactories.ModuleFactory;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.ApplicationOptions;
import com.kaching.platform.guice.KachingServices.NOP;
import com.kaching.platform.monitoring.icinga.IcingaCheck;
import com.kaching.platform.monitoring.icinga.IcingaMetadata;
import com.kaching.platform.monitoring.icinga.IcingaOutput;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.Transactional;
import com.wealthfront.test.AllowLocalFileAccess;

@AllowLocalFileAccess(paths = {"/usr/share/*"})
public class KawalaBuilderTest {

  private Errors actualErrors;

  @Before
  public void before() {
    actualErrors = new Errors();
  }

  /* Kawala.Builder tests */

  @Test
  public void aggregateQueries1() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, false);
    assertTrue(queries.isEmpty());
    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    assertFalse(actualErrors.hasErrors());
  }

  @Component(queries = Query1.class)
  static class AggregateQueries2 {}

  @Test
  @SuppressWarnings("unchecked")
  public void aggregateQueries2_noHibernateEntities() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries2.class);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, false);
    assertEquals(newHashSet(Query1.class), queries);
    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    assertFalse(actualErrors.hasErrors());
  }

  @Test
  @SuppressWarnings("unchecked")
  public void aggregateQueries2_withHibernateEntities() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries2.class);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, true);
    assertEquals(newHashSet(Query1.class), queries);
    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    assertFalse(actualErrors.hasErrors());
  }

  @Component(queries = ProblematicQuery1.class)
  static class AggregateQueries3 {}

  @Test
  public void aggregateQueries3() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries3.class);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, false);

    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    Errors expectedErrors = new Errors();
    KachingInstantiators.createInstantiator(expectedErrors, ProblematicQuery1.class,
        new DefaultJsonMarshallerFactory());
    assertEquals(
        expectedErrors,
        actualErrors);
  }

  @Component(queries = IncorrectReturnTypeQuery.class)
  static class AggregateQueries4 {}

  @Test
  public void aggregateQueries4() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries4.class);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, false);

    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    assertEquals(
        new Errors().addMessage(
            "can not serialize return type of class com.kaching.platform.components.KawalaBuilderTest$IncorrectReturnTypeQuery"),
        actualErrors);
  }

  @Component(queries = TransactionalQuery.class)
  static class AggregateQueries5 {}

  @Test
  public void aggregateQueries5_cannotHaveTransactionalQueryWithoutHibernateEntities1() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries5.class);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, false);

    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    assertEquals(
        new Errors().addMessage("class com.kaching.platform.components.KawalaBuilderTest$TransactionalQuery " +
            "is a transactional query but no Hibernate entities specified"),
        actualErrors);
  }

  @Test
  @SuppressWarnings("unchecked")
  public void aggregateQueries5_cannotHaveTransactionalQueryWithoutHibernateEntities2() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries5.class);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, true);
    assertEquals(
        newHashSet(TransactionalQuery.class),
        queries);
    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    assertFalse(actualErrors.hasErrors());
  }

  @Component(
      queriesInClient = {Query1.class, Query2.class},
      queries = {TransactionalQuery.class}
  )
  static class AggregateQueries6 {}

  @Test
  public void aggregateQueries6_queriesInClient() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries6.class);
    Set<Class<? extends Query<?>>> queriesInClient = builder.aggregateQueries(Component::queriesInClient, false);
    assertEquals(newHashSet(Query1.class, Query2.class), queriesInClient);
    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queriesInClient);
    assertFalse(actualErrors.hasErrors());
  }

  @Test
  public void aggregateQueries6_queries() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateQueries6.class);
    Set<Class<? extends Query<?>>> queries = builder.aggregateQueries(Component::queries, true);
    assertEquals(newHashSet(TransactionalQuery.class), queries);
    KawalaBuilder.verifyQueries(actualErrors, createInjector(), queries);
    assertFalse(actualErrors.hasErrors());
  }

  @Component(icingaChecks = {
      FakeIcingaCheck.class
  })
  static class AggregateIcingaChecks1 {}

  @Test
  public void aggregateIcingaChecks1() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateIcingaChecks1.class);
    Set<Class<? extends IcingaCheck>> icingaChecks = builder.aggregateIcingaChecks();
    assertEquals(Set.of(FakeIcingaCheck.class), icingaChecks);
    assertFalse(actualErrors.hasErrors());
  }

  static class HibernateEntityWithoutNoArgConstructor {

    HibernateEntityWithoutNoArgConstructor(int i) {}

  }

  @Component(hibernateEntitites = {
      HibernateEntityWithoutNoArgConstructor.class
  })
  static class AggregateHibernateEntities1 {}

  @Test
  public void aggregateHibernateEntities1() {
    assertTrue(
        new KawalaBuilder(actualErrors, AggregateHibernateEntities1.class).aggregateHibernateEntities().isEmpty());

    Errors expectedErrors = new Errors();
    KachingInstantiators.createInstantiator(expectedErrors, ProblematicQuery1.class,
        new DefaultJsonMarshallerFactory());
    assertEquals(
        new Errors().addMessage("Hibernate entity %s is missing a no argument constructor",
            HibernateEntityWithoutNoArgConstructor.class),
        actualErrors);
  }

  @Component(hibernateEntitites = {
      String.class
  })
  static class AggregateHibernateEntities2 {}

  @Test
  @SuppressWarnings("unchecked")
  public void aggregateHibernateEntities2() {
    assertEquals(
        newHashSet(String.class),
        new KawalaBuilder(actualErrors, AggregateHibernateEntities2.class).aggregateHibernateEntities());
    assertFalse(actualErrors.hasErrors());
  }

  @Component
  static class CreateCmdOptions1 {}

  @Test
  public void createCmdOptions1() {
    assertTrue(new KawalaBuilder(actualErrors, CreateCmdOptions1.class).createCmdOptions().isEmpty());
    assertFalse(actualErrors.hasErrors());
  }

  static class Options2 {}

  @Component(cmdOptions = Options2.class)
  static class CreateCmdOptions2 {}

  @Test
  public void createCmdOptions2() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, CreateCmdOptions2.class);
    Map<Class<?>, Object> cmdOptions = builder.createCmdOptions();
    assertEquals(1, cmdOptions.size());
    assertEquals(
        Options2.class,
        cmdOptions.get(Options2.class).getClass());
    assertFalse(actualErrors.hasErrors());
  }

  static class Options3 {

    Options3(int i) {}

  }

  @Component(cmdOptions = Options3.class)
  static class CreateCmdOptions3 {}

  @Test
  public void createCmdOptions3() {
    assertTrue(new KawalaBuilder(actualErrors, CreateCmdOptions3.class).createCmdOptions().isEmpty());
    assertEquals(
        new Errors().addMessage("%s is missing a no argument constructor", Options3.class),
        actualErrors);
  }

  static class Options4_1 {

    @Option(name = "--count") int count;

  }

  static class Options4_2 {

    @Option(name = "--count") int count;

  }

  @Component(cmdOptions = Options4_1.class)
  static class CreateCmdOptions4_1 {}

  @Component(cmdOptions = Options4_2.class)
  static class CreateCmdOptions4_2 {}

  @Test
  public void createCmdOptions4() {
    new KawalaBuilder(actualErrors, CreateCmdOptions4_1.class, CreateCmdOptions4_2.class).createCmdOptions();

    assertEquals(
        new Errors().addMessage("option --count is declared in %s, %s", Options4_1.class, Options4_2.class),
        actualErrors);
  }

  static class Options5 {

    @Option(name = "count") int count;

  }

  @Component(cmdOptions = Options5.class)
  static class CreateCmdOptions5 {}

  @Test
  public void createCmdOptions5() {
    new KawalaBuilder(actualErrors, CreateCmdOptions5.class).createCmdOptions();

    assertEquals(
        new Errors().addMessage("option count must start with - in %s", Options5.class),
        actualErrors);
  }

  static class Options6_1 {

    @Option(name = "--count") int count;

  }

  static class Options6_2_top {

    @Option(name = "--count") int count;

  }

  static class Options6_2 extends Options6_2_top {}

  @Component(cmdOptions = Options6_1.class)
  static class CreateCmdOptions6_1 {}

  @Component(cmdOptions = Options6_2.class)
  static class CreateCmdOptions6_2 {}

  @Test
  public void createCmdOptions6() {
    new KawalaBuilder(actualErrors, CreateCmdOptions6_1.class, CreateCmdOptions6_2.class).createCmdOptions();

    assertEquals(
        new Errors().addMessage("option --count is declared in %s, %s", Options6_1.class, Options6_2_top.class),
        actualErrors);
  }

  static class Options7 {

    @Option(name = "--count") int count1;
    @Option(name = "--count") int count2;

  }

  @Component(cmdOptions = Options7.class)
  static class CreateCmdOptions7 {}

  @Test
  public void createCmdOptions7() {
    new KawalaBuilder(actualErrors, CreateCmdOptions7.class).createCmdOptions();

    assertEquals(
        new Errors().addMessage("option --count is declared multiple times in %s", Options7.class),
        actualErrors);
  }

  @Component
  static class CreateCmdOptions8 {}

  @Test
  public void createCmdOptions8() {
    // we need to be able to add to the options map
    Map<Class<?>, Object> options = new KawalaBuilder(actualErrors, CreateCmdOptions8.class).createCmdOptions();
    assertTrue(options.isEmpty());
    assertNull(options.put(String.class, ""));
  }

  static class Options9 {

    @Option(name = "--count") int count;

  }

  @Component(cmdOptions = Options9.class, dependsOn = CreateCmdOptions9_2.class)
  static class CreateCmdOptions9_1 {}

  @Component(cmdOptions = Options9.class)
  static class CreateCmdOptions9_2 {}

  @Test
  public void createCmdOptions9_shouldNotConsiderAnOptionToBeDeclaredMultipleTimesWhenTheClassIsUsedMoreThanOnce() {
    assertFalse(new KawalaBuilder(actualErrors, CreateCmdOptions9_1.class).createCmdOptions().isEmpty());
    assertFalse(actualErrors.hasErrors());
  }

  static class PrepareModuleFactories1_module extends AbstractModule {

    @Override
    protected void configure() {}

  }

  @Component(modules = PrepareModuleFactories1_module.class)
  static class PrepareModuleFactories1 {}

  @Test
  public void prepareModuleFactories1() {
    List<ModuleFactory> factories = new KawalaBuilder(
        actualErrors, PrepareModuleFactories1.class)
        .prepareModuleFactories();
    assertFalse(actualErrors.hasErrors());

    assertEquals(1, factories.size());
    assertEquals(
        PrepareModuleFactories1_module.class,
        ModuleFactories.instantiate(factories.get(0), null, null).getClass());
  }

  static class PrepareModuleFactories2_options {}

  static class PrepareModuleFactories2_module extends AbstractModule {

    final PrepareModuleFactories2_options options;

    @Inject
    PrepareModuleFactories2_module(PrepareModuleFactories2_options options) {
      this.options = options;
    }

    @Override
    protected void configure() {}

  }

  @Component(
      modules = PrepareModuleFactories2_module.class,
      cmdOptions = PrepareModuleFactories2_options.class
  )
  static class PrepareModuleFactories2 {}

  @Test
  public void prepareModuleFactories2() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, PrepareModuleFactories2.class);
    List<ModuleFactory> factories = builder
        .prepareModuleFactories();
    assertFalse(actualErrors.hasErrors());

    assertEquals(1, factories.size());

    PrepareModuleFactories2_options options = new PrepareModuleFactories2_options();
    Module module = ModuleFactories.instantiate(factories.get(0),
        ImmutableMap.of(PrepareModuleFactories2_options.class, options), null);

    assertEquals(
        PrepareModuleFactories2_module.class,
        module.getClass());
    assertEquals(
        options,
        ((PrepareModuleFactories2_module) module).options);
  }

  static class PrepareModuleFactories3_module extends AbstractModule {

    final ApplicationOptions options;

    PrepareModuleFactories3_module(ApplicationOptions options) {
      this.options = options;
    }

    @Override
    protected void configure() {}

  }

  @Component(modules = PrepareModuleFactories3_module.class)
  static class PrepareModuleFactories3 {}

  @Test
  public void prepareModuleFactories3() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, PrepareModuleFactories3.class);
    List<ModuleFactory> factories = builder
        .prepareModuleFactories();
    assertEquals(
        new Errors().addMessage(
            "no suitable constructor for module %s. Classes must have either one (and only one) " +
                "constructor annotated with @Inject or a zero-argument constructor that is not private.",
            PrepareModuleFactories3_module.class),
        actualErrors);
    assertTrue(factories.isEmpty());
  }

  public static class PrepareModuleFactories4_module extends AbstractModule {

    public PrepareModuleFactories4_module() {}

    @Override
    protected void configure() {}

  }

  @Component(modules = PrepareModuleFactories4_module.class, dependsOn = PrepareModuleFactories4_2.class)
  static class PrepareModuleFactories4_1 {}

  @Component(modules = PrepareModuleFactories4_module.class)
  static class PrepareModuleFactories4_2 {}

  @Test
  public void prepareModuleFactories4_shouldNotCreateMultipleFactoriesForTheSameModule() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, PrepareModuleFactories4_1.class);
    List<ModuleFactory> factories = builder.prepareModuleFactories();
    assertFalse(actualErrors.hasErrors());
    assertEquals(1, factories.size());
  }

  @Component(override = PrepareModuleFactories1_module.class)
  static class PrepareOverride1 {}

  @Component(dependsOn = {PrepareOverride1.class}, override = PrepareModuleFactories4_module.class)
  static class PrepareOverride2 {}

  @Component(dependsOn = {PrepareOverride2.class}, override = PrepareModuleFactories1_module.class)
  static class PrepareOverride3 {}

  @Test
  public void prepareOverrides() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, PrepareOverride3.class);
    assertFalse(actualErrors.hasErrors());
    List<ModuleFactory> moduleFactories = builder.prepareModuleOverrides();
    List<Class<? extends Module>> moduleClasses = new ArrayList<>();
    for (ModuleFactory factory : moduleFactories) {
      moduleClasses.add(ModuleFactories.instantiate(factory, new HashMap<>(), ServiceKind.class).getClass());
    }
    assertEquals(2, moduleFactories.size());
    assertEquals(ImmutableList.of(
            PrepareModuleFactories1_module.class,
            PrepareModuleFactories4_module.class),
        moduleClasses);
  }

  @Component(crons = @Cronjob(query = Query1.class, triggers = "abc"))
  static class AggregateCrons1 {}

  @Test
  public void aggregateCrons1() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateCrons1.class);
    List<Cronjob> cronjobs = builder.aggregateCrons();
    assertTrue(cronjobs.isEmpty());
    KawalaBuilder.verifyCronJobs(actualErrors, createInjector(), cronjobs);
    assertThat(actualErrors.toString(), containsString(format("unreadable cron trigger 'abc' for %s", Query1.class)));
  }

  @Component(crons = @Cronjob(query = Query1.class, triggers = {}))
  static class AggregateCrons2 {}

  @Test
  public void aggregateCrons2() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateCrons2.class);
    List<Cronjob> cronjobs = builder.aggregateCrons();
    assertTrue(cronjobs.isEmpty());
    KawalaBuilder.verifyCronJobs(actualErrors, createInjector(), cronjobs);
    assertEquals(
        new Errors().addMessage("missing trigger for %s", Query1.class),
        actualErrors);
  }

  @Component(crons = @Cronjob(
      query = QueryCannotBeInstantiatedWithNoArgument.class,
      triggers = "0 * * * * ?"))
  static class AggregateCrons3 {}

  @Test
  public void aggregateCrons3() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateCrons3.class);
    List<Cronjob> cronjobs = builder.aggregateCrons();
    KawalaBuilder.verifyCronJobs(actualErrors, createInjector(), cronjobs);
    assertThat(actualErrors.toString(), containsString(format("cannot instantiate %s with no argument",
        QueryCannotBeInstantiatedWithNoArgument.class)));
  }

  @Component(crons = @Cronjob(query = Query1.class, triggers = "0 * * * * ?"))
  static class AggregateCrons4 {}

  @Test
  public void aggregateCrons4() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateCrons4.class);
    List<Cronjob> cronjobs = builder.aggregateCrons();
    assertEquals(1, cronjobs.size());
    KawalaBuilder.verifyCronJobs(actualErrors, createInjector(), cronjobs);
    assertFalse(actualErrors.hasErrors());
  }

  @Component(crons = @Cronjob(query = Query2.class, triggers = "0 * * * * ?"))
  static class AggregateCrons5 {}

  @Test
  public void aggregateCrons5() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateCrons5.class);
    List<Cronjob> cronjobs = builder.aggregateCrons();
    assertEquals(1, cronjobs.size());
    KawalaBuilder.verifyCronJobs(actualErrors, createInjector(), cronjobs);
    assertFalse(actualErrors.hasErrors());
  }

  @Component(dependsOn = ComponentModuleFactory2.class)
  static class ComponentModuleFactory {}

  @Component
  static class ComponentModuleFactory2 {}

  @KawalaService(port = 98765)
  static class ComponentModuleService implements ServiceKind {}

  @Test
  public void componentModuleFactory() throws InvocationTargetException, InstantiationException, IllegalAccessException {
    List<Component> expected = ImmutableList.of(
        ComponentModuleFactory2.class.getAnnotation(Component.class),
        ComponentModuleFactory.class.getAnnotation(Component.class));

    KawalaBuilder builder = new KawalaBuilder(actualErrors, ComponentModuleFactory.class);
    Injector injector = Guice.createInjector(
        builder.componentModuleFactory().instantiate(new HashMap<>(), ComponentModuleService.class));
    List<Component> components = injector.getInstance(Key.get(new TypeLiteral<List<Component>>() {}));
    assertEquals(expected, components);
  }

  static class SelfTest1 extends Implemented<Errors> {}

  @Component(selfTests = SelfTest1.class)
  static class AggregateSelfTests1 {}

  @Test
  public void aggregateSelfTests1() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateSelfTests1.class);
    Set<Class<? extends Query<Errors>>> selfTests = builder.aggregateSelfTests();
    assertEquals(SelfTest1.class, Iterators.getOnlyElement(selfTests.iterator()));
    KawalaBuilder.verifySelfTests(actualErrors, createInjector(), selfTests);
    assertFalse(actualErrors.hasErrors());
  }

  static class ProblematicSelfTest2 extends Implemented<Errors> {

    int foo;

    ProblematicSelfTest2(int foo) {
      this.foo = foo + 5;
    }

  }

  @Component(selfTests = ProblematicSelfTest2.class)
  static class AggregateSelfTests2 {}

  @Test
  public void aggregateSelfTests2() {
    KawalaBuilder builder = new KawalaBuilder(actualErrors, AggregateSelfTests2.class);
    Set<Class<? extends Query<Errors>>> selfTests = builder.aggregateSelfTests();

    KawalaBuilder.verifySelfTests(actualErrors, createInjector(), selfTests);
    Errors expectedErrors = new Errors();
    KachingInstantiators.createInstantiator(expectedErrors, ProblematicSelfTest2.class,
        new DefaultJsonMarshallerFactory());
    assertEquals(
        expectedErrors,
        actualErrors);
  }

  @Component
  static class AggregateSelfTests3 {}

  @Test
  public void aggregateSelfTests3() {
    // we need to be able to add to the set, it cannot be immutable
    Set<Class<? extends Query<Errors>>> selfTests =
        new KawalaBuilder(actualErrors, AggregateSelfTests3.class).aggregateSelfTests();
    assertTrue(selfTests.isEmpty());
    assertTrue(selfTests.add(SelfTest1.class));
  }

  private static class MonitoredSelfTest extends Implemented<Errors> {}

  @Component(
      selfTests = {ProblematicSelfTest2.class, MonitoredSelfTest.class},
      monitoredSelfTests = MonitoredSelfTest.class
  )
  static class AggregateMonitoredSelfTests {}

  @Test
  public void aggregateMonitoredSelfTests() {
    Set<Class<? extends Query<Errors>>> selfTests =
        new KawalaBuilder(actualErrors, AggregateMonitoredSelfTests.class).aggregateMonitoredSelfTests();
    assertEquals(MonitoredSelfTest.class, getOnlyElement(selfTests));
  }

  @Component(eagerlyInstantiate = {String.class, Double.class})
  static class AggregateEagers1 {}

  @Test
  @SuppressWarnings("unchecked")
  public void aggregateEagers1() {
    Set<Class<?>> eagers = new KawalaBuilder(actualErrors, AggregateEagers1.class).aggregateEagers();
    assertEquals(newHashSet(Double.class, String.class), eagers);
    assertFalse(actualErrors.hasErrors());
  }

  @Component
  static class AggregateStartupListeners1 {}

  @Test
  public void aggregateStartupListeners1() {
    List<Class<? extends StartupListener>> listeners =
        new KawalaBuilder(actualErrors, AggregateStartupListeners1.class)
            .aggregateStartupListeners();

    assertEquals(0, listeners.size());
    assertFalse(actualErrors.hasErrors());
  }

  @Component(startupListener = ListenerA.class)
  static class AggregateStartupListeners2 {}

  @Test
  public void aggregateStartupListeners2() {
    List<Class<? extends StartupListener>> listeners =
        new KawalaBuilder(actualErrors, AggregateStartupListeners2.class)
            .aggregateStartupListeners();

    assertEquals(1, listeners.size());
    assertFalse(actualErrors.hasErrors());
    assertEquals(ListenerA.class, listeners.get(0));
  }

  @Component(startupListener = ListenerA.class, dependsOn = AggregateStartupListeners3_A.class)
  static class AggregateStartupListeners3 {}

  @Component(startupListener = ListenerA.class)
  static class AggregateStartupListeners3_A {}

  @Test
  public void aggregateStartupListeners3() {
    // duplicates should not be added twice
    List<Class<? extends StartupListener>> listeners =
        new KawalaBuilder(actualErrors, AggregateStartupListeners3.class)
            .aggregateStartupListeners();

    assertEquals(1, listeners.size());
    assertFalse(actualErrors.hasErrors());
    assertEquals(ListenerA.class, listeners.get(0));
  }

  @Component(
      startupListener = ListenerA.class,
      dependsOn = {
          AggregateStartupListeners4_B.class,
          AggregateStartupListeners4_C.class,
          AggregateStartupListeners4_D.class
      }
  )
  static class AggregateStartupListeners4 {}

  @Component(startupListener = ListenerB.class)
  static class AggregateStartupListeners4_B {}

  @Component(
      startupListener = ListenerC.class,
      dependsOn = {
          AggregateStartupListeners4_D.class
      }
  )
  static class AggregateStartupListeners4_C {}

  @Component(startupListener = ListenerD.class)
  static class AggregateStartupListeners4_D {}

  @Test
  @SuppressWarnings("unchecked")
  public void aggregateStartupListeners4() {
    List<Class<? extends StartupListener>> listeners =
        new KawalaBuilder(actualErrors, AggregateStartupListeners4.class)
            .aggregateStartupListeners();

    assertEquals(4, listeners.size());
    assertFalse(actualErrors.hasErrors());
    assertEquals(
        newArrayList(
            ListenerB.class,
            ListenerD.class,
            ListenerC.class,
            ListenerA.class
        ),
        listeners);
  }

  @Component(
      queriesInClient = {Query1.class, Query2.class},
      queries = {Query1.class}
  )
  static class ComponentWithIntersectingQueriesInClientAndQueries {}

  @Test
  public void createServerShouldCheckIntersectionOfQueriesInClientAndQueries() {
    Errors actualErrors = new Errors();
    KawalaBuilder builder =
        new KawalaBuilder(actualErrors, ComponentWithIntersectingQueriesInClientAndQueries.class);
    try {
      builder.createServer(NOP.class);
      fail();
    } catch (Exception e) {
      assertEquals(
          new Errors()
              .addMessage("%s appears in both queriesInClient and queries", Query1.class.getSimpleName()),
          actualErrors);
      assertEquals(actualErrors.toString(), e.getMessage());
    }
  }

  @Test
  public void createServerShouldCheckKindWellFormdness() {
    Errors actualErrors = new Errors();
    KawalaBuilder builder = new KawalaBuilder(actualErrors);
    assertFalse(actualErrors.hasErrors());

    try {
      builder.createServer(BAD_KIND.class);
      fail();
    } catch (Exception e) {
      Errors expectedErros = new Errors();
      ServiceKinds.verifyWellformdness(BAD_KIND.class, expectedErros);
      assertEquals(expectedErros.toString(), e.getMessage());
    }
  }

  @Test
  public void componentWithHibernateEntitiesBindsSelfTest() {
    @Component(hibernateEntitites = IncomingEvent.class)
    class ComponentWithHibernateEntities {}

    Kawala server = new KawalaBuilder(new Errors(), ComponentWithHibernateEntities.class).createServer(NOP.class);
    assertEquals(ImmutableSet.of(VerifyHibernateEntities.class), server.getSelfTests());
    assertEquals(ImmutableSet.of(VerifyHibernateEntities.class), server.getMonitoredSelfTests());
  }

  @Test
  public void componentWithoutHibernateEntitiesDoesNotBindMonitoredSelfTest() {
    @Component
    class ComponentWithoutHibernateEntities {}

    Kawala server = new KawalaBuilder(new Errors(), ComponentWithoutHibernateEntities.class).createServer(NOP.class);
    assertEquals(Collections.emptySet(), server.getSelfTests());
    assertEquals(Collections.emptySet(), server.getMonitoredSelfTests());
  }

  /* queries */

  static class Query1 extends Implemented<String> {}

  static class Query2 extends Implemented<String> {

    Query2(@Optional String unused) {}

  }

  static class QueryCannotBeInstantiatedWithNoArgument extends Implemented<String> {

    QueryCannotBeInstantiatedWithNoArgument(int i) {}

  }

  static class ProblematicQuery1 extends Implemented<String> {

    int foo;

    ProblematicQuery1(int foo) {
      this.foo = foo + 5;
    }

  }

  static class IncorrectReturnTypeQuery extends Implemented<Cloneable> {
  }

  @Transactional
  static class TransactionalQuery extends Implemented<String> {}

  static class Implemented<T> extends AbstractQuery<T> {

    @Override
    public T process() {
      return null;
    }

  }

  /* listeners */

  static class ListenerA extends EmptyStartupListener {}

  static class ListenerB extends EmptyStartupListener {}

  static class ListenerC extends EmptyStartupListener {}

  static class ListenerD extends EmptyStartupListener {}

  /* kinds */

  static class BAD_KIND implements ServiceKind {}

  /* icingaChecks */

  static class FakeIcingaCheck implements IcingaCheck {

    @Override
    public IcingaMetadata getIcingaMetadata() {
      return null;
    }

    @Override
    public IcingaOutput getIcingaOutput() {
      return null;
    }

  }

}
