package com.kaching.platform.guice;

import static com.google.inject.Guice.createInjector;
import static com.wealthfront.test.Assert.assertNotEmpty;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;

import java.util.List;
import java.util.Map;
import java.util.Timer;

import org.apache.commons.httpclient.params.HttpConnectionManagerParams;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.ProvisionException;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Names;
import com.kaching.Author;
import com.kaching.platform.common.Option;
import com.kaching.platform.memcached.MemcachedModule;
import com.kaching.platform.queryengine.client.QueryEngineClient;
import com.kaching.platform.queryengine.client.QueryEngineHttpClient;
import com.kaching.util.http.ApacheHttpClient;
import com.kaching.util.http.HttpClient;
import com.kaching.util.http.LongOnlineTimeoutPooling;
import com.kaching.util.http.LongTimeoutPooling;
import com.kaching.util.http.Pooling;
import com.kaching.util.http.ProtobufHttpClient;

public class CommonModuleTest {

  private Injector injector;
  private CommonModule module;

  @Inject Map<String, Author> onCallPackageMapping;

  @Before
  public void before() {
    module = new CommonModule(new TestingApplicationOptions());
    injector = createInjector(
        new OptionsModule(new TestingApplicationOptions()), module);
    injector.injectMembers(this);
  }

  @Test
  public void httpClient() {
    ApacheHttpClient poolingHttpClient = getInjectedApacheHttpClient(Option.some(Pooling.class));
    assertEquals(1000, getHttpClientParams(poolingHttpClient).getConnectionTimeout());
    assertEquals(1200, getHttpClientParams(poolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(poolingHttpClient).getMaxTotalConnections());
    assertSame(poolingHttpClient, injector.getInstance(Key.get(HttpClient.class, Pooling.class)));

    ApacheHttpClient longTimeoutPoolingHttpClient = getInjectedApacheHttpClient(Option.some(LongTimeoutPooling.class));
    assertEquals(15000, getHttpClientParams(longTimeoutPoolingHttpClient).getConnectionTimeout());
    assertEquals(2400000, getHttpClientParams(longTimeoutPoolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(longTimeoutPoolingHttpClient).getMaxTotalConnections());
    assertSame(longTimeoutPoolingHttpClient, injector.getInstance(Key.get(HttpClient.class, LongTimeoutPooling.class)));

    ApacheHttpClient longOnlineTimeoutPoolingHttpClient =
        getInjectedApacheHttpClient(Option.some(LongOnlineTimeoutPooling.class));
    assertEquals(5000, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getConnectionTimeout());
    assertEquals(10000, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getMaxTotalConnections());
    assertSame(longOnlineTimeoutPoolingHttpClient,
        injector.getInstance(Key.get(HttpClient.class, LongOnlineTimeoutPooling.class)));
  }

  @Test
  public void protobufHttpClient() {
    ApacheHttpClient poolingHttpClient = getInjectedProtobufApacheHttpClient(Option.some(Pooling.class));
    assertEquals(1000, getHttpClientParams(poolingHttpClient).getConnectionTimeout());
    assertEquals(1200, getHttpClientParams(poolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(poolingHttpClient).getMaxTotalConnections());
    assertSame(poolingHttpClient, injector.getInstance(Key.get(HttpClient.class, Pooling.class)));

    ApacheHttpClient longTimeoutPoolingHttpClient =
        getInjectedProtobufApacheHttpClient(Option.some(LongTimeoutPooling.class));
    assertEquals(15000, getHttpClientParams(longTimeoutPoolingHttpClient).getConnectionTimeout());
    assertEquals(2400000, getHttpClientParams(longTimeoutPoolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(longTimeoutPoolingHttpClient).getMaxTotalConnections());
    assertSame(longTimeoutPoolingHttpClient, injector.getInstance(Key.get(HttpClient.class, LongTimeoutPooling.class)));

    ApacheHttpClient longOnlineTimeoutPoolingHttpClient =
        getInjectedProtobufApacheHttpClient(Option.some(LongOnlineTimeoutPooling.class));
    assertEquals(5000, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getConnectionTimeout());
    assertEquals(10000, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getMaxTotalConnections());
    assertSame(longOnlineTimeoutPoolingHttpClient,
        injector.getInstance(Key.get(HttpClient.class, LongOnlineTimeoutPooling.class)));
  }

  @Test
  public void queryEngineClient() {
    ApacheHttpClient poolingHttpClient = getInjectedQueryEngineApacheHttpClient(Option.some(Pooling.class));
    assertEquals(1000, getHttpClientParams(poolingHttpClient).getConnectionTimeout());
    assertEquals(1200, getHttpClientParams(poolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(poolingHttpClient).getMaxTotalConnections());
    assertSame(poolingHttpClient, injector.getInstance(Key.get(HttpClient.class, Pooling.class)));

    ApacheHttpClient longTimeoutPoolingHttpClient =
        getInjectedQueryEngineApacheHttpClient(Option.some(LongTimeoutPooling.class));
    assertEquals(15000, getHttpClientParams(longTimeoutPoolingHttpClient).getConnectionTimeout());
    assertEquals(2400000, getHttpClientParams(longTimeoutPoolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(longTimeoutPoolingHttpClient).getMaxTotalConnections());
    assertSame(longTimeoutPoolingHttpClient, injector.getInstance(Key.get(HttpClient.class, LongTimeoutPooling.class)));

    ApacheHttpClient longOnlineTimeoutPoolingHttpClient =
        getInjectedQueryEngineApacheHttpClient(Option.some(LongOnlineTimeoutPooling.class));
    assertEquals(5000, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getConnectionTimeout());
    assertEquals(10000, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getSoTimeout());
    assertEquals(500, getHttpClientParams(longOnlineTimeoutPoolingHttpClient).getMaxTotalConnections());
    assertSame(longOnlineTimeoutPoolingHttpClient,
        injector.getInstance(Key.get(HttpClient.class, LongOnlineTimeoutPooling.class)));
  }

  @Test(expected = ProvisionException.class)
  public void timer() {
    injector.getInstance(Timer.class);
  }

  @Test
  public void version() throws Exception {
    assertNotNull(injector
        .getInstance(Key.get(String.class, Version.class)));
    assertNotNull(injector
        .getInstance(Revision.class));
  }

  @Test
  public void metaInfMavenPomPropertiesJarNames() {
    assertNotEmpty(injector.getInstance(
        Key.get(new TypeLiteral<List<String>>() {},
            Names.named("META-INF.maven-pom.properties-jarNames"))));
  }

  @Test
  public void dateTimeShouldHaveEasternTimeZone() {
    assertEquals(ET, injector.getInstance(DateTime.class).getZone());
  }

  @Test
  public void bindOnCallPackageMapping() {
    Map<String, Author> expectedOnCallPackageMapping = ImmutableMap.<String, Author>builder()
        // Author.ADVICE_AUTOMATION_TEAM
        .put("com.kaching.autopilot", Author.ADVICE_AUTOMATION_TEAM)
        // Author.BANKING_PLATFORM_TEAM
        .put("com.kaching.ascensus.client", Author.BANKING_PLATFORM_TEAM)
        .put("com.kaching.risk", Author.BANKING_PLATFORM_TEAM)
        .put("com.kaching.user.ascensus", Author.BANKING_PLATFORM_TEAM)
        .put("com.kaching.user.riskmetrics", Author.BANKING_PLATFORM_TEAM)
        .put("com.kaching.user.withdrawals", Author.BANKING_PLATFORM_TEAM)
        .put("com.wealthfront.brokerage.funding.accounttransfers", Author.BANKING_PLATFORM_TEAM)
        // Author.BROKERAGE_PLATFORM_TEAM
        .put("com.kaching.apex", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.audit", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.documents", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.ira", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.iraadmin", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.omni", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.penson", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.tax", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.toggle", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.user.inteliclear", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.trading.bi.cax", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.trading.core.balances", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.trading.master", Author.BROKERAGE_PLATFORM_TEAM)
        .put("com.kaching.trading.market", Author.BROKERAGE_PLATFORM_TEAM)
        // Author.FRAUD_RISK_ENG_TEAM
        .put("com.kaching.user.idv", Author.FRAUD_RISK_ENG_TEAM)
        .put("com.wealthfront.risk", Author.FRAUD_RISK_ENG_TEAM)
        .put("com.wealthfront.banking.wellsfargo.logicbox", Author.FRAUD_RISK_ENG_TEAM)
        // Author.INVESTMENT_PRODUCTS_TEAM
        .put("com.kaching.trading.market.stockcollections", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.trading.market.polygon", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.trading.market.logos", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.trading.bi.bondladders", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.trading.bi.compatibleaccounts", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.trading.bi.crossaccounttransfer", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.trading.bi.otcaffiliate", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.account", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.customportfolios", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.fpsl", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.lostsecurityholder", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.risk", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.riskassessment", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.sp500di", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.stockportfolios", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.investing", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.zendesk", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.phone", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.push", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.user.email", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.util.mail", Author.INVESTMENT_PRODUCTS_TEAM)
        .put("com.kaching.link.aggregation.smartystreets", Author.INVESTMENT_PRODUCTS_TEAM)
        // Author.ONLINE_SERVICES_TEAM
        .put("com.kaching.user.callouts", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.cash", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.cashdrag", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.cashpaymentincentives", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.conversions", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.coworker", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.cross", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.depositmatch", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.drops", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.experiment", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.financialincentives", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.funding", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.householdviews", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.incentives", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.invitations", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.merchandise", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.npm", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.referrals", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.survey", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.tbi", Author.ONLINE_SERVICES_TEAM)
        .put("com.kaching.user.transfer", Author.ONLINE_SERVICES_TEAM)
        // Author.INVESTMENT_SERVICES_TEAM
        .put("com.kaching.historical", Author.INVESTMENT_SERVICES_TEAM)
        .put("com.kaching.investing", Author.INVESTMENT_SERVICES_TEAM)
        .put("com.kaching.trading", Author.INVESTMENT_SERVICES_TEAM)
        // Author.LINK_SERVICE_TEAM
        .put("com.kaching.link", Author.LINK_SERVICE_TEAM)
        .put("com.kaching.user.balancesheet", Author.LINK_SERVICE_TEAM)
        .put("com.kaching.user.link", Author.LINK_SERVICE_TEAM)
        .put("com.kaching.path", Author.LINK_SERVICE_TEAM)
        .put("com.wealthfront.integration.fbank.autopilot", Author.LINK_SERVICE_TEAM)
        .put("com.wealthfront.integration.link", Author.LINK_SERVICE_TEAM)
        // Author.TRADE_VALIDATION_TEAM
        .put("com.kaching.trading.bi.tradevalidation", Author.TRADE_VALIDATION_TEAM)
        // Author.TRADING_PRODUCTS_TEAM
        .put("com.kaching.trading.direct", Author.TRADING_PRODUCTS_TEAM)
        .put("com.kaching.trading.migration", Author.TRADING_PRODUCTS_TEAM)
        .put("com.kaching.trading.bi.ascensus", Author.TRADING_PRODUCTS_TEAM)
        .put("com.kaching.trading.master.sandp", Author.TRADING_PRODUCTS_TEAM)
        .put("com.kaching.trading.master.crsp", Author.TRADING_PRODUCTS_TEAM)
        .put("com.kaching.xignite", Author.TRADING_PRODUCTS_TEAM)
        .put("com.kaching.user.passiveplus", Author.TRADING_PRODUCTS_TEAM)
        .put("com.wealthfront.investing", Author.TRADING_PRODUCTS_TEAM)
        .build();

    assertEquals(expectedOnCallPackageMapping, onCallPackageMapping);
  }

  @Test
  public void testGetMemcachedMode() {
    assertEquals(MemcachedModule.Mode.FAKE_CACHE, new CommonModule(new ApplicationOptions() {{
      fakeMemcached = true;
    }}).getMemcacheMode());
    assertEquals(MemcachedModule.Mode.TEST_CACHE, new CommonModule(new ApplicationOptions() {{
      testMemcached = true;
    }}).getMemcacheMode());
    assertEquals(MemcachedModule.Mode.PROD_CACHE, new CommonModule(new ApplicationOptions()).getMemcacheMode());
  }

  @Test
  public void testGetMemcachedTimeout() {
    assertEquals(500, new CommonModule(new ApplicationOptions() {{
      memcachedTimeout = 500;
    }}).getMemcachedTimeout());
    assertEquals(1000, new CommonModule(new ApplicationOptions()).getMemcachedTimeout());
  }

  private ApacheHttpClient getInjectedApacheHttpClient(Option<Class> classOption) {
    for (Class className : classOption) {
      return (ApacheHttpClient) injector.getInstance(Key.get(HttpClient.class, className));
    }
    return (ApacheHttpClient) injector.getInstance(HttpClient.class);
  }

  private ApacheHttpClient getInjectedProtobufApacheHttpClient(Option<Class> classOption) {
    for (Class className : classOption) {
      return (ApacheHttpClient) ((ProtobufHttpClient) injector
          .getInstance(Key.get(ProtobufHttpClient.class, className))).getHttpClientProvider().get();
    }
    return (ApacheHttpClient) injector.getInstance(ProtobufHttpClient.class).getHttpClientProvider().get();
  }

  private ApacheHttpClient getInjectedQueryEngineApacheHttpClient(Option<Class> classOption) {
    for (Class className : classOption) {
      return (ApacheHttpClient) ((QueryEngineHttpClient) injector
          .getInstance(Key.get(QueryEngineClient.class, className)))
          .getHttpClientProvider()
          .get();
    }
    return (ApacheHttpClient) ((QueryEngineHttpClient) injector.getInstance(QueryEngineClient.class))
        .getHttpClientProvider().get();
  }

  private HttpConnectionManagerParams getHttpClientParams(ApacheHttpClient client) {
    return client.getClient().getHttpConnectionManager().getParams();
  }

}
