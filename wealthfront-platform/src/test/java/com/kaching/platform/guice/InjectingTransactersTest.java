package com.kaching.platform.guice;

import static com.google.inject.Guice.createInjector;
import static com.kaching.platform.guice.InjectingTransacters.injectingFakeTransacterBuilder;
import static com.kaching.platform.guice.InjectingTransacters.injectingTransacterBuilder;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;

import java.util.Set;

import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.google.inject.AbstractModule;
import com.google.inject.ImplementedBy;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Singleton;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.FakeTransacter;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.hibernate.WithSessionExpression;

public class InjectingTransactersTest {

  @Test
  public void testInjection() {
    InjectingTransacter transacter = injectingFakeTransacterBuilder().with(Integer.class, 1).build();
    Integer result = transacter.execute(new WithSessionExpression<Integer>() {
      @Inject Integer value;

      public Integer run(DbSession session) {
        return value;
      }
    });
    assertEquals((Integer) 1, result);
  }
  
  @Test
  public void injectingTransacterBuilder_usesBindingsFromParentInjector_unlessExplicitlyOverridden() {
    Injector parentInjector = createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        bind(Class1.class).toInstance(new Class1("FROM PARENT"));
        bind(Class2.class).toInstance(new Class2("FROM PARENT"));
      }
    });

    Transacter injectingTransacter = injectingTransacterBuilder(new FakeTransacter(), parentInjector)
        .with(Class2.class, new Class2("OVERRIDDEN IN CONFIGURATIONS"))
        .with(Class3.class, new Class3("FROM CONFIGURATIONS"))
        .build();

    injectingTransacter.execute(new WithSession() {
      @Inject Class1 class1;
      @Inject Class2 class2;
      @Inject Class3 class3;

      @Override
      public void run(DbSession session) {
        assertEquals("FROM PARENT", class1.getValue());
        assertEquals("OVERRIDDEN IN CONFIGURATIONS", class2.getValue());
        assertEquals("FROM CONFIGURATIONS", class3.getValue());
      }
    });
  }

  @Test
  public void injectingTransacterBuilder_singletonBindingInParent_isShared() {
    Injector parentInjector = createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        bind(DummyInterface.class).to(ExplicitImplementation.class).in(Singleton.class);
      }
    });
    DummyInterface parentDummy = parentInjector.getInstance(DummyInterface.class);

    injectingTransacterBuilder(new FakeTransacter(), parentInjector).build().execute(new WithSession() {
      @Inject DummyInterface childDummy;

      @Override
      public void run(DbSession session) {
        assertSame(parentDummy, childDummy);
      }
    });
  }

  @Test
  public void injectingTransacterBuilder_withParentInjector_doesNotModifyBindingsInParentInjector() {
    Injector parentWithoutExplicitBindings = createInjector();
    {
      DummyInterface justInTimeBinding = parentWithoutExplicitBindings.getInstance(DummyInterface.class);
      assertTrue(justInTimeBinding instanceof JustInTimeImplementation);
    }
    Set<Key<?>> originalParentBindings = ImmutableSet.copyOf(parentWithoutExplicitBindings.getAllBindings().keySet());

    Transacter injectingTransacter = injectingTransacterBuilder(new FakeTransacter(), parentWithoutExplicitBindings)
        .with(DummyInterface.class, new ExplicitImplementation())
        .build();
    injectingTransacter.execute(new WithSession() {
      @Inject DummyInterface explicitBinding;
      
      @Override
      public void run(DbSession session) {
        assertTrue(explicitBinding instanceof ExplicitImplementation);
      }
    });
    
    assertEquals(originalParentBindings, parentWithoutExplicitBindings.getAllBindings().keySet());
  }
  
  private abstract static class DummyClass {
    
    private final String value;

    private DummyClass(String value) {
      this.value = value;
    }

    String getValue() {
      return value;
    }
    
  }
  
  private static class Class1 extends DummyClass {
    
    private Class1(String value) {
      super(value);
    }
    
  }

  private static class Class2 extends DummyClass {
    
    private Class2(String value) {
      super(value);
    }

  }
  
  private static class Class3 extends DummyClass {
    
    private Class3(String value) {
      super(value);
    }
    
  }
  
  @ImplementedBy(JustInTimeImplementation.class)
  interface DummyInterface {
    
  }
  
  private static class JustInTimeImplementation implements DummyInterface {
    
  }
  
  private static class ExplicitImplementation implements DummyInterface {
    
  }

}
