package com.kaching.platform.sentry;

import static com.google.inject.Guice.createInjector;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.List;
import java.util.Map;

import org.junit.Ignore;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.TypeLiteral;
import com.kaching.Author;

import io.sentry.SentryClient;

public class SentryModuleTest {

  @Test
  public void inject_withIsFakeTrue_shouldReturnEmptyMap() {
    Injector injector = createInjector(new SentryModule(true));
    Map<Author, SentryClient> map = injector.getInstance(Key.get(new TypeLiteral<Map<Author, SentryClient>>() {}));
    assertTrue(map.isEmpty());

    Map<List<Author>, SentryClient> multiAuthorMap =
        injector.getInstance(Key.get(new TypeLiteral<Map<List<Author>, SentryClient>>() {}));
    assertTrue(multiAuthorMap.isEmpty());
  }

  @Test
  @Ignore("Initializes real Sentry clients")
  public void inject_withIsFakeFalse() {
    Injector injector = createInjector(new SentryModule(false));
    Map<Author, SentryClient> map = injector.getInstance(Key.get(new TypeLiteral<Map<Author, SentryClient>>() {}));
    assertEquals(25, map.size());
    assertNotNull(map.get(Author.ACATS_TEAM));
    assertNotNull(map.get(Author.ADVICE_AUTOMATION_TEAM));
    assertNotNull(map.get(Author.LINK_SERVICE_TEAM));
    assertNotNull(map.get(Author.BROKERAGE_PLATFORM_TEAM));
    assertNotNull(map.get(Author.ONLINE_SERVICES_TEAM));
    assertNotNull(map.get(Author.INVESTMENT_SERVICES_TEAM));
    assertNotNull(map.get(Author.TRADING_PRODUCTS_TEAM));
    assertNotNull(map.get(Author.DATA_TEAM));
    assertNotNull(map.get(Author.WEB_TEAM));
    assertNotNull(map.get(Author.IOS_TEAM));
    assertNotNull(map.get(Author.ANDROID_TEAM));
    assertNotNull(map.get(Author.YODLEE_INTEGRATION_TEAM));
    assertNotNull(map.get(Author.SITE_HEALTH_TEAM));
    assertNotNull(map.get(Author.TAOS_TEAM));
    assertNotNull(map.get(Author.FRAUD_RISK_ENG_TEAM));
    assertNotNull(map.get(Author.LENDING_PLATFORM_TEAM));

    Map<List<Author>, SentryClient> multiAuthorMap =
        injector.getInstance(Key.get(new TypeLiteral<Map<List<Author>, SentryClient>>() {}));
    assertEquals(1, multiAuthorMap.size());
    assertNotNull(
        multiAuthorMap.get(ImmutableList.of(Author.BROKERAGE_PLATFORM_TEAM, Author.INVESTMENT_SERVICES_TEAM)));
  }

}
