package com.kaching.platform.sentry;

import static com.wealthfront.test.Assert.assertOptionEmpty;

import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.kaching.Author;

public class EmptySentryClientProviderTest {

  @Test
  public void get() {
    assertOptionEmpty(new EmptySentryClientProvider().get(Author.DATA_TEAM));
    assertOptionEmpty(new EmptySentryClientProvider().get(
        ImmutableList.of(Author.BROKERAGE_PLATFORM_TEAM, Author.INVESTMENT_SERVICES_TEAM)));
  }

}