package com.kaching.platform.memcached;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.twolattes.json.Json;

public class RateLimitTest {

  @Test
  public void marshall() {
    Json.Value expected = object("windowLengthMillis", 10_000, "rate", 12.0);
    assertMarshalling(createEntityMarshaller(RateLimit.class), expected, new RateLimit(10_000L, 12.0));
  }

  @Test
  public void getRate() {
    RateLimit rateLimit = new RateLimit(10_000L, 10.0);
    assertEquals(10.0, rateLimit.getRate(), 0.0);
  }

  @Test
  public void getRate_withOtherWindow() {
    RateLimit rateLimitMinute = new RateLimit(60_000L, 500.0);
    assertEquals(500.0, rateLimitMinute.getRate(60_000L), 0.0);
    assertEquals(500.0 / 6.0, rateLimitMinute.getRate(10_000L), 0.0);
    assertEquals(500.0 / 60.0, rateLimitMinute.getRate(1_000L), 0.0);
    assertEquals(500.0 / 60_000.0, rateLimitMinute.getRate(1L), 0.0);

    RateLimit rateLimitTenSeconds = new RateLimit(10_000L, 10.0);
    assertEquals(60.0, rateLimitTenSeconds.getRate(60_000L), 0.0);
    assertEquals(10.0, rateLimitTenSeconds.getRate(10_000L), 0.0);
    assertEquals(1.0, rateLimitTenSeconds.getRate(1_000L), 0.0);
    assertEquals(0.001, rateLimitTenSeconds.getRate(1L), 0.0);

    RateLimit rateLimitOneSecond = new RateLimit(1_000L, 100.0);
    assertEquals(6_000.0, rateLimitOneSecond.getRate(60_000L), 0.0);
    assertEquals(1_000.0, rateLimitOneSecond.getRate(10_000L), 0.0);
    assertEquals(100.0, rateLimitOneSecond.getRate(1_000L), 0.0);
    assertEquals(0.1, rateLimitOneSecond.getRate(1L), 0.0);
  }

}