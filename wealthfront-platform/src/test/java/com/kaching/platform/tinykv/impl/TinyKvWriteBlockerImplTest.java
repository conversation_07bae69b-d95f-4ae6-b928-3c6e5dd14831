package com.kaching.platform.tinykv.impl;

import org.joda.time.Duration;
import org.junit.Test;

import com.kaching.platform.common.Option;
import com.kaching.platform.testing.WExpectations;
import com.kaching.platform.tinykv.TinyKvTestBase;

public class TinyKvWriteBlockerImplTest extends TinyKvTestBase {
  
  private final TinyKvRawWriteBlocker rawWriteBlocker = mockery.mock(TinyKvRawWriteBlocker.class);
  
  @Test
  public void blockUntilWritesReachAllServices() {
    mockery.checking(new WExpectations() {{
      oneOf(rawWriteBlocker).blockForWritesToPropagate(group1GroupId, Option.none());
    }});
    getBlocker().blockUntilWritesReachAllServices();
  }

  @Test
  public void blockForWritesToPropagate_withMaxTime() {
    mockery.checking(new WExpectations() {{
      oneOf(rawWriteBlocker).blockForWritesToPropagate(group1GroupId, Option.some(Duration.standardSeconds(5)));
    }});
    getBlocker().blockForWritesToPropagate(Option.some(Duration.standardSeconds(5)));
  }

  @Test
  public void blockForWritesToPropagate_withoutMaxTime() {
    mockery.checking(new WExpectations() {{
      oneOf(rawWriteBlocker).blockForWritesToPropagate(group1GroupId, Option.none());
    }});
    getBlocker().blockForWritesToPropagate(Option.none());
  }
  
  private TinyKvWriteBlockerImpl<UserIdStore> getBlocker() {
    TinyKvWriteBlockerImpl<UserIdStore> blocker = new TinyKvWriteBlockerImpl<>(group1GroupId);
    blocker.rawWriteBlocker = rawWriteBlocker;
    return blocker;
  }

}