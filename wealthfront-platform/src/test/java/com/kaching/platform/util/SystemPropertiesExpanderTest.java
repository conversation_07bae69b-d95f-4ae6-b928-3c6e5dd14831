package com.kaching.platform.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Test;

public class SystemPropertiesExpanderTest {

  @Test
  public void testNoExpand() throws Exception {
    String nothing = "nothing here";
    assertEquals(nothing, SystemPropertiesExpander.expand(nothing));
    assertNull(SystemPropertiesExpander.expand(null));
  }

  @Test
  public void testHappyExpand() throws Exception {
    System.setProperty("myKey", "thing");
    String oneThing = "some{myKey} here";
    assertEquals("something here", SystemPropertiesExpander.expand(oneThing));

    System.setProperty("myOtherKey", "other thing");
    String twoThings = "{myKey} second {myOtherKey}";
    assertEquals("thing second other thing", SystemPropertiesExpander.expand(twoThings));
  }

  @Test
  public void testUnhappyExpand() throws Exception {
    String oneThing = "some{myKey2} here";
    assertEquals(oneThing, SystemPropertiesExpander.expand(oneThing));

    System.setProperty("myOtherKey", "other thing");
    String twoThings = "{myKey2} second {myOtherKey}";
    assertEquals("{myKey2} second other thing", SystemPropertiesExpander.expand(twoThings));
  }

}
