package com.kaching.platform.util;

import static com.kaching.platform.util.EmojiParsers.parseToAliasesOrNull;
import static com.kaching.platform.util.EmojiParsers.parseToUnicodeOrNull;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Test;

public class EmojiParsersTest {

  @Test
  public void parseToAliasesOrNull_nonNull() throws Exception {
    assertEquals(":clap:", parseToAliasesOrNull("\uD83D\uDC4F"));
  }

  @Test
  public void parseToUnicodeOrNull_nonNull() throws Exception {
    assertEquals("\uD83D\uDC4F", parseToUnicodeOrNull(":clap:"));
  }

  @Test
  public void parseToAliasesOrNull_null() throws Exception {
    assertNull(parseToAliasesOrNull(null));
  }

  @Test
  public void parseToUnicodeOrNull_null() throws Exception {
    assertNull(parseToUnicodeOrNull(null));
  }

}