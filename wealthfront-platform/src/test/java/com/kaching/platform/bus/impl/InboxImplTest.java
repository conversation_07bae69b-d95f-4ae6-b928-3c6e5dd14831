package com.kaching.platform.bus.impl;

import static org.junit.Assert.*;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.google.inject.util.Providers;
import com.kaching.platform.bus.Event;
import com.kaching.platform.bus.Handler;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.util.tests.PersistentTest;
import com.twolattes.json.Json;

@RunWith(PersistentTestRunner.class)
@PersistentTest(
    entities = {
        IncomingEvent.class
    })
public class InboxImplTest {

  private Mockery mockery;
  private Message message;
  private InboxSignaler inboxSignaler;

  @Before
  public void before() {
    mockery = new Mockery();
    message = new Message(
        EventId.fromString("a559f564-db0b-4c8a-941d-94f57dbe77b8"),
        new EventType("type"),
        Json.string("event"));
    inboxSignaler = mockery.mock(InboxSignaler.class);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void enqueue(Transacter transacter) {
    transacter.executeWithSession(session -> {
      InboxImpl inbox = createInbox(session);

      mockery.checking(new Expectations() {{
        oneOf(inboxSignaler).signal(with(any(Id.class)));
      }});

      inbox.enqueue(HandlerA.class, message);
    });

    transacter.executeWithReadOnlySession(session -> {
      assertEquals(1, session.createCriteria(IncomingEvent.class).list().size());
    });
  }

  @Test
  public void enqueueShouldNotEnqueueWhenAnEventWithTheSameEventIdAndHandlerHasAlreadyBeenEnqueued(Transacter transacter) {
    transacter.executeWithSession(session -> {
      InboxImpl inbox = createInbox(session);

      mockery.checking(new Expectations() {{
        oneOf(inboxSignaler).signal(with(any(Id.class)));
      }});

      inbox.enqueue(HandlerA.class, message);
    });

    transacter.executeWithReadOnlySession(session -> {
      assertEquals(1, session.createCriteria(IncomingEvent.class).list().size());
    });

    transacter.executeWithSession(session -> {
      InboxImpl inbox = createInbox(session);

      mockery.checking(new Expectations() {{
        oneOf(inboxSignaler).signal(with(any(Id.class)));
      }});

      inbox.enqueue(HandlerB.class, message);
    });

    transacter.executeWithReadOnlySession(session -> {
      assertEquals(2, session.createCriteria(IncomingEvent.class).list().size());
    });

    transacter.executeWithSession(session -> {
      createInbox(session).enqueue(HandlerA.class, message);
    });

    transacter.executeWithReadOnlySession(session -> {
      assertEquals(2, session.createCriteria(IncomingEvent.class).list().size());
    });
  }

  private InboxImpl createInbox(DbSession session) {
    InboxImpl inbox = new InboxImpl();
    inbox.session = session;
    inbox.clock = Providers.of(new DateTime());
    inbox.inboxSignaler = inboxSignaler;
    return inbox;
  }

  private static class ProcessIncomingEventImpl extends ProcessIncomingEvent {

    private final Id<IncomingEvent> id;

    ProcessIncomingEventImpl(Id<IncomingEvent> id) {
      this.id = id;
    }

    @Override
    public Boolean process() {
      return process(id);
    }
  }

  private static class Event1 implements Event {}

  private static class HandlerA implements Handler<Event1> {

    @Override
    public void handle(Event1 event) {
    }
  }

  private static class HandlerB implements Handler<Event1> {

    @Override
    public void handle(Event1 event) {
    }
  }

}