package com.kaching.platform.bus.impl;

import static com.wealthfront.test.Assert.assertCollectionEquals;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.Collections;

import org.I0Itec.zkclient.IZkChildListener;
import org.I0Itec.zkclient.IZkDataListener;
import org.I0Itec.zkclient.ZkClient;
import org.hamcrest.CustomTypeSafeMatcher;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.base.Charsets;
import com.google.common.collect.Sets;
import com.google.inject.util.Providers;
import com.kaching.DefaultKachingMarshallers;
import com.kaching.platform.bus.Event;
import com.kaching.platform.common.Option;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.zk.ZkChildSync;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.wealthfront.util.time.DateTimeZones;

public class SubscriptionsSynchronizerImplTest {

  private Mockery mockery;
  private SubscriptionsSynchronizerImpl synchronizer;
  private ServiceKind um;
  private ServiceKind bi;
  private EventType eventType1;
  private EventType eventType2;

  @Before
  public void before() {
    mockery = Mockeries.mockery(true);
    synchronizer = new SubscriptionsSynchronizerImpl();
    synchronizer.zk = mockery.mock(ZkClient.class);
    synchronizer.subscriptions = new Subscriptions();
    synchronizer.clock = Providers.of(new DateTime(2016, 3, 1, 10, 30, 0, DateTimeZones.ET));
    um = KachingServices.singleton(KachingServices.UM.class);
    bi = KachingServices.singleton(KachingServices.BI.class);
    eventType1 = EventType.of(Event1.class);
    eventType2 = EventType.of(Event2.class);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void start() {
    mockery.checking(new Expectations() {{
      oneOf(synchronizer.zk).exists("/bus/subscriptions");
      will(returnValue(true));

      oneOf(synchronizer.zk).subscribeChildChanges(with(equal("/bus/subscriptions")), with(any(IZkChildListener.class)));

      oneOf(synchronizer.zk).getChildren("/bus/subscriptions");
      will(returnValue(Arrays.asList("UM", "BI")));

      oneOf(synchronizer.zk).read("/bus/subscriptions/UM");
      will(returnValue(createMarshaledData(eventType1)));

      oneOf(synchronizer.zk).subscribeDataChanges(with(equal("/bus/subscriptions/UM")), with(any(IZkDataListener.class)));

      oneOf(synchronizer.zk).read("/bus/subscriptions/BI");
      will(returnValue(createMarshaledData(eventType1, eventType2)));

      oneOf(synchronizer.zk).subscribeDataChanges(with(equal("/bus/subscriptions/BI")), with(any(IZkDataListener.class)));
    }});

    synchronizer.start();

    assertCollectionEquals(Sets.newHashSet(eventType1), synchronizer.subscriptions.getSubscriptions(um));
    assertCollectionEquals(Sets.newHashSet(eventType1, eventType2), synchronizer.subscriptions.getSubscriptions(bi));
  }

  private byte[] createMarshaledData(EventType... eventTypes) {
    SubscriptionsSynchronizerImpl.Data data = createData(eventTypes);

    Marshaller<SubscriptionsSynchronizerImpl.Data> marshaller =
        DefaultKachingMarshallers.createMarshaller(SubscriptionsSynchronizerImpl.Data.class);

    return marshaller.marshall(data).toString().getBytes(Charsets.UTF_8);
  }

  private SubscriptionsSynchronizerImpl.Data createData(EventType... eventTypes) {
    return new SubscriptionsSynchronizerImpl.Data(
        new DateTime(2016, 3, 1, 10, 30, 0, DateTimeZones.ET),
        Sets.newHashSet(eventTypes));
  }

  @Test
  public void startShouldCreatePersistentPathWhenItDoesNotExist() {
    mockery.checking(new Expectations() {{
      oneOf(synchronizer.zk).exists("/bus/subscriptions");
      will(returnValue(false));

      oneOf(synchronizer.zk).exists("/bus");
      will(returnValue(false));

      oneOf(synchronizer.zk).createPersistent("/bus");

      oneOf(synchronizer.zk).createPersistent("/bus/subscriptions");

      oneOf(synchronizer.zk).subscribeChildChanges(with(equal("/bus/subscriptions")), with(any(IZkChildListener.class)));

      oneOf(synchronizer.zk).getChildren("/bus/subscriptions");
      will(returnValue(Collections.emptyList()));
    }});

    synchronizer.start();
  }

  @Test
  public void zkChildSyncShouldUpdateSubscriptionsWhenANewChildIsAdded() {
    ZkChildSync<SubscriptionsSynchronizerImpl.Data> sync = synchronizer.createZkChildSync();

    sync.added("/bus/subscriptions/UM", createData(eventType1));
    assertCollectionEquals(Sets.newHashSet(eventType1), synchronizer.subscriptions.getSubscriptions(um));
  }

  @Test
  public void zkChildSyncShouldUpdateSubscriptionsWhenAChildIsUpdated() {
    ZkChildSync<SubscriptionsSynchronizerImpl.Data> sync = synchronizer.createZkChildSync();

    sync.added("/bus/subscriptions/UM", createData(eventType1));
    assertCollectionEquals(Sets.newHashSet(eventType1), synchronizer.subscriptions.getSubscriptions(um));

    sync.updated("/bus/subscriptions/UM", createData(eventType2));
    assertCollectionEquals(Sets.newHashSet(eventType2), synchronizer.subscriptions.getSubscriptions(um));
  }

  @Test
  public void zkChildSyncShouldUpdateSubscriptionsWhenAChildIsRemoved() {
    ZkChildSync<SubscriptionsSynchronizerImpl.Data> sync = synchronizer.createZkChildSync();

    sync.added("/bus/subscriptions/UM", createData(eventType1));
    assertCollectionEquals(Sets.newHashSet(eventType1), synchronizer.subscriptions.getSubscriptions(um));

    sync.removed("/bus/subscriptions/UM", createData(eventType1));
    assertTrue(synchronizer.subscriptions.getSubscriptions(um).isEmpty());
  }

  @Test
  public void subscribe() {
    mockery.checking(new Expectations() {{
      oneOf(synchronizer.zk).exists("/bus/subscriptions/UM");
      will(returnValue(true));

      oneOf(synchronizer.zk).writeData(
          with(equal("/bus/subscriptions/UM")),
          with(new CustomTypeSafeMatcher<byte[]>("incorrect data") {
            @Override
            public boolean matchesSafely(byte[] bytes) {
              Marshaller<SubscriptionsSynchronizerImpl.Data> marshaller =
                  DefaultKachingMarshallers.createMarshaller(SubscriptionsSynchronizerImpl.Data.class);
              SubscriptionsSynchronizerImpl.Data data =
                  marshaller.unmarshall(Json.fromString(new String(bytes, Charsets.UTF_8)));
              return data.eventTypes.size() == 2
                  && data.eventTypes.contains(eventType1)
                  && data.eventTypes.contains(eventType2)
                  && data.lastUpdatedAt.equals(new DateTime(2016, 3, 1, 10, 30, 0, DateTimeZones.ET));
            }
          }));
    }});

    synchronizer.subscribe(um, Sets.newHashSet(eventType1, eventType2));
  }

  @Test
  public void subscribeShouldCreatePersistentPathWhenItDoesNotExist() {
    mockery.checking(new Expectations() {{
      oneOf(synchronizer.zk).exists("/bus/subscriptions/UM");
      will(returnValue(false));

      oneOf(synchronizer.zk)
          .createPersistent(with(equal("/bus/subscriptions/UM")), with(Expectations.<byte[]>anything()));

      never(synchronizer.zk).writeData(with(equal("/bus/subscriptions/UM")), with(Expectations.<byte[]>anything()));
    }});

    synchronizer.subscribe(um, Sets.newHashSet(eventType1, eventType2));
  }

  @Test
  public void subscribeShouldNotUpdateDataWhenSubscriptionsHaveNotChanged() {
    synchronizer.subscriptions.update(um, Sets.newHashSet(eventType1, eventType2));

    synchronizer.subscribe(um, Sets.newHashSet(eventType1, eventType2));
  }

  @Test
  public void extractServiceKind() {
    Option<ServiceKind> maybeServiceKind = synchronizer.extractServiceKind("/bus/subscriptions/UM");
    ServiceKind serviceKind = maybeServiceKind.getOrThrow();
    assertEquals(um, serviceKind);
  }

  @Test
  public void extractServiceKindShouldReturnNoneWhenTheServiceKindIsUnknown() {
    Option<ServiceKind> maybeServiceKind = synchronizer.extractServiceKind("/bus/subscriptions/UNKNOWN");
    assertTrue(maybeServiceKind.isEmpty());
  }

  @Test
  public void extractServiceKindShouldReturnNoneWhenThePathIsMalformed() {
    Option<ServiceKind> maybeServiceKind = synchronizer.extractServiceKind("/bus/subscriptions/foo/bar");
    assertTrue(maybeServiceKind.isEmpty());
  }

  @Test
  public void getFullPath() {
    String fullPath = synchronizer.getFullPath(um);
    assertEquals("/bus/subscriptions/UM", fullPath);
  }

  @Test
  public void marshalData() {
    assertMarshalling(
        DefaultKachingMarshallers.createMarshaller(SubscriptionsSynchronizerImpl.Data.class),
        Json.object(
            "lastUpdatedAt", "2016-03-01 10:30:00.000",
            "eventTypes", Json.array(
                "com.kaching.platform.bus.impl.SubscriptionsSynchronizerImplTest$Event2",
                "com.kaching.platform.bus.impl.SubscriptionsSynchronizerImplTest$Event1")),
        createData(eventType1, eventType2));
  }

  private static class Event1 implements Event {}

  private static class Event2 implements Event {}

}