package com.kaching.platform.bus.impl;

import static com.twolattes.json.Json.object;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.Set;

import org.dbunit.DatabaseUnitException;
import org.joda.time.DateTime;
import org.junit.BeforeClass;
import org.junit.Test;

import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.util.tests.PersistentTestBase;

public class PersistentOutgoingEventRepositoryTest extends PersistentTestBase {

  private final DateTime now = new DateTime(2015, 4, 1, 15, 12, 45, ET);

  @BeforeClass
  public static void entities() throws DatabaseUnitException, SQLException {
    configure(OutgoingEvent.class);
  }

  @Test
  public void getOutgoingEventIdsWithNoReceiver() {
    Message message = new Message(EventId.random(), null, object("greeting", "Hello, World!"));
    Set<Id<OutgoingEvent>> expected = new HashSet<>();
    transacter.execute((WithSession) session -> {
      expected.add(session.save(new OutgoingEvent(null, message, now)));
      session.save(new OutgoingEvent(KachingServices.singleton(KachingServices.UM.class), message, now));
    });

    transacter.execute((WithReadOnlySession) session -> {
      PersistentOutgoingEventRepository repo = new PersistentOutgoingEventRepository(session);
      assertEquals(expected, repo.getOutgoingEventIdsWithNoReceiver());
    });
  }

}
