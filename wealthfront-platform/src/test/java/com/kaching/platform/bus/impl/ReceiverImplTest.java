package com.kaching.platform.bus.impl;

import org.jmock.Expectations;
import org.jmock.Mockery;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.kaching.platform.bus.Event;
import com.kaching.platform.bus.Handler;

public class ReceiverImplTest {

  private Mockery mockery;
  private ReceiverImpl receiver;

  @Before
  public void before() {
    mockery = new Mockery();
    receiver = new ReceiverImpl();
    receiver.handlers = new HandlersBuilder().add(HandlerA.class).add(HandlerB.class).build();
    receiver.inbox = mockery.mock(Inbox.class);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void receive() {
    final Message message = new Message(null, EventType.of(Event1.class), null);

    mockery.checking(new Expectations() {{
      oneOf(receiver.inbox).enqueue(with(same(HandlerA.class)), with(same(message)));
      oneOf(receiver.inbox).enqueue(with(same(HandlerB.class)), with(same(message)));
    }});

    receiver.receive(message);
  }

  @Test
  public void receiveShouldNotEnqueueAnyMessagesWhenThereAreNoHandlers() {
    final Message message = new Message(null, EventType.of(Event2.class), null);
    receiver.receive(message);
  }

  @Test
  public void receiveShouldEnqueueHandlersOfSuperTypes() {
    final Message message = new Message(null, EventType.of(Event3.class), null);

    mockery.checking(new Expectations() {{
      oneOf(receiver.inbox).enqueue(with(same(HandlerA.class)), with(same(message)));
      oneOf(receiver.inbox).enqueue(with(same(HandlerB.class)), with(same(message)));
    }});

    receiver.receive(message);
  }

  private static class Event1 implements Event {}

  private static class Event2 implements Event {}

  private static class Event3 extends Event1 {}

  private static class HandlerA implements Handler<Event1> {

    @Override
    public void handle(Event1 event) {
    }
  }

  private static class HandlerB implements Handler<Event1> {

    @Override
    public void handle(Event1 event) {
    }
  }

}