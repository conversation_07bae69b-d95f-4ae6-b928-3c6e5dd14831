package com.kaching.platform.bus.impl;

import static com.google.common.collect.Iterables.getOnlyElement;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.util.Map;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.kaching.monitor.esp.ResponsiblePartyFinder;
import com.kaching.monitor.esp.ResponsiblePartyFinderImpl;
import com.kaching.mq.QueryInvocation;
import com.kaching.mq.QueryPublisher;
import com.kaching.mq.rabbitmq.ChannelBinding;
import com.kaching.platform.bus.Bus;
import com.kaching.platform.bus.BusModuleBuilder;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.queryengine.client.QueryEngineClient;
import com.kaching.util.functional.Tuple2;
import com.kaching.util.http.LongTimeoutPooling;

public class BusEndToEndTest extends BusEndToEndTestBase {

  @BeforeClass
  public static void beforeClass() {
    configure(
        new AbstractModule() {
          @Override
          protected void configure() {
            bind(ServiceKind.class).toInstance(new KachingServices.NOP());
            bind(ResponsiblePartyFinder.class).toInstance(new ResponsiblePartyFinderImpl() {{
              onCallPackageMapping = Map.of();
            }});
            bind(QueryEngineClient.class).annotatedWith(LongTimeoutPooling.class).toProvider(QueryEngineClientProvider.class);
            bind(QueryPublisher.class).to(TestQueryPublisher.class);
            install(new BusModuleBuilder()
                .withProcessIncomingEventClass(ProcessIncomingEventImpl.class)
                .withProcessOutgoingEventClass(ProcessOutgoingEventImpl.class)
                .withHandler(HandlerA.class)
                .build());
          }
        },
        IncomingEvent.class,
        OutgoingEvent.class);
  }

  @Test
  public void endtoend() {
    transacter.execute(new WithSession() {
      @Inject Bus bus;
      @Override
      public void run(DbSession session) {
        bus.publish(new Event1("the message"));
      }
    });

    assertEquals(3, processQueryQueue());

    transacter.executeWithSession(session -> {
      IncomingEvent incoming = getOnlyElement(session.createCriteria(IncomingEvent.class).list());
      assertNotNull(incoming.getSucceededAt());
      OutgoingEvent outgoing = getOnlyElement(session.createCriteria(OutgoingEvent.class).list());
      assertNotNull(outgoing.getSucceededAt());
    });

    HandledEvent success = injector().getInstance(HandledEvent.class);
    assertEquals("the message", success.getOnlyEvent().value);

    TestQueryPublisher publisher = (TestQueryPublisher) injector().getInstance(QueryPublisher.class);
    assertEquals(2, publisher.getMessages().size());
    Tuple2<ChannelBinding, QueryInvocation<?>> m0 = publisher.getMessages().get(0);
    assertEquals("query.com.kaching.platform.bus.impl.BusEndToEndTestBase$ProcessOutgoingEventImpl", m0._1.getExchangeName());
    assertEquals("com.kaching.platform.bus.impl.BusEndToEndTestBase$ProcessOutgoingEventImpl", m0._2.getQueryClass());
    Tuple2<ChannelBinding, QueryInvocation<?>> m1 = publisher.getMessages().get(1);
    assertEquals("query.com.kaching.platform.bus.impl.BusEndToEndTestBase$ProcessIncomingEventImpl", m1._1.getExchangeName());
    assertEquals("com.kaching.platform.bus.impl.BusEndToEndTestBase$ProcessIncomingEventImpl", m1._2.getQueryClass());
  }

}
