package com.kaching.platform.bus.impl;

import static com.kaching.platform.bus.impl.QueuedItemProcessor.EVENT_FAILURE_THROWING_THRESHOLD;
import static com.kaching.platform.bus.impl.QueuedItemProcessor.ProcessResult.FAILED_WITH_RETRIES;
import static com.kaching.platform.bus.impl.QueuedItemProcessor.ProcessResult.NORMAL;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.IntStream;

import org.hibernate.StaleObjectStateException;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.google.inject.util.Providers;
import com.kaching.monitor.esp.ResponsiblePartyFinderImpl;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithSessionExpression;
import com.kaching.platform.queryengine.FakeStackTraceMonitor;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.util.tests.PersistentTest;

@RunWith(PersistentTestRunner.class)
@PersistentTest(
    entities = {
        DummyQueuedItem.class
    })
public class QueuedItemProcessorTest {

  private QueuedItemProcessor processor;
  private FakeStackTraceMonitor fakeStackTraceMonitor;

  @Before
  public void before(Transacter transacter) {
    processor = new QueuedItemProcessor();
    processor.transacter = transacter;
    processor.clock = Providers.of(new DateTime());
    fakeStackTraceMonitor = new FakeStackTraceMonitor();
    processor.stackTraceMonitor = fakeStackTraceMonitor;
    processor.serviceKind = new KachingServices.BUM();
    processor.responsiblePartyFinder = new ResponsiblePartyFinderImpl() {{
      onCallPackageMapping = Map.of();
    }};
  }

  @After
  public void after() {
    fakeStackTraceMonitor.assertNoUnexpectedExceptions();
  }

  @Test
  public void shouldSucceed(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNull(item.getFirstPolledAt());
      assertNull(item.getLastPolledAt());
      assertNull(item.getFirstFailedAt());
      assertNull(item.getLastFailedAt());
      assertNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(0, item.getFailedAttempts());
      assertFalse(item.isDropped());
      assertFalse(item.isInFlight());
    });

    AtomicBoolean called = new AtomicBoolean(false);
    processor.process(DummyQueuedItem.class, id, false, (item) -> {
      called.set(true);
      return true;
    });

    assertTrue(called.get());

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNull(item.getFirstFailedAt());
      assertNull(item.getLastFailedAt());
      assertNull(item.getFailedAt());
      assertNotNull(item.getSucceededAt());
      assertEquals(0, item.getFailedAttempts());
      assertFalse(item.isDropped());
      assertFalse(item.isInFlight());
    });
  }

  @Test
  public void shouldFailWhenTheActionReturnsFalse(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, false, (item) -> false);

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertFalse(item.isDropped());
      assertFalse(item.isInFlight());
    });
  }

  @Test
  public void shouldFailWhenTheActionReturnsFalse_shouldNotAddToStmIfNotDropOnFailure(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, false, (item) -> false);

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertFalse(item.isDropped());
      assertFalse(item.isInFlight());
    });
  }

  @Test
  public void shouldFailWhenTheActionThrows_shouldNotAddToStmIfNotDropOnFailure_failsOnce(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, false, (item) -> {
      throw new RuntimeException();
    });

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertFalse(item.isDropped());
      assertFalse(item.isInFlight());
    });
  }

  @Test
  public void shouldFailWhenTheActionThrows_shouldDropAndAddToStmIfNotDropOnFailure_failsManyTimes(
      Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    IntStream.rangeClosed(1, EVENT_FAILURE_THROWING_THRESHOLD).forEach(i ->
        processor.process(DummyQueuedItem.class, id, false, (item) -> {
          throw new RuntimeException();
        }));

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(EVENT_FAILURE_THROWING_THRESHOLD, item.getFailedAttempts());
      assertTrue(item.isDropped());
      assertFalse(item.isInFlight());
    });

    fakeStackTraceMonitor.assertOnlyStackTraceAndClear("QueuedItemProcessor failed 60 times");
  }

  @Test
  public void shouldFailWhenTheActionThrows_shouldNotAddToStmIfRetryable_failsOnce(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    QueuedItemProcessor.ProcessResult result = processor.process(DummyQueuedItem.class, id, true, (item) -> {
      throw new BusRetryableException();
    });

    assertEquals(FAILED_WITH_RETRIES, result);

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertFalse(item.isDropped());
      assertFalse(item.isInFlight());
    });
  }

  @Test
  public void shouldFailWhenTheActionThrows_shouldDropAndAddToStmIfRetryable_failsManyTimes(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    IntStream.rangeClosed(1, EVENT_FAILURE_THROWING_THRESHOLD).forEach(i -> {
      QueuedItemProcessor.ProcessResult result = processor.process(DummyQueuedItem.class, id, false, (item) -> {
        throw new BusRetryableException();
      });

      assertEquals(i < EVENT_FAILURE_THROWING_THRESHOLD ? FAILED_WITH_RETRIES : NORMAL, result);
    });

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(EVENT_FAILURE_THROWING_THRESHOLD, item.getFailedAttempts());
      assertTrue(item.isDropped());
      assertFalse(item.isInFlight());
    });

    fakeStackTraceMonitor.assertOnlyStackTraceAndClear("QueuedItemProcessor failed 60 times");
  }

  @Test
  public void shouldFailWhenTheActionReturnsFalse_addToStmIfDropOnFailure(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, true, (item) -> false);

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertTrue(item.isDropped());
      assertFalse(item.isInFlight());
    });

    fakeStackTraceMonitor.assertOnlyStackTraceAndClear("QueuedItemProcessor failed 1 times");
  }

  @Test
  public void shouldFailWhenTheActionThrows_addToStmIfDropOnFailure(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, true, (item) -> {
      throw new RuntimeException();
    });

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertTrue(item.isDropped());
      assertFalse(item.isInFlight());
    });

    fakeStackTraceMonitor.assertOnlyStackTraceAndClear(
        "BusUnknownException: QueuedItemProcessor failed 1 times " +
            "for DummyQueuedItem com.my.DummyQueuedItem id: ");
  }

  @Test
  public void shouldFailWhenTheActionThrows_addToStmIfNonRetryableException(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, false, (item) -> {
      throw new BusNonRetryableException();
    });

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertTrue(item.isDropped());
      assertFalse(item.isInFlight());
    });

    fakeStackTraceMonitor.assertOnlyStackTraceAndClear(
        "BusNonRetryableException: QueuedItemProcessor failed with a BusNonRetryableException " +
            "for DummyQueuedItem com.my.DummyQueuedItem id: ");
  }

  @Test
  public void shouldDropWhenDropOnFailureIsSet(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, true, (item) -> false);

    fakeStackTraceMonitor.assertOnlyStackTraceAndClear("QueuedItemProcessor failed 1 times");

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNotNull(item.getFailedAt());
      assertNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertTrue(item.isDropped());
      assertFalse(item.isInFlight());
    });
  }

  @Test
  public void shouldNotProcessWhenTheItemDoesNotExists() {
    AtomicBoolean called = new AtomicBoolean(false);
    processor.process(DummyQueuedItem.class, Id.of(1), false, (item) -> {
      called.set(true);
      return true;
    });

    assertFalse(called.get());
  }

  @Test
  public void shouldNotProcessWhenTheItemIsDropped(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.executeWithSessionExpression(session -> {
      DummyQueuedItem item = new DummyQueuedItem(new DateTime());
      item.setDropped(true);
      return session.save(item);
    });

    AtomicBoolean called = new AtomicBoolean(false);
    processor.process(DummyQueuedItem.class, id, false, (item) -> {
      called.set(true);
      return true;
    });

    assertFalse(called.get());
  }

  @Test
  public void shouldNotProcessWhenTheItemHasAlreadySucceeded(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.executeWithSessionExpression(session -> {
      DummyQueuedItem item = new DummyQueuedItem(new DateTime());
      item.setSucceededAt(new DateTime());
      return session.save(item);
    });

    AtomicBoolean called = new AtomicBoolean(false);
    processor.process(DummyQueuedItem.class, id, false, (item) -> {
      called.set(true);
      return true;
    });

    assertFalse(called.get());
  }

  @Test
  public void shouldNotProcessWhenTheItemIsInFlight(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.executeWithSessionExpression(session -> {
      DummyQueuedItem item = new DummyQueuedItem(new DateTime());
      item.setInFlight(true);
      return session.save(item);
    });

    AtomicBoolean called = new AtomicBoolean(false);
    processor.process(DummyQueuedItem.class, id, false, (item) -> {
      called.set(true);
      return true;
    });

    assertFalse(called.get());
  }

  @Test
  public void shouldNotProcessWhenDatabaseContentionOccurred_marksAsFailed() {
    AtomicBoolean markedAsFailed = new AtomicBoolean(false);
    processor = new QueuedItemProcessor() {
      @Override
      <T extends QueuedItem> int markAsFailed(Class<T> clazz, Id<T> id, boolean drop, String reason) {
        assertEquals(DummyQueuedItem.class, clazz);
        assertEquals(Id.of(1), id);
        assertFalse(drop);
        assertEquals("database contention", reason);
        markedAsFailed.set(true);
        return 1;
      }
    };
    Mockery mockery = new Mockery();
    processor.transacter = mockery.mock(Transacter.class);

    mockery.checking(new Expectations() {{
      oneOf(processor.transacter).executeWithSessionExpression(with(any(WithSessionExpression.class)));
      will(throwException(new StaleObjectStateException(null, null)));
    }});

    AtomicBoolean called = new AtomicBoolean(false);
    processor.process(DummyQueuedItem.class, Id.of(1), false, (item) -> {
      called.set(true);
      return true;
    });

    assertFalse(called.get());
    assertTrue(markedAsFailed.get());

    mockery.assertIsSatisfied();
  }

  @Test
  public void shouldNotProcessButRecordTheExceptionWhenAnExceptionIsThrownPollingTheItem() {
    Mockery mockery = new Mockery();
    processor.transacter = mockery.mock(Transacter.class);
    processor.stackTraceMonitor = fakeStackTraceMonitor;

    mockery.checking(new Expectations() {{
      oneOf(processor.transacter).executeWithSessionExpression(with(any(WithSessionExpression.class)));
      will(throwException(new RuntimeException("something bad happened")));
    }});

    AtomicBoolean called = new AtomicBoolean(false);
    processor.process(DummyQueuedItem.class, Id.of(1), false, (item) -> {
      called.set(true);
      return true;
    });

    assertFalse(called.get());
    fakeStackTraceMonitor.assertOnlyStackTraceAndClear("something bad happened");

    mockery.assertIsSatisfied();
  }

  @Test
  public void shouldProcessFailedItem(Transacter transacter) {
    Id<DummyQueuedItem> id = transacter.save(new DummyQueuedItem(new DateTime()));

    processor.process(DummyQueuedItem.class, id, false, (item) -> false);

    processor.process(DummyQueuedItem.class, id, false, (item) -> true);

    transacter.executeWithReadOnlySession(session -> {
      DummyQueuedItem item = session.getOrThrow(DummyQueuedItem.class, id);
      assertNotNull(item.getCreatedAt());
      assertNotNull(item.getFirstPolledAt());
      assertNotNull(item.getLastPolledAt());
      assertNotNull(item.getFirstFailedAt());
      assertNotNull(item.getLastFailedAt());
      assertNull(item.getFailedAt());
      assertNotNull(item.getSucceededAt());
      assertEquals(1, item.getFailedAttempts());
      assertFalse(item.isDropped());
      assertFalse(item.isInFlight());
    });
  }

}