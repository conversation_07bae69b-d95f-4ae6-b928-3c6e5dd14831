package com.kaching.platform.bus.impl;

import org.junit.After;
import org.junit.Test;

import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.QueryExecutorService;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;

public class FakeInboxSignalerImplTest {

  Mockeries.WFMockery mockery = Mockeries.mockery(true);
  QueryExecutorService executor = mockery.mockQueryExecutorService();

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void signal() {
    FakeInboxSignalerImpl inboxSignaler = new FakeInboxSignalerImpl();
    inboxSignaler.executor = executor;

    mockery.checking(new WExpectations() {{
      oneOf(executor).submitAndGetResult(with(instanceOf(ProcessIncomingEvent.class)));
      will(returnValue(true));
    }});

    inboxSignaler.signal(Id.of(1));
  }

}
