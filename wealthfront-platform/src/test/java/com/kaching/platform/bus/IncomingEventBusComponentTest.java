package com.kaching.platform.bus;

import static com.kaching.platform.guice.TestingApplicationOptions.OPTIONS;

import org.junit.Test;

import com.google.inject.AbstractModule;
import com.kaching.mq.rabbitmq.RabbitMQModule;
import com.kaching.platform.bus.impl.IncomingEvent;
import com.kaching.platform.bus.impl.ProcessIncomingEvent;
import com.kaching.platform.components.Component;
import com.kaching.platform.components.KawalaTester;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.zk.ZkAnnounceModule;

public class IncomingEventBusComponentTest {

  @Test
  public void shouldBeValidWhenTheNamedReadablePeriodBindingIsProvidedByTheBusModule() {
    KawalaTester.selfContained().validateComponent(IncomingEventBusComponentTest.MyService.class);
  }

  @Component(
      dependsOn = IncomingEventBusComponent.class,
      modules = {
          IncomingEventBusComponentTest.MyService.Module.class
      }
  )
  private static class MyService {

    static class Module extends AbstractModule {

      @Override
      protected void configure() {
        install(ZkAnnounceModule.newZkAnnounceModule(KachingServices.UM.class, OPTIONS));
        install(new BusModuleBuilder()
            .withProcessIncomingEventClass(IncomingEventBusComponentTest.ProcessIncomingEventImpl.class)
            .build());
        install(new RabbitMQModule());
      }
    }
  }

  private static class ProcessIncomingEventImpl extends ProcessIncomingEvent {

    private final Id<IncomingEvent> id;

    ProcessIncomingEventImpl(Id<IncomingEvent> id) {
      this.id = id;
    }

    @Override
    public Boolean process() {
      return process(id);
    }
  }

}