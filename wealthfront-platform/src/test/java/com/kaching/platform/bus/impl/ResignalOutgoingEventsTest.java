package com.kaching.platform.bus.impl;

import java.util.Collections;
import java.util.Set;

import org.jmock.Mockery;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.hibernate.Id;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;

public class ResignalOutgoingEventsTest {

  private final Mockery mockery = Mockeries.mockery();
  private final OutboxSignaler outboxSignaler = mockery.mock(OutboxSignaler.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process() {
    mockery.checking(new WExpectations() {{
      oneOf(outboxSignaler).signal(Id.of(1));
    }});
    getQuery(Collections.singleton(Id.of(1))).process();
  }

  private ResignalOutgoingEvents getQuery(Set<Id<OutgoingEvent>> outgoingEventIds) {
    ResignalOutgoingEvents query = new ResignalOutgoingEvents(outgoingEventIds);
    query.outboxSignaler = outboxSignaler;
    return query;
  }

}