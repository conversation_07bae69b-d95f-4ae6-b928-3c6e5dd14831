package com.kaching.platform.bus.impl;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.sql.SQLException;

import org.hibernate.exception.LockAcquisitionException;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.kaching.platform.bus.Event;
import com.kaching.platform.bus.Handler;
import com.kaching.platform.guice.InjectingTransacters;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.util.tests.PersistentTest;

@RunWith(PersistentTestRunner.class)
@PersistentTest(
    entities = {
        IncomingEvent.class
    },
    modules = {
        ReceiveEventTest.Module.class
    })
public class ReceiveEventTest {

  private final Mockery mockery = new Mockery();
  private final Receiver receiver = mockery.mock(Receiver.class);
  private final Transacter injectingTransacter = InjectingTransacters.injectingFakeTransacterBuilder()
      .with(Receiver.class, receiver)
      .build();

  @Test
  public void processShouldNotFailWhenConstraintViolationExceptionIsThrown(Transacter transacter) {
    ReceiveEvent query = new ReceiveEvent(new Message(EventId.random(), null, null));
    query.transacter = transacter;

    assertTrue(query.process());
    assertTrue(query.process());
  }

  @Test
  public void processShouldReturnFalseWhenLockAcquisitionExceptionIsThrown(Transacter transacter) {
    ReceiveEvent query = new ReceiveEvent(new Message(EventId.random(), null, null));
    query.transacter = injectingTransacter;

    mockery.checking(new Expectations() {{
      oneOf(receiver).receive(with(any(Message.class)));
      will(throwException(new LockAcquisitionException("lock acquisition error", new SQLException())));
    }});

    assertFalse(query.process());

    mockery.assertIsSatisfied();
  }

  public static class Module extends AbstractModule {

    @Override
    protected void configure() {
    }

    @Provides
    public Receiver receiver(final DbSession session, final DateTime now) {
      return new Receiver() {

        @Override
        public void receive(Message message) {
          session.save(new IncomingEvent(HandlerA.class, message, now));
        }
      };
    }
  }

  private static class Event1 implements Event {}

  private static class HandlerA implements Handler<Event1> {

    @Override
    public void handle(Event1 event) {
    }
  }

}