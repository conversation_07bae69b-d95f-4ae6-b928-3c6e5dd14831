package com.kaching.platform.hibernate.mappinglists;

import static org.junit.Assert.assertEquals;

import java.util.List;

import org.dbunit.database.IDatabaseConnection;
import org.dbunit.dataset.IDataSet;
import org.dbunit.operation.DatabaseOperation;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Order;
import org.junit.After;
import org.junit.Ignore;
import org.junit.Test;

import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.name.Named;
import com.kaching.platform.guice.InMemoryHibernateModule;

public class MappingListsTest {

  @Inject @Named("data.xml") IDataSet initialState;
  @Inject SessionFactory sessionFactory;

  @SuppressWarnings("unchecked")
  @Test
  @Ignore
  public void testRetrieveInOrder() throws Exception {
    Injector injector = Guice.createInjector(new InMemoryHibernateModule());
    IDatabaseConnection connection =
        injector.getInstance(IDatabaseConnection.class);
    injector.injectMembers(this);
    DatabaseOperation.CLEAN_INSERT.execute(connection, initialState);
    Session session = injector.getInstance(Session.class);
    List<Email> emails = session.createCriteria(Email.class).addOrder(Order.asc("user")).list();
    session.close();

    assertEquals(4, emails.size());
    assertEquals("<EMAIL>", emails.get(0).email);
    assertEquals("<EMAIL>", emails.get(1).email);
    assertEquals("<EMAIL>", emails.get(2).email);
    assertEquals("<EMAIL>", emails.get(3).email);
    //    assertEquals("<EMAIL>", emails.get(0).email);
    //    assertEquals("<EMAIL>", emails.get(1).email);
    //    assertEquals("<EMAIL>", emails.get(2).email);
    //    assertEquals("<EMAIL>", emails.get(3).email);
  }

  @After
  public void after() {
    sessionFactory.close();
  }
}
