package com.kaching.platform.hibernate.queue;

import static org.junit.Assert.assertEquals;

import java.sql.SQLException;

import org.dbunit.DatabaseUnitException;
import org.joda.time.DateTime;
import org.junit.BeforeClass;
import org.junit.Test;

import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.util.functional.Pointer;
import com.kaching.util.tests.PersistentTestBase;
import com.wealthfront.util.time.DateTimeZones;

public class PersistentQueuedEventRepositoryTest extends PersistentTestBase {

  @BeforeClass
  public static void beforeClass() throws DatabaseUnitException, SQLException {
    configure(new Class<?>[]{MockEntity.class});
  }

  @Test
  public void getOneUnpolledEvent() {
    final Pointer<Id<MockEntity>> idPtr1 = Pointer.pointer();
    final Pointer<Id<MockEntity>> idPtr2 = Pointer.pointer();
    final Pointer<Id<MockEntity>> idPtr3 = Pointer.pointer();
    final DateTime time = new DateTime(2016, 3, 15, 10, 30, 0, DateTimeZones.ET);
    transacter.execute(new WithSession() {
      @Override
      public void run(DbSession session) {
        PersistentQueuedEventRepository<MockEntity> repo =
            new PersistentQueuedEventRepository<>(MockEntity.class, session, null);
        MockEntity m1 = new MockEntity();
        MockEntity m2 = new MockEntity();
        MockEntity m3 = new MockEntity();
        m1.setCreationTime(time);
        m2.setCreationTime(time);
        m3.setCreationTime(time);
        m2.setPolledTime(time.plusSeconds(1));
        m3.setPolledTime(time.plusSeconds(2));
        m3.setSentTime(time.plusSeconds(3));
        idPtr1.set(repo.persist(m1));
        idPtr2.set(repo.persist(m2));
        idPtr3.set(repo.persist(m3));
      }
    });

    transacter.execute(new WithSession() {
      @Override
      public void run(DbSession session) {
        PersistentQueuedEventRepository<MockEntity> repo =
            new PersistentQueuedEventRepository<>(MockEntity.class, session, null);

        MockEntity m = repo.getOneUnpolledEvent();
        assertEquals(m.getId(), idPtr1.get());
      }
    });
  }

  @Test
  public void getOneUnsentEvent() {
    final Pointer<Id<MockEntity>> idPtr1 = Pointer.pointer();
    final Pointer<Id<MockEntity>> idPtr2 = Pointer.pointer();
    final Pointer<Id<MockEntity>> idPtr3 = Pointer.pointer();
    final DateTime time = new DateTime(2016, 3, 15, 10, 30, 0, DateTimeZones.ET);
    transacter.execute(new WithSession() {
      @Override
      public void run(DbSession session) {
        PersistentQueuedEventRepository<MockEntity> repo =
            new PersistentQueuedEventRepository<>(MockEntity.class, session, null);
        MockEntity m1 = new MockEntity();
        MockEntity m2 = new MockEntity();
        MockEntity m3 = new MockEntity();
        m1.setCreationTime(time);
        m2.setCreationTime(time);
        m3.setCreationTime(time);
        m2.setPolledTime(time.plusSeconds(1));
        m3.setPolledTime(time.plusSeconds(2));
        idPtr1.set(repo.persist(m1));
        idPtr2.set(repo.persist(m2));
        idPtr3.set(repo.persist(m3));
      }
    });

    transacter.execute(new WithSession() {
      @Override
      public void run(DbSession session) {
        PersistentQueuedEventRepository<MockEntity> repo =
            new PersistentQueuedEventRepository<>(MockEntity.class, session, null);

        MockEntity m = repo.getOneUnsentEvent();
        assertEquals(m.getId(), idPtr2.get());
        m.setPolledTime(time.plusSeconds(3));
        session.update(m);
      }
    });

    transacter.execute(new WithSession() {
      @Override
      public void run(DbSession session) {
        PersistentQueuedEventRepository<MockEntity> repo =
            new PersistentQueuedEventRepository<>(MockEntity.class, session, null);

        MockEntity m = repo.getOneUnsentEvent();
        assertEquals(m.getId(), idPtr3.get());
      }
    });
  }

}
