package com.kaching.platform.hibernate;

import java.sql.SQLException;
import java.util.function.Predicate;

import org.hibernate.exception.LockAcquisitionException;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.kaching.platform.common.logging.Log;
import com.kaching.util.functional.Pointer;

public class ContentionSimulatingRetryingTransacter extends RetryingTransacter {
  
  private static final Log log = Log.getLog(ContentionSimulatingRetryingTransacter.class);

  private final Transacter transacter;

  @Inject
  public ContentionSimulatingRetryingTransacter(Transacter transacter) {
    super(null);
    this.transacter = transacter;
  }

  @Override
  public void execute(WithSession callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));
    runWithRetries(1, defaultRetryingExceptions(), () -> run(callback, false));
  }

  @Override
  public void execute(WithReadOnlySession callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));
    runWithRetries(1, defaultRetryingExceptions(), () -> run(callback, false));
  }

  @Override
  public <T> T execute(WithSessionExpression<T> expression) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(expression, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(1, defaultRetryingExceptions(), () -> ptr.set(run(expression, false)));
    return ptr.get();
  }

  @Override
  public <T> T execute(WithReadOnlySessionExpression<T> expression) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(expression, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(1, defaultRetryingExceptions(), () -> ptr.set(run(expression, false)));
    return ptr.get();
  }

  @Override
  public void executeWithSession(WithSession callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));
    runWithRetries(1, defaultRetryingExceptions(), () -> run(callback, false));
  }

  @Override
  public void executeWithReadOnlySession(WithReadOnlySession callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));
    runWithRetries(1, defaultRetryingExceptions(), () -> run(callback, false));
  }

  @Override
  public <T> T executeWithSessionExpression(WithSessionExpression<T> expression) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(expression, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(1, defaultRetryingExceptions(), () -> ptr.set(run(expression, false)));
    return ptr.get();
  }

  @Override
  public <T> T executeWithReadOnlySessionExpression(WithReadOnlySessionExpression<T> expression) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(expression, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(1, defaultRetryingExceptions(), () -> ptr.set(run(expression, false)));
    return ptr.get();
  }

  @Override
  public void execute(int retries, WithSession callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));
    runWithRetries(retries - 1, defaultRetryingExceptions(), () -> run(callback, false));
  }

  @Override
  public void execute(int retries, Predicate<Exception> retryableException, WithSession callback) {
    runAndExpectException(retryableException, () -> run(callback, true));
    runWithRetries(retries - 1, retryableException, () -> run(callback, false));
  }

  @Override
  public void execute(int retries, WithReadOnlySession callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));
    runWithRetries(retries - 1, defaultRetryingExceptions(), () -> run(callback, false));
  }

  @Override
  public void execute(int retries, Predicate<Exception> retryableException, WithReadOnlySession callback) {
    runAndExpectException(retryableException, () -> run(callback, true));
    runWithRetries(retries - 1, retryableException, () -> run(callback, false));
  }

  @Override
  public <T> T execute(int retries, WithSessionExpression<T> callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(retries - 1, defaultRetryingExceptions(), () -> ptr.set(run(callback, false)));
    return ptr.get();
  }

  @Override
  public <T> T execute(int retries, Predicate<Exception> retryableException, WithSessionExpression<T> callback) {
    runAndExpectException(retryableException, () -> run(callback, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(retries - 1, retryableException, () -> ptr.set(run(callback, false)));
    return ptr.get();
  }

  @Override
  public <T> T execute(int retries, WithReadOnlySessionExpression<T> callback) {
    runAndExpectException(defaultRetryingExceptions(), () -> run(callback, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(retries - 1, defaultRetryingExceptions(), () -> ptr.set(run(callback, false)));
    return ptr.get();
  }

  @Override
  public <T> T execute(int retries, Predicate<Exception> retryableException, WithReadOnlySessionExpression<T> callback) {
    runAndExpectException(retryableException, () -> run(callback, true));

    Pointer<T> ptr = Pointer.pointer();
    runWithRetries(retries - 1, retryableException, () -> ptr.set(run(callback, false)));
    return ptr.get();
  }

  private void runAndExpectException(Predicate<Exception> retryableException, Runnable runnable) {
    try {
      runnable.run();
    } catch (Exception ex) {
      if (retryableException.test(ex)) {
        log.debug(ex);
        return;
      } else {
        throw ex;
      }
    }
    throw new IllegalStateException("HighContentionRetryingTransacter expected a LockAcquisitionException but none was thrown.");
  }

  private void runWithRetries(int retries, Predicate<Exception> retryableException, Runnable runnable) {
    int count = 0;
    while (count < retries) {
      try {
        runnable.run();
        return;
      } catch (Exception ex) {
        boolean expected = retryableException.test(ex);
        if (!expected) {
          throw ex;
        }
      }
      count++;
    }

    runnable.run();
  }

  private void run(WithSession withSession, boolean shouldRollback) {
    transacter.execute(new WithSession() {

      @Inject Injector injector;

      @Override
      public void run(DbSession session) {
        injector.injectMembers(withSession);
        withSession.run(session);
        if (shouldRollback) {
          throwForContention();
        }
      }

    });
  }

  private <T> T run(WithSessionExpression<T> withSession, boolean shouldRollback) {
    return transacter.execute(new WithSessionExpression<T>() {

      @Inject Injector injector;

      @Override
      public T run(DbSession session) {
        injector.injectMembers(withSession);
        T result = withSession.run(session);
        if (shouldRollback) {
          throwForContention();
        }
        return result;
      }

    });
  }

  private void run(WithReadOnlySession withSession, boolean shouldRollback) {
    transacter.execute(new WithReadOnlySession() {

      @Inject Injector injector;

      @Override
      public void run(DbSession session) {
        injector.injectMembers(withSession);
        withSession.run(session);
        if (shouldRollback) {
          throwForContention();
        }
      }

    });
  }

  private <T> T run(WithReadOnlySessionExpression<T> withSession, boolean shouldRollback) {
    return transacter.execute(new WithReadOnlySessionExpression<T>() {

      @Inject Injector injector;

      @Override
      public T run(DbSession session) {
        injector.injectMembers(withSession);
        T result = withSession.run(session);
        if (shouldRollback) {
          throwForContention();
        }
        return result;
      }

    });
  }

  private static void throwForContention() {
    throw new LockAcquisitionException("The HighContentionRetryingTransacter always rolls back the first transaction to make sure your unit " +
        "tests are idempotent", new SQLException());
  }

}
