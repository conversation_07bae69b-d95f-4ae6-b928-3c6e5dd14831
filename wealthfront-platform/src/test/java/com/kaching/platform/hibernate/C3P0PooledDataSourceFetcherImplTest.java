package com.kaching.platform.hibernate;

import static com.wealthfront.test.Assert.assertOptionEmpty;
import static java.util.Collections.emptyList;
import static org.junit.Assert.assertEquals;

import org.hibernate.SessionFactory;
import org.junit.After;
import org.junit.Test;

import com.google.inject.ConfigurationException;
import com.google.inject.Injector;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.mchange.v2.c3p0.PooledDataSource;
import com.mchange.v2.c3p0.management.C3P0RegistryManager;

public class C3P0PooledDataSourceFetcherImplTest {

  private final WFMockery mockery = Mockeries.mockery(true);
  private final C3P0RegistryManager c3P0RegistryManagerMock = mockery.mock(C3P0RegistryManager.class);
  private final Injector injector = mockery.mock(Injector.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void get_returnsEmptyOption() {
    mockery.checking(new WExpectations() {{
      oneOf(injector).getInstance(SessionFactory.class);

      oneOf(c3P0RegistryManagerMock).getAllPooledDataSourcesCount();
      will(returnValue(0));
    }});
    C3P0PooledDataSourceFetcherImpl fetcher = getFetcher();
    assertOptionEmpty(fetcher.get());
  }

  @Test
  public void get_returnsNonEmptyOption() {
    PooledDataSource pooledDataSourceStub = new StubPooledDataSource("test-id");
    mockery.checking(new WExpectations() {{
      oneOf(injector).getInstance(SessionFactory.class);

      oneOf(c3P0RegistryManagerMock).getAllPooledDataSourcesCount();
      will(returnValue(1));
      oneOf(c3P0RegistryManagerMock).getAllPooledDataSources();
      will(returnSet(pooledDataSourceStub));
    }});
    C3P0PooledDataSourceFetcherImpl fetcher = getFetcher();
    assertEquals("test-id", fetcher.get().getOrThrow().getIdentityToken());
  }

  @Test
  public void get_noHibernateService() {
    mockery.checking(new WExpectations() {{
      oneOf(injector).getInstance(SessionFactory.class);
      will(throwException(new ConfigurationException(emptyList())));

      oneOf(c3P0RegistryManagerMock).getAllPooledDataSourcesCount();
      will(returnValue(0));
    }});
    C3P0PooledDataSourceFetcherImpl fetcher = getFetcher();
    assertOptionEmpty(fetcher.get());
  }

  private C3P0PooledDataSourceFetcherImpl getFetcher() {
    C3P0PooledDataSourceFetcherImpl fetcher = new C3P0PooledDataSourceFetcherImpl();
    fetcher.c3P0RegistryManager = c3P0RegistryManagerMock;
    fetcher.injector = injector;
    return fetcher;
  }

}