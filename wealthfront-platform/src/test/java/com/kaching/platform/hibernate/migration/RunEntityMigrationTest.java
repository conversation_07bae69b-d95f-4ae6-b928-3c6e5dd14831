package com.kaching.platform.hibernate.migration;

import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.List;

import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.google.inject.Injector;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.kaching.platform.testing.PersistentTestRunner;
import com.kaching.platform.toggle.Toggle;
import com.kaching.util.tests.PersistentTest;

@RunWith(PersistentTestRunner.class)
@PersistentTest(entities = Toggle.class)
public class RunEntityMigrationTest {

  private static final DateTime now = new DateTime(2021, 12, 4, 12, 0, ET);

  @Test
  public void testBadClassName() {
    assertThrows(
        InvalidArgumentException.class,
        "Could not find class thisClassDoesNotExist",
        new RunEntityMigration("thisClassDoesNotExist")::process
    );
  }

  @Test(expected = InvalidArgumentException.class)
  public void testNotMigrationSubclass(Injector injector) {
    RunEntityMigration query = new RunEntityMigration(NotAMigrationClass.class.getName());
    injector.injectMembers(query);
    query.process();
  }

  @Test
  public void testRunMigration(Transacter transacter, Injector injector) {
    Id<Toggle> test1Id = transacter.save(new Toggle("test1", false, now));
    Id<Toggle> test2Id = transacter.save(new Toggle("test2", true, now));
    Id<Toggle> somethingId = transacter.save(new Toggle("something", false, now));

    assertEquals(2, runEntityMigration(EnableTToggles.class, injector));

    assertTrue(getToggle(test1Id, transacter).getState());
    assertTrue(getToggle(test2Id, transacter).getState());
    assertFalse(getToggle(somethingId, transacter).getState());
  }

  private Toggle getToggle(Id<Toggle> toggleId, Transacter transacter) {
    return transacter.executeWithReadOnlySessionExpression(session -> session.getOrThrow(Toggle.class, toggleId));
  }

  public static <T extends EntityMigration<?>>
  int runEntityMigration(Class<T> clazz, Injector injector) {
    RunEntityMigration q = new RunEntityMigration(clazz.getName());
    injector.injectMembers(q);
    return q.process();
  }

  static class EnableTToggles implements EntityMigration<Toggle> {

    @Override
    public Class<Toggle> getEntityClass() {
      return Toggle.class;
    }

    @Override
    public List<Id<Toggle>> getIdsToMigrate(DbSession session) {
      return session.createCriteria(getEntityClass())
          .add(Restrictions.like("name", "t%"))
          .<Id<Toggle>>setProjection(Projections.id())
          .list();
    }

    @Override
    public void migrateEntity(DbSession session, Toggle entity) {
      if (!entity.getState()) {
        entity.setState(true, now);
      }
    }

  }

  public static class NotAMigrationClass {}

}