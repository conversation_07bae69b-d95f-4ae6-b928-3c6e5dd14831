package com.kaching.platform.hibernate.queue.impl;

import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import org.joda.time.LocalDate;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.UncheckedExecutionException;
import com.google.inject.Inject;
import com.kaching.platform.common.Thunk;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.hibernate.ContentionSimulatingRetryingTransacter;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.kaching.platform.hibernate.queue.BatchQueueServiceRoles;
import com.kaching.platform.hibernate.queue.ConstantBatchSizer;
import com.kaching.platform.hibernate.queue.impl.BatchQueueBindingsTestBase.SomeBatchConfig;
import com.kaching.platform.queryengine.progress.FakeProgressMonitor;
import com.kaching.user.UserId;
import com.kaching.util.functional.Pointer;
import com.twolattes.json.Json;

public class OutOfSessionBatchEnqueuerImplTest extends BatchQueueTestBase {
  
  private final Pointer<Runnable> onParallelPreInsert = Pointer.pointer(() -> {});
  private final Pointer<Runnable> onParallelPostInsert = Pointer.pointer(() -> {});
  private final Pointer<Runnable> onSimplePreInsert = Pointer.pointer(() -> {});
  
  @Test
  public void enqueue_noKeys_claimUnlimited_parallelAlgorithm_success_batchesArePolledAndEnqueued() {
    batchSizer.set(new ConstantBatchSizer(2));
    HybridBatchQueueRunnerImpl<SomeBatchConfig, UserId> runner = getUserIdQueueRunner();
    runner.state.set(HybridBatchQueueRunnerImpl.State.RUNNING);
    assertEquals(BatchQueueServiceRoles.OnEnqueueBehavior.CLAIM_UNLIMITED, runner.getOnEnqueue());
    
    OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId> enqueuer = getUserIdEnqueuer(2, 4);
    enqueuer.MAX_INSERT_TRANSACTION_SIZE = 4;
    SomeBatchConfig batchData = new SomeBatchConfig(new LocalDate(2012, 1, 2));

    AtomicInteger numDbTransactions = new AtomicInteger(0);
    onParallelPostInsert.set(numDbTransactions::incrementAndGet);
    enqueuer.enqueue(batchData, ImmutableList.of(new UserId(11), new UserId(22), new UserId(33), new UserId(44), new UserId(55)));
    assertEquals(4, numDbTransactions.get()); // x2 for contention simulation
    
    transacter().executeWithReadOnlySession(new WithReadOnlySession() {
      @Inject BatchQueueItemRepository itemRepository;
      @Override
      public void run(DbSession session) {
        List<BatchQueueBatch> batches = session.createCriteria(BatchQueueBatch.class).list();
        assertEquals(3, batches.size());

        BatchQueueBatch batch1 = batches.get(0);
        assertEquals(2, batch1.getSize());
        assertEquals(Json.number(11), itemRepository.getOrThrow(batch1.getItemIdFrom()).getPayload());
        assertEquals(Json.number(22), itemRepository.getOrThrow(batch1.getItemIdTo()).getPayload());
        assertOptionEquals(new ServiceId("bank1"), batch1.getPolledBy());
        assertOptionEquals(now.get(), batch1.getSendableSince());
        
        BatchQueueBatch batch2 = batches.get(1);
        assertEquals(2, batch2.getSize());
        assertEquals(Json.number(33), itemRepository.getOrThrow(batch2.getItemIdFrom()).getPayload());
        assertEquals(Json.number(44), itemRepository.getOrThrow(batch2.getItemIdTo()).getPayload());
        assertOptionEquals(new ServiceId("bank1"), batch2.getPolledBy());
        assertOptionEquals(now.get(), batch2.getSendableSince());
        
        BatchQueueBatch batch3 = batches.get(2);
        assertEquals(1, batch3.getSize());
        assertEquals(Json.number(55), itemRepository.getOrThrow(batch3.getItemIdFrom()).getPayload());
        assertEquals(Json.number(55), itemRepository.getOrThrow(batch3.getItemIdTo()).getPayload());
        assertOptionEquals(new ServiceId("bank1"), batch3.getPolledBy());
        assertOptionEquals(now.get(), batch3.getSendableSince());
        
        assertEquals(ImmutableMap.of(
            batch1.getId(), 2,
            batch2.getId(), 2,
            batch3.getId(), 1
        ), runner.enqueuedIds);
      }
    });
  }

  @Test
  public void enqueue_noKeys_claimUnlimited_smallList_simpleAlgorithm_success_batchesArePolledAndEnqueued() {
    batchSizer.set(new ConstantBatchSizer(2));
    HybridBatchQueueRunnerImpl<SomeBatchConfig, UserId> runner = getUserIdQueueRunner();
    runner.state.set(HybridBatchQueueRunnerImpl.State.RUNNING);
    assertEquals(BatchQueueServiceRoles.OnEnqueueBehavior.CLAIM_UNLIMITED, runner.getOnEnqueue());

    OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId> enqueuer = getUserIdEnqueuer(999, 999);
    enqueuer.MAX_INSERT_TRANSACTION_SIZE = 6;
    SomeBatchConfig batchData = new SomeBatchConfig(new LocalDate(2012, 1, 2));

    AtomicInteger numDbTransactions = new AtomicInteger(0);
    onSimplePreInsert.set(numDbTransactions::incrementAndGet);
    enqueuer.enqueue(batchData, ImmutableList.of(new UserId(11), new UserId(22), new UserId(33)));
    assertEquals(2, numDbTransactions.get()); // x2 for contention simulation

    transacter().executeWithReadOnlySession(new WithReadOnlySession() {
      @Inject BatchQueueItemRepository itemRepository;
      @Override
      public void run(DbSession session) {
        List<BatchQueueBatch> batches = session.createCriteria(BatchQueueBatch.class).list();
        assertEquals(2, batches.size());

        BatchQueueBatch batch1 = batches.get(0);
        assertEquals(2, batch1.getSize());
        assertEquals(Json.number(11), itemRepository.getOrThrow(batch1.getItemIdFrom()).getPayload());
        assertEquals(Json.number(22), itemRepository.getOrThrow(batch1.getItemIdTo()).getPayload());
        assertOptionEquals(new ServiceId("bank1"), batch1.getPolledBy());
        assertOptionEquals(now.get(), batch1.getSendableSince());

        BatchQueueBatch batch2 = batches.get(1);
        assertEquals(1, batch2.getSize());
        assertEquals(Json.number(33), itemRepository.getOrThrow(batch2.getItemIdFrom()).getPayload());
        assertEquals(Json.number(33), itemRepository.getOrThrow(batch2.getItemIdTo()).getPayload());
        assertOptionEquals(new ServiceId("bank1"), batch2.getPolledBy());
        assertOptionEquals(now.get(), batch2.getSendableSince());

        assertEquals(ImmutableMap.of(
            batch1.getId(), 2,
            batch2.getId(), 1
        ), runner.enqueuedIds);
      }
    });
  }
  
  @Test
  public void enqueue_noKeys_claimNone_parallelAlgorithm_success_batchesAreSavedButNotPolled() {
    batchSizer.set(new ConstantBatchSizer(2));
    HybridBatchQueueRunnerImpl<EntityQueueConfig, JsonEntity> runner = getEntityQueueRunner();
    runner.state.set(HybridBatchQueueRunnerImpl.State.RUNNING);
    assertEquals(BatchQueueServiceRoles.OnEnqueueBehavior.CLAIM_NONE, runner.getOnEnqueue());

    OutOfSessionBatchEnqueuerImpl<EntityQueue, EntityQueueConfig, JsonEntity> enqueuer = getEntityEnqueuer(2, 2);
    enqueuer.MAX_INSERT_TRANSACTION_SIZE = 2;
    EntityQueueConfig batchData = new EntityQueueConfig();

    AtomicInteger numDbTransactions = new AtomicInteger(0);
    onParallelPostInsert.set(numDbTransactions::incrementAndGet);
    enqueuer.enqueue(batchData, ImmutableList.of(
        new JsonEntity(new UserId(11), new LocalDate(2012, 1, 2)),
        new JsonEntity(new UserId(22), new LocalDate(2012, 1, 3)),
        new JsonEntity(new UserId(33), new LocalDate(2012, 1, 4))
    ));
    assertEquals(4, numDbTransactions.get());

    transacter().executeWithReadOnlySession(new WithReadOnlySession() {
      @Inject BatchQueueItemRepository itemRepository;
      @Override
      public void run(DbSession session) {
        List<BatchQueueBatch> batches = session.createCriteria(BatchQueueBatch.class).list();
        assertEquals(2, batches.size());

        BatchQueueBatch batch1 = batches.get(0);
        assertEquals(2, batch1.getSize());
        assertEquals(Json.object(
            "userId", 11,
            "date", "20120102"
        ), itemRepository.getOrThrow(batch1.getItemIdFrom()).getPayload());
        assertEquals(Json.object(
            "userId", 22,
            "date", "20120103"
        ), itemRepository.getOrThrow(batch1.getItemIdTo()).getPayload());
        assertOptionEmpty(batch1.getPolledBy());
        assertOptionEmpty(batch1.getIgnoredAt());
        assertOptionEquals(now.get(), batch1.getSendableSince());
        
        BatchQueueBatch batch2 = batches.get(1);
        assertEquals(1, batch2.getSize());
        assertEquals(Json.object(
            "userId", 33,
            "date", "20120104"
        ), itemRepository.getOrThrow(batch2.getItemIdFrom()).getPayload());
        assertEquals(Json.object(
            "userId", 33,
            "date", "20120104"
        ), itemRepository.getOrThrow(batch2.getItemIdTo()).getPayload());
        assertOptionEmpty(batch2.getPolledBy());
        assertOptionEmpty(batch2.getIgnoredAt());
        assertOptionEquals(now.get(), batch2.getSendableSince());
        
        assertEquals(ImmutableMap.of(), runner.enqueuedIds);
      }
    });
  }

  @Test
  public void enqueue_noKeys_claimUnlimited_parallelAlgorithm_interrupted_batchesArePolledButIgnored() {
    batchSizer.set(new ConstantBatchSizer(2));
    HybridBatchQueueRunnerImpl<SomeBatchConfig, UserId> runner = getUserIdQueueRunner();
    runner.state.set(HybridBatchQueueRunnerImpl.State.RUNNING);
    assertEquals(BatchQueueServiceRoles.OnEnqueueBehavior.CLAIM_UNLIMITED, runner.getOnEnqueue());

    OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId> enqueuer = getUserIdEnqueuer(2, 4);
    enqueuer.MAX_INSERT_TRANSACTION_SIZE = 4;
    SomeBatchConfig batchData = new SomeBatchConfig(new LocalDate(2012, 1, 2));

    AtomicInteger numDbTransactions = new AtomicInteger(0);
    onParallelPostInsert.set(() -> {
      if (numDbTransactions.incrementAndGet() == 3) {
        throw new RuntimeException("Service deploying");
      }
    });
    assertThrows(UncheckedExecutionException.class, "java.util.concurrent.ExecutionException: java.lang.RuntimeException: Service deploying", () -> 
        enqueuer.enqueue(batchData, ImmutableList.of(new UserId(11), new UserId(22), new UserId(33), new UserId(44), new UserId(55))));
    assertEquals(3, numDbTransactions.get());

    transacter().executeWithReadOnlySession(new WithReadOnlySession() {
      @Inject BatchQueueItemRepository itemRepository;
      @Override
      public void run(DbSession session) {
        List<BatchQueueBatch> batches = session.createCriteria(BatchQueueBatch.class).list();
        assertEquals(2, batches.size());

        BatchQueueBatch batch1 = batches.get(0);
        assertEquals(2, batch1.getSize());
        assertEquals(Json.number(11), itemRepository.getOrThrow(batch1.getItemIdFrom()).getPayload());
        assertEquals(Json.number(22), itemRepository.getOrThrow(batch1.getItemIdTo()).getPayload());
        assertOptionEquals(new ServiceId("bank1"), batch1.getPolledBy());
        assertOptionEmpty(batch1.getSendableSince());
        assertOptionEquals(now.get(), batch1.getIgnoredAt());
        assertOptionEquals("TEMPORARILY_IGNORE", batch1.getMessage());
        
        BatchQueueBatch batch2 = batches.get(1);
        assertEquals(2, batch2.getSize());
        assertEquals(Json.number(33), itemRepository.getOrThrow(batch2.getItemIdFrom()).getPayload());
        assertEquals(Json.number(44), itemRepository.getOrThrow(batch2.getItemIdTo()).getPayload());
        assertOptionEquals(new ServiceId("bank1"), batch2.getPolledBy());
        assertOptionEmpty(batch2.getSendableSince());
        assertOptionEquals(now.get(), batch2.getIgnoredAt());

        assertEquals(ImmutableMap.of(), runner.enqueuedIds);
      }
    });
  }
  
  @Test
  public void calculateBatchSizes_usesSquareRootToCapMaxBatchSizeIncludingAtomicUnignore() {
    OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId> enqueuer =
        new OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId>(getUserIdQueue(), getUserIdQueueRunner());

    assertEquals(512, enqueuer.calculatePersistedBatchSize(1, 512));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(1, 512));
    
    assertEquals(512, enqueuer.calculatePersistedBatchSize(512, 512));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(512, 512));
    
    assertEquals(512, enqueuer.calculatePersistedBatchSize(512 * 4, 512));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(512 * 4, 512));
    
    assertEquals(512, enqueuer.calculatePersistedBatchSize(512 * 4 + 1, 512));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(512 * 4 + 1, 512));

    assertEquals(1024, enqueuer.calculatePersistedBatchSize(1024 * 1024, 512));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(1024 * 1024, 512));

    assertEquals(1024, enqueuer.calculatePersistedBatchSize(1024 * 1024 + 100, 512));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(1024 * 1024 + 100, 512));

    assertEquals(2048, enqueuer.calculatePersistedBatchSize(2048 * 2048, 512));
    assertEquals(2048, enqueuer.calculateInsertTransactionSize(2048 * 2048, 512));

    // hard cap
    assertEquals(4096, enqueuer.calculatePersistedBatchSize(8192 * 8192, 512));
    assertEquals(4096, enqueuer.calculateInsertTransactionSize(8192 * 8192, 512));
  }
  
  @Test
  public void calculateBatchSizes_adaptsToCurrentRunnerBatchSize() {
    OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId> enqueuer =
        new OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId>(getUserIdQueue(), getUserIdQueueRunner());

    assertEquals(512, enqueuer.calculatePersistedBatchSize(100, 512));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(100, 512));

    assertEquals(256, enqueuer.calculatePersistedBatchSize(100, 256));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(100, 256));

    assertEquals(1, enqueuer.calculatePersistedBatchSize(1, 0));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(1, 0));
    
    assertEquals(1, enqueuer.calculatePersistedBatchSize(1, 1));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(1, 1));

    assertEquals(2, enqueuer.calculatePersistedBatchSize(1, 2));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(1, 2));

    assertEquals(16, enqueuer.calculatePersistedBatchSize(1, 16));
    assertEquals(1024, enqueuer.calculateInsertTransactionSize(1, 16));

    assertEquals(2048, enqueuer.calculatePersistedBatchSize(1, 2048));
    assertEquals(2048, enqueuer.calculateInsertTransactionSize(1, 2048));

    assertEquals(2048, enqueuer.calculatePersistedBatchSize(1, 2048 + 1));
    assertEquals(2048, enqueuer.calculateInsertTransactionSize(1, 2048 + 1));

    assertEquals(2048, enqueuer.calculatePersistedBatchSize(1, 4096 - 1));
    assertEquals(2048, enqueuer.calculateInsertTransactionSize(1, 4096 - 1));

    assertEquals(4096, enqueuer.calculatePersistedBatchSize(1, 4096));
    assertEquals(4096, enqueuer.calculateInsertTransactionSize(1, 4096));

    assertEquals(4096, enqueuer.calculatePersistedBatchSize(1, 8192));
    assertEquals(4096, enqueuer.calculateInsertTransactionSize(1, 8192));
  }

  private OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId> getUserIdEnqueuer(int batchQueueBatchSize, int insertSize) {
    OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId> enqueuer = 
        new OutOfSessionBatchEnqueuerImpl<UserIdQueue, SomeBatchConfig, UserId>(getUserIdQueue(), getUserIdQueueRunner()) {
          @Override
          int calculatePersistedBatchSize(int numItems, int currentRunnerBatchSize) {
            return batchQueueBatchSize;
          }

          @Override
          int calculateInsertTransactionSize(int numItems, int currentRunnerBatchSize) {
            return insertSize;
          }

          @Override
          void onParallelPreInsert() {
            onParallelPreInsert.get().run();
          }

          @Override
          void onParallelPostInsert() {
            onParallelPostInsert.get().run();
          }
          
          @Override
          void onSimplePreInsert() {
            onSimplePreInsert.get().run();
          }
        };
    prepareEnqueuer(enqueuer);
    return enqueuer;
  }
  
  private OutOfSessionBatchEnqueuerImpl<EntityQueue, EntityQueueConfig, JsonEntity> getEntityEnqueuer(int batchQueueBatchSize, int insertSize) {
    OutOfSessionBatchEnqueuerImpl<EntityQueue, EntityQueueConfig, JsonEntity> enqueuer = 
        new OutOfSessionBatchEnqueuerImpl<EntityQueue, EntityQueueConfig, JsonEntity>((EntityQueue) getEntityQueueRunner().getQueue(), getEntityQueueRunner()) {
          @Override
          int calculatePersistedBatchSize(int numItems, int currentRunnerBatchSize) {
            return batchQueueBatchSize;
          }

          @Override
          int calculateInsertTransactionSize(int numItems, int currentRunnerBatchSize) {
            return insertSize;
          }

          @Override
          void onParallelPreInsert() {
            onParallelPreInsert.get().run();
          }

          @Override
          void onParallelPostInsert() {
            onParallelPostInsert.get().run();
          }

          @Override
          void onSimplePreInsert() {
            onSimplePreInsert.get().run();
          }
        };
    prepareEnqueuer(enqueuer);
    return enqueuer;
  }

  private void prepareEnqueuer(OutOfSessionBatchEnqueuerImpl<?, ?, ?> enqueuer) {
    enqueuer.numThreads = 1;
    enqueuer.executor = Thunk.thunk(Executors::newSingleThreadExecutor);
    enqueuer.transacter = new ContentionSimulatingRetryingTransacter(injector().getInstance(Transacter.class));
    enqueuer.clock = now::get;
    enqueuer.progressMonitor = new FakeProgressMonitor();
  }

}