package com.kaching.platform.hibernate.search;

import static com.wealthfront.test.Assert.assertOptionEquals;

import org.junit.After;
import org.junit.Test;

import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.id.IdExternalizerFetcher;

public class GetAccountIdForEntityIdTest {

  private final Mockeries.WFMockery mockery = Mockeries.mockery(true);
  private final EntityIdToGeneralIdMapper mapper = mockery.mock(EntityIdToGeneralIdMapper.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process() {
    GetAccountIdForEntityId query = getQuery("test", "1");
    mockery.checking(new WExpectations() {{
      oneOf(mapper).searchForId("test", query.idExternalizerFetcher, "1");
      will(returnOption(1L));
    }});
    assertOptionEquals(1L, query.process());
  }

  private GetAccountIdForEntityId getQuery(String entitySimpleClassName, String id) {
    GetAccountIdForEntityId query = new GetAccountIdForEntityId(entitySimpleClassName, id);
    query.entityAccountIdMapper = mapper;
    query.idExternalizerFetcher = new IdExternalizerFetcher();
    return query;
  }

}