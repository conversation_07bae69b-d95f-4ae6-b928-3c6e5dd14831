package com.kaching.platform.hibernate.queue;

import org.joda.time.Duration;

import com.kaching.platform.common.Option;
import com.twolattes.json.Json;

public class NoOpBatchQueueThrottler implements BatchQueueThrottler {

  @Override
  public Duration getPreProcessDelay(Json.Value batch) {
    return Duration.ZERO;
  }

  @Override
  public void observeSuccessfulBatch(Json.Value batch) {

  }

  @Override
  public Duration observeFailedBatchAndGetRetryDelay(Json.Value batch, int tryNumber, Option<Exception> exception) {
    return Duration.ZERO;
  }
   
}
