package com.kaching.platform.hibernate.queue.impl;

import static com.kaching.platform.hibernate.queue.impl.BatchQueueEntityFactory.createBatch;
import static com.kaching.platform.hibernate.queue.impl.BatchQueueEntityFactory.createName;
import static com.kaching.platform.testing.Mockeries.mockery;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import java.util.List;

import org.jmock.Sequence;
import org.jmock.api.Action;
import org.jmock.api.Invocation;
import org.jmock.lib.action.CustomAction;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.hibernate.queue.BatchQueueUnsentItemGroup;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.Sleeper;
import com.kaching.util.time.NanoTimeProvider;
import com.twolattes.json.Json;

public class BatchQueueUnsentItemCacheImplTest extends BatchQueueTestBase {

  private final WFMockery mockery = mockery();
  private final Sleeper sleeper = mockery.mock(Sleeper.class);
  private final NanoTimeProvider nanoTimeProvider = mockery.mock(NanoTimeProvider.class);
  
  private final DateTime now = new DateTime(2012, 2, 2, 12, 35, 0, ET);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }
  
  @Test
  public void getMaybeStaleUnsentItems_forwardsToCache() {
    mockery.checking(new WExpectations() {{
      oneOf(nanoTimeProvider).get();
      will(returnValue(123L));
    }});
    createBatch()
        .withCreatedAt(now)
        .withQueueId(createName().withQueueName("SomeOtherQueue").buildAndPersist(transacter()).getId())
        .withItem(Json.number(44))
        .withItem(Json.number(55))
        .buildAndPersist(transacter());
    Id<BatchQueueBatch> unsentBatchId = createBatch()
        .withCreatedAt(now)
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(11))
        .withItem(Json.number(12))
        .withItem(Json.number(13))
        .buildAndPersist(transacter())
        .getId();
    BatchQueueUnsentItemCacheImpl cache = getCache();
    List<BatchQueueUnsentItemGroup> result = cache.getMaybeStaleUnsentItems(UserIdQueue.class);
    assertEquals(ImmutableList.of(
        BatchQueueUnsentItemGroup.builder()
            .withCreatedAt(now)
            .withSendableSince(now)
            .withNumItems(3)
            .withNumBatches(1)
            .withBatchId(unsentBatchId)
            .build()
    ), result);
  }
  
  @Test
  public void blockForNewUnsentItems_waitsForCacheRefresh() {
    Id<BatchQueueBatch> unsentBatchId = createBatch()
        .withCreatedAt(now)
        .withQueueId(getUserIdQueueId())
        .withItem(Json.number(11))
        .withItem(Json.number(12))
        .withItem(Json.number(13))
        .buildAndPersist(transacter())
        .getId();
    
    Sequence sequence = mockery.sequence("sequence");
    BatchQueueUnsentItemCacheImpl cache = getCache();
    mockery.checking(new WExpectations() {{
      oneOf(nanoTimeProvider).get();
      will(returnValue(100L));
      inSequence(sequence);
    }});
    assertEquals(1, cache.getMaybeStaleUnsentItems(UserIdQueue.class).size());
    mockery.assertIsSatisfied();

    Action invalidateCache = new CustomAction("invalidate cache") {
      @Override
      public Object invoke(Invocation invocation) throws Throwable {
        cache.unsentItemsCache.invalidateAll();
        return true;
      }
    };
    
    mockery.checking(new WExpectations() {{
      oneOf(nanoTimeProvider).get();
      will(returnValue(200L));
      inSequence(sequence);
      
      oneOf(sleeper).sleep(Duration.millis(1_000));
      inSequence(sequence);
      will(invalidateCache);
      
      oneOf(nanoTimeProvider).get();
      will(returnValue(150L));
      inSequence(sequence);
      
      oneOf(sleeper).sleep(Duration.millis(1_000));
      inSequence(sequence);
      will(invalidateCache);
      
      oneOf(nanoTimeProvider).get();
      will(returnValue(250L));
      inSequence(sequence);
    }});
    
    assertEquals(1, cache.blockForNewUnsentItems(UserIdQueue.class).size());
  }
  
  private BatchQueueUnsentItemCacheImpl getCache() {
    BatchQueueUnsentItemCacheImpl cache = new BatchQueueUnsentItemCacheImpl();
    cache.sleeper = sleeper;
    cache.nanoTimeProvider = nanoTimeProvider;
    cache.transacter = RetryingTransacter.retrying(transacter());
    cache.binder = injector().getInstance(BatchQueueBinder.class);
    return cache;
  }

}