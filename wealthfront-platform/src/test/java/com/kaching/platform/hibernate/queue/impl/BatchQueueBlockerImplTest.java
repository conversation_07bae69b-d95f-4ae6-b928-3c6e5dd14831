package com.kaching.platform.hibernate.queue.impl;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.kaching.platform.testing.Mockeries.mockery;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.kaching.platform.hibernate.queue.BatchQueueBlocker;
import com.kaching.platform.hibernate.queue.BatchQueueUnsentItemCache;
import com.kaching.platform.hibernate.queue.BatchQueueUnsentItemGroup;
import com.kaching.platform.hibernate.queue.impl.BatchQueueTestBase.UserIdQueue;
import com.kaching.platform.queryengine.progress.ProgressMonitor;
import com.kaching.platform.queryengine.progress.ProgressMonitor.StageProgress;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;

public class BatchQueueBlockerImplTest {
  
  private final DateTime start = new DateTime(2012, 2, 2, 12, 35, 0);

  private final WFMockery mockery = mockery();
  private final BatchQueueUnsentItemCache unsentItemCache = mockery.mock(BatchQueueUnsentItemCache.class);
  private final Provider<DateTime> clock = mockery.mockGeneric(new TypeLiteral<Provider<DateTime>>() {});

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }
  
  @Test
  public void test_defaultConfig_noProblematicItems_blocksUntilAllItemsSent_ignoresNewerItems() {
    BatchQueueBlockerImpl<UserIdQueue> blocker = createBlocker();
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(start));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start).withNumItems(3).withNumBatches(1).build(),
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start.plusMinutes(1)).withNumItems(2).withNumBatches(1).build()
      )));

      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start).withNumItems(4).withNumBatches(1).build(),
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start.plusMinutes(1)).withNumItems(2).withNumBatches(1).build()
      )));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start).withNumItems(2).withNumBatches(1).build(),
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start.plusMinutes(1)).withNumItems(2).withNumBatches(1).build()
      )));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start).withNumItems(1).withNumBatches(1).build(),
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start.plusMinutes(1)).withNumItems(2).withNumBatches(1).build()
      )));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder().withCreatedAt(start.plusMinutes(1)).withNumItems(2).withNumBatches(1).build()
      )));

    }});
    blocker.blockUntilItemsProcessed("test", BatchQueueBlocker.BlockingConfig.builder().build());
    StageProgress stageProgress = getOnlyElement(blocker.progressMonitor.getStages());
    assertEquals(4, stageProgress.getTotalWorkToDo());
    assertEquals(4, stageProgress.getWorkCompletedSoFar());
  }
  
  @Test
  public void blockUntilItemsProcessed_defaultConfig_erroredItems_continuesBlocking() {
    BatchQueueBlockerImpl<UserIdQueue> blocker = createBlocker();
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(start));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder()
              .withCreatedAt(start.minusSeconds(1))
              .withNumItems(3)
              .withNumBatches(1)
              .build()
      )));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder()
              .withCreatedAt(start.minusSeconds(1))
              .withErroredAt(start.plusMinutes(10))
              .withNumItems(1)
              .withNumBatches(1)
              .build()
      )));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of()));
    }});
    blocker.blockUntilItemsProcessed("test", BatchQueueBlocker.BlockingConfig.builder().build());
  }
  
  @Test
  public void blockUntilItemsProcessed_allowSomeErroredItems() {
    BatchQueueBlockerImpl<UserIdQueue> blocker = createBlocker();
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(start));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder()
              .withCreatedAt(start.minusSeconds(1))
              .withErroredAt(start.plusMinutes(10))
              .withNumItems(2)
              .withNumBatches(1)
              .build(),
          BatchQueueUnsentItemGroup.builder()
              .withCreatedAt(start.minusSeconds(1))
              .withErroredAt(start.plusMinutes(10))
              .withNumItems(2)
              .withNumBatches(1)
              .build()
      )));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder()
              .withCreatedAt(start.minusSeconds(1))
              .withErroredAt(start.plusMinutes(10))
              .withNumItems(2)
              .withNumBatches(1)
              .build(),
          BatchQueueUnsentItemGroup.builder()
              .withCreatedAt(start.minusSeconds(1))
              .withErroredAt(start.plusMinutes(10))
              .withNumItems(1)
              .withNumBatches(1)
              .build()
      )));
      
      oneOf(unsentItemCache).blockForNewUnsentItems(UserIdQueue.class);
      will(returnValue(ImmutableList.of(
          BatchQueueUnsentItemGroup.builder()
              .withCreatedAt(start.minusSeconds(1))
              .withErroredAt(start.plusMinutes(10))
              .withNumItems(2)
              .withNumBatches(1)
              .build()
      )));
    }});
    blocker.blockUntilItemsProcessed("test", BatchQueueBlocker.BlockingConfig.builder().allowSomeFailedItems(2).build());
  }
  
  private BatchQueueBlockerImpl<UserIdQueue> createBlocker() {
    BatchQueueBlockerImpl<UserIdQueue> blocker = new BatchQueueBlockerImpl<>(UserIdQueue.class);
    blocker.unsentItemCache = unsentItemCache;
    blocker.clock = clock;
    blocker.progressMonitor = new ProgressMonitor();
    return blocker;
  }

}