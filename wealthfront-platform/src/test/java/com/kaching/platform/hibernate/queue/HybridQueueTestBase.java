package com.kaching.platform.hibernate.queue;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static com.kaching.platform.hibernate.queue.HybridQueueTestBase.SimulatedOutcome.SEND_SUCCESSFULLY;
import static com.kaching.platform.hibernate.queue.HybridQueueTestBase.SimulatedOutcome.THROW_ERROR_FLAG_IN_SEND;
import static com.kaching.platform.hibernate.queue.HybridQueueTestBase.SimulatedOutcome.THROW_ERROR_FLAG_IN_TRANSFORM;
import static com.kaching.platform.hibernate.queue.HybridQueueTestBase.SimulatedOutcome.THROW_IGNORED_IN_SEND;
import static com.kaching.platform.hibernate.queue.HybridQueueTestBase.SimulatedOutcome.THROW_IGNORED_IN_TRANSFORM;
import static com.kaching.platform.hibernate.queue.HybridQueueTestBase.SimulatedOutcome.THROW_UNKNOWN_IN_SEND;
import static com.kaching.platform.hibernate.queue.HybridQueueTestBase.SimulatedOutcome.THROW_UNKNOWN_IN_TRANSFORM;
import static com.kaching.platform.multicolo.MultiColoStatusProvider.ColoStatus.MASTER;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.lang.Integer.valueOf;
import static java.util.Collections.nCopies;
import static java.util.Collections.synchronizedList;
import static java.util.Collections.synchronizedMap;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.fail;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;

import org.joda.time.DateTime;

import com.google.common.base.Throwables;
import com.google.common.util.concurrent.Striped;
import com.kaching.monitor.esp.ResponsiblePartyFinderImpl;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.FakeStackTraceMonitor;
import com.kaching.platform.zk.FakeLocalAnnouncement;
import com.kaching.util.DefaultSleeper;
import com.kaching.util.functional.Pointer;
import com.kaching.util.functional.Tuple2;
import com.kaching.util.tests.PersistentTestBase;

public abstract class HybridQueueTestBase<E extends HybridQueueEntity, S> extends
    PersistentTestBase {

  protected enum SimulatedOutcome {
    THROW_ERROR_FLAG_IN_TRANSFORM,
    THROW_IGNORED_IN_TRANSFORM,
    THROW_UNKNOWN_IN_TRANSFORM,
    THROW_ERROR_FLAG_IN_SEND,
    THROW_IGNORED_IN_SEND,
    THROW_UNKNOWN_IN_SEND,
    SEND_SUCCESSFULLY,
  }

  private static final Log log = Log.getLog(HybridQueueTestBase.class);
  protected HybridQueueWrapper<E, S> wrapperQueue;
  protected final List<Runnable> tasksOnStart = newArrayList();
  private final DateTime now = new DateTime(2016, 2, 13, 1, 2, 3, 4, ET);

  public void setupTest(AbstractHybridQueue<E, S> queue) {
    queue.transacter = transacter;
    // For a more realistic but less reliable test, the queue clock can be changed to provide DateTime.now()
    AtomicReference<DateTime> dateTimePointer = new AtomicReference<>(now);
    queue.clock = () -> dateTimePointer.accumulateAndGet(null, (dateTime, ignored) -> dateTime.plusMillis(200));
    queue.isLeader = () -> true;
    queue.sleeper = new DefaultSleeper();
    queue.stackTraceMonitor = new FakeStackTraceMonitor();
    queue.multiColoStatusProvider = () -> MASTER;
    queue.localAnnouncement = new FakeLocalAnnouncement(true);
    queue.responsiblePartyFinder = new ResponsiblePartyFinderImpl() {{
      onCallPackageMapping = Map.of();
    }};
    queue.serviceKind = new KachingServices.BUM();
    wrapperQueue = HybridQueueWrapper.wrapQueue(queue, this::createIgnoredException, this::createErrorFlagException);
  }

  protected AtomicReference<Id<E>> persistWithOutcomes(E entity, List<SimulatedOutcome> entitySimulatedOutcomes) {
    AtomicReference<Id<E>> idPointer = new AtomicReference<>();
    wrapperQueue.transacter.executeWithSession(session -> {
      wrapperQueue.outcomesByEntity.add(Tuple2.of(entity, newArrayList(entitySimulatedOutcomes)));
      idPointer.set(session.save(entity));
    });
    log.trace("finished adding id " + entity.getId());
    return idPointer;
  }

  protected AtomicReference<Id<E>> persistWithOutcomesOnStart(
      E entity, List<SimulatedOutcome> entitySimulatedOutcomes) {
    AtomicReference<Id<E>> idPointer = new AtomicReference<>();
    tasksOnStart.add(() ->
        wrapperQueue.transacter.executeWithSession(session -> {
          wrapperQueue.outcomesByEntity.add(Tuple2.of(entity, newArrayList(entitySimulatedOutcomes)));
          idPointer.set(session.save(entity));
        })
    );
    return idPointer;
  }

  protected AtomicReference<Id<E>> enqueueWithOutcomes(E entity, List<SimulatedOutcome> entitySimulatedOutcomes) {
    AtomicReference<Id<E>> idPointer = new AtomicReference<>();
    wrapperQueue.transacter.executeWithSession(session -> {
      wrapperQueue.outcomesByEntity.add(Tuple2.of(entity, newArrayList(entitySimulatedOutcomes)));
      idPointer.set(wrapperQueue.persistAndEnqueue(entity, session));
    });
    return idPointer;
  }

  protected AtomicReference<Id<E>> enqueueWithOutcomesOnStart(
      E entity, List<SimulatedOutcome> entitySimulatedOutcomes) {
    AtomicReference<Id<E>> idPointer = new AtomicReference<>();
    tasksOnStart.add(() ->
        wrapperQueue.transacter.executeWithSession(session -> {
          wrapperQueue.outcomesByEntity.add(Tuple2.of(entity, newArrayList(entitySimulatedOutcomes)));
          idPointer.set(wrapperQueue.persistAndEnqueue(entity, session));
        })
    );
    return idPointer;
  }

  protected void runQueueToCompletion() {
    Pointer<Throwable> unhandledException = new Pointer<>();
    Thread thread = startQueueAsync(unhandledException);
    tasksOnStart.forEach(Runnable::run);
    wrapperQueue.grantPermissionToHaltWhenFinished();
    wrapperQueue.checkDatabaseNow();
    waitForCompletionOrTimeout(thread, unhandledException);
  }

  protected void checkResults(List<AtomicReference<Id<E>>> sendIds, List<AtomicReference<Id<E>>> errorIds) {
    assertEquals(0, wrapperQueue.enqueuedIds.size());

    sendIds.forEach(idPointer -> {
      assertEntityWasSentExactlyOnce(idPointer.get());
      transacter.executeWithReadOnlySession(session -> {
        E retrievedEntity = wrapperQueue.getEntityRepository(session).getOrThrow(idPointer.get());
        assertNotNull("id " + retrievedEntity.getId(), retrievedEntity.getPolledTime());
        assertNotNull("id " + retrievedEntity.getId(), retrievedEntity.getSentTime());
        assertEquals("id " + retrievedEntity.getId(), false, retrievedEntity.getErrorFlag());
      });
    });

    errorIds.forEach(idPointer -> {
      assertEntityWasNotSent(idPointer.get());
      transacter.executeWithReadOnlySession(session -> {
        E retrievedEntity = wrapperQueue.getEntityRepository(session).getOrThrow(idPointer.get());
        assertNull("id " + retrievedEntity.getId(), retrievedEntity.getSentTime());
        assertEquals("id " + retrievedEntity.getId(), true, retrievedEntity.getErrorFlag());
      });
    });
    int countOfStackTraces = ((FakeStackTraceMonitor) wrapperQueue.realQueue.stackTraceMonitor)
        .getStackTracesAndClear()
        .stream()
        .mapToInt(Map.Entry::getValue)
        .sum();
    assertEquals(errorIds.size(), countOfStackTraces);
  }

  /**
   * The full standard test suite below can be run by:
   * 1. Having your test class extend this class
   * 2. (Optional) Overriding the below assert methods to check your actual/fake destination
   * 3. Overriding each method starting with "standardTest" and just delegating to super
   * 4. (Optional) Overriding each method starting with "optionalTest" and just delegating to super
   * 5. Annotating all the overrides with @Test
   * <p>
   * If you need to debug your queue's behavior, try adding this to your log4j2-test.xml:
   * <Logger name="com.kaching.platform.hibernate.queue" level="trace"/>
   */

  protected void assertEntityWasSentExactlyOnce(Id<E> id) {
    assertEquals("should have sent entity id " + id + " exactly once",
        valueOf(1), wrapperQueue.successfulSendCounts.get(id));
  }

  protected void assertEntityWasNotSent(Id<E> id) {
    assertFalse("should not have sent entity id " + id, wrapperQueue.successfulSendCounts.containsKey(id));
  }

  /**
   * This function should return a variety of possible valid queue entities.
   * It would be best if this spanned most/all possible combinations of entity values/types.
   * <p>
   * As with any test, this should be deterministic so that failures can be reproduced.
   */
  protected abstract E createValidEntity();

  protected RuntimeException createIgnoredException(String message) {
    return new RuntimeException(message, new IgnoredException(message));
  }

  protected RuntimeException createErrorFlagException(String message) {
    return new RuntimeException(message, new HybridQueueErrorFlagException(message));
  }

  List<SimulatedOutcome> sendSuccessfully = newArrayList(SEND_SUCCESSFULLY);
  List<SimulatedOutcome> transformExceptionsSuccess =
      newArrayList(THROW_UNKNOWN_IN_TRANSFORM, THROW_UNKNOWN_IN_TRANSFORM, SEND_SUCCESSFULLY);
  List<SimulatedOutcome> sendExceptionsSuccess =
      newArrayList(THROW_UNKNOWN_IN_SEND, THROW_UNKNOWN_IN_SEND, SEND_SUCCESSFULLY);
  List<SimulatedOutcome> bothExceptionsSuccess =
      newArrayList(THROW_UNKNOWN_IN_TRANSFORM, THROW_UNKNOWN_IN_SEND, SEND_SUCCESSFULLY);
  List<SimulatedOutcome> sendExceptionsError = nCopies(3, THROW_UNKNOWN_IN_SEND);
  List<SimulatedOutcome> transformExceptionsError = nCopies(3, THROW_UNKNOWN_IN_TRANSFORM);
  List<SimulatedOutcome> transformTempExceptionsSuccess =
      newArrayList(THROW_IGNORED_IN_TRANSFORM, THROW_IGNORED_IN_TRANSFORM, THROW_IGNORED_IN_TRANSFORM,
          THROW_IGNORED_IN_TRANSFORM, THROW_IGNORED_IN_TRANSFORM, THROW_IGNORED_IN_TRANSFORM, SEND_SUCCESSFULLY);
  List<SimulatedOutcome> sendTempExceptionsSuccess =
      newArrayList(THROW_IGNORED_IN_SEND, THROW_IGNORED_IN_SEND, THROW_IGNORED_IN_SEND, THROW_IGNORED_IN_SEND,
          THROW_IGNORED_IN_SEND, THROW_IGNORED_IN_SEND, SEND_SUCCESSFULLY);
  List<SimulatedOutcome> sendPermExceptionsError = nCopies(1, THROW_ERROR_FLAG_IN_SEND);
  List<SimulatedOutcome> transformPermExceptionsError = nCopies(1, THROW_ERROR_FLAG_IN_TRANSFORM);

  public void standardTest_basicSending() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    for (int i = 0; i < 20; i++) {
      sendEntities.add(enqueueNewEntity(sendSuccessfully));
    }

    runQueueToCompletion();
    checkResults(sendEntities, newArrayList());
  }

  public void standardTest_variousOutcomes() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    List<AtomicReference<Id<E>>> errorEntities = new ArrayList<>();

    for (int i = 0; i < 30; i++) {
      sendEntities.add(enqueueNewEntity(sendSuccessfully));
      sendEntities.add(enqueueNewEntity(sendExceptionsSuccess));
      errorEntities.add(enqueueNewEntity(sendExceptionsError));
      sendEntities.add(enqueueNewEntity(transformTempExceptionsSuccess));
      sendEntities.add(enqueueNewEntity(transformExceptionsSuccess));
      sendEntities.add(enqueueNewEntity(bothExceptionsSuccess));
      errorEntities.add(enqueueNewEntity(transformExceptionsError));
      errorEntities.add(enqueueNewEntity(transformPermExceptionsError));
    }

    runQueueToCompletion();
    checkResults(sendEntities, errorEntities);
  }

  public void standardTest_errorFlagExceptionsInTransform_shouldSetErrorFlag() {
    List<AtomicReference<Id<E>>> errorEntities = new ArrayList<>();
    for (int i = 0; i < 40; i++) {
      errorEntities.add(enqueueNewEntity(transformPermExceptionsError));
    }

    runQueueToCompletion();
    checkResults(newArrayList(), errorEntities);
  }

  public void standardTest_ignoredExceptionsInTransform_shouldNotSetErrorFlag() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    for (int i = 0; i < 20; i++) {
      sendEntities.add(enqueueNewEntity(transformTempExceptionsSuccess));
    }

    runQueueToCompletion();
    checkResults(sendEntities, newArrayList());
  }

  public void standardTest_tooManyUnknownExceptionsInTransform_shouldSetErrorFlag() {
    List<AtomicReference<Id<E>>> errorEntities = new ArrayList<>();
    for (int i = 0; i < 40; i++) {
      errorEntities.add(enqueueNewEntity(transformExceptionsError));
    }

    runQueueToCompletion();
    checkResults(newArrayList(), errorEntities);
  }

  public void standardTest_allowableUnknownExceptionsInTransform_shouldNotSetErrorFlag() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    for (int i = 0; i < 40; i++) {
      sendEntities.add(enqueueNewEntity(transformExceptionsSuccess));
    }

    runQueueToCompletion();
    checkResults(sendEntities, newArrayList());
  }

  public void standardTest_errorFlagExceptionsInSend_shouldSetErrorFlag() {
    List<AtomicReference<Id<E>>> errorEntities = new ArrayList<>();
    for (int i = 0; i < 40; i++) {
      errorEntities.add(enqueueNewEntity(sendPermExceptionsError));
    }

    runQueueToCompletion();
    checkResults(newArrayList(), errorEntities);
  }

  public void standardTest_ignoredExceptionsInSend_shouldNotSetErrorFlag() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    for (int i = 0; i < 20; i++) {
      sendEntities.add(enqueueNewEntity(sendTempExceptionsSuccess));
    }

    runQueueToCompletion();
    checkResults(sendEntities, newArrayList());
  }

  public void standardTest_tooManyUnknownExceptionsInSend_shouldSetErrorFlag() {
    List<AtomicReference<Id<E>>> errorEntities = new ArrayList<>();
    for (int i = 0; i < 40; i++) {
      errorEntities.add(enqueueNewEntity(sendExceptionsError));
    }

    runQueueToCompletion();
    checkResults(newArrayList(), errorEntities);
  }

  public void standardTest_allowableUnknownExceptionsInSend_shouldNotSetErrorFlag() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    for (int i = 0; i < 40; i++) {
      sendEntities.add(enqueueNewEntity(sendExceptionsSuccess));
    }

    runQueueToCompletion();
    checkResults(sendEntities, newArrayList());
  }

  public void standardTest_persistTransactionRollsBack_shouldNotHaveAnyEnqueuedIds() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    tasksOnStart.add(() -> {
      try {
        wrapperQueue.transacter.executeWithSession(session -> {
          for (int i = 0; i < 100; i++) {
            wrapperQueue.outcomesByEntity.add(Tuple2.of(createValidEntity(), newArrayList(sendSuccessfully)));
            wrapperQueue.persistAndEnqueue(createValidEntity(), session);
          }
          // Cause transaction to roll back on commit:
          MockEntity mockEntity = new MockEntity();
          mockEntity.setLocalId(null);
          session.save(mockEntity);
        });
      } catch (RuntimeException ignored) {
      }

      wrapperQueue.transacter.executeWithSession(session -> {
        for (int i = 0; i < 100; i++) {
          AtomicReference<Id<E>> entityReference = new AtomicReference<>();
          wrapperQueue.outcomesByEntity.add(Tuple2.of(createValidEntity(), newArrayList(sendSuccessfully)));
          entityReference.set(wrapperQueue.persistAndEnqueue(createValidEntity(), session));
          sendEntities.add(entityReference);
        }
      });
    });

    runQueueToCompletion();
    assertEquals(0, wrapperQueue.enqueuedIds.size());
    checkResults(sendEntities, newArrayList());
  }

  public void optionalTest_multithreading_sendSuccessfully_stressTest() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    List<AtomicReference<Id<E>>> errorEntities = new ArrayList<>();
    for (int i = 0; i < 20_000; i++) {
      sendEntities.add(persistNewEntity(sendSuccessfully));
      sendEntities.add(enqueueNewEntity(sendSuccessfully));
    }

    runQueueToCompletion();
    checkResults(sendEntities, errorEntities);
  }

  public void optionalTest_multithreading_variousOutcomes_stressTest() {
    List<AtomicReference<Id<E>>> sendEntities = new ArrayList<>();
    List<AtomicReference<Id<E>>> errorEntities = new ArrayList<>();
    for (int i = 0; i < 4_000; i++) {
      sendEntities.add(enqueueNewEntity(sendSuccessfully));
      sendEntities.add(enqueueNewEntity(transformExceptionsSuccess));
      sendEntities.add(enqueueNewEntity(sendExceptionsSuccess));
      sendEntities.add(enqueueNewEntity(bothExceptionsSuccess));
      errorEntities.add(enqueueNewEntity(sendExceptionsError));
      errorEntities.add(enqueueNewEntity(transformExceptionsError));
      sendEntities.add(enqueueNewEntity(transformTempExceptionsSuccess));
      errorEntities.add(enqueueNewEntity(sendExceptionsError));
      sendEntities.add(enqueueNewEntity(sendTempExceptionsSuccess));
    }

    runQueueToCompletion();
    checkResults(sendEntities, errorEntities);
  }

  private AtomicReference<Id<E>> persistNewEntity(List<SimulatedOutcome> entitySimulatedOutcomes) {
    return persistWithOutcomesOnStart(createValidEntity(), entitySimulatedOutcomes);
  }

  private AtomicReference<Id<E>> enqueueNewEntity(List<SimulatedOutcome> entitySimulatedOutcomes) {
    return enqueueWithOutcomesOnStart(createValidEntity(), entitySimulatedOutcomes);
  }

  protected Thread startQueueAsync(Pointer<Throwable> unhandledException) {
    Thread thread = new Thread(wrapperQueue);
    thread.setUncaughtExceptionHandler((r, t) -> unhandledException.set(t));
    thread.start();
    return thread;
  }

  @SuppressWarnings("deprecation")
  protected void waitForCompletionOrTimeout(Thread thread, Pointer<Throwable> unhandledException) {
    int secondsToWait = 600;
    try {
      thread.join(secondsToWait * 1000);
      thread.interrupt();
      if (thread.isAlive()) {
        thread.stop();
        fail("queue test did not complete in time (" + secondsToWait + " seconds). presumed infinite loop.");
      }
    } catch (InterruptedException ignored) {
      log.trace("ignored exception");
    }
    if (!unhandledException.isEmpty()) {
      throw new AssertionError("Unhandled exception in queue's main thread.", unhandledException.get());
    }
  }

  /**
   * This queue is a shim that introduces artificial test failures as necessary, but otherwise delegates to the
   * real queue.
   * <p>
   * Note that the real queue will never have its run() method called or its executor used, though this queue
   * will copy the initial number of workers specified in the real queue's constructor.
   */
  protected static class HybridQueueWrapper<E extends HybridQueueEntity, S>
      extends AbstractHybridQueue<E, S> {

    private static final Log log = Log.getLog(HybridQueueWrapper.class);

    private boolean mayHalt = false;
    private final AtomicInteger pendingEntityCount = new AtomicInteger(0);
    private final AtomicReference<Id<E>> pendingEntityId = new AtomicReference<>();
    private final AtomicInteger identicalLoopCount = new AtomicInteger(0);

    private final List<Tuple2<E, List<SimulatedOutcome>>> outcomesByEntity = synchronizedList(new ArrayList<>());
    private final Map<Id<E>, Integer> successfulSendCounts = synchronizedMap(newHashMap());

    private final AbstractHybridQueue<E, S> realQueue;
    Function<String, RuntimeException> temporaryExceptionGenerator;
    Function<String, RuntimeException> permanentExceptionGenerator;

    private HybridQueueWrapper(
        int initialWorkers,
        AbstractHybridQueue<E, S> realQueue,
        Function<String, RuntimeException> temporaryExceptionGenerator,
        Function<String, RuntimeException> permanentExceptionGenerator) {
      super(initialWorkers);
      this.realQueue = realQueue;
      this.temporaryExceptionGenerator = temporaryExceptionGenerator;
      this.permanentExceptionGenerator = permanentExceptionGenerator;
    }

    protected static <E extends HybridQueueEntity, S> HybridQueueWrapper<E, S> wrapQueue(
        AbstractHybridQueue<E, S> realQueue,
        Function<String, RuntimeException> temporaryExceptionGenerator,
        Function<String, RuntimeException> permanentExceptionGenerator) {
      HybridQueueWrapper<E, S> wrapper = new HybridQueueWrapper<>(realQueue.initialWorkers, realQueue,
          temporaryExceptionGenerator, permanentExceptionGenerator);
      realQueue.executor.shutdown();
      wrapper.transacter = realQueue.transacter;
      wrapper.clock = realQueue.clock;
      wrapper.isLeader = realQueue.isLeader;
      wrapper.sleeper = realQueue.sleeper;
      wrapper.multiColoStatusProvider = realQueue.multiColoStatusProvider;
      wrapper.localAnnouncement = realQueue.localAnnouncement;
      wrapper.stackTraceMonitor = realQueue.stackTraceMonitor;
      wrapper.responsiblePartyFinder = realQueue.responsiblePartyFinder;
      wrapper.serviceKind = realQueue.serviceKind;
      return wrapper;
    }

    private List<SimulatedOutcome> getOutcomes(Id<E> id) {
      synchronized (outcomesByEntity) {
        for (Tuple2<E, List<SimulatedOutcome>> tuple : outcomesByEntity) {
          if (id.equals(tuple._1.getId())) {
            return tuple._2;
          }
        }
      }
      return newArrayList(SEND_SUCCESSFULLY);
    }

    public void grantPermissionToHaltWhenFinished() {
      mayHalt = true;
    }

    @Override
    public HybridQueueEntityRepository<E> getEntityRepository(DbSession session) {
      return realQueue.getEntityRepository(session);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected S transform(E entity, DbSession session) {
      Id<E> id = (Id<E>) entity.getId();
      List<SimulatedOutcome> entitySimulatedOutcomes = getOutcomes(id);
      if (entitySimulatedOutcomes.isEmpty()) {
        throw new RuntimeException("not enough expected outcomes for id " + id);
      }
      return switch (entitySimulatedOutcomes.get(0)) {
        case THROW_UNKNOWN_IN_TRANSFORM -> {
          entitySimulatedOutcomes.remove(0);
          throw new RuntimeException("deliberate failure (unclassified) in transform()");
        }
        case THROW_IGNORED_IN_TRANSFORM -> {
          entitySimulatedOutcomes.remove(0);
          throw temporaryExceptionGenerator.apply("deliberate failure (temporary) in transform()");
        }
        case THROW_ERROR_FLAG_IN_TRANSFORM -> {
          entitySimulatedOutcomes.remove(0);
          throw permanentExceptionGenerator.apply("deliberate failure (permanent) in transform()");
        }
        case THROW_UNKNOWN_IN_SEND, SEND_SUCCESSFULLY, THROW_IGNORED_IN_SEND, THROW_ERROR_FLAG_IN_SEND ->
            realQueue.transform(entity, session);
      };
    }

    @Override
    protected void send(S sendValue, Id<E> id) {
      List<SimulatedOutcome> entitySimulatedOutcomes = getOutcomes(id);
      if (entitySimulatedOutcomes.isEmpty()) {
        throw new RuntimeException("not enough expected outcomes for id " + id);
      }
      switch (entitySimulatedOutcomes.get(0)) {
        case THROW_UNKNOWN_IN_SEND:
          entitySimulatedOutcomes.remove(0);
          throw new RuntimeException("deliberate failure in send()");
        case THROW_IGNORED_IN_SEND:
          entitySimulatedOutcomes.remove(0);
          throw temporaryExceptionGenerator.apply("deliberate failure (temporary) in send()");
        case THROW_ERROR_FLAG_IN_SEND:
          entitySimulatedOutcomes.remove(0);
          throw permanentExceptionGenerator.apply("deliberate failure (permanent) in send()");
        case SEND_SUCCESSFULLY:
          realQueue.send(sendValue, id);
          log.debug("successfully sent entity id " + id);
          entitySimulatedOutcomes.remove(0);
          successfulSendCounts.put(id, 1 + successfulSendCounts.getOrDefault(id, 0));
          return;
        case THROW_UNKNOWN_IN_TRANSFORM:
        case THROW_IGNORED_IN_TRANSFORM:
        case THROW_ERROR_FLAG_IN_TRANSFORM:
          throw new RuntimeException("shouldn't be possible to hit this branch");
        default:
          throw new RuntimeException("unknown enum value " + entitySimulatedOutcomes.get(0));
      }
    }

    @Override
    protected void onSetErrorFlag(Exception e, E entity, DbSession session) {
      realQueue.onSetErrorFlag(e, entity, session);
    }

    @Override
    protected int getMinDbPollIntervalMillis() {
      return realQueue.getMinDbPollIntervalMillis() / 100;
    }

    @Override
    protected int getMaxPollIntervalMillis() {
      return realQueue.getMaxPollIntervalMillis() / 100;
    }

    @Override
    protected int getShortDelayLengthMillis() {
      return realQueue.getShortDelayLengthMillis() / 100;
    }

    @Override
    protected int getLongDelayLengthMillis() {
      return realQueue.getLongDelayLengthMillis() / 100;
    }

    @Override
    protected boolean isIgnoredException(Exception e) {
      return Throwables.getCausalChain(e).stream().map(Throwable::getClass).anyMatch(IgnoredException.class::equals);
    }

    @Override
    Striped<Lock> getIdLocks() {
      return realQueue.getIdLocks();
    }

    @Override
    boolean shouldShutdown() {
      if (!mayHalt) {
        return false;
      }
      int activeCount = executor.getActiveCount();
      int queueSize = executor.getQueue().size();
      int delayedSize = delayedEnqueues.size();
      if (activeCount > 0 || queueSize > 0 || delayedSize > 0) {
        log.debug(activeCount + " active threads, " + queueSize + " enqueued, " + delayedSize + " delayed enqueues");
        return false;
      }
      if (realQueue.shouldShutdown()) {
        return true;
      }

      return transacter.executeWithReadOnlySessionExpression(session -> {
        HybridQueueEntityRepository<E> repo = getEntityRepository(session);
        List<Id<E>> unsentIds = repo.getSomeUnpolledIds(1_000_000);
        unsentIds.addAll(repo.getSomePolledUnsentIds(1_000_000));
        int count = unsentIds.size();
        if (count > 0) {
          Id<E> exampleId = unsentIds.get(0);
          log.debug(count + " unsent events remain. not exiting. example: entity id " + exampleId);

          if (count == pendingEntityCount.get() && exampleId.equals(pendingEntityId.get())) {
            if (identicalLoopCount.incrementAndGet() > 30) {
              throw new RuntimeException("too many loops with no progress -- presumed infinite");
            }
          } else {
            identicalLoopCount.set(0);
            pendingEntityCount.set(count);
            pendingEntityId.set(exampleId);
          }
          return false;
        } else {
          log.debug("no unsent events remain. exiting.");
          return true;
        }
      });
    }

    @Override
    protected boolean extraTryLock(E entity, DbSession session) {
      return realQueue.extraTryLock(entity, session);
    }

    @Override
    protected void extraUnlock(E entity, DbSession session) {
      realQueue.extraUnlock(entity, session);
    }

    @Override
    protected Comparable<?> getCustomComparable(E entity, DbSession session) {
      return realQueue.getCustomComparable(entity, session);
    }

    @Override
    protected Map<Id<E>, Comparable<?>> bulkGetCustomComparables(Set<Id<E>> ids, DbSession session) {
      return realQueue.bulkGetCustomComparables(ids, session);
    }

    @Override
    protected void onTransformException(Exception e, E entity, DbSession session) {
      if (e.toString().contains("deliberate failure")) {
        Throwables.getCausalChain(e).forEach(ex -> ex.setStackTrace(new StackTraceElement[0]));
        realQueue.onTransformException(e, entity, session);
      } else {
        shutdownGracefully(false);
        throw new AssertionError("Non-deliberate exception was thrown in transform. entity id " + entity.getId(), e);
      }
    }

    @Override
    protected void onSendException(Exception e, Id<E> id, S sendValue) {
      if (e.toString().contains("deliberate failure")) {
        Throwables.getCausalChain(e).forEach(ex -> ex.setStackTrace(new StackTraceElement[0]));
        realQueue.onSendException(e, id, sendValue);
      } else {
        shutdownGracefully(false);
        throw new AssertionError("Non-deliberate exception was thrown in send. entity id " + id, e);
      }
    }

  }

  protected static class IgnoredException extends RuntimeException {

    public IgnoredException(String message) {
      super(message);
    }

  }

}