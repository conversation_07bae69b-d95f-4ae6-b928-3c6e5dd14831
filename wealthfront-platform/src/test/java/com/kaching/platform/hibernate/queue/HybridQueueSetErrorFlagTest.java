package com.kaching.platform.hibernate.queue;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.util.Collections.singleton;
import static org.junit.Assert.assertEquals;

import org.joda.time.DateTime;
import org.junit.BeforeClass;
import org.junit.Test;

import com.kaching.platform.hibernate.DatabaseType;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.AbstractHybridQueueExampleTest.TestHybridQueue;
import com.kaching.util.FakeSleeper;
import com.kaching.util.tests.PersistentTestBase;

public class HybridQueueSetErrorFlagTest extends PersistentTestBase {

  private final DateTime now = new DateTime(2017, 2, 13, 1, 2, 3, ET);

  @BeforeClass
  public static void beforeClass() {
    configure(DatabaseType.MARIA, MockEntity.class);
  }

  @Test
  public void process_setFalse_successful() {
    Id<MockEntity> entityId = transacter.executeWithSessionExpression(session -> {
      MockEntity entity = new MockEntity();
      entity.setErrorFlag(true);
      return session.save(entity);
    });

    String result = createQuery(entityId, false).process();

    assertEquals("Successfully changed 1 error flag(s)", result);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity retrievedEntity = session.createCriteria(MockEntity.class).uniqueResult();
      assertEquals(false, retrievedEntity.getErrorFlag());
    });
  }

  @Test
  public void process_setTrue_successful() {
    Id<MockEntity> entityId = transacter.executeWithSessionExpression(session -> {
      MockEntity entity = new MockEntity();
      entity.setErrorFlag(false);
      return session.save(entity);
    });

    String result = createQuery(entityId, true).process();

    assertEquals("Successfully changed 1 error flag(s)", result);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity retrievedEntity = session.createCriteria(MockEntity.class).uniqueResult();
      assertEquals(true, retrievedEntity.getErrorFlag());
    });
  }

  @Test
  public void process_alreadyEqual() {
    Id<MockEntity> entityId = transacter.executeWithSessionExpression(session -> {
      MockEntity entity = new MockEntity();
      entity.setErrorFlag(true);
      return session.save(entity);
    });

    String result = createQuery(entityId, true).process();

    assertEquals("1 error flag(s) were already true", result);
  }

  @Test
  public void process_alreadySent_settingToTrue() {
    Id<MockEntity> entityId = transacter.executeWithSessionExpression(session -> {
      MockEntity entity = new MockEntity();
      entity.setErrorFlag(false);
      entity.setSentTime(now);
      return session.save(entity);
    });

    String result = createQuery(entityId, true).process();

    assertEquals("1 error flag(s) were not set because entity was already sent", result);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity retrievedEntity = session.createCriteria(MockEntity.class).uniqueResult();
      assertEquals(false, retrievedEntity.getErrorFlag());
    });
  }

  @Test
  public void process_alreadySent_settingToFalse() {
    Id<MockEntity> entityId = transacter.executeWithSessionExpression(session -> {
      MockEntity entity = new MockEntity();
      entity.setErrorFlag(true);
      entity.setSentTime(now);
      return session.save(entity);
    });

    String result = createQuery(entityId, false).process();

    assertEquals("Successfully changed 1 error flag(s)", result);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity retrievedEntity = session.createCriteria(MockEntity.class).uniqueResult();
      assertEquals(false, retrievedEntity.getErrorFlag());
    });
  }

  @Test
  public void process_cannotGetLock() {
    Id<MockEntity> entityId = transacter.executeWithSessionExpression(session -> {
      MockEntity entity = new MockEntity();
      entity.setErrorFlag(false);
      return session.save(entity);
    });

    HybridQueueSetErrorFlag query = createQuery(entityId, true);
    query.queueBinder = FakeQueueBinder.of("queue-name", new TestHybridQueue(1, null) {
      @Override
      protected boolean extraTryLock(MockEntity mockEntity, DbSession session) {
        return false;
      }
    });
    String result = query.process();

    assertEquals("Failed to acquire lock on these id(s). Maybe retry them? " + entityId, result);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity retrievedEntity = session.createCriteria(MockEntity.class).uniqueResult();
      assertEquals(false, retrievedEntity.getErrorFlag());
    });
  }

  private HybridQueueSetErrorFlag createQuery(Id<MockEntity> id, boolean newValue) {
    HybridQueueSetErrorFlag query = new HybridQueueSetErrorFlag("queue-name", newValue, singleton(id));
    query.transacter = transacter;
    TestHybridQueue queue = new TestHybridQueue(1, null);
    queue.clock = () -> now;
    queue.sleeper = new FakeSleeper();
    query.queueBinder = FakeQueueBinder.of("queue-name", queue);
    return query;
  }

}