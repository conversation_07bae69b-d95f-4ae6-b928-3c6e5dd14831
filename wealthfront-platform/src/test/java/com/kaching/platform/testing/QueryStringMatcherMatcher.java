package com.kaching.platform.testing;

import static com.kaching.KachingInstantiators.createInstantiator;
import static java.lang.String.format;

import java.util.List;

import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.TypeSafeMatcher;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.converters.Instantiator;
import com.kaching.platform.queryengine.Query;

public class QueryStringMatcherMatcher<T extends Query<?>> extends TypeSafeMatcher<T> {

  private static final Log log = Log.getLog(QueryStringMatcherMatcher.class);
  private final List<Matcher<String>> expectedParams;
  private final Class<? extends T> queryClass;

  @SafeVarargs
  public QueryStringMatcherMatcher(Class<? extends T> queryClass, Matcher<String>... params) {
    this.queryClass = queryClass;
    this.expectedParams = Lists.newArrayList(params);
  }

  @Override
  @SuppressWarnings("unchecked")
  public boolean matchesSafely(T item) {
    if (!queryClass.equals(item.getClass())) {
      return false;
    }
    Instantiator instantiator = createInstantiator(queryClass, new DefaultJsonMarshallerFactory());
    // TODO: instantiator.paramsFromInstance(...) to get the objects, which may be matchers or literals
    List<String> itemParams = instantiator.fromInstance(item);
    if (expectedParams.size() != itemParams.size()) {
      return false;
    }
    for (int i = 0; i < expectedParams.size(); i++) {
      if (!expectedParams.get(i).matches(itemParams.get(i))) {
        log.info(Joiner.on(", ").useForNull("null").join(itemParams));
        return false;
      }
    }
    return true;
  }

  @Override
  public void describeTo(Description description) {
    description.appendText(format(
        "query %s(%s)",
        queryClass.getSimpleName(),
        Joiner.on(", ").useForNull("null").join(expectedParams)));
  }
}
