package com.kaching.platform.testing;

import static com.google.common.collect.Iterables.size;
import static com.kaching.platform.testing.QueryMatcher.query;
import static java.util.Arrays.asList;

import java.util.List;

import com.google.common.collect.Lists;
import com.kaching.platform.queryengine.Query;

public class UnorderedQueriesMatcher<T extends Query<U>, U> extends QueriesMatcher<T, U> {

  private UnorderedQueriesMatcher(Iterable<T> queries) {
    super(queries);
  }

  @Override
  public boolean matchesSafely(List<T> items) {
    if (size(queries) != size(items)) {
      return false;
    }

    List<T> queriesList = Lists.newArrayList(queries);
    for (T item : items) {
      boolean foundAMatch = false;
      for (int i = 0; i < queriesList.size(); i++) {
        if (query(queriesList.get(i)).matchesSafely(item)) {
          queriesList.remove(i);
          foundAMatch = true;
          break;
        }
      }
      if (!foundAMatch) {
        return false;
      }
    }

    return true;
  }

  public static <T extends Query<U>, U> UnorderedQueriesMatcher<T, U> unorderedQueries(T... queries) {
    return new UnorderedQueriesMatcher<>(asList(queries));
  }

  public static <T extends Query<U>, U> UnorderedQueriesMatcher<T, U> unorderedQueries(Iterable<T> queries) {
    return new UnorderedQueriesMatcher<>(queries);
  }

}
