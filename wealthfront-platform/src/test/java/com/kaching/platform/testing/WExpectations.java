package com.kaching.platform.testing;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Sets.newLinkedHashSet;
import static com.kaching.platform.common.Strings.format;
import static com.wealthfront.util.stream.WFCollectors.toMapWithKey;
import static java.util.stream.Collectors.toList;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.hamcrest.Matchers;
import org.hamcrest.TypeSafeMatcher;
import org.hamcrest.core.IsAnything;
import org.hamcrest.core.IsNot;
import org.hamcrest.core.IsNull;
import org.jmock.Expectations;
import org.jmock.api.Action;
import org.jmock.api.Invocation;
import org.jmock.internal.Cardinality;
import org.jmock.internal.InvocationExpectationBuilder;
import org.jmock.lib.action.CustomAction;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.kaching.entities.Repository;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.exceptions.NotFoundException;
import com.kaching.platform.testing.QueryParamMatcherMatcher.QueryParameterMatcherBuilder;

/**
 * Wealthfront Expectations extend expectations with additional matchers and methods.
 * <p/>
 * Use it just like Expectations, but with less pounding on Ctrl-1:
 * <pre>
 * mockery.checking(new WExpectations() {{
 *   oneOf(executor).invoke(with(query(new MyAwesomeQuery())));
 *   will(returnValue(true));
 * }});
 * </pre>
 */
public class WExpectations extends Expectations {

  public <T> T withArg(Matcher<T> matcher) {
    return super.with(matcher);
  }

  public <T> T withArg(T t) {
    return super.with(t);
  }

  /**
   * Caution! This method doesn't work with primitives, because it returns null.
   * There is no way to make type inference work and have it return a value of the correct primitive type.
   * Use with(any(Integer.class)) or similar instead.
   */
  public <T> T withAnything() {
    return super.with(new IsAnything<T>());
  }

  public <T> T withNull() {
    return super.with(new IsNull<T>());
  }

  public <T> T withNonNull() {
    return super.with(new IsNot<T>(new IsNull<>()));
  }

  @SuppressWarnings("varargs")
  @SafeVarargs
  public static <T> Matcher<T> allOf(Matcher<? super T>... matchers) {
    return Matchers.allOf(matchers);
  }

  public static Action returnConsecutive(Object... returnValues) {
    Action[] actions = new Action[returnValues.length];
    for (int i = 0; i < returnValues.length; i++) {
      actions[i] = returnValue(returnValues[i]);
    }
    return onConsecutiveCalls(actions);
  }

  public static Action returnWithId(final Id<?> id) {
    return new CustomAction("return with id set") {
      @Override
      public Object invoke(Invocation invocation) throws Throwable {
        Object object = invocation.getParameter(0);
        Class<?> klazz = object.getClass();
        while (klazz.getSuperclass() != AbstractHibernateEntity.class) {
          if (klazz.getSuperclass() == null) {
            throw new IllegalStateException(format("%s not a subclass of AbstractHibernateEntity", object));
          }
          klazz = klazz.getSuperclass();
        }
        Field field = klazz.getDeclaredField("id");
        field.setAccessible(true);
        field.set(object, id);
        return id;
      }
    };
  }

  public static <T> Action returnResultOf(String description, Supplier<T> supplier) {
    return new CustomAction(description) {
      @Override
      public Object invoke(Invocation invocation) throws Throwable {
        return supplier.get();
      }
    };
  }

  public static <T> Action returnResultOf(Supplier<T> supplier) {
    return returnResultOf("return the result of a supplier", supplier);
  }

  public static <T extends Query<U>, U> com.kaching.platform.testing.QueryStringMatcher<T> query(Class<T> queryClass, String... params) {
    return new com.kaching.platform.testing.QueryStringMatcher<>(queryClass, params);
  }

  @SafeVarargs
  public static <T extends Query<U>, U> QueryStringMatcherMatcher<T> query(Class<T> queryClass, Matcher<String>... params) {
    return new QueryStringMatcherMatcher<>(queryClass, params);
  }

  @SafeVarargs
  public static <T extends Query<U>, U> QueryMatcher<T> query(Class<T> queryClass, Option<String>... params) {
    return QueryMatcher.query(queryClass, params);
  }

  /**
   * Uses a builder to construct a QueryParamMatcherMatcher, where a custom Matcher can be supplied for every query argument.
   * Example:
   * <pre>
   * oneOf(smartClient).invoke(with(queryWithParams(b -> new MyQuery(b.with("arg1"), b.with("arg 2")))));
   * </pre>
   */
  public static <T extends Query<?>> QueryParamMatcherMatcher<T> queryWithParams(Function<QueryParameterMatcherBuilder, T> function) {
    return QueryParamMatcherMatcher.query(function);
  }

  public static <T extends Query<U>, U> QueryMatcher<T> query(T query) {
    return QueryMatcher.query(query);
  }

  public static <T extends Query<?>> QueryMatcher<T> queryFlexPrecision(T query) {
    return QueryMatcher.queryFlexPrecision(query);
  }

  public static <T extends Query<U>, U> QueryMatcher<T> queryWithUnorderedParams(T query) {
    return QueryMatcher.queryWithUnorderedParams(query);
  }

  public static <T extends Query<U>, U> QueriesMatcher<T, U> queries(List<T> queries) {
    return QueriesMatcher.queries(queries);
  }

  @SuppressWarnings("varargs")
  @SafeVarargs
  public static <T extends Query<U>, U> QueriesMatcher<T, U> queries(T... queries) {
    return QueriesMatcher.queries(queries);
  }

  public static <T extends Query<U>, U> UnorderedQueriesMatcher<T, U> unorderedQueries(Collection<T> queries) {
    return UnorderedQueriesMatcher.unorderedQueries(queries);
  }

  @SuppressWarnings("varargs")
  @SafeVarargs
  public static <T extends Query<U>, U> UnorderedQueriesMatcher<T, U> unorderedQueries(T... queries) {
    return UnorderedQueriesMatcher.unorderedQueries(queries);
  }

  public static <K, V extends Query<T>, T> QueryMapMatcher<K, V, T> queryMap(Map<K, V> expected) {
    return QueryMapMatcher.queryMap(expected);
  }

  public static Matcher<Exception> exception(Class<? extends Exception> exceptionType, String messageStub) {
    return new TypeSafeMatcher<Exception>() {
      @Override
      public void describeTo(Description description) {
        description.appendText(Strings.format("Exception of type %s containing the message \"%s\"",
            exceptionType, messageStub));
      }

      @Override
      public boolean matchesSafely(Exception exception) {
        return exception.getClass() == exceptionType && exception.getMessage().contains(messageStub);
      }
    };
  }

  @SuppressWarnings({"rawtypes", "unchecked"})
  public static <T> Matcher<Collection<T>> collection(final Matcher<T> matcher, final int count) {
    return new TypeSafeMatcher() {
      @Override
      public void describeTo(Description description) {
        description.appendText("each item matching").appendValue(matcher);
      }

      @Override
      public boolean matchesSafely(Object item) {
        Collection<T> collection = (Collection) item;
        if (collection.size() != count) {
          return false;
        }
        for (T t : collection) {
          if (!matcher.matches(t)) {
            return false;
          }
        }
        return true;
      }
    };
  }

  protected static <T> Matcher<Option<T>> none(Class<T> klass) {
    return new TypeSafeMatcher<Option<T>>() {
      @Override
      public void describeTo(Description description) {
        description.appendText("none");
      }

      @Override
      protected boolean matchesSafely(Option<T> param) {
        return param.isEmpty();
      }
    };
  }

  @SuppressWarnings("varargs")
  @SafeVarargs
  public static <T> Action returnList(T... values) {
    return returnValue(Arrays.asList(values));
  }

  @SuppressWarnings("varargs")
  @SafeVarargs
  protected static <T> Action returnSet(T... values) {
    // Preserve order to make testing easier
    return returnValue(newLinkedHashSet(newArrayList(values)));
  }

  protected static <T> Action returnSome(T value) {
    return returnValue(Option.some(value));
  }

  protected static <T> Action returnNone() {
    return returnValue(Option.<T>none());
  }

  protected static <T> Action returnOption(T value) {
    return returnValue(Option.of(value));
  }

  protected static <T> Action callConsumerParameterWithValues(int parameterIndex, Iterable<T> values) {
    return new Action() {
      @Override
      public void describeTo(Description description) {
        description.appendText(Strings.format("call consumer parameter %s with values %s", parameterIndex, values));
      }

      @Override
      @SuppressWarnings("unchecked")
      public Object invoke(Invocation invocation) {
        Consumer<T> consumer = (Consumer<T>) invocation.getParameter(parameterIndex);
        for (T value : values) {
          consumer.accept(value);
        }
        return null;
      }
    };
  }

  /**
   * {@link Expectations#any(Class)} is a typed-safe variant of {@link Expectations#anything()}
   * which is probably not what you expect.
   */
  protected static <T> Matcher<T> instanceOf(Class<T> type) {
    return Matchers.instanceOf(type);
  }

  public static <T extends HibernateEntity> IdSettingSideEffect<T> setId(Id<T> id) {
    return new IdSettingSideEffect<>("setting id", id);
  }

  public static <T extends HibernateEntity> IdSettingSideEffect<T> setId(long id) {
    return setId(Id.of(id));
  }

  /**
   * Note that this requires a public setId (@VisibleForTesting is okay)
   */
  static class IdSettingSideEffect<T extends com.kaching.platform.hibernate.HibernateEntity>
      extends org.jmock.lib.action.CustomAction {

    private final Id<T> id;

    IdSettingSideEffect(String description, Id<T> id) {
      super(description);
      this.id = id;
    }

    @Override
    public Object invoke(Invocation invocation) throws Throwable {
      for (Object o : invocation.getParametersAsArray()) {
        if (AbstractHibernateEntity.class.isAssignableFrom(o.getClass())) {
          Class<? extends Object> actualClass = o.getClass();
          Method method = actualClass.getMethod("setId", Id.class);
          method.invoke(o, id);
          return id;
        }
      }
      return null;
    }

  }

  public <E extends HibernateEntity> void setMockRepositoryContents(Repository<E> repository, Collection<E> entities) {
    Map<Id<E>, E> entitiesById = entities.stream().collect(toMapWithKey(entity -> Id.of(entity.getId())));
    setMockRepositoryContents(repository, entitiesById);
  }

  public <E extends HibernateEntity> void setMockRepositoryContents(
      Repository<E> repository,
      Map<Id<E>, E> entitiesById) {
    allowing(repository);
    InvocationExpectationBuilder builder = currentBuilder();
    builder.of(repository);
    builder.method(new TypeSafeMatcher<Method>() {
      @Override
      protected boolean matchesSafely(Method item) {
        return ImmutableSet.of("get", "getOrThrow", "getAll", "getAllIds").contains(item.getName());
      }

      @Override
      public void describeTo(Description description) {
        description.appendText("{get|getOrThrow|getAll|getAllIds}");
      }
    });
    will(new Action() {
      @Override
      public void describeTo(Description description) {
        description.appendText(format("returns entities from set %s", entitiesById.keySet()));
      }

      @SuppressWarnings("unchecked")
      @Override
      public Object invoke(Invocation invocation) throws Throwable {
        Id<E> id;
        switch (invocation.getInvokedMethod().getName()) {
          case "get":
            Object parameter = invocation.getParameter(0);
            if (parameter instanceof Id) {
              id = (Id<E>) invocation.getParameter(0);
              return entitiesById.get(id);
            } else {
              Collection<Id<E>> ids = (Collection<Id<E>>) parameter;
              return ids.stream()
                  .filter(entitiesById::containsKey)
                  .map(entitiesById::get)
                  .collect(toList());
            }
          case "getOrThrow":
            id = (Id<E>) invocation.getParameter(0);
            if (!entitiesById.containsKey(id)) {
              throw new NotFoundException(format("Could not find entity with Id %s in mocked repository", id));
            }
            return entitiesById.get(id);
          case "getAll":
            return ImmutableList.copyOf(entitiesById.values());
          case "getAllIds":
            return ImmutableList.sortedCopyOf(entitiesById.keySet());
          default:
            throw new IllegalStateException("Method on stubbed repository not supported: " + invocation.getInvokedMethod().getName());
        }
      }
    });
  }

  /**
   * The semantics of scala's default parameters make it nearly impossible to use our standard jmock patterns in scala
   * code.

   * The call to sparkRunner#readLastEmrInput has 4 parameters, two of which are always specified, two of which have
   * default values of false (and are generally left out).

   * The following code:

   * mockery.checking(new WExpectations() {{
   *    oneOf(mockSparkRunner).readLastEmrInput(classOf[Trn], IcdbRegularTrnsDailyEmr.tapInfo)
   *    will(returnValue(sc.emptyRdd))
   * }}

   * fails when it hits this line in production:

   * sparkRunner.readLastEmrInput(classOf[Trn], IcdbRegularTrnsDailyEmr.tapInfo))

   * The error we see is an unexpected invocation of sparkRunner.readLastEmrInput$default$3() which isn't even a method
   * on that object! The reason for this is that scala runs compiled code when computing default values, which are
   * basically hidden methods on an object. This makes mocking any object that has methods with default values
   * very difficult.

   * The API below is an attempt at making this slightly easier to work around. The framework tells the mockery to
   * expect some number of compiled method calls starting with the method name provided. The caller specifies the
   * method that will be called that has default parameters, as well as the mock object that method will be called on
   * and the number of times that method will be called. The caller then specifies the value that should be returned
   * for each default parameter (which will be used in the expectation of the call), as well the index (one indexed) in
   * the parameter list for each parameter.

   * As an example, we'd fix the broken expectation above by writing:

   * mockery.checking(new WExpectations() {{
   *   scalaMethod("readLastEmrInput")
   *     .calledOn(mockSparkRunner)
   *     .numberOfTimes(1)
   *     .hasDefaultParameters(
   *        new DefaultParameter(false, 3),
   *        new DefaultParameter(false, 4)
   *      )
   *   oneOf(mockSparkRunner).readLastEmrInput(classOf[Trn], IcdbRegularTrnsDailyEmr.tapInfo)
   *   will(returnValue(sc.emptyRdd))
   * }}

   * In some cases, things can be more complex. Some parameters with default values may themselves have parameters.
   * For now, to keep things simple, we'll add an "anything()" matcher for each "sub-parameter.

   * RDD#coalesce is a good example of this. It has one required parameter (a boolean), a boolean default parameter,
   * an Option[String] default parameter, and a default Ordering, which is a parameter that itself takes 3 parameters.

   * To run the following code on a mock rdd:

   * mockRdd.coalesce(1)

   * We would specify:

   * mockery.checking(new WExpectations() {{
   *    scalaMethod("coalesce")
   *     .calledOn(mockRdd)
   *     .numberOfTime(1)
   *     .hasDefaultParameters(
   *        new DefaultParameter(false, 2),
   *        new DefaultParameter(null, 3),
   *        new DefaultParameter(null, 4, 3)
   *      )
   *   oneOf(mockRdd).coalesce(1)
   *   will(returnValue(sc.emptyRdd))
   * }}

   */

  public ScalaDefaultParameterExpectationMethod scalaMethod(String scalaMethodName) {
    return new ScalaDefaultParameterExpectationMethod(scalaMethodName);
  }

  public class ScalaDefaultParameterExpectationMethod {

    private final String methodName;

    private ScalaDefaultParameterExpectationMethod(String methodName) {
      this.methodName = methodName;
    }

    public <T> ScalaDefaultParameterExpectationMethodAndReceiver<T> calledOn(T mockObject) {
      return new ScalaDefaultParameterExpectationMethodAndReceiver<>(methodName, mockObject);
    }

  }

  public class ScalaDefaultParameterExpectationMethodAndReceiver<T> {

    private final String methodName;
    private final T receiver;

    private ScalaDefaultParameterExpectationMethodAndReceiver(String methodName, T receiver) {
      this.methodName = methodName;
      this.receiver = receiver;
    }

    public ScalaDefaultParameterExpectationMethodAndReceiverAndCount<T> numberOfTimes(int numberOfTimes) {
      return new ScalaDefaultParameterExpectationMethodAndReceiverAndCount<>(methodName, receiver, numberOfTimes);
    }

  }

  public class ScalaDefaultParameterExpectationMethodAndReceiverAndCount<T> {

    private final String methodName;
    private final T receiver;
    private final int count;

    private ScalaDefaultParameterExpectationMethodAndReceiverAndCount(String methodName, T receiver, int count) {
      this.methodName = methodName;
      this.receiver = receiver;
      this.count = count;
    }

    public void hasDefaultParameters(DefaultParameter... parameters) {
      for (int i = 0; i < count; i++) {
        for (DefaultParameter parameter : parameters) {
          oneOf(receiver);
          currentBuilder().method(Strings.format("%s\\$default\\$%s()", methodName, parameter.getParameterIndex()));
          if (parameter.getNumberOfParameters() == 0) {
            currentBuilder().withNoArguments();
          } else {
            for (int j = 0; j < parameter.getNumberOfParameters(); j++) {
              currentBuilder().addParameterMatcher(anything());
            }
          }
          currentBuilder().setAction(returnValue(parameter.getReturnValue()));
          currentBuilder().setCardinality(Cardinality.exactly(1));
          currentBuilder().checkWasFullySpecified();
        }
      }
    }
  }

  public static class DefaultParameter {

    private final Object returnValue;
    private final int parameterIndex;
    private final int numberOfParameters;

    public DefaultParameter(Object returnValue, int parameterIndex) {
      this(returnValue, parameterIndex, 0);
    }

    public DefaultParameter(Object returnValue, int parameterIndex, int numberOfParameters) {
      this.returnValue = returnValue;
      this.parameterIndex = parameterIndex;
      this.numberOfParameters = numberOfParameters;
    }

    public Object getReturnValue() {
      return returnValue;
    }

    public int getParameterIndex() {
      return parameterIndex;
    }

    public int getNumberOfParameters() {
      return numberOfParameters;
    }

  }

}
