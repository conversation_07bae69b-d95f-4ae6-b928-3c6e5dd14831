package com.kaching.platform.zk.degraded;

import static com.kaching.platform.zk.degraded.DegradedModeStatus.DEGRADED_MODE_STATUS_MARSHALLER;
import static com.kaching.platform.zk.degraded.DegradedModeStatus.TrafficRestrictionLevel.NONE;
import static com.kaching.platform.zk.degraded.DegradedModeStatus.TrafficRestrictionLevel.PARTIAL;
import static com.kaching.platform.zk.degraded.DegradedModeStatus.inactiveDegradedModeStatus;
import static com.kaching.platform.zk.degraded.DegradedModeStatusProvider.API_DEGRADED_PAGERS;
import static com.kaching.platform.zk.degraded.DegradedModeStatusProvider.DegradationReason.C3P0_WAITING_THREADS;
import static com.kaching.platform.zk.degraded.DegradedModeStatusProvider.DegradationReason.MANUAL;
import static com.kaching.platform.zk.degraded.DegradedModeStatusProvider.DegradationReason.QUERY_ENGINE_ACTIVE_THREADS;
import static com.kaching.platform.zk.degraded.DegradedModeStatusProvider.SFE_DEGRADED_PAGERS;
import static com.kaching.util.mail.Pager.Device.PAGER_DAVID;
import static com.kaching.util.mail.Pager.Device.PAGER_IOS;
import static com.kaching.util.mail.Pager.Device.PAGER_LINKING_SERVICES;
import static com.kaching.util.mail.Pager.Device.PAGER_SITE_HEALTH_AND_ONLINE_SERVICES;
import static com.kaching.util.mail.Pager.Device.PAGER_WEB;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static org.junit.Assert.assertEquals;

import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.kaching.Author;
import com.kaching.platform.common.Strings;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.guice.KachingServices.BANK;
import com.kaching.platform.guice.KachingServices.CBUS;
import com.kaching.platform.guice.KachingServices.LINK;
import com.kaching.platform.guice.KachingServices.UM;
import com.kaching.platform.mattermost.HipChatMention;
import com.kaching.platform.mattermost.MattermostPublisher;
import com.kaching.platform.mattermost.OnCallHipChatMentioner;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.mail.Pager;

public class ZkDegradedModeStatusProviderImplTest {

  private static final ServiceDescriptor descriptor = new ServiceDescriptor(ServiceId.of("um0"), UM.class, null);

  private final WFMockery mockery = Mockeries.mockery(true);
  private final Pager pager = mockery.mock(Pager.class);
  private final MattermostPublisher mattermostPublisher = mockery.mock(MattermostPublisher.class);
  private final OnCallHipChatMentioner hipChatMentioner = mockery.mock(OnCallHipChatMentioner.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void setSfeDegraded() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(false, PARTIAL, true);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_SITE_HEALTH_AND_ONLINE_SERVICES);

      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_WEB);

      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, SFE_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void setSfeDegraded_blockingScrapers_clientsAffected() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, PARTIAL, true);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_SITE_HEALTH_AND_ONLINE_SERVICES);

      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_WEB);

      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, SFE_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void setSfeDegraded_restrictingUserTraffic_clientImpact_pages() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, PARTIAL, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_SITE_HEALTH_AND_ONLINE_SERVICES);

      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_WEB);

      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, SFE_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void setSfeDegraded_restrictingMarketingPages_clientImpact_pages() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, NONE, true);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_SITE_HEALTH_AND_ONLINE_SERVICES);

      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format("SFE degraded mode has been enabled (%s): manually enabled on um0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_WEB);

      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, SFE_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void setSfeDegraded_blockingScrapers_noClientImpact_notPage_stillPublishesToMattermost() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, SFE_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void setSfeDegraded_withDegradationReason() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(false, PARTIAL, true);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.descriptor = new ServiceDescriptor(ServiceId.of("link1"), LINK.class, null);
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format(
              "SFE degraded mode has been enabled (%s): c3p0 waiting threads exceeded threshold on link1 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_DAVID);

      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format(
              "SFE degraded mode has been enabled (%s): c3p0 waiting threads exceeded threshold on link1 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_LINKING_SERVICES);

      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider
        .setSfeDegradedStatus(degradedModeStatus, ImmutableSet.of(PAGER_DAVID, PAGER_LINKING_SERVICES),
            C3P0_WAITING_THREADS);
  }

  @Test
  public void setSfeDegraded_blockingScrapers_withDegradationReason() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, PARTIAL, true);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.descriptor = new ServiceDescriptor(ServiceId.of("link1"), LINK.class, null);
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format(
              "SFE degraded mode has been enabled (%s): c3p0 waiting threads exceeded threshold on link1 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_DAVID);

      oneOf(pager).alert(
          "SFE Degraded Mode Enabled",
          Strings.format(
              "SFE degraded mode has been enabled (%s): c3p0 waiting threads exceeded threshold on link1 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_LINKING_SERVICES);

      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider
        .setSfeDegradedStatus(degradedModeStatus, ImmutableSet.of(PAGER_DAVID, PAGER_LINKING_SERVICES),
            C3P0_WAITING_THREADS);
  }

  @Test
  public void setSfeDegraded_noPagerDevice_doesNotPage() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(provider.sfeSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.sfeSync).set(degradedModeStatus);

      oneOf(hipChatMentioner).mentionCurrentOnCallFor(Author.WEB_TEAM);
      will(returnSome(HipChatMention.of("room", "harry potter", emptyList())));

      oneOf(mattermostPublisher).publish(
          "web-ops",
          "ZkDegradedModeStatusProviderImpl",
          Strings.format("SFE Degraded mode status changed: %s\n@harry potter", DEGRADED_MODE_STATUS_MARSHALLER.marshall(degradedModeStatus).toPrettyPrintString())
      );
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, emptySet(), MANUAL);
  }

  @Test
  public void setSfeDegraded_blockingScrapers_degradedModeAlreadyActive_doesNotPage() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(provider.sfeSync).get();
      will(returnValue(new DegradedModeStatus(false, PARTIAL, true)));

      oneOf(provider.sfeSync).set(degradedModeStatus);
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, SFE_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void setSfeDegraded_degradedModeAlreadyActive_doesNotPage() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(false, PARTIAL, true);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(provider.sfeSync).get();
      will(returnValue(new DegradedModeStatus(false, PARTIAL, true)));

      oneOf(provider.sfeSync).set(degradedModeStatus);
    }});

    provider.setSfeDegradedStatus(degradedModeStatus, SFE_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void getSfeDegradedModeStatus() {
    DegradedModeStatus degradedModeStatus1 = new DegradedModeStatus(true, NONE, false);
    DegradedModeStatus degradedModeStatus2 = new DegradedModeStatus(false, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.sfeSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      exactly(3).of(provider.sfeSync).get();
      will(onConsecutiveCalls(
          returnValue(null),
          returnValue(degradedModeStatus1),
          returnValue(degradedModeStatus2)));
    }});

    assertEquals(inactiveDegradedModeStatus(), provider.getSfeDegradedModeStatus());
    assertEquals(degradedModeStatus1, provider.getSfeDegradedModeStatus());
    assertEquals(degradedModeStatus2, provider.getSfeDegradedModeStatus());
  }

  @Test
  public void setApiDegraded_blockingScrapers_noClientImpact_notAlert() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.apiSync = mockery.mock(ZkDegradedModeStatusSync.class);
    provider.descriptor = new ServiceDescriptor(ServiceId.of("cbus0"), CBUS.class, null);
    provider.region = "ny4";

    mockery.checking(new WExpectations() {{
      oneOf(provider.apiSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.apiSync).set(degradedModeStatus);
    }});

    provider.setApiDegradedStatus(degradedModeStatus, API_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void setApiDegraded_withDegradationReason() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, PARTIAL, true);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.apiSync = mockery.mock(ZkDegradedModeStatusSync.class);
    provider.descriptor = new ServiceDescriptor(ServiceId.of("bank0"), BANK.class, null);

    mockery.checking(new WExpectations() {{
      oneOf(pager).alert(
          "API Degraded Mode Enabled",
          Strings.format(
              "API degraded mode has been enabled (%s): active query engine threads exceeded threshold on bank0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_DAVID);

      oneOf(pager).alert(
          "API Degraded Mode Enabled",
          Strings.format(
              "API degraded mode has been enabled (%s): active query engine threads exceeded threshold on bank0 in sv2 region",
              degradedModeStatus.toString()),
          PAGER_IOS);

      oneOf(provider.apiSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.apiSync).set(degradedModeStatus);
    }});

    provider.setApiDegradedStatus(degradedModeStatus, ImmutableSet.of(PAGER_DAVID, PAGER_IOS),
        QUERY_ENGINE_ACTIVE_THREADS);
  }

  @Test
  public void setApiDegraded_noPagerDevice_doesNotPage() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.apiSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(provider.apiSync).get();
      will(returnValue(inactiveDegradedModeStatus()));

      oneOf(provider.apiSync).set(degradedModeStatus);
    }});

    provider.setApiDegradedStatus(degradedModeStatus, emptySet(), MANUAL);
  }

  @Test
  public void setApiDegraded_degradedModeAlreadyActive_doesNotPage() throws Exception {
    DegradedModeStatus degradedModeStatus = new DegradedModeStatus(true, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.apiSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      oneOf(provider.apiSync).get();
      will(returnValue(new DegradedModeStatus(false, PARTIAL, true)));

      oneOf(provider.apiSync).set(degradedModeStatus);
    }});

    provider.setApiDegradedStatus(degradedModeStatus, API_DEGRADED_PAGERS, MANUAL);
  }

  @Test
  public void getApiDegradedModeStatus() {
    DegradedModeStatus degradedModeStatus1 = new DegradedModeStatus(true, NONE, false);
    DegradedModeStatus degradedModeStatus2 = new DegradedModeStatus(false, NONE, false);
    ZkDegradedModeStatusProviderImpl provider = getProvider();
    provider.apiSync = mockery.mock(ZkDegradedModeStatusSync.class);

    mockery.checking(new WExpectations() {{
      exactly(3).of(provider.apiSync).get();
      will(onConsecutiveCalls(
          returnValue(null),
          returnValue(degradedModeStatus1),
          returnValue(degradedModeStatus2)));
    }});

    assertEquals(inactiveDegradedModeStatus(), provider.getApiDegradedModeStatus());
    assertEquals(degradedModeStatus1, provider.getApiDegradedModeStatus());
    assertEquals(degradedModeStatus2, provider.getApiDegradedModeStatus());
  }

  private ZkDegradedModeStatusProviderImpl getProvider() {
    ZkDegradedModeStatusProviderImpl provider = new ZkDegradedModeStatusProviderImpl();
    provider.pager = pager;
    provider.mattermostPublisher = mattermostPublisher;
    provider.hipChatMentioner = hipChatMentioner;
    provider.descriptor = descriptor;
    provider.region = "sv2";
    return provider;
  }

}
