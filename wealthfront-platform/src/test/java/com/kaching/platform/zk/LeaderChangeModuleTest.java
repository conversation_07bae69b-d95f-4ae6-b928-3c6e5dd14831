package com.kaching.platform.zk;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.google.inject.Guice.createInjector;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.HashSet;
import java.util.Set;

import org.junit.Test;

import com.google.inject.Inject;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.zk.LeaderChangeNotifier.LeaderListener;

public class LeaderChangeModuleTest {

  @Test
  public void configure() {
    Mockeries.WFMockery mock = Mockeries.mockery();
    LeaderListener listener1 = mock.mock(LeaderListener.class, "one");
    LeaderListener listener2 = mock.mock(LeaderListener.class, "two");
    Tester test = createInjector(
        new LeaderChangeModule().withListeners(listener1),
        new LeaderChangeModule().withListeners(listener2),
        new LeaderChangeModule().withListeners(TestLeaderListener.class))
        .getInstance(Tester.class);

    Set<LeaderListener> listeners = new HashSet<>(test.listeners);
    assertEquals(3, listeners.size());
    assertTrue(listeners.remove(listener1));
    assertTrue(listeners.remove(listener2));
    assertEquals(1, listeners.size());
    assertEquals(TestLeaderListener.class, getOnlyElement(listeners).getClass());
  }

  static class Tester {

    @Inject Set<LeaderListener> listeners;

  }

  static class TestLeaderListener implements LeaderListener {

    @Override
    public void onBecameLeader() {

    }

    @Override
    public void onLostLeader() {

    }

  }

}