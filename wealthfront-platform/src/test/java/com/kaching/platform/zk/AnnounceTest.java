package com.kaching.platform.zk;

import static org.junit.Assert.assertEquals;

import java.io.IOException;

import org.junit.After;
import org.junit.Test;

import com.kaching.platform.common.Option;
import com.kaching.platform.discovery.LocalAnnouncement;
import com.kaching.platform.discovery.Status;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;

public class AnnounceTest {

  private final WFMockery mockery = Mockeries.mockery();

  private final LocalAnnouncement localAnnouncement = mockery.mock(LocalAnnouncement.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_withoutForceAnnouncement() throws IOException {
    mockery.checking(new WExpectations() {{
      oneOf(localAnnouncement).announce();
      oneOf(localAnnouncement).getStatus();
      will(returnValue(Status.DEGRADED));
    }});

    assertEquals(Status.DEGRADED, getQuery(Option.none()).process());
  }

  @Test
  public void process_withForceAnnouncement() throws IOException {
    mockery.checking(new WExpectations() {{
      oneOf(localAnnouncement).announce();
      oneOf(localAnnouncement).setStatus(Status.FAILED);
      oneOf(localAnnouncement).getStatus();
      will(returnValue(Status.FAILED));
    }});

    assertEquals(Status.FAILED, getQuery(Option.some(Status.FAILED)).process());
  }

  private Announce getQuery(Option<Status> maybeStatusToForceAnnounce) {
   Announce query = new Announce(maybeStatusToForceAnnounce);
   query.localAnnouncement = localAnnouncement;
   return query;
  }

}