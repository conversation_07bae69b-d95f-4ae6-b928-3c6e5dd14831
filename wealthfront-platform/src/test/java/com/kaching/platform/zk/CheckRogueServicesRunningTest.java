package com.kaching.platform.zk;

import static java.util.Collections.emptyList;
import static org.junit.Assert.assertEquals;

import java.net.URI;
import java.util.List;

import org.junit.After;
import org.junit.Test;

import com.kaching.platform.discovery.Discovery;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;

public class CheckRogueServicesRunningTest {

  private static final ServiceDescriptor nop =  new ServiceDescriptor(new ServiceId("nop1"), KachingServices.NOP.class, URI.create("http://127.0.0.1/"));
  private final Mockeries.WFMockery mockery = Mockeries.mockery(true);
  private final Discovery discovery = mockery.mock(Discovery.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_noRogueServices() {
    mockery.checking(new WExpectations() {{
      oneOf(discovery).getRogueAnnouncements();
      will(returnValue(emptyList()));
    }});

    assertEquals("OK", getQuery().process());
  }

  @Test
  public void process_hasRogueServices() {
    mockery.checking(new WExpectations() {{
      oneOf(discovery).getRogueAnnouncements();
      will(returnValue(List.of(nop)));
    }});

    assertEquals("WARN services up but not in manifest: " +
        "[ServiceDescriptor[id=nop1,kind=class com.kaching.platform.guice.KachingServices$NOP," +
        "uri=http://127.0.0.1/,status=UP,debugPort=null,failureReason=null,revision=null]]", getQuery().process());
  }

  private CheckRogueServicesRunning getQuery() {
    CheckRogueServicesRunning query = new CheckRogueServicesRunning();
    query.discovery = discovery;
    return query;
  }
}