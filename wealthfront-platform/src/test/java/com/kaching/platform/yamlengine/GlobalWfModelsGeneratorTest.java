package com.kaching.platform.yamlengine;

import static com.kaching.api.GeneratedFileAssertions.getAllNewFiles;
import static com.kaching.platform.util.Closer.closeQuietly;
import static java.util.stream.Collectors.toSet;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;

import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.jimfs.Jimfs;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.kaching.fs.ExposeEntitiesClassLoader;
import com.kaching.util.io.UncheckedIo;

public class GlobalWfModelsGeneratorTest {

  private static FileSystem fakeFs;

  @Before
  public void before() throws IOException {
    fakeFs = Jimfs.newFileSystem();
    Files.createDirectories(fakeFs.getPath("example/target/classes"));
  }

  @After
  public void after() {
    closeQuietly(fakeFs);
  }

  @Test
  public void main_invalidArguments() throws IOException {
    try {
      TwoLattesYamlGenerator.main("buildDirectory1", "buildDirectory2");
      fail();
    } catch (IllegalArgumentException e) {
      assertEquals("exactly one argument required (build directory)", e.getMessage());
    }
  }

  @Test
  public void generate() throws IOException {
    Files.createDirectories(fakeFs.getPath("example/target/classes/subfolder"));
    Set<Path> exampleRepoClassPaths =
        Files.walk(FileSystems.getDefault().getPath("target/test-classes/com/kaching/platform/yamlengine/testentities"))
            .filter(Files::isRegularFile)
            .collect(toSet());
    for (Path classPath : exampleRepoClassPaths) {
      Files.copy(classPath, fakeFs.getPath("example/target/classes/subfolder/" + classPath.getFileName()));
    }

    getGenerator().generate();
    String expected = IOUtils.toString(getClass().getResourceAsStream("TwoLattesYamlGenerator_ExpectedResult.yaml"), StandardCharsets.UTF_8);
    assertEquals(expected, readFile("./src/main/resources/swagger-definitions.yaml"));

    List<String> allFiles = getAllNewFiles(fakeFs);
    assertEquals(ImmutableList.of(
        "./src/main/java/com/wealthfront/auto/types/global/AbstractAccountOverview.java",
        "./src/main/java/com/wealthfront/auto/types/global/CashAccountOverview.java",
        "./src/main/java/com/wealthfront/auto/types/global/InvestmentAccountOverview.java",
        "./src/main/java/com/wealthfront/auto/types/global/NestedClass.java",
        "./src/main/java/com/wealthfront/auto/types/global/TestingNested.java",
        "./src/main/java/com/wealthfront/auto/types/global/UnusedEnum.java",
        "./src/main/resources/swagger-definitions.yaml"
    ), allFiles);
  }

  private String readFile(String fname) {
    return String.join("\n", UncheckedIo.get(() -> Files.readAllLines(fakeFs.getPath(fname))));
  }
  
  private GlobalWfModelsGenerator getGenerator() {
    GlobalWfModelsGenerator generator = new GlobalWfModelsGenerator("example");
    generator.fs = fakeFs;
    Injector injector = Guice.createInjector();
    generator.exposeEntitiesClassLoader = injector.getInstance(ExposeEntitiesClassLoader.class);
    generator.javaJsonEntityGenerator.fs = fakeFs;
    generator.twoLattesYamlGenerator.fs = fakeFs;
    return generator;
  }
} 