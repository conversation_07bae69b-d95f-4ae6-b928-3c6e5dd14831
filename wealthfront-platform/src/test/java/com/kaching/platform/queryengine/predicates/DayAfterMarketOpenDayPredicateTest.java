package com.kaching.platform.queryengine.predicates;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import org.joda.time.LocalDate;
import org.junit.BeforeClass;
import org.junit.Test;

import com.wealthfront.entities.Market;

public class DayAfterMarketOpenDayPredicateTest {

  private static final DayAfterMarketOpenDayPredicate predicate = new DayAfterMarketOpenDayPredicate();

  @BeforeClass
  public static void setup() {
    predicate.market = Market.MARKET;
  }

  @Test
  public void testRegularWeek() {
    testHelper(new LocalDate(2014, 12, 15), false);
    testHelper(new LocalDate(2014, 12, 16), true);
    testHelper(new LocalDate(2014, 12, 20), true);
    testHelper(new LocalDate(2014, 12, 21), false);
  }

  @Test
  public void testHoliday() {
    testHelper(new LocalDate(2014, 12, 25), true);
    testHelper(new LocalDate(2014, 12, 24), true);
    testHelper(new LocalDate(2014, 12, 26), false);
    testHelper(new LocalDate(2015, 1, 1), true);
    testHelper(new LocalDate(2015, 1, 2), false);
    testHelper(new LocalDate(2015, 1, 3), true);
  }

  private void testHelper(LocalDate localDate, boolean b) {
    predicate.clock = () ->
        localDate.toDateTimeAtStartOfDay(ET).plusHours(7); // 7 AM - so before market open, just to be more general
    assertEquals(b, predicate.satisfied());
  }
  
}
