package com.kaching.platform.queryengine;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;

import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.kaching.platform.queryengine.postprocessors.QueryOutput;

public class WritingQueryDriverTest {

  private WritingQueryDriver driver;
  private Map<Key<?>, Object> relaxedCache;
  private ByteArrayOutputStream byteArrayOutputStream;

  @Before
  public void before() throws IOException {
    driver = new WritingQueryDriver(new QueryDriver() {
      @Override
      public <T> Object execute(
          Query<T> query, Map<Key<?>, Object> relaxedCache, Provider<? extends PostProcessor> postProcessor) {
        return null;
      }
    });
    ClearingStreamHolder clearingStreamHolder = new ClearingStreamHolder();
    clearingStreamHolder.getStream().write("HELLO".getBytes(Charsets.UTF_8));
    byteArrayOutputStream = new ByteArrayOutputStream();
    relaxedCache = ImmutableMap.of(
        Key.get(ClearingStreamHolder.class, QueryOutput.class), clearingStreamHolder,
        Key.get(OutputStream.class, QueryOutput.class), byteArrayOutputStream);
  }

  @Test
  public void copiesStreamOver() {
    assertNull(driver.execute(null, relaxedCache, null));
    assertEquals("HELLO", byteArrayOutputStream.toString());
  }

  @Test
  public void noOpOnNoStream() {
    relaxedCache = new HashMap<>();
    assertNull(driver.execute(null, relaxedCache, null));
  }

}
