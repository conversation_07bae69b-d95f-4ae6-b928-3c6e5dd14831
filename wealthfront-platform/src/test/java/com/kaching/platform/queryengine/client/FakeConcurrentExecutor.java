package com.kaching.platform.queryengine.client;

import static com.kaching.platform.common.Strings.format;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.kaching.platform.common.Pair;
import com.kaching.util.concurrent.ConcurrentExecutor;
import com.kaching.util.functional.Result;

public class FakeConcurrentExecutor implements ConcurrentExecutor {

  @Override
  public <I, T> CompletableFuture<List<Pair<I, Result<T>>>> executeConcurrently(
      Function<I, CompletableFuture<T>> executeItem, Collection<? extends I> items, int maxConcurrency,
      String stageName) {
    return executeConcurrently(executeItem, items, maxConcurrency);
  }

  @Override
  public <I, T> CompletableFuture<List<Pair<I, Result<T>>>> executeConcurrently(
      Function<I, CompletableFuture<T>> executeItem, Collection<? extends I> items, int maxConcurrency) {
    return CompletableFuture.completedFuture(items.stream()
        .map(item -> Pair.of(item, Result.compute(() -> executeItem.apply(item))))
        .map(pair -> {
          I item = pair.getLeft();
          Result<CompletableFuture<T>> futureCreationResult = pair.getRight();
          if (!futureCreationResult.isSuccess()) {
            return Pair.of(item, Result.<T>fail(futureCreationResult.getException().getOrThrow()));
          }

          CompletableFuture<T> future = futureCreationResult.getOrThrow();
          try {
            T resultValue = future.get();
            if (resultValue == null) {
              throw new IllegalStateException(format("null return value is not allowed for %s", item));
            }
            return Pair.of((I) pair.getLeft(), Result.success(resultValue));
          } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof Exception) {
              return Pair.of((I) pair.getLeft(), Result.<T>fail((Exception) cause));
            } else {
              return Pair.of((I) pair.getLeft(), Result.<T>fail(new RuntimeException(cause)));
            }
          } catch (InterruptedException e) {
            throw new RuntimeException(e);
          }
        })
        .collect(Collectors.toList()));
  }

}
