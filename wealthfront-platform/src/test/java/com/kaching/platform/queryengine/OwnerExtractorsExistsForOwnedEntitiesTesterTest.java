package com.kaching.platform.queryengine;

import static com.google.inject.Guice.createInjector;
import static com.kaching.platform.queryengine.InjectorTester.TEST_MODULE_ARGS;
import static com.kaching.platform.queryengine.Owner.owner;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.STRICT;
import static com.kaching.platform.queryengine.SoleOwnership.soleOwnership;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertThat;

import java.util.List;

import org.hibernate.Interceptor;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.jmock.Mockery;
import org.junit.Test;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.inject.AbstractModule;
import com.google.inject.Injector;
import com.google.inject.Module;
import com.kaching.platform.common.Errors;
import com.kaching.platform.guice.CommonModule;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.KcHibernateModule;
import com.kaching.platform.guice.OptionsModule;
import com.kaching.platform.guice.ServiceModuleBuilder;
import com.kaching.platform.guice.TestingApplicationOptions;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.interceptor.SqlMetadataInterceptor;
import com.kaching.platform.testing.Mockeries;
import com.kaching.user.UserId;

public class OwnerExtractorsExistsForOwnedEntitiesTesterTest {

  @Test
  public void runTests_noMissingOwnerExtractors() {
    Injector injector = getInjector(ValidQuery.class, ValidQueryWithoutOwned.class);
    OwnerExtractorsExistsForOwnedEntitiesTester tester =
        injector.getInstance(OwnerExtractorsExistsForOwnedEntitiesTester.class);
    tester.queryProxies = injector.getInstance(QueryProxies.class);
    Errors errors = new Errors();
    tester.runTests(errors, injector);
    assertThat(errors.hasErrors(), is(false));
  }

  @Test
  public void runTests_missingOwnerExtractors() {
    Injector injector = getInjector(InvalidQuery.class, ValidQueryWithoutOwned.class);
    OwnerExtractorsExistsForOwnedEntitiesTester tester =
        injector.getInstance(OwnerExtractorsExistsForOwnedEntitiesTester.class);
    tester.queryProxies = injector.getInstance(QueryProxies.class);
    Errors errors = new Errors();
    tester.runTests(errors, injector);
    assertThat(errors.hasErrors(), is(true));
    assertThat(Iterables.getOnlyElement(errors.getMessages()),
        is("The following types are missing OwnerExtractors: [java.lang.String=[class com.kaching.platform.queryengine.OwnerExtractorsExistsForOwnedEntitiesTesterTest$InvalidQuery]]"));
  }

  private Injector getInjector(Class<?>... queries) {
    TestingApplicationOptions options = new TestingApplicationOptions();
    options.parse(TEST_MODULE_ARGS);

    List<Module> modules = Lists.newArrayList(
        new QueryEngineModule(
            9000,
            STRICT,
            options,
            HibernateQueryDriver.class,
            queries),
        new OptionsModule(options),
        new CommonModule(options),
        new ServiceModuleBuilder(options).announceAs(KachingServices.BI.class).build(),
        new AbstractModule() {
          @Override
          protected void configure() {
            Mockery mockery = Mockeries.mockery();
            bind(SessionFactory.class).toInstance(mockery.mock(SessionFactory.class));

            bind(Session.class).toProvider(KcHibernateModule.SessionProvider.class);
            bind(Interceptor.class).to(SqlMetadataInterceptor.class);
          }
        },
        new OwnerExtractorModule().add(new OwnerExtractor<UserId>() {
          @Override
          public Ownership extract(UserId owned, DbSession session) {
            return soleOwnership(owner(owned));
          }
        }));

    return createInjector(modules);
  }

  static class ValidQuery extends AbstractQuery<Boolean> {

    ValidQuery(@Owned UserId userId) {
    }

    @Override
    public Boolean process() {
      return true;
    }

  }

  static class ValidQueryWithoutOwned extends AbstractQuery<Boolean> {

    ValidQueryWithoutOwned(String stringParam) {
    }

    @Override
    public Boolean process() {
      return true;
    }

  }

  static class InvalidQuery extends AbstractQuery<Boolean> {

    InvalidQuery(@Owned String entityWithoutOwnerExtractor) {
    }

    @Override
    public Boolean process() {
      return true;
    }

  }

}