package com.kaching.platform.queryengine;

import static com.kaching.platform.queryengine.ExceptionClassifier.QueryResultClassification.FAILURE;
import static com.kaching.platform.queryengine.ExceptionClassifier.QueryResultClassification.INVALID;
import static com.kaching.platform.queryengine.ExceptionClassifier.QueryResultClassification.SUCCESS;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_SESSION_ID_KEY;
import static com.kaching.platform.queryengine.QueryScope.EMPTY_CACHE;
import static com.wealthfront.test.Assert.assertSetEquals;
import static java.util.Collections.emptyMap;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.sameInstance;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.Timer;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import org.hamcrest.CustomTypeSafeMatcher;
import org.hamcrest.Matcher;
import org.jmock.Expectations;
import org.jmock.Mockery;
import org.jmock.api.Invocation;
import org.jmock.lib.action.CustomAction;
import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.util.Providers;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.kaching.platform.testing.WExpectations;

public class MonitoringQueryDriverTest {

  Mockery mockery;
  QueryDriver delegate;
  AnnotatedQueryW10E20 query;
  QueryProxies proxies;
  QueryEngineStatus status;
  ExceptionClassifier classifier;

  @Before
  public void setup() {
    proxies = new QueryProxies(new DefaultJsonMarshallerFactory(), null);
    mockery = new Mockery();
    delegate = mockery.mock(QueryDriver.class);
    query = new AnnotatedQueryW10E20();
    status = new QueryEngineStatus(Providers.of(new DateTime()));
    classifier = mockery.mock(ExceptionClassifier.class);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void execute() {
    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());
    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(query), with(cacheWithSessionId()), with(processorProvider));
      oneOf(classifier).classifyResult(query, null);
      will(returnValue(SUCCESS));
    }});
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), status, classifier),
        proxies);
    driver.execute(query, EMPTY_CACHE, processorProvider);
    assertEquals(1, status.getProcessedQueries());
  }

  @Test
  public void execute_ikq_success_skipsRecording() {
    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());
    Map<Key<?>, Object> relaxedCache = ImmutableMap.of(
        QueryEngineModule.REQUEST_HEADERS_KEY, Map.of("User-Agent", "ikq")
    );
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), status, classifier),
        proxies);

    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(query), with(cacheWithSessionId(relaxedCache)), with(processorProvider));
    }});

    driver.execute(query, relaxedCache, processorProvider);
    assertEquals(0, status.getProcessedQueries());
    assertEquals(0, status.getFailedQueries());
    assertEquals(0, status.getInvalidQueries());

    InvalidArgumentException exception400 = new InvalidArgumentException("400");
    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(query), with(cacheWithSessionId(relaxedCache)), with(processorProvider));
      will(throwException(exception400));
    }});

    try {
      driver.execute(query, relaxedCache, processorProvider);
    } catch (InvalidArgumentException ex) {
      assertEquals(exception400.getMessage(), ex.getMessage());
    }
    assertEquals(0, status.getProcessedQueries());
    assertEquals(0, status.getFailedQueries());
    assertEquals(0, status.getInvalidQueries());

    IllegalStateException exception500 = new IllegalStateException("400");
    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(query), with(cacheWithSessionId(relaxedCache)), with(processorProvider));
      will(throwException(exception500));
    }});
    try {
      driver.execute(query, relaxedCache, processorProvider);
    } catch (IllegalStateException ex) {
      assertEquals(exception500.getMessage(), ex.getMessage());
    }
    assertEquals(0, status.getProcessedQueries());
    assertEquals(0, status.getFailedQueries());
    assertEquals(0, status.getInvalidQueries());
  }

  @Test
  public void execute_cantUnregister() {
    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());
    mockery.checking(new Expectations() {{
      allowing(delegate).execute(with(query), with(cacheWithSessionId()), with(processorProvider));
    }});
    final AtomicInteger counter = new AtomicInteger(20);
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), new QueryEngineStatus(Providers.of(new DateTime())), new DefaultExceptionClassifier()) {

          @Override
          public void queryStopSuccessfully(QuerySession session, Object result) {
            assertEquals(query, session.getQuery());
            if (counter.decrementAndGet() > 0) {
              throw new RuntimeException("won't let you do it!");
            }
          }
        },
        proxies);
    driver.execute(query, EMPTY_CACHE, processorProvider);
    counter.set(5);
    driver.execute(query, EMPTY_CACHE, processorProvider);
    counter.set(-1);
    driver.execute(query, EMPTY_CACHE, processorProvider);
  }

  @Test
  public void execute_throwsExceptions() {
    final RuntimeException exception = new RuntimeException();
    mockery.checking(new WExpectations() {{
      oneOf(delegate).execute(with(query), with(cacheWithSessionId()), withNull());
      will(throwException(exception));
    }});
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), new QueryEngineStatus(Providers.of(new DateTime())),
            new DefaultExceptionClassifier()), proxies);
    try {
      driver.execute(query, EMPTY_CACHE, null);
    } catch (RuntimeException e) {
      assertThat(exception, sameInstance(exception));
    }
  }

  @Test
  public void slaAnnotations() {
    AnnotatedQueryW10E20 annotated = new AnnotatedQueryW10E20();
    Sla sla = proxies.getProxy(annotated.getClass()).getSla();
    assertEquals(10, sla.getWarn());
    assertEquals(20, sla.getError());

    UnAnnotatedQuery notAnnotated = new UnAnnotatedQuery();
    sla = proxies.getProxy(notAnnotated.getClass()).getSla();
    assertEquals(Sla.DEFAULT_SLA.getWarn(), sla.getWarn());
    assertEquals(Sla.DEFAULT_SLA.getError(), sla.getError());
  }

  @Test
  public void execute_failureToCast_doesNotThrow() {
    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());

    Map<Key<?>, Object> relaxedCache = ImmutableMap.of(
        QueryEngineModule.TRACE_KEY, "notATrace"
    );
    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(query), with(cacheWithSessionId(relaxedCache)), with(processorProvider));
      oneOf(classifier).classifyResult(query, null);
      will(returnValue(SUCCESS));
    }});
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), status, classifier),
        proxies);
    driver.execute(query, relaxedCache, processorProvider);
    assertEquals(1, status.getProcessedQueries());
  }

  @Test
  public void execute_logContext200() {
    Map<String, Object> logContextPutMap = new HashMap<>();
    Set<String> logContextRemoveSet = new HashSet<>();

    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());
    AnnotatedQueryW0E0 w0e0Query = new AnnotatedQueryW0E0();
    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(w0e0Query), with(cacheWithSessionId()), with(processorProvider));
      oneOf(classifier).classifyResult(w0e0Query, null);
      will(returnValue(SUCCESS));
    }});
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), status, classifier),
        proxies,
        logContextPutMap, logContextRemoveSet);
    driver.execute(w0e0Query, EMPTY_CACHE, processorProvider);
    assertEquals(1, status.getProcessedQueries());

    assertTrue(logContextPutMap.containsKey("ms"));
    assertTrue(logContextPutMap.containsKey("start"));
    Map<String, Object> expectedLogContextPutMap = ImmutableMap.of(
        "ms", logContextPutMap.get("ms"),
        "start", logContextPutMap.get("start"),
        "error", false,
        "httpStatusCode", 200
    );
    assertEquals(expectedLogContextPutMap, logContextPutMap);
    assertSetEquals(ImmutableSet.of("ms", "start", "error", "httpStatusCode"), logContextRemoveSet);
  }

  @Test
  public void execute_logContext400() {
    Map<String, Object> logContextPutMap = new HashMap<>();
    Set<String> logContextRemoveSet = new HashSet<>();

    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());
    AnnotatedQueryW0E0 w0e0Query = new AnnotatedQueryW0E0();
    InvalidArgumentException exception400 = new InvalidArgumentException("400");
    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(w0e0Query), with(cacheWithSessionId()), with(processorProvider));
      will(throwException(exception400));
      oneOf(classifier).classifyResult(w0e0Query, exception400);
      will(returnValue(INVALID));
    }});
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), status, classifier),
        proxies,
        logContextPutMap, logContextRemoveSet);
    try {
      driver.execute(w0e0Query, EMPTY_CACHE, processorProvider);
    } catch (InvalidArgumentException ex) {
      assertEquals(exception400.getMessage(), ex.getMessage());
    }
    assertEquals(1, status.getProcessedQueries());

    assertTrue(logContextPutMap.containsKey("ms"));
    assertTrue(logContextPutMap.containsKey("start"));
    Map<String, Object> expectedLogContextPutMap = ImmutableMap.of(
        "ms", logContextPutMap.get("ms"),
        "start", logContextPutMap.get("start"),
        "error", true,
        "httpStatusCode", 400
    );
    assertEquals(expectedLogContextPutMap, logContextPutMap);
    assertSetEquals(ImmutableSet.of("ms", "start", "error", "httpStatusCode"), logContextRemoveSet);
  }

  @Test
  public void execute_logContext500() {
    Map<String, Object> logContextPutMap = new HashMap<>();
    Set<String> logContextRemoveSet = new HashSet<>();

    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());
    AnnotatedQueryW0E0 w0e0Query = new AnnotatedQueryW0E0();
    IllegalStateException exception500 = new IllegalStateException("400");
    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(w0e0Query), with(cacheWithSessionId()), with(processorProvider));
      will(throwException(exception500));
      oneOf(classifier).classifyResult(w0e0Query, exception500);
      will(returnValue(FAILURE));
    }});
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), status, classifier),
        proxies,
        logContextPutMap, logContextRemoveSet);
    try {
      driver.execute(w0e0Query, EMPTY_CACHE, processorProvider);
    } catch (IllegalStateException ex) {
      assertEquals(exception500.getMessage(), ex.getMessage());
    }
    assertEquals(1, status.getProcessedQueries());

    assertTrue(logContextPutMap.containsKey("ms"));
    assertTrue(logContextPutMap.containsKey("start"));
    Map<String, Object> expectedLogContextPutMap = ImmutableMap.of(
        "ms", logContextPutMap.get("ms"),
        "start", logContextPutMap.get("start"),
        "error", true,
        "httpStatusCode", 500
    );
    assertEquals(expectedLogContextPutMap, logContextPutMap);
    assertSetEquals(ImmutableSet.of("ms", "start", "error", "httpStatusCode"), logContextRemoveSet);
  }

  @Test
  public void execute_logContext_withInjectionTime() {
    Map<String, Object> logContextPutMap = new HashMap<>();
    Set<String> logContextRemoveSet = new HashSet<>();

    final Provider<NullPostProcessor> processorProvider = Providers.of(new NullPostProcessor());
    AnnotatedQueryW0E0 w0e0Query = new AnnotatedQueryW0E0();
    Long injectionTime = 20L;
    AtomicReference<QuerySession> querySession = new AtomicReference<>();
    MonitoringQueryDriver driver = createDriver(
        delegate, new QueryRuntimeMonitor(Providers.of(new DateTime()),
            new Timer(true), status, classifier) {

          @Override
          public QuerySession queryStart(Query<?> query, Sla sla, Thread queryMainThread, Trace trace) {
            assertEquals(w0e0Query, query);
            querySession.set(new QuerySession(query, sla, queryMainThread, trace));
            return querySession.get();
          }
        },
        proxies,
        logContextPutMap,
        logContextRemoveSet);

    mockery.checking(new Expectations() {{
      oneOf(delegate).execute(with(w0e0Query), with(cacheWithSessionId()), with(processorProvider));
      will(new CustomAction("Set injection time on querySession") {
        @Override
        public Object invoke(Invocation invocation) {
          querySession.get().setInjectionTime(injectionTime);
          return null;
        }
      });
      oneOf(classifier).classifyResult(w0e0Query, null);
      will(returnValue(SUCCESS));
    }});

    driver.execute(w0e0Query, EMPTY_CACHE, processorProvider);
    assertEquals(1, status.getProcessedQueries());

    assertTrue(logContextPutMap.containsKey("ms"));
    assertTrue(logContextPutMap.containsKey("start"));
    Map<String, Object> expectedLogContextPutMap = Maps.newHashMap();
    expectedLogContextPutMap.put("ms", logContextPutMap.get("ms"));
    expectedLogContextPutMap.put("start", logContextPutMap.get("start"));
    expectedLogContextPutMap.put("error", false);
    expectedLogContextPutMap.put("httpStatusCode", 200);
    expectedLogContextPutMap.put("injectionTime", 20L);

    assertEquals(expectedLogContextPutMap, logContextPutMap);
    assertSetEquals(ImmutableSet.of("ms", "start", "error", "httpStatusCode", "injectionTime"),
        logContextRemoveSet);
  }

  static class UnAnnotatedQuery extends AbstractQuery<String> {

    @Override
    public String process() {
      return null;
    }

  }

  @ResponseTime(warn = 10, error = 20)
  public static class AnnotatedQueryW10E20 extends AbstractQuery<String> {

    @Override
    public String process() {
      return null;
    }

  }

  @ResponseTime(warn = 0, error = 0)
  public static class AnnotatedQueryW0E0 extends AbstractQuery<String> {

    @Override
    public String process() {
      return null;
    }

  }

  private MonitoringQueryDriver createDriver(
      QueryDriver delegate, QueryRuntimeMonitor runtimeMonitor,
      QueryProxies proxies) {
    return new MonitoringQueryDriver(delegate, runtimeMonitor, proxies, ThreadLocal.withInitial(Trace::newTrace));
  }

  private MonitoringQueryDriver createDriver(
      QueryDriver delegate, QueryRuntimeMonitor runtimeMonitor,
      QueryProxies proxies, Map<String, Object> logContextPutMap, Set<String> logContextRemoveSet) {
    return new MonitoringQueryDriver(delegate, runtimeMonitor, proxies, ThreadLocal.withInitial(Trace::newTrace)) {
      @Override
      void logContextPutWrapper(String key, Object value) {
        logContextPutMap.put(key, value);
      }

      @Override
      void logContextRemoveWrapper(String key) {
        logContextRemoveSet.add(key);
      }
    };
  }

  private Matcher<Map<Key<?>, Object>> cacheWithSessionId() {
    return cacheWithSessionId(emptyMap());
  }

  private Matcher<Map<Key<?>, Object>> cacheWithSessionId(Map<Key<?>, Object> relaxedCache) {
    return new CustomTypeSafeMatcher<>(
        "A map with any QuerySessionId in addition to " + relaxedCache) {
      @Override
      protected boolean matchesSafely(Map<Key<?>, Object> item) {
        Object sessionId = item.get(QUERY_SESSION_ID_KEY);
        if (!(sessionId instanceof QuerySessionId)) {
          return false;
        }
        HashMap<Key<?>, Object> matchingMap = new HashMap<>(relaxedCache);
        matchingMap.put(QUERY_SESSION_ID_KEY, sessionId);
        return item.equals(matchingMap);
      }
    };
  }

}
