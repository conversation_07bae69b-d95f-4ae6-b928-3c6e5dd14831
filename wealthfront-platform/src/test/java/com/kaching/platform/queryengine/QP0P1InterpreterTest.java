package com.kaching.platform.queryengine;

import static com.google.inject.Guice.createInjector;
import static com.kaching.platform.guice.TestingApplicationOptions.OPTIONS;
import static com.kaching.platform.queryengine.HttpMethod.POST;
import static com.kaching.platform.queryengine.HttpMethod.PUT;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.STRICT;
import static com.wealthfront.test.Assert.assertThrows;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import com.google.common.collect.ImmutableMap;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.OptionsModule;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.exceptions.MethodNotAllowedException;
import com.kaching.platform.queryengine.exceptions.MissingRequiredParameterException;
import com.kaching.platform.queryengine.exceptions.QueryInstantiationException;
import com.kaching.util.functional.Tuple2;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

public class QP0P1InterpreterTest {

  private QP0P1Interpreter interpreter;

  @Before
  public void before() {
    interpreter = new QP0P1Interpreter();
  }

  @Test
  public void createQuery1() {
    Query<?> query = query(ImmutableMap.of(
        "q", new String[]{"MyLittleQuery"},
        "p0", new String[]{"mandatory"},
        "p1", new String[]{"not default"}));

    assertNotNull(query);
    assertEquals(MyLittleQuery.class, query.getClass());
    assertEquals("mandatory", ((MyLittleQuery) query).mandatory);
    assertEquals("not default", ((MyLittleQuery) query).value);
  }

  @Test
  public void createQuery2() {
    Query<?> query = query(ImmutableMap.of(
        "q", new String[]{"MyLittleQuery"},
        "p0", new String[]{"mandatory"}));

    assertNotNull(query);
    assertEquals(MyLittleQuery.class, query.getClass());
    assertEquals("mandatory", ((MyLittleQuery) query).mandatory);
    assertEquals("default", ((MyLittleQuery) query).value);
  }

  @Test
  public void createQuery3() {
    Query<?> query = query(ImmutableMap.of(
        "q", new String[]{"MyLittleQuery"},
        "p0", new String[]{"mandatory"},
        "p2", new String[]{"second is here"}));

    assertNotNull(query);
    assertEquals(MyLittleQuery.class, query.getClass());
    assertEquals("mandatory", ((MyLittleQuery) query).mandatory);
    assertEquals("default", ((MyLittleQuery) query).value);
    assertEquals("second is here", ((MyLittleQuery) query).secondValue);
  }

  @Test
  public void createQuery4() {
    try {
      query(ImmutableMap.of(
          "q", new String[]{"MyLittleQuery"}));
      fail();
    } catch (QueryInstantiationException e) {
      assertEquals("parameter 1 is not optional but null was provided", e.getMessage());
    }
  }

  static class HibernateThingy implements HibernateEntity {

    @Override
    public Id<HibernateThingy> getId() {
      throw new UnsupportedOperationException();
    }

  }

  static class IdQuery extends AbstractQuery<Boolean> {

    @SuppressWarnings("unused")
    private final Id<HibernateThingy> hibernateId;

    IdQuery(Id<HibernateThingy> hibernateId) {
      this.hibernateId = hibernateId;
    }

    @Override
    public Boolean process() {
      return true;
    }

  }

  @Test(expected = QueryInstantiationException.class)
  public void queryInstantiationException() {
    query(IdQuery.class, ImmutableMap.of("q", new String[]{"IdQuery"}, "p0", new String[]{"foobar"}));
  }

  static class KindQuery extends AbstractQuery<Boolean> {

    @SuppressWarnings("unused")
    private final ServiceKind kind;

    KindQuery(ServiceKind kind) {
      this.kind = kind;
    }

    @Override
    public Boolean process() {
      return true;
    }

  }

  @Test(expected = RuntimeException.class)
  public void verboseException() {
    query(KindQuery.class, ImmutableMap.of("q", new String[]{"KindQuery"}, "p0", new String[]{"com.kaching.Kinds$BS"}));
  }

  @Test
  public void methodNotAllowed() {
    QP0P1Interpreter interpreter = new QP0P1Interpreter();
    interpreter.queryFactories = new QueryProxies(new DefaultJsonMarshallerFactory(), null);
    interpreter.queryFactories.register(MyLittleQuery.class);
    interpreter.jsonMarshallerFactory = new DefaultJsonMarshallerFactory();
    assertThrows(
        MethodNotAllowedException.class,
        "Method not allowed: PUT",
        () -> interpreter.getQuery(
            PUT,
            "",
            ImmutableMap.of(
                "q", new String[]{"MyLittleQuery"},
                "p0", new String[]{"mandatory"},
                "p1", new String[]{"not default"}),
            emptyMap()));
  }

  private Query<?> query(Map<String, String[]> parameters) {
    return query(MyLittleQuery.class, parameters);
  }

  private Query<?> query(Class<? extends Query<?>> queryClass, Map<String, String[]> parameters) {
    QP0P1Interpreter interpreter = new QP0P1Interpreter();
    interpreter.queryFactories = new QueryProxies(new DefaultJsonMarshallerFactory(), null);
    interpreter.queryFactories.register(queryClass);
    interpreter.jsonMarshallerFactory = new DefaultJsonMarshallerFactory();
    return interpreter.getQuery(
        POST, "", parameters, emptyMap());
  }

  static class MyLittleQuery extends AbstractQuery<String> {

    private final String mandatory;
    private final String value;
    private final String secondValue;

    MyLittleQuery(
        String mandatory,
        @Optional("default") String value,
        @Optional String secondValue) {
      this.mandatory = mandatory;
      this.value = value;
      this.secondValue = secondValue;
    }

    @Override
    public String process() {
      return null;
    }

  }

  @Test
  public void parameters1() throws Exception {
    Tuple2<String, ? extends List<String>> result =
        interpreter.extractCall(ImmutableMap.of(
            "q", new String[]{"HelloWorld"}));

    assertEquals("HelloWorld", result._1);
    assertEquals(emptyList(), result._2);
  }

  @Test
  public void parameters2() {
    Tuple2<String, ? extends List<String>> result =
        interpreter.extractCall(ImmutableMap.of(
            "q", new String[]{"HelloWorld"},
            "p0", new String[]{"value0"}));

    assertEquals("HelloWorld", result._1);
    assertEquals(List.of("value0"), result._2);
  }

  @Test
  public void parameters3() {
    Tuple2<String, ? extends List<String>> result =
        interpreter.extractCall(ImmutableMap.of(
            "q", new String[]{"HelloWorld"},
            "p0", new String[]{"value0"},
            "p1", new String[]{"value1"}));

    assertEquals("HelloWorld", result._1);
    assertEquals(asList("value0", "value1"), result._2);
  }

  @Test
  public void parameters4() {
    Tuple2<String, ? extends List<String>> result =
        interpreter.extractCall(ImmutableMap.of(
            "q", new String[]{"HelloWorld"},
            "p1", new String[]{"value1"}));

    assertEquals("HelloWorld", result._1);
    assertEquals(asList(null, "value1"), result._2);
  }

  @Test
  public void parameters5() {
    Tuple2<String, ? extends List<String>> result =
        interpreter.extractCall(ImmutableMap.of(
            "q", new String[]{"HelloWorld"},
            "p0", new String[]{"value0"},
            "p2", new String[]{"value2"}));

    assertEquals("HelloWorld", result._1);
    assertEquals(asList("value0", null, "value2"), result._2);
  }

  @Test
  public void parameters6() {
    Tuple2<String, ? extends List<String>> result =
        interpreter.extractCall(ImmutableMap.of(
            "q", new String[]{"HelloWorld"},
            "p5", new String[]{"value5"}));

    assertEquals("HelloWorld", result._1);
    assertEquals(asList(null, null, null, null, null, "value5"), result._2);
  }

  @Test(expected = MissingRequiredParameterException.class)
  public void parametersInvalid1() {
    // missing q
    interpreter.extractCall(emptyMap());
  }

  @Test(expected = MissingRequiredParameterException.class)
  public void parametersInvalid2() {
    // missing q
    interpreter.extractCall(ImmutableMap.of("p0", new String[]{"value0"}));
  }

  @Test
  public void getPostProcessorProvider() {
    interpreter.injector = createInjector(
        new QueryEngineModule(9000, STRICT, OPTIONS, SessionlessQueryDriver.class, new Class[0]),
        new OptionsModule(OPTIONS));
    interpreter.queryFactories = new QueryProxies(new DefaultJsonMarshallerFactory(), null);
    assertNotNull(interpreter.getPostProcessorProvider(
        null, null, null, null, IntegerQuery.class));
  }

  @Test
  public void getQuery_NoArgs() {
    interpreter.queryFactories = new QueryProxies(new DefaultJsonMarshallerFactory(), null);
    interpreter.queryFactories.register(IntegerQuery.class);
    Query<?> sut = interpreter.getQuery(HttpMethod.POST,
        "https://kaching.com", new HashMap<>() {{
          put("q", new String[]{"IntegerQuery"});
        }}, null);

    assertEquals(IntegerQuery.class, sut.getClass());
  }

  @Test
  public void getQuery_twoInts() {
    interpreter.queryFactories = new QueryProxies(new DefaultJsonMarshallerFactory(), null);
    interpreter.queryFactories.register(TwoParamQuery.class);
    TwoParamQuery sut = (TwoParamQuery)
        interpreter.getQuery(HttpMethod.POST,
            "https://kaching.com",
            new HashMap<String, String[]>() {{
              put("q", new String[]{"TwoParamQuery"});
              put("p0", new String[]{"123"});
              put("p1", new String[]{"42"});
            }},
            null);

    assertEquals(123, sut.p0);
    assertEquals(42, sut.p1);
  }

  // TODO(vlad): figure out how to deal with vararg queries
  @Test
  @Ignore
  public void getQuery_vararg() {
    interpreter.queryFactories = new QueryProxies(new DefaultJsonMarshallerFactory(), null);
    interpreter.queryFactories.register(VarargQuery.class);
    VarargQuery sut = (VarargQuery)
        interpreter.getQuery(HttpMethod.POST,
            "https://kaching.com",
            new HashMap<String, String[]>() {{
              put("q", new String[]{"VarargQuery"});
              put("p0", new String[]{"123"});
              put("p1", new String[]{"42"});
              put("p2", new String[]{"43"});
              put("p3", new String[]{"44"});
            }},
            null);

    assertEquals(123, sut.p0);
    assertEquals(new int[]{42, 43, 44}, sut.p1);
  }

  static class TwoParamQuery implements Query<Integer> {

    private final int p0;
    private final int p1;

    TwoParamQuery(int p0, int p1) {
      this.p0 = p0;
      this.p1 = p1;
    }

    public boolean isAuthenticated() {
      return true;
    }

    @Override
    public Integer process() {
      return null;
    }

    @Override
    public IntegerQuery clone() {
      throw new UnsupportedOperationException();
    }

  }

  static class VarargQuery implements Query<Integer> {

    private final int p0;
    private final int[] p1;

    VarargQuery(int p0, int... p1) {
      this.p0 = p0;
      this.p1 = p1;
    }

    public boolean isAuthenticated() {
      return true;
    }

    @Override
    public Integer process() {
      return null;
    }

    @Override
    public IntegerQuery clone() {
      throw new UnsupportedOperationException();
    }

  }

  static class IntegerQuery implements Query<Integer> {

    public boolean isAuthenticated() {
      return true;
    }

    @Override
    public Integer process() {
      return null;
    }

    @Override
    public IntegerQuery clone() {
      throw new UnsupportedOperationException();
    }

  }

  @Test
  public void createQuery5() {
    assertThrows(QueryInstantiationException.class,
        "parameter 1 is not optional but null was provided",
        () -> query(ViewQuery.class, ImmutableMap.of("q", new String[]{"ViewQuery"})));
  }

  @Test
  public void createQuery6() {
    assertThrows(QueryInstantiationException.class,
        "converter class com.kaching.entities.converters.EntityConverter produced a null value",
        () -> query(ViewQuery.class, ImmutableMap.of("q", new String[]{"ViewQuery"}, "p0", new String[]{"null"})));
  }

  @Entity
  static class View {

    @Value String key;

  }

  static class ViewQuery implements Query<Integer> {

    View view;

    ViewQuery(View view) {
      this.view = view;
    }

    @Override
    public Integer process() {
      return null;
    }

    @Override
    public Query<Integer> clone() {
      return null;
    }

  }

}
