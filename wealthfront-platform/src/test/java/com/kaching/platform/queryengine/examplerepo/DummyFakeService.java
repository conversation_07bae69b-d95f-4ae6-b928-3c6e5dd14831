package com.kaching.platform.queryengine.examplerepo;

import static com.kaching.api.ExposeTo.BACKEND;
import static com.kaching.api.ExposeType.RewriteNamespace.SERVICE;

import com.kaching.api.ExposeQuery;
import com.kaching.api.ExposeType;
import com.kaching.platform.components.Component;
import com.kaching.platform.components.Component.ParameterMapping;
import com.kaching.platform.components.Component.ParameterMapping.Type;
import com.kaching.platform.queryengine.AbstractQuery;

@Component(
    clientName = "fakeservice-overridden-client",
    queries = {
        DummyFakeService.ExposedQuery1InDummyFakeService.class,
        DummyFakeService.HiddenQueryInDummyFakeService.class
    },
    queriesInClient = {
        DummyFakeService.ExposedQuery2InDummyFakeService.class
    },
    clientQueryParameterMappings = {
        @ParameterMapping(
            internalType = @Type(Long.class),
            externalType = @Type(Integer.class))
    },
    dependsOn = {
        ExampleComponent.class
    }
)
public class DummyFakeService {
  
  @ExposeType(value = BACKEND, namespace = SERVICE)
  public enum ExposedEnum {
    UNIT
  }

  @ExposeQuery(BACKEND)
  public static class ExposedQuery1InDummyFakeService extends AbstractQuery<ExposedEnum> {

    @Override
    public ExposedEnum process() {
      return null;
    }

  }

  public static class ExposedQuery2InDummyFakeService extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      return null;
    }

  }

  public static class HiddenQueryInDummyFakeService extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      return null;
    }

  }

}
