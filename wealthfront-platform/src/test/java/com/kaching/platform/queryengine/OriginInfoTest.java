package com.kaching.platform.queryengine;

import static com.wealthfront.test.Assert.assertMarshalling;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.twolattes.json.Json;

public class OriginInfoTest {

  @Test
  public void fieldGetter() {
    OriginInfo info =
        new OriginInfo(OriginInfo.Type.API, "getRetirementPlan", "v1/planning/retirement", HttpMethod.GET);
    assertEquals(OriginInfo.Type.API, info.getOriginType());
    assertEquals("getRetirementPlan", info.getOriginIdentifier());
    assertOptionEquals("v1/planning/retirement", info.getOriginUrlPath());
    assertOptionEquals(HttpMethod.GET, info.getOriginUrlMethod());
  }

  @Test
  public void fieldGetter_optionalsEmpty() {
    OriginInfo info = new OriginInfo(OriginInfo.Type.RAILS, "getRetirementPlan");
    assertEquals(OriginInfo.Type.RAILS, info.getOriginType());
    assertEquals("getRetirementPlan", info.getOriginIdentifier());
    assertOptionEmpty(info.getOriginUrlPath());
    assertOptionEmpty(info.getOriginUrlMethod());
  }

  @Test
  public void marshall() {
    Json.Value expected = Json.object(
        "originType", "API",
        "originIdentifier", "getRetirementPlan",
        "originUrlPath", "v1/planning/retirement",
        "originUrlMethod", "GET"
    );
    assertMarshalling(
        OriginInfo.MARSHALLER, expected,
        new OriginInfo(OriginInfo.Type.API, "getRetirementPlan", "v1/planning/retirement", HttpMethod.GET));
  }

  @Test
  public void marshall_optionalsEmpty() {
    Json.Value expected = Json.object(
        "originType", "API",
        "originIdentifier", "getRetirementPlan"
    );
    assertMarshalling(OriginInfo.MARSHALLER, expected, new OriginInfo(OriginInfo.Type.API, "getRetirementPlan"));
  }

}
