package com.kaching.platform.queryengine.progress;

import static com.wealthfront.test.WMatchers.listInAnyOrder;
import static org.junit.Assert.assertEquals;

import org.jmock.Mockery;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;

public class GetAllQueryProgressTest {

  private final Mockery mockery = Mockeries.mockery();

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_someQueriesWithProgress() {
    GetAllQueryProgress query = getQuery();
    query.runtimeMonitor.queryStart(new QueryWithoutProgress(), null, null, null);
    query.runtimeMonitor.queryStart(new QueryWithoutProgress(), null, null, null);
    QuerySession sessionWithProgress1 = query.runtimeMonitor.queryStart(new QueryWithProgress(), null, null, null);
    QuerySession sessionWithProgress2 = query.runtimeMonitor.queryStart(new QueryWithProgress(), null, null, null);

    mockery.checking(new WExpectations() {{
      oneOf(query.formatter).formatAllQueryProgress(with(listInAnyOrder(sessionWithProgress1, sessionWithProgress2)));
      will(returnValue("formatted output"));
    }});

    assertEquals("formatted output", query.process());
  }

  @Test
  public void process_noQueriesWithProgress() {
    GetAllQueryProgress query = getQuery();
    query.runtimeMonitor.queryStart(new QueryWithoutProgress(), null, null, null);
    query.runtimeMonitor.queryStart(new QueryWithoutProgress(), null, null, null);

    assertEquals("No running queries with progress reports.", query.process());
  }

  private GetAllQueryProgress getQuery() {
    GetAllQueryProgress query = new GetAllQueryProgress();
    query.runtimeMonitor = new QueryRuntimeMonitor();
    query.formatter = mockery.mock(QueryProgressFormatter.class);
    return query;
  }

  static class QueryWithoutProgress extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      return null;
    }

    @Override
    public ProgressMonitor getProgressMonitorForOtherThreads() {
      return new ProgressMonitor();
    }

  }

  static class QueryWithProgress extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      return null;
    }

    @Override
    public ProgressMonitor getProgressMonitorForOtherThreads() {
      ProgressMonitor progressMonitor = new ProgressMonitor();
      progressMonitor.startNewStage("stage", 2).incrementProgress(1);
      return progressMonitor;
    }

  }

}