package com.kaching.platform.queryengine.progress;

import static com.google.common.collect.Iterables.getOnlyElement;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.platform.queryengine.progress.ProgressMonitor.QueryProgress;
import static com.kaching.platform.queryengine.progress.ProgressMonitor.StageProgress;
import static com.kaching.util.functional.Pointer.pointer;
import static com.kaching.util.mail.Pager.Device.PAGER_BROKERAGE_OPS;
import static com.kaching.util.mail.Pager.Device.PAGER_BROKERAGE_PLATFORM;
import static com.twolattes.json.Json.array;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertMarshalling;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static java.util.Collections.emptyList;
import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static org.joda.time.Duration.millis;
import static org.joda.time.Duration.standardMinutes;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import org.joda.time.Duration;
import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.queryengine.QueryProxyTest.Queries.IntegerQuery;
import com.kaching.platform.queryengine.QueryProxyTest.Queries.StringQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.queryengine.QuerySessionId;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.UncheckedInterruptedException;
import com.kaching.util.functional.Pointer;
import com.kaching.util.mail.Pager;
import com.kaching.util.time.NanoTimeProvider;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

public class ProgressMonitorTest {

  private static final Mockeries.WFMockery mockery = Mockeries.mockery(true);
  private static final Pager pager = mockery.mock(Pager.class);
  private static final QueryRuntimeMonitor queryRuntimeMonitor = mockery.mock(QueryRuntimeMonitor.class);
  private static final Marshaller<QueryProgress> marshaller = createMarshaller(QueryProgress.class);
  private static final NanoTimeProvider nanoTimeProvider = () -> 15_000_000_000L;

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void marshalling() {
    QueryProgress queryProgress = new QueryProgress(123, "QueryName", ImmutableList.of(
        new StageProgress("Some stage name", 1000, nanoTimeProvider).incrementProgress(13),
        new StageProgress("Some stage name 2", 2000, nanoTimeProvider),
        new StageProgress("Some stage name 3", 3000, nanoTimeProvider).incrementProgress(130)
    ));

    Json.Value expected = object(
        "stages", array(
            object(
                "stageName", "Some stage name",
                "totalWorkToDo", 1000,
                "workCompletedSoFar", 13
            ),
            object(
                "stageName", "Some stage name 2",
                "totalWorkToDo", 2000,
                "workCompletedSoFar", 0
            ),
            object(
                "stageName", "Some stage name 3",
                "totalWorkToDo", 3000,
                "workCompletedSoFar", 130
            )
        ),
        "queryName", "QueryName",
        "sessionId", 123
    );

    assertMarshalling(marshaller, expected, queryProgress);
  }

  @Test
  public void incrementProgress_shouldThrowIfThreadInterrupted() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider);

    Thread.currentThread().interrupt();
    assertThrows(UncheckedInterruptedException.class, () -> stageProgress.incrementProgress(1));
  }

  @Test
  public void getEstimatedRemainingTime_beforeStart() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider);

    assertOptionEmpty(stageProgress.getEstimatedRemainingTime());
  }

  @Test
  public void getEstimatedRemainingTime_partiallyFinished() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider);
    stageProgress.incrementProgress(5);

    assertFalse(stageProgress.getEstimatedRemainingTime().isEmpty());
  }

  @Test
  public void getEstimatedRemainingTime_finished() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider);
    stageProgress.incrementProgress(10);

    assertOptionEmpty(stageProgress.getEstimatedRemainingTime());
  }

  @Test
  public void getEstimatedRemainingTime_unknownSize() {
    StageProgress stageProgress = new StageProgress("", Long.MAX_VALUE, nanoTimeProvider);
    stageProgress.incrementProgress(10);

    assertOptionEmpty(stageProgress.getEstimatedRemainingTime());
  }
  
  @Test
  public void setTotalWorkAndWorkRemaining() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider);
    stageProgress.setTotalWorkAndWorkRemaining(5, 5);
    assertOptionEmpty(stageProgress.getEstimatedRemainingTime());
    
    stageProgress.setTotalWorkAndWorkRemaining(5, 3);
    assertEquals(5, stageProgress.getTotalWorkToDo());
    assertEquals(2, stageProgress.getWorkCompletedSoFar());
    assertEquals(-1L, stageProgress.getEndTimeNanos());
    assertOptionEquals("0 minutes, 0 seconds", stageProgress.getEstimatedRemainingTime());

    stageProgress.setTotalWorkAndWorkRemaining(5, 0);
    assertEquals(5, stageProgress.getTotalWorkToDo());
    assertEquals(5, stageProgress.getWorkCompletedSoFar());
    assertEquals((long) nanoTimeProvider.get(), stageProgress.getEndTimeNanos());
  }

  @Test
  public void setTotalWorkAndWorkRemaining_outOfBounds_clips() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider);
    stageProgress.setTotalWorkAndWorkRemaining(5, 8);
    assertEquals(5, stageProgress.getTotalWorkToDo());
    assertEquals(0, stageProgress.getWorkCompletedSoFar());
    
    stageProgress.setTotalWorkAndWorkRemaining(5, -1);
    assertEquals(5, stageProgress.getTotalWorkToDo());
    assertEquals(5, stageProgress.getWorkCompletedSoFar());
    
    stageProgress.setTotalWorkAndWorkRemaining(-1, 3);
    assertEquals(0, stageProgress.getTotalWorkToDo());
    assertEquals(0, stageProgress.getWorkCompletedSoFar());
  }

  @Test
  public void setTotalWorkAndWorkRemaining_shouldThrowIfThreadInterrupted() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider);

    Thread.currentThread().interrupt();
    assertThrows(UncheckedInterruptedException.class, () -> stageProgress.setTotalWorkAndWorkRemaining(5, 3));
  }

  @Test
  public void getElapsedTime_partiallyFinished() {
    StageProgress stageProgress = new StageProgress("", 10, () -> 6_000_000_000L) {

      @Override
      public long getStartTimeNanos() {
        return 1_000_000_000L;
      }

      @Override
      public long getEndTimeNanos() {
        return -1L;
      }

    };

    assertEquals("0 minutes, 5 seconds", stageProgress.getElapsedTime());
  }

  @Test
  public void getElapsedTime_finished() {
    StageProgress stageProgress = new StageProgress("", 10, nanoTimeProvider) {

      @Override
      public long getStartTimeNanos() {
        return 1_000_000_000L;
      }

      @Override
      public long getEndTimeNanos() {
        return 11_000_000_000L;
      }

    };

    assertEquals("0 minutes, 10 seconds", stageProgress.getElapsedTime());
  }

  @Test
  public void monitorIteration() {
    ProgressMonitor progressMonitor = new ProgressMonitor();
    Iterable<Integer> iterable = progressMonitor.monitor(Arrays.asList(1, 2, 3), "iterate");
    assertTrue(progressMonitor.getStages().isEmpty());

    Iterator<Integer> iterator = iterable.iterator();
    StageProgress stage = getOnlyElement(progressMonitor.getStages());
    assertEquals("iterate", stage.getStageName());
    assertEquals(3, stage.getTotalWorkToDo());
    assertEquals(0, stage.getWorkCompletedSoFar());

    assertTrue(iterator.hasNext());
    assertEquals(1, (int) iterator.next());
    assertEquals(1, stage.getWorkCompletedSoFar());

    assertTrue(iterator.hasNext());
    assertEquals(2, (int) iterator.next());
    assertEquals(2, stage.getWorkCompletedSoFar());

    assertTrue(iterator.hasNext());
    assertEquals(3, (int) iterator.next());
    assertEquals(3, stage.getWorkCompletedSoFar());

    assertFalse(iterator.hasNext());
    assertEquals(3, stage.getWorkCompletedSoFar());
  }

  @Test
  public void monitorStream_lazy() {
    ProgressMonitor progressMonitor = new ProgressMonitor();
    var stream = progressMonitor.monitorStream(List.of(1, 2, 3), "stageName");
    var stage = getOnlyElement(progressMonitor.getStages());

    assertEquals(0, stage.getWorkCompletedSoFar());
    stream.forEach(i -> assertEquals((long) i, stage.getWorkCompletedSoFar()));
    assertEquals(3, stage.getWorkCompletedSoFar());
  }

  @Test
  public void monitorStream_lazy_iter() {
    ProgressMonitor progressMonitor = new ProgressMonitor();
    Iterator<Integer> iter = progressMonitor.monitorStream(List.of(1, 2, 3), "stageName").iterator();
    var stage = getOnlyElement(progressMonitor.getStages());

    assertEquals(0, stage.getWorkCompletedSoFar());

    assertEquals(1, iter.next().intValue());
    assertEquals(1, stage.getWorkCompletedSoFar());

    assertEquals(2, iter.next().intValue());
    assertEquals(2, stage.getWorkCompletedSoFar());

    assertEquals(3, iter.next().intValue());
    assertEquals(3, stage.getWorkCompletedSoFar());

    assertFalse(iter.hasNext());
  }

  @Test
  public void verifyProgress_allStagesHaveProgressed_noAlert() {
    ProgressMonitor progressMonitor = new ProgressMonitor();

    Pointer<List<Long>> previousRemainingWorkInEachStagePointer = pointer(ImmutableList.of(0L, 5L));
    StageProgress stageThatIsFinished = new StageProgress("1", 7L, nanoTimeProvider);
    stageThatIsFinished.incrementProgress(7L);
    StageProgress stageThatMadeProgress = new StageProgress("2", 5L, nanoTimeProvider);
    stageThatMadeProgress.incrementProgress(1L);
    progressMonitor.stages = ImmutableList.of(stageThatIsFinished, stageThatMadeProgress);

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList((QuerySession) null));
      never(pager);
    }});

    assertTrue(progressMonitor.runProgressAlerting(previousRemainingWorkInEachStagePointer, standardMinutes(15),
        PAGER_BROKERAGE_OPS, IntegerQuery.class, pager, queryRuntimeMonitor, 123));
  }

  @Test
  public void verifyProgress_fallbackQueryClass_schedulesAgainIfFound() {
    ProgressMonitor progressMonitor = new ProgressMonitor();

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).allRunningQueryClasses();
      will(returnSet(IntegerQuery.class, StringQuery.class));
      never(pager);
    }});

    assertTrue(progressMonitor.runProgressAlerting(pointer(ImmutableList.of()), standardMinutes(15),
        PAGER_BROKERAGE_OPS, IntegerQuery.class, pager, queryRuntimeMonitor, -1));
  }

  @Test
  public void verifyProgress_fallbackQueryClass_doesNotScheduleIfNotFound() {
    ProgressMonitor progressMonitor = new ProgressMonitor();

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).allRunningQueryClasses();
      will(returnSet(StringQuery.class));
      never(pager);
    }});

    assertFalse(progressMonitor.runProgressAlerting(pointer(ImmutableList.of()), standardMinutes(15),
        PAGER_BROKERAGE_OPS, IntegerQuery.class, pager, queryRuntimeMonitor, -1));
  }

  @Test
  public void verifyProgress_aStageHasNotProgressed_alerts() {
    ProgressMonitor progressMonitor = new ProgressMonitor();

    Pointer<List<Long>> previousRemainingWorkInEachStagePointer = pointer(ImmutableList.of(0L, 5L));
    StageProgress stageThatIsFinished = new StageProgress("1", 7L, nanoTimeProvider);
    stageThatIsFinished.incrementProgress(7L);
    StageProgress stageThatDidNotMakeProgress = new StageProgress("2", 5L, nanoTimeProvider);
    progressMonitor.stages = ImmutableList.of(stageThatIsFinished, stageThatDidNotMakeProgress);

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList((QuerySession) null));
      oneOf(pager)
          .alert("IntegerQuery(123) may be stuck", "a stage has not made progress in PT900S",
              PAGER_BROKERAGE_PLATFORM);
    }});

    assertTrue(progressMonitor.runProgressAlerting(previousRemainingWorkInEachStagePointer, standardMinutes(15),
        PAGER_BROKERAGE_PLATFORM, IntegerQuery.class, pager, queryRuntimeMonitor, 123));
  }

  @Test
  public void verifyProgress_allStagesHaveFinished_shutsDown() {
    ProgressMonitor progressMonitor = new ProgressMonitor();

    Pointer<List<Long>> previousRemainingWorkInEachStagePointer = pointer(ImmutableList.of(0L, 5L));
    StageProgress stageThatIsFinished1 = new StageProgress("1", 7L, nanoTimeProvider);
    stageThatIsFinished1.incrementProgress(7L);
    StageProgress stageThatIsFinished2 = new StageProgress("2", 5L, nanoTimeProvider);
    stageThatIsFinished2.incrementProgress(5L);
    progressMonitor.stages = ImmutableList.of(stageThatIsFinished1, stageThatIsFinished2);

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList((QuerySession) null));
      never(pager);
    }});

    assertFalse(progressMonitor.runProgressAlerting(previousRemainingWorkInEachStagePointer, standardMinutes(15),
        PAGER_BROKERAGE_PLATFORM, IntegerQuery.class, pager, queryRuntimeMonitor, 123));
  }

  @Test
  public void verifyProgress_queryIsNotRunning_shutsDown() {
    ProgressMonitor progressMonitor = new ProgressMonitor();

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList());
      never(pager);
    }});

    assertFalse(progressMonitor.runProgressAlerting(pointer(), standardMinutes(15), PAGER_BROKERAGE_PLATFORM,
        IntegerQuery.class, pager, queryRuntimeMonitor, 123));
  }

  @Test
  public void verifyProgress_queryHasNotEnteredStage_doNotShutDown() {
    ProgressMonitor progressMonitor = new ProgressMonitor();

    Pointer<List<Long>> previousRemainingWorkInEachStagePointer = pointer(emptyList());
    progressMonitor.stages = null;

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList((QuerySession) null));
      never(pager);
    }});

    assertTrue(progressMonitor.runProgressAlerting(previousRemainingWorkInEachStagePointer, standardMinutes(15),
        PAGER_BROKERAGE_PLATFORM, IntegerQuery.class, pager, queryRuntimeMonitor, 123));
  }

  @Test
  public void enableProgressAlerting_schedulesCorrectly() throws InterruptedException, TimeoutException, ExecutionException {
    ProgressMonitor progressMonitor = new ProgressMonitor(new QuerySessionId(123L), nanoTimeProvider);
    Duration timeUnit = millis(250);

    CompletableFuture<Unit> future = progressMonitor.enableProgressAlerting(timeUnit.multipliedBy(5L), timeUnit.multipliedBy(2L),
        PAGER_BROKERAGE_PLATFORM, IntegerQuery.class, pager, queryRuntimeMonitor);

    try {
      future.get(timeUnit.multipliedBy(4L).getMillis(), MILLISECONDS);
      fail();
    } catch (TimeoutException ignored) {
    }
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList((QuerySession) null));
    }});
    try {
      future.get(timeUnit.multipliedBy(2L).getMillis(), MILLISECONDS);
      fail();
    } catch (TimeoutException ignored) {
    }
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList((QuerySession) null));
    }});
    try {
      future.get(timeUnit.multipliedBy(2L).getMillis(), MILLISECONDS);
      fail();
    } catch (TimeoutException ignored) {
    }
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList((QuerySession) null));
    }});
    try {
      future.get(timeUnit.multipliedBy(2L).getMillis(), MILLISECONDS);
      fail();
    } catch (TimeoutException ignored) {
    }
    mockery.assertIsSatisfied();

    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).getQuerySessions("123");
      will(returnList());
    }});
    future.get(timeUnit.multipliedBy(2L).getMillis(), MILLISECONDS);
    mockery.assertIsSatisfied();
  }

}
