package com.kaching.platform.queryengine;

import static com.google.common.base.Preconditions.checkState;

import java.util.Deque;
import java.util.LinkedList;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import com.google.common.util.concurrent.UncheckedExecutionException;
import com.kaching.util.LazyFuture;
import com.kaching.util.UncheckedInterruptedException;

public class LazyExecutorService extends StubExecutorService {
  
  private final Deque<LazyFuture<?>> allTasks = new LinkedList<>();
  
  public void completeAllTasks() {
    while (!allTasks.isEmpty()) {
      completeSingleTask();
    }
  }
  
  public Object completeSingleTask() {
    checkState(!allTasks.isEmpty(), "No tasks to complete");
    try {
      return allTasks.removeFirst().get();
    } catch (InterruptedException e) {
      throw new UncheckedInterruptedException(e);
    } catch (ExecutionException e) {
      throw new UncheckedExecutionException(e);
    }
  }

  @Override
  public Future<?> submit(final Runnable task) {
    LazyFuture<Object> future = new LazyFuture<>(() -> {
      task.run();
      return null;
    });
    allTasks.add(future);
    return future;
  }

  @Override
  public <T> Future<T> submit(Callable<T> task) {
    LazyFuture<T> future = new LazyFuture<>(() -> {
      try {
        return task.call();
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    });
    allTasks.add(future);
    return future;
  }

  @Override
  public void execute(Runnable task) {
    LazyFuture<Object> future = new LazyFuture<>(() -> {
      task.run();
      return null;
    });
    allTasks.add(future);
  }

}
