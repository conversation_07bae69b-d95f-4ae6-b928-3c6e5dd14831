package com.kaching.platform.queryengine;

import static com.google.common.collect.Maps.newHashMap;
import static com.kaching.platform.queryengine.StackTraceMonitorImpl.getStackTrace;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.emptyString;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Iterables;
import com.google.common.collect.Ordering;
import com.kaching.Author;
import com.kaching.platform.common.Option;

public class FakeStackTraceMonitor implements StackTraceMonitor {

  private final Map<String, Integer> m = newHashMap();

  @Override
  public synchronized void add(Throwable t) {
    add(getStackTrace(t));
  }

  private void add(String s) {
    if (m.containsKey(s)) {
      m.put(s, m.get(s) + 1);
    } else {
      m.put(s, 1);
    }
  }

  @Override
  public synchronized void add(Throwable t, Query<?> query) {
    add(getStackTrace(t, query));
  }

  @Override
  public synchronized void add(Throwable t, Set<Author> responsibleParties) {
    add(getStackTrace(t));
  }

  @Override
  public List<Entry<String, Integer>> getStackTraces() {
    return Ordering.from(StackTraceMonitorImpl.COMPARATOR).immutableSortedCopy(m.entrySet());
  }

  public List<Entry<String, Integer>> getStackTracesAndClear() {
    List<Entry<String, Integer>> stackTraces = getStackTraces();
    clearStackTraces();
    return stackTraces;
  }

  public String getOnlyStackTraceAndClear() {
    Entry<String, Integer> onlyElement = Iterables.getOnlyElement(getStackTracesAndClear());
    assertEquals("Expected only one exception", 1, onlyElement.getValue().intValue());
    return onlyElement.getKey();
  }

  public void assertOnlyStackTraceAndClear(String... substrings) {
    String onlyStacktrace = getOnlyStackTraceAndClear();
    assertSubstringsAreInStackTrace(onlyStacktrace, substrings);
  }

  public <T extends Exception> void assertOnlyStackTraceAndClear(Class<T> exceptionClass, String... substrings) {
    String onlyStacktrace = getOnlyStackTraceAndClear();
    assertThat(onlyStacktrace, containsString(exceptionClass.toString().replace("class ", "")));
    assertSubstringsAreInStackTrace(onlyStacktrace, substrings);
  }

  public void assertNoUnexpectedExceptions() {
    assertThat(
        "If your test adds exceptions to FakeStackTraceMonitor, make sure to assert on them and then clear them!",
        getStackTraces(),
        is(empty())
    );
  }

  @Override
  public void close() throws IOException {

  }

  @Override
  public void clearStackTraces() {
    m.clear();
  }

  @Override
  public Option<Author> getResponsibleParty(String fullyQualifiedClassName) {
    return Option.none();
  }

  @VisibleForTesting
  public void assertSubstringsAreInStackTrace(String stackTrace, String... substrings) {
    for (String s : substrings) {
      assertThat(s, is(not(emptyString())));
      assertThat(stackTrace, containsString(s));
    }
  }

}
