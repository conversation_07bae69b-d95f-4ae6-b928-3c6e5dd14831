package com.kaching.platform.queryengine.client;

import static org.junit.Assert.assertEquals;

import java.util.Timer;

import org.junit.Before;
import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.name.Names;
import com.kaching.platform.guice.CommonModule;
import com.kaching.platform.guice.KachingServices.BI;
import com.kaching.platform.guice.OptionsModule;
import com.kaching.platform.guice.TestingApplicationOptions;
import com.kaching.util.http.LongOnlineTimeoutPooling;
import com.kaching.util.http.LongTimeoutPooling;
import com.kaching.util.http.Pooling;

public class EnableBISmartClientOverrideTest {

  @Inject @Pooling SmartClient<BI> poolingClient;
  @Inject @LongTimeoutPooling SmartClient<BI> longTimeoutPoolingClient;
  @Inject @LongOnlineTimeoutPooling SmartClient<BI> longOnlineTimeoutPoolingClient;

  private Injector injector;

  @Before
  public void before() {
    injector = Guice.createInjector(
        new CommonModule(new TestingApplicationOptions()),
        new OptionsModule(new TestingApplicationOptions()),
        new EnableBISmartClientOverride(),
        new AbstractModule() {
          @Override
          protected void configure() {
            bind(Timer.class)
                .annotatedWith(Names.named("daemon"))
                .toInstance(new Timer(true));
          }
        });
    injector.injectMembers(this);
  }

  @Test
  public void inject() {
    assertEquals(BI.class, ((SmartHttpClient) poolingClient).getService());
    assertEquals(BI.class, ((SmartHttpClient) longTimeoutPoolingClient).getService());
    assertEquals(BI.class, ((SmartHttpClient) longOnlineTimeoutPoolingClient).getService());
  }

}