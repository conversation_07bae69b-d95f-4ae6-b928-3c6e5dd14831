package com.kaching.platform.queryengine;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class ToggleIkqQueryInvocationRecorderTest {

  @Test
  public void enable() {
    ToggleIkqQueryInvocationRecorder query = new ToggleIkqQueryInvocationRecorder(true);
    query.toggle = new IkqQueryInvocationRecordingDriver.Toggle();
    query.toggle.disable();

    assertTrue(query.process());
    assertTrue(query.toggle.isEnabled());
  }

  @Test
  public void disable() {
    ToggleIkqQueryInvocationRecorder query = new ToggleIkqQueryInvocationRecorder(false);
    query.toggle = new IkqQueryInvocationRecordingDriver.Toggle();
    query.toggle.enable();

    assertFalse(query.process());
    assertFalse(query.toggle.isEnabled());
  }

}
