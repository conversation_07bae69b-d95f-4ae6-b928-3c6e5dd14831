package com.kaching.platform.queryengine.fake;

import static com.wealthfront.test.Assert.assertSameInstant;

import org.joda.time.DateTime;
import org.junit.Test;

import com.wealthfront.util.time.DateTimeZones;

public class GetCurrentTimeTest {

  private static final DateTime NOW = new DateTime(2019, 1, 2, 3, 4, 5, DateTimeZones.ET);

  @Test
  public void process() {
    assertSameInstant(NOW, buildQuery().process());
  }

  private GetCurrentTime buildQuery() {
    GetCurrentTime query = new GetCurrentTime();
    query.now = NOW;
    return query;
  }

}