package com.kaching.platform.queryengine.predicates;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.joda.time.DateTime;
import org.junit.Test;

import com.wealthfront.entities.Market;

public class MarketOpenAndFullDayPredicateTest {

  @Test
  public void testFullMarketDay_IsTrue() {
    assertTrue(createFrom(2014, 11, 26, 12, 0).satisfied());
  }

  @Test
  public void testFullMarketDayAfterHours_IsFalse() {
    assertFalse(createFrom(2014, 11, 26, 16, 1).satisfied());
  }

  @Test
  public void testHalfMarketDay_IsFalse() {
    assertFalse(createFrom(2014, 11, 28, 12, 0).satisfied());
  }

  @Test
  public void testMarketClosedDay_IsFalse() {
    assertFalse(createFrom(2014, 11, 27, 12, 0).satisfied());
  }

  private MarketOpenAndFullDayPredicate createFrom(int year, int month, int day, int hour, int minute) {
    MarketOpenAndFullDayPredicate predicate = new MarketOpenAndFullDayPredicate();
    predicate.market = Market.MARKET;
    predicate.clock = () -> new DateTime(year, month, day, hour, minute, 0, 0, ET);
    return predicate;
  }
  
}
