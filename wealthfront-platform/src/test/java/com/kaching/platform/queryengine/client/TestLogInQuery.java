package com.kaching.platform.queryengine.client;

import com.kaching.entities.EmailAddress;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.SecureField;

public class TestLogInQuery extends AbstractQuery<Boolean> {

  private final EmailAddress emailAddress;

  @SecureField
  private final String password;

  public TestLogInQuery(EmailAddress emailAddress, String password) {
    this.emailAddress = emailAddress;
    this.password = password;
  }

  @Override
  public Boolean process() {
    throw new UnsupportedOperationException();
  }
}
