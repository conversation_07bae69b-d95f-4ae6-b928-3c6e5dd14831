package com.kaching;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.hibernate.type.Type;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.kaching.platform.common.Errors;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.hibernate.EntityWithArchivedState;
import com.kaching.platform.hibernate.EntityWithJoinedSubclasses;
import com.kaching.platform.hibernate.EntityWithJoinedTables;
import com.kaching.platform.hibernate.EntityWithSubclasses;
import com.kaching.platform.hibernate.HbmDbSchemaSyncCheck.DbColumn;
import com.kaching.util.tests.PersistentTestBase;
import com.wealthfront.avro.dbmetadata.DescribeTable;
import com.wealthfront.test.AllowDNSResolution;
import com.wealthfront.test.AllowLocalFileAccess;
import com.wealthfront.test.AllowNetworkAccess;

@AllowDNSResolution
@AllowNetworkAccess(endpoints = "*:443")
@AllowLocalFileAccess(paths = "*snappy*")
public class OfflineHbmDbSchemaSyncCheckTest extends PersistentTestBase {

  @BeforeClass
  public static void beforeClass() {
    configure(EntityWithJoinedSubclasses.class, EntityWithSubclasses.class, EntityWithJoinedTables.class,
        EntityWithArchivedState.class);
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithSubclasses() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck() {
      @Override
      List<DescribeTable> getDescribeTablesFromS3() {
        return ImmutableList.of(
            getDescribeTable("fake", "entity_with_subclasses", "id", false, "1"),
            getDescribeTable("fake", "entity_with_subclasses", "type", false, "subclass-one"),
            getDescribeTable("fake", "entity_with_subclasses", "active", true, null),
            getDescribeTable("fake", "entity_with_subclasses", "name", true, null));
      }
    };

    transacter.executeWithSession(dbSession -> Unchecked.run(() ->
        check.entitiesDbSchemaSyncCheck("fake", dbSession, Collections.singleton(EntityWithSubclasses.class))
    ));
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithArchivedState() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck() {
      @Override
      List<DescribeTable> getDescribeTablesFromS3() {
        return ImmutableList.of(
            getDescribeTable("fake", "entity_with_archived_state", "id", false, "1"),
            getDescribeTable("fake", "entity_with_archived_state", "some_boolean", false, "false"),
            getDescribeTable("fake", "entity_with_archived_state", "state", false, "ALPHA"),

            getDescribeTable("fake", "version_entity_with_archived_state_state", "id", false, "1"),
            getDescribeTable("fake", "version_entity_with_archived_state_state", "entity_with_archived_state_id",
                false, "1"),
            getDescribeTable("fake", "version_entity_with_archived_state_state", "version", false, "1"),
            getDescribeTable("fake", "version_entity_with_archived_state_state", "created_at", false, ""),
            getDescribeTable("fake", "version_entity_with_archived_state_state", "deleted", false, "0"),
            getDescribeTable("fake", "version_entity_with_archived_state_state", "value", false, "ALPHA",
                "varchar(255)")
        );
      }
    };

    transacter.executeWithSession(dbSession -> Unchecked.run(() ->
        check.entitiesDbSchemaSyncCheck("fake", dbSession, List.of(EntityWithArchivedState.class))
    ));
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithArchivedState_missingArchivedTable() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck() {
      @Override
      List<DescribeTable> getDescribeTablesFromS3() {
        return ImmutableList.of(
            getDescribeTable("fake", "entity_with_archived_state", "id", false, "1"),
            getDescribeTable("fake", "entity_with_archived_state", "some_boolean", false, "false"),
            getDescribeTable("fake", "entity_with_archived_state", "state", false, "ALPHA")
        );
      }

      @Override
      String buildExceptionMessage(Errors errors) {
        assertEquals(
            "no table version_entity_with_archived_state_state found for entity version_entity_with_archived_state_state",
            Iterables.getOnlyElement(errors.getMessages()));
        return super.buildExceptionMessage(errors);
      }
    };

    transacter.executeWithSession(dbSession -> {
      try {
        check.entitiesDbSchemaSyncCheck("fake", dbSession, Collections.singleton(EntityWithArchivedState.class));
        Assert.fail();
      } catch (Exception ignored) {
      }
    });
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithSubclasses_missingDiscriminator() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck() {
      @Override
      List<DescribeTable> getDescribeTablesFromS3() {
        return ImmutableList.of(
            getDescribeTable("fake", "entity_with_subclasses", "id", false, "1"),
            getDescribeTable("fake", "entity_with_subclasses", "active", true, null),
            getDescribeTable("fake", "entity_with_subclasses", "name", true, null));
      }

      @Override
      String buildExceptionMessage(Errors errors) {
        assertEquals(3, errors.size());
          assertTrue(errors.getMessages().containsAll(ImmutableList.of(
              "entity com.kaching.platform.hibernate.EntityWithSubclasses$Subclass1: " +
                  "missing db column in table entity_with_subclasses for hbm discriminator type",
              "entity com.kaching.platform.hibernate.EntityWithSubclasses$Subclass2: " +
                  "missing db column in table entity_with_subclasses for hbm discriminator type",
              "entity com.kaching.platform.hibernate.EntityWithSubclasses: " +
                  "missing db column in table entity_with_subclasses for hbm discriminator type")));
        return super.buildExceptionMessage(errors);
      }
    };

    transacter.executeWithSession(dbSession -> {
      try {
        check.entitiesDbSchemaSyncCheck("fake", dbSession, Collections.singleton(EntityWithSubclasses.class));
        Assert.fail();
      } catch (Exception ignored) {
      }
    });
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithJoinedSubclasses() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck();
    transacter.executeWithSession(dbSession -> {
      try {
        check.entitiesDbSchemaSyncCheck("fake", dbSession, Collections.singleton(EntityWithJoinedSubclasses.class));
      } catch (Exception e) {
        Assert.fail();
      }
    });
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithJoinedSubclasses_missingColumn() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck() {
      @Override
      List<DescribeTable> getDescribeTablesFromS3() {
        return ImmutableList.of(
            getDescribeTable("fake", "entity_with_joined_subclasses", "id", false, "1"),
            getDescribeTable("fake", "entity_with_joined_subclasses_subclass1", "id", false, "1"),
            getDescribeTable("fake", "entity_with_joined_subclasses_subclass1", "type", true, null));
      }

      @Override
      String buildExceptionMessage(Errors errors) {
        assertEquals(
            "entity com.kaching.platform.hibernate.EntityWithJoinedSubclasses: " +
                "missing db column in table entity_with_joined_subclasses for hbm property active",
            Iterables.getOnlyElement(errors.getMessages()));
        return super.buildExceptionMessage(errors);
      }
    };

    transacter.executeWithSession(dbSession -> {
      try {
        check.entitiesDbSchemaSyncCheck("fake", dbSession, Collections.singleton(EntityWithJoinedSubclasses.class));
        Assert.fail();
      } catch (Exception ignored) {
      }
    });
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithJoinedTables() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck();
    transacter.executeWithSession(dbSession -> {
      try {
        check.entitiesDbSchemaSyncCheck("fake", dbSession, Collections.singleton(EntityWithJoinedTables.class));
      } catch (Exception e) {
        Assert.fail();
      }
    });
  }

  @Test
  public void entitiesDbSchemaSyncCheck_entityWithJoinedTables_nullableColumnIsNotNullable() {
    OfflineHbmDbSchemaSyncCheck check = new FakeOfflineHbmDbSchemaSyncCheck() {
      @Override
      List<DescribeTable> getDescribeTablesFromS3() {
        return ImmutableList.of(
            getDescribeTable("fake", "entity_with_joined_tables", "id", false, "1"),
            getDescribeTable("fake", "entity_with_joined_tables", "type", false, "subclass-one"),
            getDescribeTable("fake", "entity_with_joined_tables", "active", false, "true"),
            getDescribeTable("fake", "entity_with_joined_tables_subclass1", "id", false, "1"),
            getDescribeTable("fake", "entity_with_joined_tables_subclass1", "state", true, null));
      }

      @Override
      String buildExceptionMessage(Errors errors) {
        assertEquals(
            "entity com.kaching.platform.hibernate.EntityWithJoinedTables: " +
                "property active is nullable but entity_with_joined_tables.active is not",
            Iterables.getOnlyElement(errors.getMessages()));
        return super.buildExceptionMessage(errors);
      }
    };

    transacter.executeWithSession(dbSession -> {
      try {
        check.entitiesDbSchemaSyncCheck("fake", dbSession, Collections.singleton(EntityWithJoinedTables.class));
        Assert.fail();
      } catch (Exception ignored) {
      }
    });
  }

  @Test
  public void getDbColumnMapForEntitiy() {
    OfflineHbmDbSchemaSyncCheck fakeCheck = new FakeOfflineHbmDbSchemaSyncCheck();

    Map<String, DbColumn> userAccountsResult =
        fakeCheck.getDbColumnMapForTable("user", "user_accounts");
    assertEquals(2, userAccountsResult.size());
    assertFalse(userAccountsResult.get("user_id").isNullable());
    assertTrue(userAccountsResult.get("user_id").hasDefaultValue());
    assertFalse(userAccountsResult.get("email_address").hasDefaultValue());

    Map<String, DbColumn> userSessionsResult =
        fakeCheck.getDbColumnMapForTable("user", "user_sessions");
    assertEquals(1, userSessionsResult.size());
    assertTrue(userSessionsResult.get("session_id").isIgnorable());
    assertFalse(userSessionsResult.get("session_id").isNullable());

    Map<String, DbColumn> userNoTableResult =
        fakeCheck.getDbColumnMapForTable("user", "user_demographic_data");
    assertTrue(userNoTableResult.isEmpty());

    Map<String, DbColumn> assetClassesResult =
        fakeCheck.getDbColumnMapForTable("bi", "asset_classes");
    assertEquals(1, assetClassesResult.size());
    assertTrue(assetClassesResult.get("risk_level").isIgnorable());
    assertTrue(assetClassesResult.get("risk_level").hasDefaultValue());

    Map<String, DbColumn> linkResult =
        fakeCheck.getDbColumnMapForTable("link", "some_table");
    assertTrue(linkResult.isEmpty());
  }

  @Test
  public void dbColumn_hasDefaultValue() {
    assertTrue(getDbColumn()
        .withDefaultValue("A")
        .build()
        .hasDefaultValue());
    assertFalse(getDbColumn()
        .build()
        .hasDefaultValue());
  }

  @Test
  public void dbColumn_isIgnorable() {
    assertTrue(getDbColumn()
        .withNullable(true)
        .build()
        .isIgnorable());
    assertTrue(getDbColumn()
        .withDefaultValue("A")
        .withNullable(true)
        .build()
        .isIgnorable());
    assertFalse(getDbColumn()
        .build()
        .isIgnorable());
    assertTrue(getDbColumn()
        .withDefaultValue("A")
        .build()
        .isIgnorable());
  }

  private static class FakeOfflineHbmDbSchemaSyncCheck extends OfflineHbmDbSchemaSyncCheck {

    @Override
    List<DescribeTable> getDescribeTablesFromS3() {
      return ImmutableList.of(
          getDescribeTable("fake", "entity_with_subclasses", "id", false, "1"),
          getDescribeTable("fake", "entity_with_subclasses", "type", false, "subclass-one"),
          getDescribeTable("fake", "entity_with_subclasses", "active", true, null),
          getDescribeTable("fake", "entity_with_subclasses", "name", true, null),
          getDescribeTable("fake", "entity_with_joined_subclasses", "id", false, "1"),
          getDescribeTable("fake", "entity_with_joined_subclasses", "active", true, null),
          getDescribeTable("fake", "entity_with_joined_subclasses_subclass1", "id", false, "1"),
          getDescribeTable("fake", "entity_with_joined_subclasses_subclass1", "type", true, null),
          getDescribeTable("fake", "entity_with_joined_tables", "id", false, "1"),
          getDescribeTable("fake", "entity_with_joined_tables", "type", false, "subclass-one"),
          getDescribeTable("fake", "entity_with_joined_tables", "active", true, null),
          getDescribeTable("fake", "entity_with_joined_tables_subclass1", "id", false, "1"),
          getDescribeTable("fake", "entity_with_joined_tables_subclass1", "state", true, null),
          getDescribeTable("user", "user_accounts", "user_id", false, "test1"),
          getDescribeTable("user", "user_accounts", "email_address", true, null),
          getDescribeTable("user", "user_sessions", "session_id", false, "test2"),
          getDescribeTable("bi", "asset_classes", "risk_level", false, "0"));
    }

    DescribeTable getDescribeTable(
        String dbName,
        String tableName,
        String fieldName,
        boolean nullable,
        String defaultValue) {
      return getDescribeTable(dbName, tableName, fieldName, nullable, defaultValue, "");
    }

    DescribeTable getDescribeTable(
        String dbName,
        String tableName,
        String fieldName,
        boolean nullable,
        String defaultValue,
        String fieldType) {
      return DescribeTable.newBuilder()
          .setDatabaseName(dbName)
          .setTableName(tableName)
          .setFieldName(fieldName)
          .setNullable(nullable)
          .setDefaultValue(defaultValue)
          .setFieldType(fieldType)
          .build();
    }

  }

  private PartialDbColumn getDbColumn() {
    return new PartialDbColumn();
  }

  private static class PartialDbColumn {

    private boolean nullable = false;
    private String defaultValue = null;
    private String type = null;
    private String name = null;
    private Type propertyType = null;

    public PartialDbColumn withNullable(boolean nullable) {
      this.nullable = nullable;
      return this;
    }

    public PartialDbColumn withDefaultValue(String defaultValue) {
      this.defaultValue = defaultValue;
      return this;
    }

    public PartialDbColumn withType(String type) {
      this.type = type;
      return this;
    }

    public PartialDbColumn withName(String name) {
      this.name = name;
      return this;
    }

    public PartialDbColumn withPropertyType(Type propertyType) {
      this.propertyType = propertyType;
      return this;
    }

    public DbColumn build() {
      return new DbColumn(nullable, defaultValue, type, name, propertyType);
    }

  }

}
