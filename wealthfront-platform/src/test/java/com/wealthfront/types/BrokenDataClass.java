package com.wealthfront.types;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Mu;
import com.kaching.util.id.IntIdentifiersTest;
import com.kaching.util.id.LongIdentifiersTest;
import com.kaching.util.types.ListOfDateTimeJsonType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeType(ExposeTo.FRONTEND)
@Entity
public class BrokenDataClass implements BrokenDataInterface {

  @Value
  String variable1 = "";
  @Value(nullable = true, optional = true, type = IntIdentifiersTest.SomePrimitiveIntId.JsonType.class)
  String variable2 = null;
  @Value(nullable = true, optional = true, types = { Mu.JsonType.class, LongIdentifiersTest.SomeLongId.JsonType.class })
  String variable3 = null;
  @Value(nullable = true, optional = true, types = { ListOfDateTimeJsonType.class })
  String variable4 = null;

  @Override
  public String getVariable1() {
    return variable1;
  }

  @Override
  public String getVariable2() {
    return variable2;
  }

  @Override
  public String getVariable3() {
    return variable3;
  }

  @Override
  public String getVariable4() {
    return variable4;
  }

}