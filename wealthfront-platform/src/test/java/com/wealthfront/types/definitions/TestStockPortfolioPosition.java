package com.wealthfront.types.definitions;

import java.math.BigDecimal;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = {
        ExposeTo.LOCAL,
        ExposeTo.BACKEND,
        ExposeTo.API_SERVER,
        ExposeTo.FRONTEND
    }
)
@Entity
public class TestStockPortfolioPosition {

  @Value(
      nullable = false
  )
  private Long instrumentId;

  @Value(
      nullable = false
  )
  private BigDecimal quantity;

  @Value(
      nullable = false
  )
  private BigDecimal marketValue;

  @Value(
      nullable = false
  )
  private BigDecimal percentageWeight;
  @Value(
      nullable = true,
      optional = true
  )
  private AbstractPositionDetails someAbstractPositionDetails;
  @Value(
      nullable = true,
      optional = true
  )
  private AbstractPositionDetails nonNullAbstractPositionDetails;

}
