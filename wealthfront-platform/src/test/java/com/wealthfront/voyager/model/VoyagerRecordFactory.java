package com.wealthfront.voyager.model;

import static com.wealthfront.util.time.DateTimeZones.ET;

import org.joda.time.DateTime;

import com.kaching.platform.hibernate.Id;
import com.kaching.user.UserId;
import com.kaching.util.tests.PartialObjects;
import com.wealthfront.voyager.model.VoyagerRecord.State;
import com.wealthfront.voyager.navigation.VoyagerStepId;

public class VoyagerRecordFactory {

  public static PartialVoyagerRecord voyagerRecord() {
    return new PartialVoyagerRecord();
  }

  public static class PartialVoyagerRecord extends PartialObjects.AbstractPartialObject<VoyagerRecord> {

    private Id<VoyagerRecord> voyagerRecordId = null;
    private UserId userId = new UserId(123);
    private VoyagerType voyagerType = VoyagerType.MORTGAGE_APPLICATION;
    private VoyagerStepId currentStep = VoyagerStepId.of("applications/1/loan-type");
    private DateTime createdAt = new DateTime(2022, 1, 1, 0, 0, 0, ET);
    private State state = State.ACTIVE;

    @Override
    public VoyagerRecord build() {
      VoyagerRecord voyagerRecord = new VoyagerRecord(userId, voyagerType, currentStep, createdAt, state);
      if (voyagerRecordId != null) {
        voyagerRecord.setId(voyagerRecordId);
      }
      return voyagerRecord;
    }

    public PartialVoyagerRecord withId(Id<VoyagerRecord> voyagerRecordId) {
      this.voyagerRecordId = voyagerRecordId;
      return this;
    }

    public PartialVoyagerRecord withId(int id) {
      return withId(Id.of(id));
    }

    public PartialVoyagerRecord withUserId(UserId userId) {
      this.userId = userId;
      return this;
    }

    public PartialVoyagerRecord withVoyagerType(VoyagerType voyagerType) {
      this.voyagerType = voyagerType;
      return this;
    }

    public PartialVoyagerRecord withCurrentStep(String currentStep) {
      this.currentStep = VoyagerStepId.of(currentStep);
      return this;
    }

    public PartialVoyagerRecord withCurrentStep(VoyagerStepId currentStep) {
      this.currentStep = currentStep;
      return this;
    }

    public PartialVoyagerRecord withCreatedAt(DateTime createdAt) {
      this.createdAt = createdAt;
      return this;
    }

    public PartialVoyagerRecord withState(State state) {
      this.state = state;
      return this;
    }
  }
}