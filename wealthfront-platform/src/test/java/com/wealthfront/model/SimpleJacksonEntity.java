package com.wealthfront.model;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@JsonClassDescription
@ExposeType(value = ExposeTo.FRONTEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
public class SimpleJacksonEntity {

  @JsonProperty int jacksonField;
  int nonJacksonField;

}
