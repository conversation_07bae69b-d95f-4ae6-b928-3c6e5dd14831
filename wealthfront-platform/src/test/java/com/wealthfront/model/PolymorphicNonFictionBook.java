package com.wealthfront.model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import javax.annotation.Nullable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

@com.fasterxml.jackson.annotation.JsonClassDescription("")
public class PolymorphicNonFictionBook extends PolymorphicBook {

  private static final com.kaching.platform.common.logging.Log log =
      com.kaching.platform.common.logging.Log.getLog("com.wealthfront.model.nullabilityLogging");

  @Nullable
  @JsonProperty("publisher")
  private String publisher = null;

  @JsonProperty("chapters")
  private List<String> chapters = new ArrayList<String>();

  public PolymorphicNonFictionBook() {}

  private PolymorphicNonFictionBook(
      @Nullable BookTypeEnum bookType,
      @Nullable String bookId,
      @Nullable String title,
      @Nullable String author,
      @Nullable Integer pagesCount,
      @Nullable String publisher,
      List<String> chapters
  ) {
    super(bookType, bookId, title, author, pagesCount);
    this.publisher = publisher;
    this.chapters = chapters;
  }

  /**
   * Get publisher
   *
   * @return publisher
   **/
  @Nullable
  @ApiModelProperty(value = "")
  public String getPublisher() {
    return publisher;
  }

  /**
   * Get chapters
   *
   * @return chapters
   **/

  @ApiModelProperty(value = "")
  public List<String> getChapters() {
    return chapters;
  }

  String nullabilityLogString() {
    StringBuilder sb = new StringBuilder();
    sb.append("(nullability log for class PolymorphicNonFictionBook) " + super.nullabilityLogString()).append("\n");
    sb.append("nullability log for class PolymorphicNonFictionBook {");
    sb.append("publisher:").append(publisher == null ? "null" : "present").append(",");
    sb.append("chapters:").append(chapters == null ? "null" : "present").append(",");
    sb.setLength(sb.length() - 1);
    sb.append("}");
    return sb.toString();
  }

  void validate() {
    super.validate();
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PolymorphicNonFictionBook {\n");
    sb.append("    ").append(super.toString()).append("\n");
    sb.append("    publisher: ").append(publisher).append("\n");
    sb.append("    chapters: ").append(chapters).append("\n");
    sb.append("}");
    return sb.toString();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PolymorphicNonFictionBook polymorphicNonFictionBook = (PolymorphicNonFictionBook) o;
    return Objects.equals(this.publisher, polymorphicNonFictionBook.publisher) &&
        Objects.equals(this.chapters, polymorphicNonFictionBook.chapters) &&
        super.equals(o);
  }

  @Override
  public int hashCode() {
    return Objects.hash(publisher, chapters, super.hashCode());
  }

  @Override
  public Builder copy() {
    return with(this);
  }

  public static Builder with() {
    return new Builder();
  }

  public static Builder with(PolymorphicNonFictionBook that) {
    Builder builder = new Builder();
    builder
        .publisher(that.getPublisher())
        .chapters(that.getChapters())
        .bookType(that.getBookType())
        .bookId(that.getBookId())
        .title(that.getTitle())
        .author(that.getAuthor())
        .pagesCount(that.getPagesCount());
    return builder;
  }

  public static class Builder extends PolymorphicBook.Builder<Builder, PolymorphicNonFictionBook> {

    @Nullable
    private String publisher = null;

    private List<String> chapters = new ArrayList<String>();

    @Override
    public Builder bookType(@Nullable BookTypeEnum bookType) {
      this.bookType = bookType;
      return this;
    }

    @Override
    public Builder bookId(@Nullable String bookId) {
      this.bookId = bookId;
      return this;
    }

    @Override
    public Builder title(@Nullable String title) {
      this.title = title;
      return this;
    }

    @Override
    public Builder author(@Nullable String author) {
      this.author = author;
      return this;
    }

    @Override
    public Builder pagesCount(@Nullable Integer pagesCount) {
      this.pagesCount = pagesCount;
      return this;
    }

    public Builder publisher(@Nullable String publisher) {
      this.publisher = publisher;
      return this;
    }

    public Builder chapters(List<String> chapters) {
      this.chapters = chapters;
      return this;
    }

    public Builder addChapters(String... chapters) {
      if (this.chapters == null) {
        this.chapters = new ArrayList<>();
      }
      this.chapters.addAll(Arrays.asList(chapters));
      return this;
    }

    @Override
    public PolymorphicNonFictionBook build() {
      PolymorphicNonFictionBook val =
          new PolymorphicNonFictionBook(bookType, bookId, title, author, pagesCount, publisher, chapters);
      log.debug(val.nullabilityLogString());
      val.validate();
      return val;
    }

    @Override
    public PolymorphicNonFictionBook buildForTesting() {
      return new PolymorphicNonFictionBook(bookType, bookId, title, author, pagesCount, publisher, chapters);
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      Builder builder = (Builder) o;
      return Objects.equals(this.publisher, builder.publisher) &&
          Objects.equals(this.chapters, builder.chapters) &&
          super.equals(o);
    }

    @Override
    public int hashCode() {
      return Objects.hash(publisher, chapters, super.hashCode());
    }

  }

}

