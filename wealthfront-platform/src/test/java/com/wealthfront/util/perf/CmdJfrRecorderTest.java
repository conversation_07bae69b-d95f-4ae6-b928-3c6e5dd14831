package com.wealthfront.util.perf;

import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

import org.hamcrest.Description;
import org.hamcrest.TypeSafeMatcher;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.junit.After;
import org.junit.Test;

import com.google.inject.Provider;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.UncheckedInterruptedException;
import com.wealthfront.util.perf.JfrRecorder.JfrContext;

public class CmdJfrRecorderTest {

  private static final DateTime now = new DateTime(2024, 1, 2, 3, 4, 5, ET);

  private final WFMockery mockery = Mockeries.mockery(true);
  private final CmdJfrRecorder delegate = mockery.mock(CmdJfrRecorder.class);
  private final Provider<DateTime> clock = mockery.mockClock();

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void scope() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=test-proc-123")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
    }});
    assertEquals(123, getRecorder().scope("test-proc-123", () -> 123).intValue());
  }

  @Test
  public void scope_lessThanMinDuration_thrownAway() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(clock).get();
      will(returnValue(now.plusMinutes(9)));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
    }});
    assertEquals(
        123,
        getRecorder()
            .scope(
                JfrRecorder.Config.uniquelyNamed("test-proc-123")
                    .setOption(JfrRecorder.Config::minimumDuration).to(Duration.standardMinutes(10))
                    .build(),
                () -> 123
            )
            .intValue()
    );
  }

  @Test
  public void scope_greaterThanMinDuration_saved() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(clock).get();
      will(returnValue(now.plusMinutes(11)));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=test-proc-123")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
    }});
    assertEquals(
        123,
        getRecorder()
            .scope(
                JfrRecorder.Config.uniquelyNamed("test-proc-123")
                    .setOption(JfrRecorder.Config::minimumDuration).to(Duration.standardMinutes(10))
                    .build(),
                () -> 123
            )
            .intValue()
    );
  }

  @Test
  public void scope_manualAbort() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
    }});
    assertEquals(123, getRecorder().scope("test-proc-123", context -> {
      context.abort();
      return 123;
    }).intValue());
  }

  @Test
  public void scope_overlappingSameName_oneDump() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      exactly(2).of(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=asdf", "filename=/tmp/asdf.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=asdf")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=asdf")));
    }});
    CmdJfrRecorder recorder = getRecorder();
    try (JfrContext ctx1 = recorder.jfrContext("asdf");
         JfrContext ctx2 = recorder.jfrContext("asdf")) {
      int ignored = 1 + 2;
    }
  }

  @Test
  public void scope_overlappingDifferingNames_twoDump() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      exactly(2).of(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=asdf", "filename=/tmp/asdf.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=asdf")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=asdf")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=asdf2", "filename=/tmp/asdf2.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=asdf2")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=asdf2")));
    }});
    CmdJfrRecorder recorder = getRecorder();
    try (JfrContext ctx1 = recorder.jfrContext("asdf");
         JfrContext ctx2 = recorder.jfrContext("asdf2")) {
      int ignored = 1 + 2;
    }
  }

  @Test
  public void invalidUniqueName() {
    assertThrows(IllegalArgumentException.class, "invalid unique name: test proc",
        () -> getRecorder().scope("test proc", () -> 123));
  }

  @Test
  public void scope_throw() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=test-proc-123")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
    }});
    assertThrows(RuntimeException.class, "oh no",
        () -> getRecorder().scope("test-proc-123", () -> {
          throw new RuntimeException("oh no");
        }));
  }

  @Test
  public void scope_startThrows() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      will(throwException(new IOException("bad handle")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
      will(throwException(new IOException("no such jfr")));
    }});
    assertEquals(123, getRecorder().scope("test-proc-123", () -> 123).intValue());
  }

  @Test
  public void scope_startThrows_deletesJfrFile() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      will(throwException(new IOException("bad handle")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
    }});
    assertEquals(123, getRecorder().scope("test-proc-123", () -> 123).intValue());
  }

  @Test
  public void scope_dumpThrows() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=test-proc-123")));
      will(throwException(new IOException("bad handle")));
    }});
    assertEquals(123, getRecorder().scope("test-proc-123", () -> 123).intValue());
  }

  @Test
  public void scope_stopThrows() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(clock).get();
      will(returnValue(now));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.dump", "name=test-proc-123")));
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.stop", "name=test-proc-123")));
      will(throwException(new IOException("bad handle")));
    }});
    assertEquals(123, getRecorder().scope("test-proc-123", () -> 123).intValue());
  }

  @Test
  public void scope_interrupted_abort() throws IOException, ExecutionException, InterruptedException {
    mockery.checking(new WExpectations() {{
      oneOf(delegate).runProc(withAnything(), with(stringArrayEquals(
          "jcmd", "10101", "JFR.start", "name=test-proc-123", "filename=/tmp/test-proc-123.jfr")));
      will(throwException(new InterruptedException("cease")));
    }});
    assertThrows(UncheckedInterruptedException.class, "java.lang.InterruptedException: cease",
        () -> getRecorder().scope("test-proc-123", () -> 123));
  }

  private CmdJfrRecorder getRecorder() {
    CmdJfrRecorder recorder = new CmdJfrRecorder() {
      @Override
      void runProc(ExecutorService executor, String[] args)
          throws IOException, InterruptedException, ExecutionException {
        delegate.runProc(executor, args);
      }

      @Override
      long getProcessId() {
        return 10101;
      }
    };
    recorder.clock = clock;
    return recorder;
  }

  private TypeSafeMatcher<String[]> stringArrayEquals(String... data) {
    return new TypeSafeMatcher<String[]>() {
      @Override
      protected boolean matchesSafely(String[] strings) {
        return Arrays.asList(strings).equals(Arrays.asList(data));
      }

      @Override
      public void describeTo(Description description) {
        description.appendText(Arrays.asList(data).toString());
      }
    };
  }

}