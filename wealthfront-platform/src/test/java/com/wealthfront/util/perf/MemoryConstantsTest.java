package com.wealthfront.util.perf;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import org.junit.Test;

public class MemoryConstantsTest {
  
  @Test
  public void test_concurrentMapOverhead() {
    int num = 10_000;
    Object[] control = new Object[num * 2];
    Map<Object, Object> testForRetained = new ConcurrentHashMap<>();
    for (int i = 0; i < num; i++) {
      testForRetained.put(UUID.randomUUID(), UUID.randomUUID());
      control[i * 2] = UUID.randomUUID();
      control[i * 2 + 1] = UUID.randomUUID();
    }
    // put breakpoint here and inspect
  }

}