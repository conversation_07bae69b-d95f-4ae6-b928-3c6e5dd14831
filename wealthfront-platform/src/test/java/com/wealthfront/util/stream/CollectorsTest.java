package com.wealthfront.util.stream;

import static com.wealthfront.util.stream.Collectors.singleOrDefault;
import static com.wealthfront.util.stream.Collectors.singleOrThrow;
import static java.util.Collections.emptySet;
import static org.hamcrest.Matchers.containsString;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.function.Supplier;
import java.util.stream.Collector;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.wealthfront.util.stream.Collectors.CollectorImpl;

public class CollectorsTest {

  Object streamObject = new Object();
  Object defaultObject = new Object();

  Supplier<Object> defaultSupplier = () -> defaultObject;

  @Test
  public void collectorImplTest() {
    Collector<Integer, ArrayList<Integer>, ImmutableList<Integer>> collector = new CollectorImpl<>(
        ArrayList::new,
        ArrayList::add,
        (l1, l2) -> {
          l1.addAll(l2);
          return l1;
        },
        ImmutableList::copyOf,
        emptySet()
    );
    List<Integer> collected = IntStream.range(1, 5)
        .boxed()
        .map(i -> i * 2)
        .collect(collector);
    assertEquals(ImmutableList.of(2, 4, 6, 8), collected);
  }

  @Test
  public void singleOrDefault_singleElement_shouldReturnThatElement() {
    Stream<Object> stream = Stream.builder().add(streamObject).build();
    assertEquals(streamObject, stream.collect(singleOrDefault(null)));
  }

  @Test
  public void singleOrDefault_singleNullElement_shouldReturnThatElement() {
    Stream<Object> stream = Stream.builder().add(null).build();
    assertEquals(null, stream.collect(singleOrDefault(null)));
  }

  @Test
  public void singleOrDefault_noElements_shouldReturnDefault() {
    Stream<Object> stream = Stream.builder().build();
    assertEquals(defaultObject, stream.collect(singleOrDefault(defaultSupplier)));
  }

  @Test
  public void singleOrDefault_multipleElements1_shouldReturnDefault() {
    Stream<Object> stream = Stream.builder().add(streamObject).add(streamObject).build();
    assertEquals(defaultObject, stream.collect(singleOrDefault(defaultSupplier)));
  }

  @Test
  public void singleOrDefault_multipleElements2_shouldReturnDefault() {
    Stream<Object> stream = Stream.builder().add(null).add(streamObject).build();
    assertEquals(defaultObject, stream.collect(singleOrDefault(defaultSupplier)));
  }

  @Test
  public void singleOrDefault_multipleElements3_shouldReturnDefault() {
    Stream<Object> stream = Stream.builder().add(streamObject).add(null).build();
    assertEquals(defaultObject, stream.collect(singleOrDefault(defaultSupplier)));
  }

  @Test
  public void singleOrDefault_multipleElements4_shouldReturnDefault() {
    Stream<Object> stream = Stream.builder().add(null).add(null).build();
    assertEquals(defaultObject, stream.collect(singleOrDefault(defaultSupplier)));
  }

  @Test
  public void singleOrThrow_noElements() {
    Stream<Object> stream = Stream.builder().build();
    try {
      stream.collect(singleOrThrow());
      fail();
    } catch (IllegalStateException e) {
      assertThat(e.getMessage(), containsString("zero"));
    }
  }

  @Test
  public void singleOrThrow_multipleElements() {
    Integer[] alreadyProcessedElementCount = new Integer[]{0};

    Stream<Object> stream = Stream.generate(() -> {
      if (alreadyProcessedElementCount[0] < 2) {
        alreadyProcessedElementCount[0]++;
        return streamObject;
      } else {
        fail("singleOrThrow should short-circuit after only two elements.");
        return null;
      }
    });

    try {
      stream.collect(singleOrThrow());
      fail();
    } catch (IllegalStateException e) {
      assertThat(e.getMessage(), containsString("multiple"));
    }
  }

  @Test
  public void singleOrThrow_specifiedExceptionMessage() {
    Stream<Object> stream = Stream.builder().build();
    try {
      stream.collect(singleOrThrow("custom message"));
      fail();
    } catch (IllegalStateException e) {
      assertThat(e.getMessage(), containsString("zero"));
      assertThat(e.getMessage(), containsString("custom message"));
    }
  }

  @Test
  public void singleOrThrow_specifiedException() {
    Stream<Object> stream = Stream.builder().build();
    try {
      stream.collect(singleOrThrow(() -> new NoSuchElementException("custom message")));
      fail();
    } catch (NoSuchElementException e) {
      assertThat(e.getMessage(), containsString("custom message"));
    }
  }

}