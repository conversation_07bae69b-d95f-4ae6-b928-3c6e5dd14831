<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.wealthfront</groupId>
    <artifactId>wealthfront-platform-parent</artifactId>
    <version>1.3149-SNAPSHOT</version>
  </parent>
  <artifactId>wealthfront-platform</artifactId>
  <version>1.3149-SNAPSHOT</version>
  <packaging>jar</packaging>
  <properties>
    <project.build.sourceJdk>17</project.build.sourceJdk>
    <project.build.targetJdk>17</project.build.targetJdk>
    <maven.compiler.release>17</maven.compiler.release>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${wlth.slf4j-api.version}</version>
        <scope>runtime</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-glass</artifactId>
      <version>1.183-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>jakarta.xml.bind</groupId>
      <artifactId>jakarta.xml.bind-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-impl</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>${wlth.httpclient.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>${wlth.httpcore.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <classifier>tests</classifier>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
      <version>${wlth.aws-java-sdk.version}</version>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-sns</artifactId>
      <version>${wlth.aws-java-sdk.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-core</artifactId>
      <version>${wlth.aws-java-sdk.version}</version>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-xray-recorder-sdk-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-xray-recorder-sdk-log4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.weakref</groupId>
      <artifactId>jmxutils</artifactId>
      <version>1.5</version>
    </dependency>
    <dependency>
      <groupId>org.reflections</groupId>
      <artifactId>reflections</artifactId>
      <version>0.9.12</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.collections</groupId>
      <artifactId>eclipse-collections</artifactId>
      <version>11.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util</artifactId>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-io</artifactId>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-server</artifactId>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-jmx</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.ftpserver</groupId>
      <artifactId>ftplet-api</artifactId>
      <version>1.1.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.ftpserver</groupId>
      <artifactId>ftpserver-core</artifactId>
      <version>1.1.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.avro</groupId>
      <artifactId>avro</artifactId>
      <version>1.11.4</version>
    </dependency>
    <dependency>
      <groupId>org.xerial.snappy</groupId>
      <artifactId>snappy-java</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>javax.mail</groupId>
      <artifactId>mail</artifactId>
    </dependency>
    <dependency>
      <groupId>gs.hitchin.hitchfs</groupId>
      <artifactId>hitchfs-prod</artifactId>
      <version>0.0.4</version>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>s3-connection</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>wealthfront-platform</artifactId>
          <groupId>com.wealthfront</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.rabbitmq</groupId>
      <artifactId>amqp-client</artifactId>
      <version>1.7.2</version>
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>com.vdurmont</groupId>
      <artifactId>emoji-java</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-commons</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.kaching.platform</groupId>
      <artifactId>kawala-common</artifactId>
    </dependency>
    <dependency>
      <groupId>org.perf4j</groupId>
      <artifactId>perf4j</artifactId>
      <version>0.9.14</version>
    </dependency>
    <dependency>
      <groupId>com.kaching.platform</groupId>
      <artifactId>kawala-converters</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kaching.platform</groupId>
      <artifactId>kawala-guice</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront.data</groupId>
      <artifactId>avro</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>wealthfront-platform</artifactId>
          <groupId>com.wealthfront</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-configuration</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-args4j</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-discovery</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-httpclient-31</artifactId>
      <version>1.1</version>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-time</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-time-json</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-rabbitmq-event</artifactId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <!-- do not upgrade this. migrate to mariadb-java-client instead. see PROD-35579 -->
      <version>8.0.23</version>
    </dependency>
    <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
      <version>3.3.1</version>
      <exclusions>
        <exclusion>
          <artifactId>waffle-jna</artifactId>
          <groupId>com.github.waffle</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>3.6.10.Final</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-ehcache</artifactId>
      <version>3.6.10.Final</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-c3p0</artifactId>
      <version>3.6.10.Final</version>
      <exclusions>
        <exclusion>
          <artifactId>c3p0</artifactId>
          <groupId>c3p0</groupId>
        </exclusion>
        <exclusion>
          <artifactId>com.mchange</artifactId>
          <groupId>c3p0</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.dropwizard.metrics</groupId>
      <artifactId>metrics-core</artifactId>
      <version>3.2.5</version>
    </dependency>
    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <version>2.7.3</version>
    </dependency>
    <dependency>
      <groupId>xerces</groupId>
      <artifactId>xercesImpl</artifactId>
      <version>2.10.0</version>
    </dependency>
    <dependency>
      <groupId>com.twolattes</groupId>
      <artifactId>jsonmarshaller</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mchange</groupId>
      <artifactId>c3p0</artifactId>
      <version>0.9.5.5</version>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.19.4</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz</artifactId>
      <version>1.7.2</version>
    </dependency>
    <dependency>
      <groupId>wf</groupId>
      <artifactId>collectd-0.1.0.jar</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>wf</groupId>
      <artifactId>zkclient-0.1-dev.jar</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
      <version>3.8.0</version>
      <exclusions>
        <exclusion>
          <artifactId>logback-core</artifactId>
          <groupId>ch.qos.logback</groupId>
        </exclusion>
        <exclusion>
          <artifactId>logback-classic</artifactId>
          <groupId>ch.qos.logback</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>xml-apis</groupId>
      <artifactId>xml-apis</artifactId>
      <version>1.4.01</version>
    </dependency>
    <dependency>
      <groupId>javax.transaction</groupId>
      <artifactId>jta</artifactId>
      <version>1.1</version>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
    </dependency>
    <dependency>
      <groupId>aopalliance</groupId>
      <artifactId>aopalliance</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.apis</groupId>
      <artifactId>google-api-services-drive</artifactId>
      <version>${wlth.google.apis.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-jackson2</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-oauth2-http</artifactId>
      <version>${wlth.google.oauth2.http.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>gson</artifactId>
          <groupId>com.google.code.gson</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.inject.extensions</groupId>
      <artifactId>guice-multibindings</artifactId>
      <version>4.2.3</version>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
    </dependency>
    <dependency>
      <groupId>gs.hitchin.hitchfs</groupId>
      <artifactId>hitchfs-test</artifactId>
      <version>0.0.4</version>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <version>3.1.0</version>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.6</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-email</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>wf</groupId>
      <artifactId>voldemort-0.90.1.jar</artifactId>
      <version>1.0</version>
    </dependency>
    <dependency>
      <groupId>com.wealthfront.guice</groupId>
      <artifactId>wealthfront-guice</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dbunit</groupId>
      <artifactId>dbunit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.wealthfront.platform</groupId>
      <artifactId>sample-protobuf</artifactId>
      <version>1.5</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-test-commons</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>wealthfront-platform</artifactId>
          <groupId>com.wealthfront</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.wealthfront.test</groupId>
      <artifactId>backend-test-configuration</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-configuration</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockftpserver</groupId>
      <artifactId>MockFtpServer</artifactId>
      <version>2.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.sshd</groupId>
      <artifactId>sshd-core</artifactId>
      <version>1.7.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.mwiede</groupId>
      <artifactId>jsch</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <version>3.6</version>
    </dependency>
    <dependency>
      <groupId>net.java.dev.jets3t</groupId>
      <artifactId>jets3t</artifactId>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpg-jdk15on</artifactId>
    </dependency>
    <dependency>
      <groupId>com.upplication</groupId>
      <artifactId>s3fs</artifactId>
      <version>1.2.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.jimfs</groupId>
      <artifactId>jimfs</artifactId>
      <version>1.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.lingala.zip4j</groupId>
      <artifactId>zip4j</artifactId>
      <version>1.3.2</version>
    </dependency>
    <dependency>
      <groupId>net.spy</groupId>
      <artifactId>spymemcached</artifactId>
      <version>2.12.3</version>
    </dependency>
    <dependency>
      <groupId>io.sentry</groupId>
      <artifactId>sentry</artifactId>
      <version>1.7.10</version>
    </dependency>
    <dependency>
      <groupId>com.squareup</groupId>
      <artifactId>javapoet</artifactId>
      <version>1.11.1</version>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-kafka-event</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>snappy-java</artifactId>
          <groupId>org.xerial.snappy</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>antlr</groupId>
      <artifactId>antlr</artifactId>
      <version>2.7.7</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>3.2.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>commons-cli</groupId>
      <artifactId>commons-cli</artifactId>
      <version>1.4</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-core</artifactId>
      <version>7.0.0</version>
    </dependency>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna</artifactId>
      <version>5.12.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mariadb</artifactId>
      <version>1.15.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-profile-renderer</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jetbrains</groupId>
      <artifactId>annotations</artifactId>
      <version>24.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>${swagger.version}</version>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-tests-2</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <ignoredUnusedDeclaredDependencies>
            <ignoredUnusedDeclaredDependency>org.javassist:javassist</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.amazonaws:aws-java-sdk-sns</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>wf:spy-2.4.jar</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>antlr:antlr</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-collections:commons-collections</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-cli:commons-cli</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.slf4j:slf4j-api</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.google.inject.extensions:guice-multibindings</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront.test:backend-test-configuration</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.mchange:c3p0</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>io.dropwizard.metrics:metrics-core</ignoredUnusedDeclaredDependency>
          </ignoredUnusedDeclaredDependencies>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources-including-tests</id>
            <phase>package</phase>
            <configuration>
              <skipSource>false</skipSource>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <configuration>
          <rules>
            <bannedDependencies>
              <excludes>
                <exclude>wealthfront-models</exclude>
              </excludes>
            </bannedDependencies>
          </rules>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
