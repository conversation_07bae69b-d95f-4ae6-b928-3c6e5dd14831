<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.wealthfront</groupId>
    <artifactId>wealthfront-parent</artifactId>
    <version>9509</version>
    <relativePath />
  </parent>
  <artifactId>wealthfront-platform-parent</artifactId>
  <version>1.3149-SNAPSHOT</version>
  <packaging>pom</packaging>
  <modules>
    <module>wealthfront-glass</module>
    <module>wealthfront-platform</module>
  </modules>
  <scm>
    <connection>scm:git:ssh://*****************:7999/back/wealthfront-platform.git</connection>
    <developerConnection>scm:git:ssh://*****************:7999/back/wealthfront-platform.git</developerConnection>
    <tag>HEAD</tag>
  </scm>
  <properties>
    <project.build.sourceJdk>17</project.build.sourceJdk>
    <project.build.targetJdk>17</project.build.targetJdk>
    <maven.compiler.release>17</maven.compiler.release>
    <swagger.version>1.5.9</swagger.version>
  </properties>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>never</updatePolicy>
        <checksumPolicy>fail</checksumPolicy>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>wealthfront-releases</id>
      <name>Wealthfront Releases</name>
      <url>https://maven.wlth.fr/nexus/content/repositories/wealthfront-releases</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
        <checksumPolicy>fail</checksumPolicy>
      </snapshots>
      <id>wealthfront-snapshots</id>
      <name>Wealthfront Snapshots</name>
      <url>https://maven.wlth.fr/nexus/content/repositories/wealthfront-snapshots</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>public</id>
      <name>Public</name>
      <url>https://maven.wlth.fr/nexus/content/repositories/public</url>
    </repository>
  </repositories>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <ignoredUnusedDeclaredDependencies>
            <ignoredUnusedDeclaredDependency>org.javassist:javassist</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.amazonaws:aws-java-sdk-sns</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>wf:spy-2.4.jar</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>antlr:antlr</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-collections:commons-collections</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-cli:commons-cli</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.slf4j:slf4j-api</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.google.inject.extensions:guice-multibindings</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront.test:backend-test-configuration</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.mchange:c3p0</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>io.dropwizard.metrics:metrics-core</ignoredUnusedDeclaredDependency>
          </ignoredUnusedDeclaredDependencies>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources-including-tests</id>
            <phase>package</phase>
            <configuration>
              <skipSource>false</skipSource>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
