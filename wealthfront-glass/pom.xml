<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.wealthfront</groupId>
    <artifactId>wealthfront-platform-parent</artifactId>
    <version>1.3149-SNAPSHOT</version>
  </parent>
  <artifactId>wealthfront-glass</artifactId>
  <version>1.183-SNAPSHOT</version>
  <packaging>jar</packaging>
  <properties>
    <project.build.sourceJdk>17</project.build.sourceJdk>
    <project.build.targetJdk>17</project.build.targetJdk>
    <maven.compiler.release>17</maven.compiler.release>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${wlth.slf4j-api.version}</version>
        <scope>runtime</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-commons</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.kaching.platform</groupId>
      <artifactId>kawala-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kaching.platform</groupId>
      <artifactId>kawala-converters</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kaching.platform</groupId>
      <artifactId>kawala-guice</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-configuration</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-time</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-time-json</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
      <version>3.3.1</version>
      <exclusions>
        <exclusion>
          <artifactId>waffle-jna</artifactId>
          <groupId>com.github.waffle</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <version>2.7.3</version>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.6</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-email</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dbunit</groupId>
      <artifactId>dbunit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-test-commons</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>wealthfront-platform</artifactId>
          <groupId>com.wealthfront</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.wealthfront.test</groupId>
      <artifactId>backend-test-configuration</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.wealthfront</groupId>
      <artifactId>wealthfront-configuration</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mariadb</artifactId>
      <version>1.15.3</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-tests-2</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <ignoredUnusedDeclaredDependencies>
            <ignoredUnusedDeclaredDependency>org.javassist:javassist</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.amazonaws:aws-java-sdk-sns</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>wf:spy-2.4.jar</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>antlr:antlr</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-collections:commons-collections</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-cli:commons-cli</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.slf4j:slf4j-api</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.google.inject.extensions:guice-multibindings</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront.test:backend-test-configuration</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.mchange:c3p0</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>io.dropwizard.metrics:metrics-core</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.logging.log4j:log4j-core</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.logging.log4j:log4j-api</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.commons:commons-email</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.commons:commons-io</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.commons:commons-lang3</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.commons:commons-lang</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.hsqldb:hsqldb</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>joda-time:joda-time</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.mariadb.jdbc:mariadb-java-client</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.google.guava:guava</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront:wealthfront-time</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront:wealthfront-configuration</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.kaching.platform:kawala-common</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.kaching.platform:kawala-converters</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.kaching.platform:kawala-guice</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront:wealthfront-time-json</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-lang:commons-lang</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront:wealthfront-commons</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront:wealthfront-test-commons</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront.test:backend-test-configuration</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.mchange:c3p0</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>io.dropwizard.metrics:metrics-core</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.logging.log4j:log4j-core</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.apache.logging.log4j:log4j-api</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.hsqldb:hsqldb</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>joda-time:joda-time</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>org.mariadb.jdbc:mariadb-java-client</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.google.guava:guava</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront:wealthfront-time</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.wealthfront:wealthfront-configuration</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.kaching.platform:kawala-common</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>commons-io:commons-io</ignoredUnusedDeclaredDependency>
            <ignoredUnusedDeclaredDependency>com.google.inject:guice</ignoredUnusedDeclaredDependency>
          </ignoredUnusedDeclaredDependencies>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources-including-tests</id>
            <phase>package</phase>
            <configuration>
              <skipSource>false</skipSource>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
